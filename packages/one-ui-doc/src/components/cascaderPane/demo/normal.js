import React, {PureComponent} from 'react';
import {CascaderPane} from '@baidu/one-ui';

const options = [
    {
        value: 'beijing',
        label: '北京',
        children: [
            {
                value: 'haidian',
                label: '海淀区',
                loading: true,
                children: [
                    {
                        value: 'houchangcun',
                        label: '后厂村'
                    }
                ]
            }
        ]
    },
    {
        value: 'hunan',
        label: '湖南省',
        children: [
            {
                value: 'changsha',
                label: '长沙市',
                children: [
                    {
                        value: 'yuelushan',
                        label: '岳麓山'
                    },
                    {
                        value: 'juzizhou',
                        label: '橘子洲'
                    },
                    {
                        value: 'hunanweishi',
                        label: '湖南卫视'
                    }
                ]
            }
        ]
    }
];

export default class NoramlCascader extends PureComponent {
    state = {
        value: [],
        options: []
    }

    onSelect = (targetOption, menuIndex, activeValue) => {
        this.setState({
            value: activeValue
        });
    }

    onSelectNew = (a, b, c) => {
        console.log(a);
        console.log(b);
        console.log(c);
    }

    componentDidMount() {
        this.setState({
            options
        });
    }

    render() {
        return (
            <div>
                <br />
                <br />
                受控
                <br />
                <br />
                <CascaderPane
                    options={this.state.options}
                    value={this.state.value}
                    onSelect={this.onSelect}
                />
                <br />
                <br />
                非受控
                <br />
                <br />
                <CascaderPane
                    options={this.state.options}
                    onSelect={this.onSelectNew}
                />
            </div>
        );
    }
}
