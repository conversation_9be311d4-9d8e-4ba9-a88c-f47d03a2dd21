import React, {useCallback, useState} from 'react';
import {CascaderPane, Tooltip} from '@baidu/one-ui';

const options = [
    {
        value: '1',
        label: '计划1',
        isLeaf: false
    },
    {
        value: '2',
        label: '计划2',
        disabled: true,
        isLeaf: false
    },
    {
        value: '3',
        label: '计划3'
    }
];

const genValue = () => Math.random().toString(32).slice(2);

export default () => {
    const [data, setData] = useState(options);
    const [value, setValue] = useState([]);

    const loadData = useCallback(({
        parent,
        scope,
        trigger
    }) => {
        return new Promise(resolve => {
            setTimeout(() => {
                const item = data.find(o => o.value === parent.value);
                const value1 = genValue();
                const value2 = genValue();
                item.children = [{
                    value: value1,
                    label: `单元${value1}`
                },{
                    value: value2,
                    label: `单元${value2}`
                }];
                setData([...data]);

                resolve();
            }, 3000);
        });
    }, [data]);

    return (
        <CascaderPane
            options={data}
            value={value}
            loadData={loadData}
            onSelect={(option, menuIndex, value) => setValue(value)}
            renderOption={
                ({node, option}) => !option.disabled
                    ? node
                    : <Tooltip title="本计划，不支持选择">{node}</Tooltip>
            }
        />
    );
}
