import React, {useState} from 'react';
import {CascaderPane, Tooltip, Checkbox} from '@baidu/one-ui';

const beijing = {
    value: 'beijing',
    label: '北京',
    selectable: false,
    expandType: 'toggle',
    children: [
        {
            value: 'haidian',
            label: '海淀区',
            children: [
                {
                    value: 'houchangcun',
                    label: '后厂村',
                    children: [
                        {
                            value: 'hcc1',
                            label: '后厂村1店'
                        },
                        {
                            value: 'hcc2',
                            label: '后厂村2店'
                        }
                    ]
                }
            ]
        },
        {
            value: 'test',
            label: '禁选区',
            disabled: true
        }
    ]
};

const options = [
    {
        value: 'selected',
        label: '已选地区',
        expandType: 'inline',
        selectable: false,
        children: [
            beijing
        ]
    },
    {
        value: 'all',
        label: '全部地区',
        expandType: 'inline',
        selectable: false,
        children: [
            {
                value: 'hunan',
                label: '湖南省',
                expandType: 'toggle',
                selectable: false,
                children: [
                    {
                        value: 'changsha',
                        label: '长沙市',
                        children: [
                            {
                                value: 'yuelushan',
                                label: '岳麓山',
                                children: [
                                    {
                                        value: 'yls1',
                                        label: '岳麓山1店'
                                    },
                                    {
                                        value: 'yls2',
                                        label: '岳麓山2店'
                                    }
                                ]
                            },
                            {
                                value: 'hunanweishi',
                                label: '湖南卫视'
                            },
                            {
                                value: 'juzizhou',
                                label: '橘子洲',
                                children: [
                                    {
                                        value: 'jzz1',
                                        label: '橘子洲1店'
                                    },
                                    {
                                        value: 'jzz2',
                                        label: '橘子洲2店'
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            beijing
        ]
    }
];
export default () => {
    const [value, setValue] = useState([]);
    const [multiple, setMultiple] = useState(false);
    console.log(value);
    let props;
    if (multiple) {
        props = {
            onCheckboxChange: setValue,
            checkedKeys: value
        };
    }
    else {
        props = {
            onSelect: (_, __, value) => setValue(value),
            value
        };
    }
    return (
        <>
            <Checkbox
                checked={multiple}
                onChange={e => {
                    setValue([]);
                    setMultiple(e.target.checked);
                }}
            >
                多选
            </Checkbox>
            <br />
            <br />
            <CascaderPane
                options={options}
                showCheckbox={multiple}
                renderOption={
                    ({node, option}) => (
                        !option.disabled
                            ? node
                            : <Tooltip title="本区无数据，不支持选择">{node}</Tooltip>
                    )
                }
            />
        </>
    );
};
