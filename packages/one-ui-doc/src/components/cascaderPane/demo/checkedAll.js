import React, {PureComponent} from 'react';
import {CascaderPane, Checkbox} from '@baidu/one-ui';

const options = [
    {
        value: 'beijing',
        label: '北京',
        children: [
            {
                value: 'haidian',
                label: '海淀区',
                children: [
                    {
                        value: 'houchangcun',
                        label: '后厂村'
                    }
                ]
            }
        ]
    },
    {
        value: 'hunan',
        label: '湖南省',
        children: [
            {
                value: 'changsha',
                label: '长沙市',
                children: [
                    {
                        value: 'yuelushan',
                        label: '岳麓山'
                    },
                    {
                        value: 'juzizhou',
                        label: '橘子洲'
                    },
                    {
                        value: 'hunanweishi',
                        label: '湖南卫视'
                    }
                ]
            },
            {
                value: 'zhuzhou',
                label: '株洲',
                children: [
                    {
                        value: 'mougedifang',
                        label: '某个地方'
                    }
                ]
            }
        ]
    }
];

export default class NoramlCascader extends PureComponent {
    state = {
        value: [],
        options: [],
        checkedkeys: [],
        checkAll: false
    }

    onSelect = (targetOption, menuIndex, activeValue) => {
        this.setState({
            value: activeValue
        });
    }

    componentDidMount() {
        this.setState({
            options
        });
    }

    onChange = e => {
        const checked = e.target.checked;
        if (checked) {
            this.setState({
                checkAll: checked,
                checkedkeys: ['beijing', 'hunan']
            });
        } else {
            this.setState({
                checkAll: checked,
                checkedkeys: []
            });
        }
    }

    render() {
        return (
            <div>
                <br />
                <br />
                <CascaderPane
                    options={this.state.options}
                    checkedKeys={this.state.checkedkeys}
                    showCheckbox
                    paneWidth={400}
                    CustomItemRender={(
                        <Checkbox
                            checked={this.state.checkAll}
                            onChange={this.onChange}
                        >
                            全选
                        </Checkbox>
                    )}
                    onCheckboxChange={checkedkeys => {
                        console.log('checkedkeys', checkedkeys);
                        this.setState({
                            checkedkeys
                        });
                    }}
                    showSearch
                    firstColumnGroup={[
                        {
                            label: '华北',
                            value: 'huabei',
                            children: ['beijing']
                        },
                        {
                            label: '华南',
                            value: 'huanan',
                            children: ['hunan']
                        }
                    ]}
                />
            </div>
        );
    }
}
