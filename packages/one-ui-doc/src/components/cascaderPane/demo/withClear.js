import React, {PureComponent} from 'react';
import {CascaderPane} from '@baidu/one-ui';

const options = [
    {
        value: 'beijing',
        label: '北京',
        children: [
            {
                value: 'haidian',
                label: '海淀区',
                children: [
                    {
                        value: 'houchangcun',
                        label: '后厂村'
                    }
                ]
            }
        ]
    },
    {
        value: 'hunan',
        label: '湖南省',
        children: [
            {
                value: 'changsha',
                label: '长沙市',
                children: [
                    {
                        value: 'yuelushan',
                        label: '岳麓山'
                    },
                    {
                        value: 'juzizhou',
                        label: '橘子洲'
                    },
                    {
                        value: 'hunanweishi',
                        label: '湖南卫视'
                    }
                ]
            },
            {
                value: 'zhuzhou',
                label: '株洲',
                children: [
                    {
                        value: 'mougedifang',
                        label: '某个地方'
                    }
                ]
            }
        ]
    }
];

for (let i = 0; i < 20; i++) {
    options.push({
        value: `value${i}`,
        label: `地区${i}`
    });
    options[0].children.push({
        value: `2value${i}`,
        label: `二级地区${i}`
    });
    options[0].children[0].children.push({
        value: `3value${i}`,
        label: `三级地区${i}`
    });
}

export default class NoramlCascader extends PureComponent {
    state = {
        value: [],
        options: [],
        checkedkeys: [],
        searchValue: ''
    }

    onSelect = (targetOption, menuIndex, activeValue) => {
        this.setState({
            value: activeValue
        });
    }

    componentDidMount() {
        this.setState({
            options
        });
    }

    onClickSearchItem = searchOption => {
        const values = searchOption.map(option => option.value);
        this.setState({
            searchValue: '',
            checkedkeys: [values[values.length - 1]]
        });
    }

    onChangeSearch = e => {
        const newState = {
            searchValue: e.value
        };
        this.setState(newState);
    }

    onClear = e => {
        const newState = {
            searchValue: ''
        };
        this.setState(newState);
    }

    render() {
        return (
            <div>
                <br />
                <br />
                <CascaderPane
                    options={this.state.options}
                    checkedKeys={this.state.checkedkeys}
                    showCheckbox
                    onCheckboxChange={checkedkeys => {
                        console.log('checkedkeys', checkedkeys);
                        this.setState({
                            checkedkeys
                        });
                    }}
                    showSearch
                    searchValue={this.state.searchValue}
                    onInputChange={this.onChangeSearch}
                    onClickSearchItem={this.onClickSearchItem}
                    searchProps={{
                        placeholder: '请输入',
                        showClear: true,
                        onClear: this.onClear
                    }}
                />
            </div>
        );
    }
}
