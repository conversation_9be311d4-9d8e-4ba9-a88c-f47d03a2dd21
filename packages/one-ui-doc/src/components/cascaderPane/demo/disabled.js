import React, {PureComponent, useState} from 'react';
import {CascaderPane, Stack} from '@baidu/one-ui';

const options = [
    {
        value: 'beijing',
        label: '北京',
        children: [
            {
                value: 'haidian',
                label: '海淀区',
                children: [
                    {
                        value: 'houchangcun',
                        label: '后厂村'
                    }
                ]
            }
        ]
    },
    {
        value: 'hunan',
        label: '湖南省',
        children: [
            {
                value: 'changsha',
                label: '长沙市',
                children: [
                    {
                        value: 'yuelushan',
                        label: '岳麓山'
                    },
                    {
                        value: 'juzizhou',
                        label: '橘子洲',
                        disabled: true
                    },
                    {
                        value: 'hunanweishi',
                        label: '湖南卫视'
                    }
                ]
            },
            {
                value: 'zhuzhou',
                label: '株洲',
                disabled: true,
                children: [
                    {
                        value: 'mougedifang',
                        label: '某个地方'
                    },
                    {
                        value: 'mougedifang2',
                        label: '某个地方2'
                    }
                ]
            }
        ]
    }
];

export default () => {
    const [checkedkeys, setCheckedKeys] = useState(['yuelushan']);
    const [value, setValue] = useState(['hunan', 'changsha', 'yuelushan']);
    console.log('checkedkeys', checkedkeys);

    return (
        <Stack gap="large">
            <div>
                <div>单选</div>
                <CascaderPane
                    options={options}
                    value={value}
                    onSelect={(_, __, value) => setValue(value)}
                    showSearch
                />
            </div>
            <div>
                <div>多选</div>
                <CascaderPane
                    options={options}
                    checkedKeys={checkedkeys}
                    showCheckbox
                    onCheckboxChange={checkedkeys => setCheckedKeys(checkedkeys)}

                    showSearch
                />
            </div>
        </Stack>
    );
}
