import React, {useState} from 'react';
import {CascaderPane, Tooltip} from '@baidu/one-ui';

const options = [
    {
        value: 'selected',
        label: '已选地区',
        children: [
            {
                value: 'haidian',
                label: '海淀区',
                children: [
                    {
                        value: 'houchangcun',
                        label: '后厂村'
                    }
                ]
            }
        ]
    },
    {
        value: 'beijing',
        label: '北京',
        children: [
            {
                value: 'haidian',
                label: '海淀区',
                children: [
                    {
                        value: 'houchangcun',
                        label: '后厂村'
                    }
                ]
            },
            {
                value: 'test',
                label: '禁选区',
                disabled: true
            }
        ]
    },
    {
        value: 'hunan',
        label: '湖南省',
        children: [
            {
                value: 'changsha',
                label: '长沙市',
                children: [
                    {
                        value: 'yuelushan',
                        label: '岳麓山'
                    },
                    {
                        value: 'juzizhou',
                        label: '橘子洲'
                    },
                    {
                        value: 'hunanweishi',
                        label: '湖南卫视'
                    }
                ]
            }
        ]
    }
];

export default () => {
    const [value, setValue] = useState([]);
    return (
        <CascaderPane
            options={options}
            value={value}
            onSelect={(option, menuIndex, value) => setValue(value)}
            firstColumnGroup
            renderOption={
                ({node, option}) => !option.disabled
                    ? node
                    : <Tooltip title="本区无数据，不支持选择">{node}</Tooltip>
            }
        />
    );
};
