import React, {useState} from 'react';
import {CascaderPane, Stack} from '@baidu/one-ui';

const options = [
    {
        value: 'beijing',
        label: '北京',
        children: [
            {
                value: 'haidian',
                label: '海淀区',
                children: [
                    {
                        value: 'houchangcun',
                        label: '后厂村'
                    }
                ]
            }
        ]
    },
    {
        value: 'hunan',
        label: '湖南省',
        children: [
            {
                value: 'changsha',
                label: '长沙市',
                children: [
                    {
                        value: 'yuelushan',
                        label: '岳麓山'
                    },
                    {
                        value: 'juzizhou',
                        label: '橘子洲'
                    },
                    {
                        value: 'hunanweishi',
                        label: '湖南卫视'
                    }
                ]
            }
        ]
    }
];

export default () =>  {
    const [checkedkeys, setCheckedKeys] = useState(['yuelushan']);
    console.log('checkedkeys', checkedkeys);

    return (
        <Stack gap="large">
            <div>
                <div>非受控</div>
                <CascaderPane
                    options={options}
                    defaultCheckedKeys={['beijing', 'changsha']}
                    showCheckbox
                />
            </div>
            <div>
                <div>受控</div>
                <CascaderPane
                    options={options}
                    checkedKeys={checkedkeys}
                    showCheckbox
                    onCheckboxChange={checkedkeys => setCheckedKeys(checkedkeys)}
                />
            </div>
        </Stack>
    );
}
