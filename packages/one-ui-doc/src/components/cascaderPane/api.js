export default {
    CascaderPane: [
        {
            param: 'value',
            type: 'array',
            desc: '展开的key值，对应option的value，受控属性',
            option: '',
            default: ''
        },
        {
            param: 'defaultValue',
            type: 'array',
            desc: '默认展开的key值，对应option的value，非受控属性',
            option: '',
            default: ''
        },
        {
            param: 'options',
            type: 'array',
            desc: '级联面板的数据源，参考cascader的options',
            option: '',
            default: '[]'
        },
        {
            param: 'expandTrigger',
            type: 'string',
            desc: '触发下一级展开的方式',
            option: 'click | hover',
            default: 'click'
        },
        {
            param: 'onSelect',
            type: 'func(targetOption, menuIndex, activeValue)',
            desc: '点击item触发',
            option: '',
            default: ''
        },
        {
            param: 'visible',
            type: 'bool',
            desc: '面板是否visible，受控属性',
            option: '',
            default: ''
        },
        {
            param: 'cascaderPaneStyle',
            type: 'object',
            desc: '级联面板的object',
            option: '',
            default: ''
        },
        {
            param: 'className',
            type: 'string',
            desc: '自定义样式类名',
            option: '',
            default: ''
        },
        {
            param: 'fieldNames',
            type: 'object',
            desc: '节点可以映射到options的结构',
            option: '',
            default: '{label: "label", value: "value", children: "children", icon: "icon"}'
        },
        {
            param: 'checkedKeys',
            type: 'array',
            desc: '多选情况下，选中的key值，key对应option的value，受控属性',
            option: '',
            default: ''
        },
        {
            param: 'defaultCheckedKeys',
            type: 'array',
            desc: '多选情况下，默认选中的key值，key对应option的value，非受控属性',
            option: '',
            default: ''
        },
        {
            param: 'showCheckbox',
            type: 'string',
            desc: '是否展示多选',
            option: '',
            default: 'false'
        },
        {
            param: 'onCheckboxChange',
            type: 'func(checkedKeys)',
            desc: '点击多选选中/非选中的回调',
            option: '',
            default: ''
        },
        {
            param: 'showSearch',
            type: 'bool',
            desc: '是否展示搜索功能',
            option: '',
            default: 'false'
        },
        {
            param: 'searchProps',
            type: 'object',
            desc: '自定义搜索框的props',
            option: '',
            default: '{}'
        },
        {
            param: 'paneWidth',
            type: 'number | string',
            desc: '带搜索条件下，级联面板的宽度',
            option: '',
            default: '360'
        },
        {
            param: 'menuWidth',
            type: 'number | string',
            desc: '级联菜单的宽度(最后一列随paneWidth自适应)',
            option: '',
            default: ''
        },
        {
            param: 'useDefaultFilter',
            type: 'bool',
            desc: '是否使用默认的搜索',
            option: '',
            default: 'true'
        },
        {
            param: 'onInputChange',
            type: 'func(e)',
            desc: '搜索框input时，onChange',
            option: '',
            default: ''
        },
        {
            param: 'defaultSearchValue',
            type: 'string',
            desc: '搜索框的默认value，非受控属性',
            option: '',
            default: ''
        },
        {
            param: 'searchValue',
            type: 'string',
            desc: '搜索框的value，受控属性',
            option: '',
            default: ''
        },
        {
            param: 'emptyNode',
            type: 'node',
            desc: '搜索不到结果时候展示话术',
            option: '',
            default: ''
        },
        {
            param: 'onClickSearchItem',
            type: 'func(searchOption)',
            desc: '点击搜索结果的item触发的回调',
            option: '',
            default: ''
        },
        {
            param: 'size',
            type: 'string [small medium large]',
            desc: '尺寸',
            option: '',
            default: 'medium'
        },
        {
            param: 'firstColumnGroup',
            type: '[{label, value, children}]',
            desc: 'firstColumnGroup为第一栏的二级树结构，label为node，value对应的是group的value，children为group的对应的key，具体示例参考treeLike示例',
            option: '',
            default: ''
        },
        {
            param: 'CustomItemRender',
            type: 'ReactNode',
            desc: '自定义custom的render，可以自定义全选',
            option: '',
            default: ''
        },
        {
            param: 'inputType',
            type: 'string',
            desc: 'input 的 type，有两种 normal和inline',
            option: '',
            default: 'inline'
        }
    ]
};
