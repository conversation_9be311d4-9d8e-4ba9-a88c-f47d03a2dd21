import BaseComponent from '../base';

export default {
    cascaderPane: {
        value: BaseComponent,
        label: 'CascaderPane 级联面板',
        demos: [
            {
                title: '单选',
                desc: '普通单选级联器',
                source: 'normal'
            },
            {
                title: '多选',
                desc: '普通多选级联器',
                source: 'checkbox'
            },
            {
                title: '节点展示方式',
                desc: '展开方式{expandType}: {column: 列展开} {inline: 内联展开} {toggle: 内联展开可收起}；不可选{selectable: false}',
                source: 'expandType'
            },
            {
                title: '搜索',
                desc: '带搜索的级联面板',
                source: 'search'
            },
            {
                title: '禁用',
                desc: '禁用选项',
                source: 'disabled'
            },
            {
                title: '第一栏树层级',
                desc: '第一栏为树层级',
                source: 'treeLike'
            },
            {
                title: '首列顶部自定义',
                desc: '可自定义全选（废弃，可用`showCheckAll`代替）',
                source: 'checkedAll'
            },
            {
                title: '选项自定义render',
                desc: '',
                source: 'renderOption'
            },
            {
                title: '异步数据加载',
                desc: `配置{loadData}进行异步数据加载({isLeaf}为{false}标记为非叶子节点)；
参数{scope}为{children}表示需要加载子项列表，为{descendants}则表示需要加载子树；
参数{trigger}表示具体的行为{expand}或{check}；参数{value}为所有祖先节点值`,
                source: 'loadData'
            },
            {
                title: '带清除icon',
                desc: '带清除icon的用法',
                source: 'withClear'
            }
        ],
        apis: [
            {
                apiKey: 'CascaderPane',
                title: 'CascaderPane'
            }
        ]
    }
};
