import React, {PureComponent} from 'react';
import {Transfer} from '@baidu/one-ui';

const allDataMap = {
    1: {
        key: 1,
        title: '计划1',
        children: [4]
    },
    2: {
        key: 2,
        title: '计划2',
        children: [5, 6]
    },
    3: {
        key: 3,
        title: '计划3',
        children: [7, 8, 9]
    },
    4: {
        key: 4,
        title: '单元1',
        children: [10]
    },
    5: {
        key: 5,
        title: '单元2',
        children: [11, 12]
    },
    6: {
        key: 6,
        title: '单元3',
        children: [13, 14]
    },
    7: {
        key: 7,
        title: '单元4',
        children: [15, 16]
    },
    8: {
        key: 8,
        title: '单元5',
        children: [17, 18, 19]
    },
    9: {
        key: 9,
        title: '单元6',
        children: [20, 21, 22]
    },
    10: {
        key: 10,
        title: '关键词1'
    },
    11: {
        key: 11,
        title: '关键词2'
    },
    12: {
        key: 12,
        title: '关键词3'
    },
    13: {
        key: 13,
        title: '关键词4'
    },
    14: {
        key: 14,
        title: '关键词5'
    },
    15: {
        key: 15,
        title: '关键词6'
    },
    16: {
        key: 16,
        title: '关键词7'
    },
    17: {
        key: 17,
        title: '关键词8'
    },
    18: {
        key: 18,
        title: '关键词9'
    },
    19: {
        key: 19,
        title: '关键词10'
    },
    20: {
        key: 20,
        title: '关键词11'
    },
    21: {
        key: 21,
        title: '关键词12'
    },
    22: {
        key: 22,
        title: '关键词13'
    }
};

const levelOptions = [
    {
        value: 'campaign',
        label: '计划'
    },
    {
        value: 'adgroup',
        label: '单元'
    },
    {
        value: 'keyword',
        label: '关键词'
    }
];
const a = [];
for (let i = 23; i < 100; i++) {
    allDataMap[i] = {
        key: `${i}`,
        title: `计划${i}`
    };
    a.push(i);
}

export default class FirstDemo extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            allDataMap,
            value: [],
            expandedSelectedKeys: [],
            searchValue: '',
            candidateList: [1, 2, 3].concat(a),
            level: 'campaign',
            placeholder: ''
        };
    }

    handleSelect = (value, allDataMap, expandedSelectedKeys) => {
        this.setState({
            allDataMap,
            value,
            expandedSelectedKeys
        });
    };

    handleSelectAll = (value, allDataMap, expandedSelectedKeys) => {
        this.setState({
            allDataMap,
            value,
            expandedSelectedKeys
        });
    };

    handleDelete = (value, allDataMap) => {
        this.setState({
            allDataMap,
            value
        });
    };

    handleDeleteAll = (value, allDataMap, expandedSelectedKeys) => {
        this.setState({
            allDataMap,
            value,
            expandedSelectedKeys
        });
    };

    handleSelectedExpand = expandedSelectedKeys => {
        this.setState({
            expandedSelectedKeys
        });
    };

    onSearchChange = e => {
        this.setState({
            searchValue: e.target.value
        });
    };

    handleLevelChange = e => {
        const newState = {};
        if (e === 'adgroup') {
            newState.candidateList = [4, 5, 6, 7, 8, 9];
        }
        if (e === 'campaign') {
            newState.candidateList = [1, 2, 3];
        }
        if (e === 'keyword') {
            newState.candidateList = [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22];
        }
        newState.level = e;
        this.setState(newState);
    };

    onSearchBoxFocus = () => {
        this.setState({
            placeholder: 'aaa'
        });
    };

    handleSearch = () => {
        if (this.state.level === 'adgroup') {
            this.setState({
                candidateList: [4, 5, 6, 7, 8, 9]
            });
        }
        if (this.state.level === 'campaign') {
            this.setState({
                candidateList: [1, 2, 3]
            });
        }
        if (this.state.level === 'keyword') {
            this.setState({
                candidateList: [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22]
            });
        }
    };

    render() {
        const props = {
            showCandidateTotalCount: false,
            placeholder: this.state.placeholder,
            treeName: '关键词',
            candidateList: this.state.candidateList,
            allDataMap: this.state.allDataMap,
            expandedSelectedKeys: this.state.expandedSelectedKeys,
            value: this.state.value,
            onSearchBoxFocus: this.onSearchBoxFocus,
            handleSelect: this.handleSelect,
            handleSelectAll: this.handleSelectAll,
            handleDelete: this.handleDelete,
            handleDeleteAll: this.handleDeleteAll,
            handleSelectedExpand: this.handleSelectedExpand,
            isShowLevel: true,
            showCandidateFooter: false,
            searchValue: this.state.searchValue,
            handleLevelChange: this.handleLevelChange,
            onSearchChange: this.onSearchChange,
            handleSearch: this.handleSearch,
            levelOptions,
            isShowLevelSelect: true,
            className: 'hehehehe',
            candidateTreeStyle: {
                width: 250
            },
            selectedTreeStyle: {
                width: 250
            },
            levelKey: this.state.level
        };
        return (
            <Transfer {...props} />
        );
    }
}
