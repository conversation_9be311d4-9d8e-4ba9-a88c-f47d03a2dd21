import React, {PureComponent} from 'react';
import {Transfer} from '@baidu/one-ui';

const allDataMap = {
    1: {
        key: 1,
        title: '计划1'
    },
    2: {
        key: 2,
        title: '计划2'
    },
    3: {
        key: 3,
        title: '计划3'
    },
    4: {
        key: 4,
        title: '计划4'
    },
    5: {
        key: 5,
        title: '计划5'
    },
    6: {
        key: 6,
        title: '计划6'
    },
    7: {
        key: 7,
        title: '计划7'
    },
    8: {
        key: 8,
        title: '计划8'
    },
    9: {
        key: 9,
        title: '计划9'
    },
    0: {
        key: 0,
        title: '计划10',
        children: [11, 12, 13, 14]
    },
    11: {
        key: 11,
        title: '计划11'
    },
    12: {
        key: 12,
        title: '计划12'
    },
    13: {
        key: 13,
        title: '计划13'
    },
    14: {
        key: 14,
        title: '计划14'
    }
};

export default class FirstDemo extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            allDataMap: {},
            value: [11, 1, 2],
            expandedSelectedKeys: [],
            searchValue: '',
            candidateList: []
            // candidateList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        };
    }

    componentDidMount() {
        this.setState({
            allDataMap,
            candidateList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 0]
        });
    }

    handleSelect = (value, allDataMap, expandedSelectedKeys) => {
        this.setState({
            allDataMap,
            value,
            expandedSelectedKeys
        });
    };

    handleSelectAll = (value, allDataMap, expandedSelectedKeys) => {
        this.setState({
            allDataMap,
            value,
            expandedSelectedKeys
        });
    };

    handleDelete = (value, allDataMap) => {
        this.setState({
            allDataMap,
            value
        });
    };

    handleDeleteAll = (value, allDataMap, expandedSelectedKeys) => {
        this.setState({
            allDataMap,
            value,
            expandedSelectedKeys
        });
    };

    handleSelectedExpand = expandedSelectedKeys => {
        this.setState({
            expandedSelectedKeys
        });
    };

    onSearchChange = e => {
        this.setState({
            searchValue: e.target.value
        });
        let filters = [];
        Object.keys(allDataMap).forEach(key => {
            if (allDataMap[key].title.indexOf(e.target.value) > -1) {
                filters.push(key);
            }
        });
        if (filters.indexOf('10') > -1) {
            filters = filters.filter(key => ['11', '12', '13', '14'].indexOf(key) < 0);
        }
        this.setState({
            candidateList: filters
        });
    };

    render() {
        const props = {
            treeName: '计划',
            candidateList: this.state.candidateList,
            allDataMap,
            expandedSelectedKeys: this.state.expandedSelectedKeys,
            value: this.state.value,
            handleSelect: this.handleSelect,
            handleSelectAll: this.handleSelectAll,
            handleDelete: this.handleDelete,
            handleDeleteAll: this.handleDeleteAll,
            handleSelectedExpand: this.handleSelectedExpand,
            showCandidateFooter: true,
            onSearchChange: this.onSearchChange,
            searchValue: this.state.searchValue
        };
        return (
            <div>
                <Transfer {...props} showSearchBox={false} />
                <br />
                <br />
                <Transfer {...props} />
                <br />
                <br />
            </div>
        );
    }
}
