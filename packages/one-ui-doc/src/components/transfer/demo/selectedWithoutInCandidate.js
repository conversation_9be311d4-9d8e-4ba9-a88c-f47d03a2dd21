import React, {PureComponent} from 'react';
import {Transfer} from '@baidu/one-ui';

const allDataMap = {
    1: {
        key: 1,
        title: '计划1'
    },
    2: {
        key: 2,
        title: '计划2',
        children: []
    },
    3: {
        key: 3,
        title: '计划3'
    },
    4: {
        key: 4,
        title: '计划4'
    },
    5: {
        key: 5,
        title: '计划5'
    },
    6: {
        key: 6,
        title: '计划6'
    },
    7: {
        key: 7,
        title: '计划7'
    },
    8: {
        key: 8,
        title: '计划8'
    },
    9: {
        key: 9,
        title: '计划9'
    },
    10: {
        key: 10,
        title: '计划10',
        children: [11, 12, 13, 14]
    },
    11: {
        key: 11,
        title: '计划11'
    },
    12: {
        key: 12,
        title: '计划12'
    },
    13: {
        key: 13,
        title: '计划13'
    },
    14: {
        key: 14,
        title: '计划14',
        children: [15, 16]
    },
    15: {
        key: 15,
        title: '计划15'
    },
    16: {
        key: 16,
        title: '计划16'
    },
    17: {
        key: 17,
        title: '计划17',
        children: [18]
    },
    18: {
        key: 18,
        title: '计划18'
    }
};

const candidateList = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

export default class FirstDemo extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            allDataMap,
            value: [18],
            expandedSelectedKeys: [],
            searchValue: ''
        };
    }

    handleSelect = (value, allDataMap, expandedSelectedKeys) => {
        this.setState({
            allDataMap,
            value,
            expandedSelectedKeys
        });
    };

    handleSelectAll = (value, allDataMap, expandedSelectedKeys) => {
        this.setState({
            allDataMap,
            value,
            expandedSelectedKeys
        });
    };

    handleDelete = (value, allDataMap) => {
        this.setState({
            allDataMap,
            value
        });
    };

    handleDeleteAll = (value, allDataMap, expandedSelectedKeys) => {
        this.setState({
            allDataMap,
            value,
            expandedSelectedKeys
        });
    };

    handleSelectedExpand = expandedSelectedKeys => {
        this.setState({
            expandedSelectedKeys
        });
    };

    onSearchChange = e => {
        this.setState({
            searchValue: e.target.value
        });
    };

    render() {
        const props = {
            treeName: '计划',
            candidateList: candidateList,
            allDataMap,
            expandedSelectedKeys: this.state.expandedSelectedKeys,
            value: this.state.value,
            handleSelect: this.handleSelect,
            handleSelectAll: this.handleSelectAll,
            handleDelete: this.handleDelete,
            handleDeleteAll: this.handleDeleteAll,
            handleSelectedExpand: this.handleSelectedExpand,
            onSearchChange: this.onSearchChange,
            showSearchBox: false
        };
        return (
            <Transfer {...props} />
        );
    }
}
