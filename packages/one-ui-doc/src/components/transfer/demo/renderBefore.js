import React, {PureComponent} from 'react';
import {Transfer} from '@baidu/one-ui';

const allDataMap = {
    1: {
        key: 1,
        title: '计划1'
    },
    2: {
        key: 2,
        title: '计划2'
    },
    3: {
        key: 3,
        title: '计划3'
    },
    4: {
        key: 4,
        title: '计划4'
    },
    5: {
        key: 5,
        title: '计划5'
    },
    6: {
        key: 6,
        title: '计划6'
    },
    7: {
        key: 7,
        title: '计划7'
    },
    8: {
        key: 8,
        title: '计划8'
    },
    9: {
        key: 9,
        title: '计划9'
    },
    10: {
        key: 10,
        title: '计划10'
    }
};

export default class BeforeDemo extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            allDataMap,
            value: [],
            expandedSelectedKeys: [],
            searchValue: '',
            candidateList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            alertCandidate: true,
            alertSelected: true
        };
    }

    handleSelect = (value, allDataMap, expandedSelectedKeys) => {
        this.setState({
            allDataMap,
            value,
            expandedSelectedKeys
        });
    };

    handleSelectAll = (value, allDataMap, expandedSelectedKeys) => {
        this.setState({
            allDataMap,
            value,
            expandedSelectedKeys
        });
    };

    handleDelete = (value, allDataMap) => {
        this.setState({
            allDataMap,
            value
        });
    };

    handleDeleteAll = (value, allDataMap, expandedSelectedKeys) => {
        this.setState({
            allDataMap,
            value,
            expandedSelectedKeys
        });
    };

    handleSelectedExpand = expandedSelectedKeys => {
        this.setState({
            expandedSelectedKeys
        });
    };

    dismissCandidate = () => {
        this.setState({
            alertCandidate: false
        });
    };

    dismissSelected = () => {
        this.setState({
            alertSelected: false
        });
    };

    onSearchChange = e => {
        this.setState({
            searchValue: e.target.value
        });
        const filters = [];
        Object.keys(allDataMap).forEach(key => {
            if (allDataMap[key].title.indexOf(e.target.value) > -1) {
                filters.push(key);
            }
        });
        this.setState({
            candidateList: filters
        });
    };

    render() {
        const props = {
            treeName: '计划',
            candidateList: this.state.candidateList,
            allDataMap,
            expandedSelectedKeys: this.state.expandedSelectedKeys,
            value: this.state.value,
            handleSelect: this.handleSelect,
            handleSelectAll: this.handleSelectAll,
            handleDelete: this.handleDelete,
            handleDeleteAll: this.handleDeleteAll,
            handleSelectedExpand: this.handleSelectedExpand,
            showCandidateFooter: true,
            onSearchChange: this.onSearchChange,
            searchValue: this.state.searchValue,
            useVirtualScroll: true,
            BeforeCandidatePane: () => (this.state.alertCandidate
                ? <h4
                    style={{
                        padding: '4px 8px',
                        backgroundColor: '#e9e9e9'
                    }}
                    onClick={this.dismissCandidate}
                >
                    Click to dismiss...
                </h4> : null),
            BeforeSelectedPane: () => (this.state.alertSelected
                ? <h4
                    style={{
                        padding: '4px 8px',
                        backgroundColor: '#e9e9e9'
                    }}
                    onClick={this.dismissSelected}
                >
                    Click to dismiss...
                </h4> : null)
        };
        return (
            <div>
                <Transfer {...props} />
            </div>
        );
    }
}
