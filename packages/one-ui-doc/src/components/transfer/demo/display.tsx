import React, {useState} from 'react';
import {Transfer, Radio} from '@baidu/one-ui';

const dataSource = [
    {
        key: 1,
        title: '计划1'
    },
    {
        key: 2,
        title: '计划2'
    },
    {
        key: 3,
        title: '计划3'
    },
    {
        key: 4,
        title: '计划4'
    },
    {
        key: 10,
        title: '计划10'
    }
];

export default () => {
    const [display, setDisplay] = useState('both');
    return (
        <>
            <Radio.Group
                options={['candidate', 'selected', 'both']}
                value={display}
                type="strong"
                size="small"
                onChange={e => {
                    setDisplay(e.target.value);
                }}
            />
            <br />
            <Transfer
                key={display}
                defaultValue={[1, 2, 3, 4]}
                dataSource={dataSource}
                display={display}
            />
        </>
    );
};
