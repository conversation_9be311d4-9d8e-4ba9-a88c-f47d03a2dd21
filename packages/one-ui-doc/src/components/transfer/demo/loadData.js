import React, {useCallback, useState, useRef} from 'react';
import {Transfer} from '@baidu/one-ui';

const dataSource = [
    {
        key: '1',
        title: '计划1'
    },
    {
        key: '2',
        title: '计划2'
    },
    {
        key: '3',
        title: '计划3',
        isLeaf: true
    }
];

const genKey = () => Math.random().toString(32).slice(2);

export default () => {
    const [value, setValue] = useState([]);
    const [data, setData] = useState(dataSource);

    // 引入ref解决闭包问题
    const valueRef = useRef(value);
    valueRef.current = value;

    const loadData = useCallback(({
        parent,
        scope,
        trigger
    }) => {
        const item = dataSource.find(o => o.key === parent.key);
        return new Promise(resolve => {
            setTimeout(() => {
                const key1 = genKey();
                const key2 = genKey();
                item.children = [{
                    key: key1,
                    title: `单元${key1}`,
                    isLeaf: true
                },{
                    key: key2,
                    title: `单元${key2}`,
                    isLeaf: true
                }];
                setData([...dataSource]);

                // 勾选：移除父项，补齐子项
                if (trigger === 'check') {
                    setValue(valueRef.current.filter(key => key !== parent.key).concat(key1, key2));
                }
                resolve();
            }, 3000);
        });
    }, [dataSource, value]);
    console.log(value)
    return (
        <Transfer
            loadData={loadData}
            dataSource={data}
            value={value}
            onChange={value => setValue(value)}
        />
    );
}
