import React, {PureComponent} from 'react';
import {Transfer, CascaderPane} from '@baidu/one-ui';

const allDataMap = {
    1: {
        key: 1,
        title: '湖南省',
        children: [5, 6]
    },
    2: {
        key: 2,
        title: '湖北省'
    },
    3: {
        key: 3,
        title: '河北省'
    },
    4: {
        key: 4,
        title: '河南省'
    },
    5: {
        key: 5,
        title: '长沙市'
    },
    6: {
        key: 6,
        title: '株洲市'
    }
};

const options = [{
    value: 1,
    label: '湖南省',
    children: [{
        value: 5,
        label: '长沙市'
    }, {
        value: 6,
        label: '株洲市'
    }]
},
{
    value: 2,
    label: '湖北省'
},
{
    value: 3,
    label: '河北省'
},
{
    value: 4,
    label: '河南省'
}];

export default class FirstDemo extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            allDataMap,
            value: [],
            expandedSelectedKeys: [],
            searchValue: '',
            candidateList: [1, 2, 3, 4]
        };
    }

    handleSelect = (value, allDataMap, expandedSelectedKeys) => {
        this.setState({
            allDataMap,
            value,
            expandedSelectedKeys
        });
    };

    handleSelectAll = (value, allDataMap, expandedSelectedKeys) => {
        this.setState({
            allDataMap,
            value,
            expandedSelectedKeys
        });
    };

    handleDelete = (value, allDataMap) => {
        this.setState({
            allDataMap,
            value
        });
    };

    handleDeleteAll = (value, allDataMap, expandedSelectedKeys) => {
        this.setState({
            allDataMap,
            value,
            expandedSelectedKeys
        });
    };

    handleSelectedExpand = expandedSelectedKeys => {
        this.setState({
            expandedSelectedKeys
        });
    };

    onClickSearchItem = selectedOptions => {
        const values = selectedOptions.map(option => option.value);
        this.setState({
            value: [values[values.length - 1]]
        });
    }

    customCandidatePane = props => {
        const cascaderPaneProps = {
            options,
            checkedKeys: this.state.value,
            showCheckbox: true,
            onCheckboxChange: checkedKeys => {
                this.setState({
                    value: checkedKeys
                });
            },
            showSearch: true,
            onClickSearchItem: this.onClickSearchItem
        };
        return (<CascaderPane {...cascaderPaneProps} />);
    };

    selectedTitleRender = () => {
        return (<div>清除</div>);
    }

    render() {
        const props = {
            treeName: '地域',
            candidateList: this.state.candidateList,
            allDataMap,
            expandedSelectedKeys: this.state.expandedSelectedKeys,
            value: this.state.value,
            handleSelect: this.handleSelect,
            handleSelectAll: this.handleSelectAll,
            handleDelete: this.handleDelete,
            handleDeleteAll: this.handleDeleteAll,
            handleSelectedExpand: this.handleSelectedExpand,
            CustomCandidatePane: this.customCandidatePane,
            SelectedTitleRender: this.selectedTitleRender
        };
        return (
            <div>
                <Transfer {...props} showSearchBox={false} />
                <br />
                <br />
            </div>
        );
    }
}
