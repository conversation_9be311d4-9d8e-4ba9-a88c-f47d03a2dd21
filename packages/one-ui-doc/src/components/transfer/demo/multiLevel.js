import React, {PureComponent} from 'react';
import {Transfer} from '@baidu/one-ui';

const allDataMap = {
    1: {
        key: 1,
        title: '计划1',
        children: [4],
        disabled: true
    },
    2: {
        key: 2,
        title: '计划2',
        children: [5, 6, 23]
    },
    3: {
        key: 3,
        title: '计划3',
        children: [7, 8, 9]
    },
    4: {
        key: 4,
        title: '单元1',
        children: [10]
    },
    5: {
        key: 5,
        title: '单元2',
        children: [11, 12],
        disabled: true
    },
    6: {
        key: 6,
        title: '单元3',
        children: [13, 14]
    },
    7: {
        key: 7,
        title: '单元4',
        children: [15, 16]
    },
    8: {
        key: 8,
        title: '单元5',
        children: [17, 18, 19]
    },
    9: {
        key: 9,
        title: '单元6',
        children: [20, 21, 22]
    },
    23: {
        key: 23,
        title: '单元7'
    },
    10: {
        key: 10,
        title: '关键词1'
    },
    11: {
        key: 11,
        title: '关键词2'
    },
    12: {
        key: 12,
        title: '关键词3'
    },
    13: {
        key: 13,
        title: '关键词4'
    },
    14: {
        key: 14,
        title: '关键词5'
    },
    15: {
        key: 15,
        title: '关键词6'
    },
    16: {
        key: 16,
        title: '关键词7'
    },
    17: {
        key: 17,
        title: '关键词8'
    },
    18: {
        key: 18,
        title: '关键词9'
    },
    19: {
        key: 19,
        title: '关键词10'
    },
    20: {
        key: 20,
        title: '关键词11'
    },
    21: {
        key: 21,
        title: '关键词12'
    },
    22: {
        key: 22,
        title: '关键词13',
        disabled: true
    }
};

export default class FirstDemo extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            allDataMap,
            value: [10, 22],
            expandedSelectedKeys: [],
            searchValue: ''
        };
    }

    handleSelect = (value, allDataMap, expandedSelectedKeys) => {
        this.setState({
            allDataMap,
            value,
            expandedSelectedKeys
        });
    };

    handleSelectAll = (value, allDataMap, expandedSelectedKeys) => {
        this.setState({
            allDataMap,
            value,
            expandedSelectedKeys
        });
    };

    handleDelete = (value, allDataMap) => {
        this.setState({
            allDataMap,
            value
        });
    };

    handleDeleteAll = (value, allDataMap) => {
        this.setState({
            allDataMap,
            value
        });
    };

    handleSelectedExpand = expandedSelectedKeys => {
        this.setState({
            expandedSelectedKeys
        });
    };

    onSearchChange = e => {
        this.setState({
            searchValue: e.target.value
        });
    };

    handleLevelChange = e => {
        // eslint-disable-next-line no-console
        console.log(e);
    };

    render() {
        const props = {
            showCandidateTotalCount: false,
            treeName: '关键词',
            candidateList: [1, 2, 3],
            allDataMap: this.state.allDataMap,
            expandedSelectedKeys: this.state.expandedSelectedKeys,
            value: this.state.value,
            handleSelect: this.handleSelect,
            handleSelectAll: this.handleSelectAll,
            handleDelete: this.handleDelete,
            handleSelectedExpand: this.handleSelectedExpand,
            showCandidateFooter: true,
            searchValue: this.state.searchValue,
            handleLevelChange: this.handleLevelChange,
            onSearchChange: this.onSearchChange,
            showSearchBox: false,
            handleDeleteAll: this.handleDeleteAll
        };
        return (
            <Transfer {...props} />
        );
    }
}
