import React, {PureComponent} from 'react';
import {Transfer} from '@baidu/one-ui';

const allDataMap = {
    1: {
        key: 1,
        title: '计划1'
    },
    2: {
        key: 2,
        title: '计划2'
    },
    3: {
        key: 3,
        title: '计划3'
    },
    4: {
        key: 4,
        title: '计划4'
    },
    5: {
        key: 5,
        title: '计划5'
    },
    6: {
        key: 6,
        title: '计划6'
    },
    7: {
        key: 7,
        title: '计划7'
    },
    8: {
        key: 8,
        title: '计划8'
    },
    9: {
        key: 9,
        title: '计划9'
    },
    10: {
        key: 10,
        title: '计划10'
    }
};

const CandidateItem = props => {
    const {title} = props;
    return (
        <span>
            <span style={{display: 'inline-block', width: 100}}>{title}</span>
            <span style={{display: 'inline-block', width: 80}}>{Math.floor((Math.random()*1000)+1)}</span>
            <span style={{display: 'inline-block', width: 80}}>北京</span>
        </span>
    );
};

const CandidateTitleRender = props => {
    const {title, treeName, selectedNum, maxSelectedNum, showSelectedNum, unSelectedNum, showCandidateNum} = props;
    let titleDetail = `${title}${treeName}`;
    if (selectedNum != null && showSelectedNum) {
        titleDetail = `${titleDetail}(${selectedNum}/${maxSelectedNum})`;
    }
    if (unSelectedNum != null && showCandidateNum) {
        titleDetail = `${titleDetail}(${unSelectedNum})`;
    }
    return (
        <span>
            <span style={{display: 'inline-block', width: 100}}>{titleDetail}</span>
            <span style={{display: 'inline-block', width: 80}}>消费</span>
            <span style={{display: 'inline-block', width: 80}}>地域</span>
        </span>
    );
};

export default class FirstDemo extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            allDataMap,
            value: [],
            expandedSelectedKeys: [],
            searchValue: ''
        };
    }

    handleSelect = (value, allDataMap, expandedSelectedKeys) => {
        this.setState({
            allDataMap,
            value,
            expandedSelectedKeys
        });
    };

    handleSelectAll = (value, allDataMap, expandedSelectedKeys) => {
        this.setState({
            allDataMap,
            value,
            expandedSelectedKeys
        });
    };

    handleDelete = (value, allDataMap) => {
        this.setState({
            allDataMap,
            value
        });
    };

    handleDeleteAll = (value, allDataMap, expandedSelectedKeys) => {
        this.setState({
            allDataMap,
            value,
            expandedSelectedKeys
        });
    };

    handleSelectedExpand = expandedSelectedKeys => {
        this.setState({
            expandedSelectedKeys
        });
    };

    onSearchChange = e => {
        this.setState({
            searchValue: e.target.value
        });
    };

    render() {
        const props = {
            treeName: '计划',
            maxSelectedNum: 10,
            candidateList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            allDataMap: this.state.allDataMap,
            expandedSelectedKeys: this.state.expandedSelectedKeys,
            value: this.state.value,
            CandidateItem,
            CandidateTitleRender,
            handleSelect: this.handleSelect,
            handleSelectAll: this.handleSelectAll,
            handleDelete: this.handleDelete,
            handleDeleteAll: this.handleDeleteAll,
            handleSelectedExpand: this.handleSelectedExpand,
            showCandidateFooter: true,
            searchValue: this.state.searchValue,
            onSearchChange: this.onSearchChange,
            showSearchBox: false
        };
        return (
            <Transfer {...props} />
        );
    }
}
