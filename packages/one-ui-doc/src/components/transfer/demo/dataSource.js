import React, {useState} from 'react';
import {Transfer, Radio} from '@baidu/one-ui';

const dataSource = [
    {
        key: 1,
        title: '计划1'
    },
    {
        key: 2,
        title: '计划2'
    },
    {
        key: 3,
        title: '计划3'
    },
    {
        key: 4,
        title: '计划4'
    },
    {
        key: 10,
        title: '计划10',
        children: [
            {
                key: 11,
                title: '计划11',
                children: [
                    {
                        key: 111,
                        title: '计划111'
                    },
                    {
                        key: 121,
                        title: '计划121'
                    },
                    {
                        key: 131,
                        title: '计划131'
                    }
                ]
            },
            {
                key: 12,
                title: '计划12'
            },
            {
                key: 13,
                title: '计划13'
            }
        ]
    }
];

export default () => {
    const [value, setValue] = useState([]);
    const [mergeChecked, setMergeChecked] = useState('downwards');
    return (
        <>
            <Radio.Group
                options={['keep-all', 'upwards', 'downwards']}
                value={mergeChecked}
                type="strong"
                size="small"
                onChange={e => {
                    setMergeChecked(e.target.value);
                    setValue([]);
                }}
            />
            <br />
            <Transfer
                key={mergeChecked}
                dataSource={dataSource}
                value={value}
                onChange={value => setValue(value)}
                mergeChecked={mergeChecked}
            />
            <br />
            已选值：[{value.join(', ')}]
        </>
    );
};
