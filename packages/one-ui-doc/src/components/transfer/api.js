export default {
    Transfer: [
        {
            param: 'className',
            type: 'String',
            desc: '自定义类名',
            option: '',
            default: ''
        },
        {
            param: 'candidateList',
            type: 'string/number[]',
            desc: '左侧候选的树节点，注意多层树的话只包含顶层节点即可，控制左侧树如何展现，例如筛选可通过修改此字段实现【必填项 受控】',
            option: '',
            default: '[]'
        },
        {
            param: 'selectedList',
            type: 'string/number[]',
            desc: '右侧已选中的树节点，注意这里只包含叶子节点即可，控制右侧树如何展现【选填 受控】',
            option: '',
            default: ''
        },
        {
            param: 'allDataMap',
            type: 'Object',
            desc: '穿梭框的所有资源信息，示例：{[key]: {key: 1,title: \'计划1\'},...}',
            option: '',
            default: ''
        },
        {
            param: 'expandedSelectedKeys',
            type: 'string/number[]',
            desc: '右侧已选树的展开节点【选填 受控】',
            option: '',
            default: ''
        },
        /* {
            param: 'parentRelationMap',
            type: 'Object',
            desc: '子节点到父节点的映射关系，通常不用传，组件内部会计算【选填 受控】',
            option: '',
            default: ''
        },
        {
            param: 'childrenRelationMap',
            type: 'Object',
            desc: '父节点到子节点的映射关系，通常不用传，组件内部会计算【选填 受控】',
            option: '',
            default: ''
        }, */
        {
            param: 'handleSelect',
            type: 'Function(selectedList: Array, allDataMap: Object, expandedSelectedKeys: Array)',
            desc: '左侧候选树节点单个选择回调',
            option: '',
            default: ''
        },
        {
            param: 'handleSelectAll',
            type: 'Function(selectedList: Array, allDataMap: Object, expandedSelectedKeys: Array)',
            desc: '全选回调',
            option: '',
            default: ''
        },
        {
            param: 'handleDelete',
            type: 'Function(selectedList: Array, allDataMap: Object)',
            desc: '右侧已选树单个删除回调',
            option: '',
            default: ''
        },
        {
            param: 'handleDeleteAll',
            type: 'Function(selectedList: Array, allDataMap: Object, expandedSelectedKeys: Array)',
            desc: '右侧已选树全部删除回调',
            option: '',
            default: ''
        },
        {
            param: 'handleSelectedExpand',
            type: 'Function(expandedSelectedKeys: Array)',
            desc: '右侧已选树展开/收起时触发',
            option: '',
            default: ''
        },
        {
            param: 'handleCandidateExpand',
            type: 'Function(expandedKeys: Array, {expanded: bool, node})',
            desc: '左侧候选树展开/收起时触发',
            option: '',
            default: ''
        },
        {
            param: 'treeName',
            type: 'String',
            desc: '穿梭框名称',
            option: '',
            default: ''
        },
        {
            param: 'placeholder',
            type: 'String',
            desc: '搜索框placeholder',
            option: '',
            default: ''
        },
        {
            param: 'candidateTreeStyle',
            type: 'Object',
            desc: '自定义左侧树样式',
            option: '',
            default: '{}'
        },
        {
            param: 'selectedTreeStyle',
            type: 'Object',
            desc: '自定义右侧树样式',
            option: '',
            default: '{}'
        },
        {
            param: 'showCandidateNum',
            type: 'Boolean',
            desc: '是否展示可选项数目',
            option: '',
            default: 'true'
        },
        {
            param: 'showSelectedNum',
            type: 'Boolean',
            desc: '是否显示已选数目',
            option: '',
            default: 'true'
        },
        {
            param: 'maxSelectedNum',
            type: 'Number',
            desc: '已选节点上限，不传则无上限',
            option: '',
            default: ''
        },
        {
            param: 'CandidateTitleRender',
            type: 'Function(props)',
            desc: '自定义候选树头部标题render',
            option: '',
            default: '默认候选title'
        },
        {
            param: 'SelectedTitleRender',
            type: 'Function(props)',
            desc: '自定义已选树头部标题render',
            option: '',
            default: '默认已选title'
        },
        {
            param: 'CandidateItem',
            type: 'ReactNode | Function(props)',
            desc: '自定义候选项格式，可实现多列选择器',
            option: '',
            default: ''
        },
        {
            param: 'candidateItemProps',
            type: 'Object',
            desc: '自定义候选项的props，在传CandidateItem为func时生效',
            option: '',
            default: '{}'
        },
        {
            param: 'SelectedItem',
            type: 'ReactNode | Function(props)',
            desc: '自定义已选项格式，可实现多列选择器',
            option: '',
            default: ''
        },
        {
            param: 'selectedItemProps',
            type: 'Object',
            desc: '自定义已选项的props，在传SelectedItem为func时生效',
            option: '',
            default: '{}'
        },
        {
            param: 'SearchBoxRender',
            type: 'Function(props) | ReactNode',
            desc: '自定义搜索筛选render',
            option: '',
            default: ''
        },
        {
            param: 'searchRenderProps',
            type: 'Object',
            desc: '自定义搜索筛选props',
            option: '',
            default: '{}'
        },
        {
            param: 'showSearchBox',
            type: 'Boolean',
            desc: '是否显示搜索框',
            option: '',
            default: 'true'
        },
        {
            param: 'handleSearch',
            type: 'Function()',
            desc: '搜索框搜索回调',
            option: '',
            default: ''
        },
        {
            param: 'searchValue',
            type: 'String',
            desc: '搜索框的值',
            option: '',
            default: ''
        },
        {
            param: 'onSearchChange',
            type: 'Function()',
            desc: '搜索框输入回调',
            option: '',
            default: ''
        },
        {
            param: 'isShowLevel',
            type: 'Boolean',
            desc: '是否显示结点所属的层级信息',
            option: '',
            default: 'false'
        },
        {
            param: 'levelOptions',
            type: 'Array',
            desc: '层级信息配置',
            option: '',
            default: ''
        },
        {
            param: 'handleLevelChange',
            type: 'Function()',
            desc: '层级切换回调，参数同Select组件',
            option: '',
            default: ''
        },
        {
            param: 'isShowLevelSelect',
            type: 'Boolean',
            desc: '是否启用层级选择筛选',
            option: '',
            default: 'false'
        },
        /* {
            param: 'onSearchBoxFocus',
            type: 'Function()',
            desc: '搜索框获取焦点回调',
            option: '',
            default: ''
        },
        {
            param: 'onSearchBoxBlur',
            type: 'Function()',
            desc: '搜索框失焦回调',
            option: '',
            default: ''
        }, */
        {
            param: 'levelKey',
            type: 'String',
            desc: '当前选中层级',
            option: '',
            default: ''
        },
        {
            param: 'showCandidateFooter',
            type: 'Boolean',
            desc: '是否展示新建按钮',
            option: '',
            default: 'false'
        },
        {
            param: 'candidateFooterProps',
            type: 'Object',
            desc: '新建按钮自定义props',
            option: '',
            default: '{}'
        },
        {
            param: 'CandidateFooterRender',
            type: 'Function(props) | ReactNode',
            desc: '自定义footer',
            option: '',
            default: ''
        },
        {
            param: 'useVirtualScroll',
            type: 'bool',
            desc: '是否开启虚拟滚动',
            option: '',
            default: 'false'
        },
        {
            param: 'CustomCandidatePane',
            type: 'ReactNode | func',
            desc: '自定义备选区面板',
            option: '',
            default: ''
        },
        {
            param: 'CustomSelectedPane',
            type: 'ReactNode | func',
            desc: '自定义选中区面板',
            option: '',
            default: ''
        },
        {
            param: 'BeforeCandidatePane',
            type: 'ReactNode | func',
            desc: '自定义备选区前内容（位于搜索以下）',
            option: '',
            default: ''
        },
        {
            param: 'BeforeSelectedPane',
            type: 'ReactNode | func',
            desc: '自定义已选区前内容',
            option: '',
            default: ''
        },
        {
            param: 'CustomCandidateOperation',
            type: 'ReactNode',
            desc: '备选区支持自定义操作（类似全选），如果需要操作放在右侧需要额外写样式',
            option: '',
            default: ''
        },
        {
            param: 'CustomSelectedOperation',
            type: 'ReactNode',
            desc: '候选区支持自定义操作（类似清空），如果需要操作放在右侧需要额外写样式',
            option: '',
            default: ''
        },
        {
            param: 'loading',
            type: 'boolean',
            desc: '是否加载',
            option: '',
            default: 'false'
        },
        {
            param: 'loadingText',
            type: 'string/ReactNode',
            desc: '加载文案',
            option: '',
            default: '加载中...'
        }
    ]
};
