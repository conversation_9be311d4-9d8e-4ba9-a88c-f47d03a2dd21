import BaseComponent from '../base';

export default {
    transfer: {
        value: BaseComponent,
        label: 'Transfer 穿梭框',
        demos: [
            {
                title: '基础使用',
                desc: '{dataSource} {value} {onChange}',
                source: 'dataSource'
            },
            {
                title: '搜索',
                desc: '支持底部{新建功能}，{搜索功能}',
                source: 'normal'
            },
            {
                title: '异步加载',
                desc: `配置{loadData}进行异步数据加载({isLeaf}为{true}标记为叶子节点)；
参数{scope}为{children}表示需要加载子项列表，为{descendants}则表示需要加载子树；
参数{trigger}表示具体的行为{expand}或{check}`,
                source: 'loadData'
            },
            {
                title: '无搜索',
                desc: '不带搜索功能和底部footer的穿梭框',
                source: 'noSearch'
            },
            {
                title: '尺寸',
                desc: '尺寸，支持 {small} {medium}，默认medium尺寸，下面是small尺寸',
                source: 'size'
            },
            {
                title: '加载中',
                desc: '加载中',
                source: 'loading'
            },
            {
                title: '禁用',
                desc: '',
                source: 'disabled'
            },
            {
                title: '自定义选项',
                desc: '具体每行格式均可自定义，此处只作示意',
                source: 'multiColumns'
            },
            {
                title: '多层级',
                desc: '',
                source: 'multiLevel'
            },
            {
                title: '虚拟滚动',
                desc: '虚拟滚动',
                source: 'virtualList'
            },
            {
                title: '结果不在备选区',
                desc: '支持已选区展示不在备选区的选项',
                source: 'selectedWithoutInCandidate'
            },
            {
                title: '自定义搜索',
                desc: '筛选逻辑在组件之外写',
                source: 'levelSearch'
            },
            {
                title: '自定义面板展示',
                desc: '可自定义仅展示`candidate`(备选区)或`selected`(已选区)',
                source: 'display'
            },
            {
                title: '内容前后扩展',
                desc: '允许在备选区/已选区前增加扩展内容',
                source: 'renderBefore'
            },
            {
                title: '自定义操作按钮',
                desc: '允许在备选区/已选区自定义操作的按钮(默认接在title后面，如果需要右对齐需要使用方自己写样式)',
                source: 'renderCustomRender'
            }
        ],
        apis: [
            {
                apiKey: 'Transfer',
                title: 'Transfer'
            }
        ]
    }
};
