import BaseComponent from '../base';

export default {
    lightbox: {
        value: BaseComponent,
        label: 'Lightbox 预览组件',
        demos: [
            {
                title: '基础用法',
                desc: '最基础用法',
                source: 'normal'
            },
            {
                title: '单个',
                desc: '最基础用法',
                source: 'single'
            },
            {
                title: '点击遮罩关闭',
                desc: '点击遮罩时关闭',
                source: 'maskClosable'
            },
            {
                title: '隐藏页码',
                desc: '最基础用法 - 不展示计步器',
                source: 'noIndicator'
            },
            {
                title: '循环',
                desc: '最基础用法 - 循环',
                source: 'auto'
            },
            {
                title: '图片缩放',
                desc: '仅支持于图片类型',
                source: 'zoom'
            }
        ],
        apis: [
            {
                apiKey: 'Lightbox',
                title: 'Lightbox'
            }
        ]
    }
};