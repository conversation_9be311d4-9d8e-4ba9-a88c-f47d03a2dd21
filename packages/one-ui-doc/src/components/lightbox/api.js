export default {
    Lightbox: [
        {
            param: 'open',
            type: 'boolean',
            desc: '是否open预览',
            option: '',
            default: 'false'
        },
        {
            param: 'maskClosable',
            type: 'boolean',
            desc: '点击遮罩关闭',
            option: '',
            default: 'false'
        },
        {
            param: 'onClose',
            type: 'func',
            desc: '关闭时的回调',
            option: '',
            default: ''
        },
        {
            param: 'indicator',
            type: 'string',
            desc: '展示翻页的类型，目前有两种，为number的时候展示数字，为none则不展示翻页页码',
            option: '',
            default: ''
        },
        {
            param: 'className',
            type: 'string',
            desc: '自定义class',
            option: '',
            default: ''
        },
        {
            param: 'onChange',
            type: 'func(slideIndex)',
            desc: '点击翻页的时候触发',
            option: '',
            default: ''
        },
        {
            param: 'defaultActiveIndex',
            type: 'number',
            desc: '设置当前slide',
            option: '',
            default: ''
        },
        {
            param: 'activeIndex',
            type: 'number',
            desc: '设置当前slide（受控）',
            option: '',
            default: ''
        },
        {
            param: 'zIndex',
            type: 'number',
            desc: 'z-index',
            option: '',
            default: ''
        },
        {
            param: 'datasource',
            type: 'array',
            desc: `
            数据源，[{type, desc, src, alt}]，
            其中type为预览的类型，image、video为图片和视频预览，
            如果需要自定义render，则type为custom（src为自定义的render）`,
            option: '',
            default: '[]'
        },
        {
            param: 'mode',
            type: 'string',
            desc: '类型，如果是normal则不会无限循环，如果是auto则循环',
            option: '[auto, normal]',
            default: 'normal'
        }
    ]
};