import React, {PureComponent} from 'react';
import {Lightbox, Button} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {
        open: false
    }

    onClick = () => {
        this.setState({
            open: true
        });
    }

    onClose = () => {
        this.setState({
            open: false
        });
    }

    render() {
        return (
            <div>
                <Lightbox
                    open={this.state.open}
                    onClose={this.onClose}
                    dataSource={[
                        {
                            src:
                            'https://cms-image.cdn.bcebos.com/1919965741%2C177922360.jpg',
                            alt: 'A cute kitty looking at you with its greenish eyes.',
                            type: 'image',
                            desc: 'hahahahahhahaha'.repeat(10)
                        }
                    ]}
                />
                <Button onClick={this.onClick}>这是一个lightbox的点击按钮</Button>
            </div>
        );
    }
}