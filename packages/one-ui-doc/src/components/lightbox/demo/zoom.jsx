import React, {useState} from 'react';
import {Lightbox, Button, Radio} from '@baidu/one-ui';

const dataSource = [
    {
        src:
        'https://cms-image.cdn.bcebos.com/1919965741%2C177922360.jpg',
        alt: 'A cute kitty looking at you with its greenish eyes.',
        type: 'image',
        desc: 'hahahahahhahaha'.repeat(10)
    },
    {
        src:
        'https://cms-image.cdn.bcebos.com/3349723427%2C2625224597.jpg',
        alt: 'A common kingfisher flying above river.',
        type: 'image',
        desc: 'hahahahahhahaha1'
    },
    {
        src:
        'https://cms-image.cdn.bcebos.com/1214177510%2C515504630.jpg',
        alt: 'A white and gray dolphin in blue water.',
        type: 'image',
        desc: 'hahahahahhahaha2'.repeat(10)
    },
    {
        src: 'https://cms-image.cdn.bcebos.com/2608100595%2C1414551417.jpg',
        alt: 'Baidu logo.',
        type: 'image',
        desc: 'hahahahahhahaha3'
    }
];

const RadioGroup = Radio.Group;
export default function zoom() {

    const [open, setOpen] = useState(false);
    const [minScale, setMinScale] = useState();
    const [maxScale, setMaxScale] = useState();
    const [scaleStep, setScaleStep] = useState();

    function onChangeMinScale(e) {
        const value = e.target.value;
        setMinScale(value);
    }

    function onChangeMaxScale(e) {
        const value = e.target.value;
        setMaxScale(value);
    }

    function onScaleChangeStep(e) {
        const value = e.target.value;
        setScaleStep(value);
    }

    return (
        <div>
            <Lightbox
                open={open}
                onClose={() => setOpen(false)}
                dataSource={dataSource}
                minScale={minScale}
                maxScale={maxScale}
                scaleStep={scaleStep}
                onScaleToFit={activeIndex => console.log('onScaleToFit', activeIndex)}
                onZoomIn={(activeIndex, scale) => console.log('onZoomIn', activeIndex, scale)}
                onZoomOut={(activeIndex, scale) => console.log('onZoomOut', activeIndex, scale)}
                onScaleToOriginal={activeIndex => console.log('onScaleToOriginal', activeIndex)}
                onTransform={(activeIndex, {x, y, scale}, actionType) =>
                    console.log('onTransform', {activeIndex, x, y, scale, actionType})
                }
            />
            最小倍数限制（默认0.1）：<RadioGroup options={[0.1, 0.25, 0.5]} value={minScale} onChange={onChangeMinScale} />
            <br/>
            最大倍数限制（默认4）：<RadioGroup options={[2, 3, 4]} value={maxScale} onChange={onChangeMaxScale} />
            <br/>
            缩放倍数步长（默认0.25）：<RadioGroup options={[0.1, 0.3, 0.25]} value={scaleStep} onChange={onScaleChangeStep} />
            <br/>
            <Button onClick={() => setOpen(true)}>这是一个lightbox的点击按钮</Button>
        </div>
    );
}
