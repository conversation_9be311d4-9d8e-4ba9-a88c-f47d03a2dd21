import React, {PureComponent} from 'react';
import {Lightbox, Button} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {
        open: false
    }

    onClick = () => {
        this.setState({
            open: true
        });
    }

    onClose = () => {
        this.setState({
            open: false
        });
    }

    render() {
        return (
            <div>
                <Lightbox
                    open={this.state.open}
                    onClose={this.onClose}
                    dataSource={[
                        {
                            src:
                            'https://cms-image.cdn.bcebos.com/1919965741%2C177922360.jpg',
                            alt: 'A cute kitty looking at you with its greenish eyes.',
                            type: 'image',
                            desc: 'hahahahahhahaha'.repeat(10)
                        },
                        {
                            src:
                            'https://cms-image.cdn.bcebos.com/3349723427%2C2625224597.jpg',
                            alt: 'A common kingfisher flying above river.',
                            type: 'image',
                            desc: 'hahahahahhahaha1'
                        },
                        {
                            src:
                            'https://cms-image.cdn.bcebos.com/1214177510%2C515504630.jpg',
                            alt: 'A white and gray dolphin in blue water.',
                            type: 'image',
                            desc: 'hahahahahhahaha2'.repeat(10)
                        },
                        {
                            src: 'https://cms-image.cdn.bcebos.com/2608100595%2C1414551417.jpg',
                            alt: 'Baidu logo.',
                            type: 'image',
                            desc: 'hahahahahhahaha3'
                        },
                        {
                            src: 'https://interactive-examples.mdn.mozilla.net/media/cc0-videos/flower.webm',
                            alt: 'Tesla logo.',
                            type: 'video',
                            desc: 'hahahahahhahaha4'.repeat(10)
                        }
                    ]}
                    indicator="none"
                />
                <Button onClick={this.onClick}>这是一个lightbox的点击按钮</Button>
            </div>
        );
    }
}