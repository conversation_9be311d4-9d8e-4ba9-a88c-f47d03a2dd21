export default {
    Breadcrumb: [
        {
            param: 'className',
            type: 'string',
            desc: '可以自定义breadcrumb的className',
            option: '',
            default: ''
        },
        {
            param: 'style',
            type: 'object',
            desc: '可以自定义breadcrumb的style',
            option: '',
            default: ''
        },
        {
            param: 'separator',
            desc: '定义的分隔符号',
            type: 'string|ReactNode',
            option: '',
            default: '/'
        },
        {
            param: 'size',
            desc: '面包屑的尺寸',
            type: 'string',
            option: 'medium|small',
            default: 'medium'
        },
        {
            param: 'type',
            desc: '面包屑的种类',
            type: 'string',
            option: 'normal|strong',
            default: 'normal'
        }
    ],
    BreadcrumbItem: [
        {
            param: 'separator',
            desc: '定义的分隔符号',
            type: 'string|ReactNode',
            option: '',
            default: '/'
        },
        {
            param: 'href',
            desc: '链接的目的地， 传入的话item为a标签',
            type: 'string',
            option: '',
            default: ''
        },
        {
            param: 'onClick',
            desc: '单击事件',
            type: '(e)=>void',
            option: '',
            default: ''
        },
        {
            param: 'disabled',
            desc: '禁用',
            type: 'bool',
            option: '',
            default: 'false'
        }
    ]
};
