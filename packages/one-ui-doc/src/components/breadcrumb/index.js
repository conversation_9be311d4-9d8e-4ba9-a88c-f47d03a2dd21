import BaseComponent from '../base';

export default {
    breadcrumb: {
        value: BaseComponent,
        label: 'Breadcrumb 面包屑',
        demos: [
            {
                title: '基本用法',
                desc: '普通面包屑',
                source: 'normal'
            },
            {
                title: '小尺寸',
                desc: '普通面包屑 - 小尺寸',
                source: 'small'
            },
            {
                title: '禁用',
                desc: '普通面包屑 - 存在disabled',
                source: 'disabled'
            },
            {
                title: '加强样式',
                desc: '普通面包屑 - 加强样式',
                source: 'strong'
            },
            {
                title: 'a标签',
                desc: '普通面包屑 - a标签的面包屑',
                source: 'aTag'
            }
        ],
        apis: [
            {
                apiKey: 'Breadcrumb',
                title: 'Breadcrumb'
            },
            {
                apiKey: 'BreadcrumbItem',
                title: 'Breadcrumb.Item'
            }
        ]
    }
};
