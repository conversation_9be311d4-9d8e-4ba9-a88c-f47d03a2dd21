import React, {PureComponent} from 'react';
import {Carousel, Radio} from '@baidu/one-ui';
import './index.less';

export default class NoramlCarousel extends PureComponent {
    beforeChange = (from, to) => {
        console.log(from);
        console.log(to);
    }

    afterChange = current => {
        console.log(current);
    }

    state = {
        dotPosition: 'top'
    };

    handlePositionChange = ({target: {value: dotPosition}}) => this.setState({dotPosition});

    render() {
        return (
            <div>
                <Radio.Group
                    onChange={this.handlePositionChange}
                    value={this.state.dotPosition}
                    style={{marginBottom: 8}}
                >
                    <Radio.Button value="top">Top</Radio.Button>
                    <Radio.Button value="bottom">Bottom</Radio.Button>
                    <Radio.Button value="left">Left</Radio.Button>
                    <Radio.Button value="right">Right</Radio.Button>
                </Radio.Group>
                <div style={{width: '365px'}}>
                    <div style={{marginBottom: '20px'}}>
                        <div style={{marginBottom: '10px'}}>默认轮播图</div>
                        <Carousel
                            beforeChange={this.beforeChange}
                            afterChange={this.afterChange}
                            dotPosition={this.state.dotPosition}
                            mode="single"
                            sliderMode="button"
                            className="demo-carousel"
                        >
                            <div>
                                <h3>1</h3>
                            </div>
                            <div>
                                <h3>2</h3>
                            </div>
                            <div>
                                <h3>3</h3>
                            </div>
                            <div>
                                <h3>4</h3>
                            </div>
                        </Carousel>
                    </div>
                </div>
            </div>
        );
    }
}
