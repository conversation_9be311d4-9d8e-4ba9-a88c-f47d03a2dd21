import React, {PureComponent} from 'react';
import {Carousel} from '@baidu/one-ui';

export default class NoramlCarousel extends PureComponent {
    beforeChange = (from, to) => {
        console.log(from);
        console.log(to);
    }

    afterChange = current => {
        console.log(current);
    }

    getImage = () => {
        return [
            <div key="1">
                <img
                    alt="1"
                    width="100%"
                    src="https://s3.amazonaws.com/static.neostack.com/img/react-slick/abstract01.jpg"
                />
            </div>,
            <div key="2">
                <img
                    alt="2"
                    width="100%"
                    src="https://s3.amazonaws.com/static.neostack.com/img/react-slick/abstract02.jpg"
                />
            </div>,
            <div key="3">
                <img
                    key="3"
                    alt="3"
                    width="100%"
                    src="https://s3.amazonaws.com/static.neostack.com/img/react-slick/abstract03.jpg"
                />
            </div>,
            <div key="4">
                <img
                    key="4"
                    alt="4"
                    width="100%"
                    src="https://s3.amazonaws.com/static.neostack.com/img/react-slick/abstract01.jpg"
                />
            </div>,
            <div key="5">
                <img
                    key="5"
                    alt="5"
                    width="100%"
                    src="https://s3.amazonaws.com/static.neostack.com/img/react-slick/abstract02.jpg"
                />
            </div>,
            <div key="6">
                <img
                    alt="6"
                    width="100%"
                    src="https://s3.amazonaws.com/static.neostack.com/img/react-slick/abstract03.jpg"
                />
            </div>
        ];
    }

    render() {
        const images = this.getImage();
        return (
            <div style={{width: '880px'}}>
                <div style={{marginBottom: '20px'}}>
                    <div style={{marginBottom: '10px'}}>轮播图样式： 多图完整出现，展现N张，切换时候1张切换</div>
                    <br />
                    <br />
                    展示翻页按钮
                    <br />
                    <br />
                    <Carousel
                        slidesToScroll={1}
                        slidesToShow={3}
                        showButton
                    >
                        {images}
                    </Carousel>
                </div>
                <div style={{marginBottom: '20px'}}>
                    <div style={{marginBottom: '10px'}}>轮播图样式： 多图完整出现，展现N张，切换时候N张一起切换 允许无限循环切换</div>
                    <br />
                    <br />
                    不展示翻页按钮
                    <br />
                    <br />
                    <Carousel
                        slidesToScroll={3}
                        slidesToShow={3}
                        infinite
                    >
                        {images}
                    </Carousel>
                </div>
                <div style={{marginBottom: '20px'}}>
                    <div style={{marginBottom: '10px'}}>轮播图样式： 多图完整出现，展现N + 0.5张，切换时候N张一起切换</div>
                    <br />
                    <br />
                    不展示翻页按钮
                    <br />
                    <br />
                    <Carousel
                        slidesToScroll={2}
                        slidesToShow={2.5}
                        width={750}
                    >
                        {images}
                    </Carousel>
                </div>
                <div style={{marginBottom: '20px'}}>
                    <div style={{marginBottom: '10px'}}>轮播图样式： 多图完整出现，传入N，切换时候N张一起切换 自动循环播放</div>
                    <br />
                    <br />
                    不展示翻页按钮
                    <br />
                    <br />
                    <Carousel
                        slidesToScroll={1}
                        slidesToShow={3}
                        infinite
                        autoplay
                        autoplaySpeed={5000}
                    >
                        {images}
                    </Carousel>
                </div>
            </div>
        );
    }
}
