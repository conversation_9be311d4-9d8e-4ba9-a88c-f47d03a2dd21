import React, {PureComponent} from 'react';
import {Carousel} from '@baidu/one-ui';
import './index.less';

export default class NoramlCarousel extends PureComponent {
    beforeChange = (from, to) => {
        console.log(from);
        console.log(to);
    }

    afterChange = current => {
        console.log(current);
    }

    render() {
        return (
            <div style={{width: '365px', left: '100px'}}>
                <div style={{marginBottom: '20px'}}>
                    <div style={{marginBottom: '10px'}}>默认轮播图</div>
                    <Carousel
                        beforeChange={this.beforeChange}
                        afterChange={this.afterChange}
                        mode="single"
                        className="demo-carousel"
                        showButton
                        prevButtonProps={{
                            style: {
                                left: '-48px',
                                borderRadius: '4px'
                            }
                        }}
                        nextButtonProps={{
                            style: {
                                right: '-48px',
                                borderRadius: '4px'
                            }
                        }}
                    >
                        <div>
                            <h3>1</h3>
                        </div>
                        <div>
                            <h3>2</h3>
                        </div>
                        <div>
                            <h3>3</h3>
                        </div>
                        <div>
                            <h3>4</h3>
                        </div>
                    </Carousel>
                </div>
            </div>
        );
    }
}
