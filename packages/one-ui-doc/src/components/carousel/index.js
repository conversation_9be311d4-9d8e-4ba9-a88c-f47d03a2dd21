import BaseComponent from '../base';

export default {
    carousel: {
        value: BaseComponent,
        label: 'Carousel 轮播图',
        demos: [
            {
                title: '单屏模式',
                desc: '',
                source: 'single'
            },
            {
                title: '自定义翻页',
                desc: '自定义翻页按钮样式',
                source: 'customButton'
            },
            {
                title: '自动播放速度',
                desc: '',
                source: 'autoplaySpeed'
            },
            {
                title: '无限滚动',
                desc: '可无限滚动',
                source: 'infinite'
            },
            {
                title: '不同位置',
                desc: '条状轮播图 - 不同位置',
                source: 'position'
            },
            {
                title: '点状轮播图',
                desc: '点状轮播图',
                source: 'dot'
            },
            {
                title: '数字轮播图',
                desc: '数字轮播图',
                source: 'number'
            },
            {
                title: '隐藏翻页',
                desc: '隐藏翻页按钮',
                source: 'hideButton'
            },
            {
                title: '多页翻页',
                desc: '多页翻页',
                source: 'normal'
            }
        ],
        apis: [
            {
                apiKey: 'Carousel',
                title: 'Carousel'
            },
            {
                apiKey: 'CarouselMethods',
                title: 'methods'
            }
        ]
    }
};
