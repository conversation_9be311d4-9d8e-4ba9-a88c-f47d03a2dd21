export default {
    Carousel: [
        {
            param: 'className',
            type: 'String',
            desc: '自定义类名',
            option: '',
            default: ''
        },
        {
            param: 'autoplay',
            type: 'boolean',
            desc: '是否自动播放',
            option: '',
            default: 'false'
        },
        {
            param: 'infinite',
            type: 'boolean',
            desc: '是否无限循环',
            option: '',
            default: 'false'
        },
        {
            param: 'slidesToScroll',
            type: 'number',
            desc: '每次滚动几张',
            option: '',
            default: '1'
        },
        {
            param: 'slidesToShow',
            type: 'number',
            desc: '每屏幕展示多少张',
            option: '',
            default: '1'
        },
        {
            param: 'width',
            type: 'number',
            desc: '宽度',
            option: '',
            default: ''
        },
        {
            param: 'afterChange',
            type: 'Function(current)',
            desc: '轮播图切换后的回调',
            option: '',
            default: ''
        },
        {
            param: 'beforeChange',
            type: 'Function(from, to)',
            desc: '轮播图切换前的回调',
            option: '',
            default: ''
        },
        {
            param: 'mode',
            type: 'string',
            desc: '轮播图是一屏单张模式，还是一屏多张模式',
            option: 'multiple | single',
            default: 'multiple'
        },
        {
            param: 'dotPosition',
            type: 'string',
            desc: '面板指示点的位置',
            option: 'top bottom left right',
            default: 'bottom'
        },
        {
            param: 'sliderMode',
            type: 'string',
            desc: '切换点类型，{条状} {点状} {数字} {不展示切换点}',
            option: 'line | dot | number | hide',
            default: 'line'
        },
        {
            param: 'initialSlide',
            type: 'number',
            desc: '初始状态下从第几页开始（index, 第一页index: 0）',
            option: '',
            default: 0
        },
        {
            param: 'showButton',
            type: 'bool',
            desc: '只是展示翻页按钮',
            option: '',
            default: false
        },
        {
            param: 'prevButtonProps',
            type: 'object',
            desc: '向前翻页按钮的button的props',
            option: '',
            default: ''
        },
        {
            param: 'nextButtonProps',
            type: 'object',
            desc: '向后翻页按钮的button的props',
            option: '',
            default: ''
        },
        {
            param: 'customSuffix',
            type: 'ReactNode',
            desc: '自定义下排切换点，如果当前切换点类型不满足你的需求，可以自定义切换点',
            option: '',
            default: ''
        },
        {
            param: 'autoplaySpeed',
            type: 'number',
            desc: '自动翻页下翻页速度,autoplay为true时生效',
            option: '',
            default: '3000'
        }
    ],
    CarouselMethods: [
        {
            param: 'goTo(slideNumber, dontAnimate)',
            type: '',
            desc: '切换到指定面板',
            option: '',
            default: ''
        },
        {
            param: 'next()',
            type: '',
            desc: '切换到下一面板',
            option: '',
            default: ''
        },
        {
            param: 'prev()',
            type: '',
            desc: '切换到上一面板',
            option: '',
            default: ''
        },
        {
            param: 'prev()',
            type: '',
            desc: '切换到上一面板',
            option: '',
            default: ''
        }
    ]
};
