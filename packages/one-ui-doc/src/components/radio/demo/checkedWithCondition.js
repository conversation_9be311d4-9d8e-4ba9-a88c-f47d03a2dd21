import React, {PureComponent} from 'react';
import {Radio, Dialog} from '@baidu/one-ui';

const RadioGroup = Radio.Group;
const RadioButton = Radio.Button;

const CHILDREN = [
    <RadioButton value={1} key={1}>加强单选1</RadioButton>,
    <RadioButton value={2} key={2}>加强单选2</RadioButton>
];

export default class CheckedWithCondition extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            value: 1
        };
    }
    onChange = e => {
        const me = this;
        Dialog.confirm({
            title: '提示',
            content: '确认切换么？',
            onOk() {
                me.setState({value: e.target.value});
            }
        });
    }
    render() {
        return (
            <div>
                <div>当满足特定条件时才选中</div>
                <br />
                <RadioGroup value={this.state.value} onChange={this.onChange}>
                    {CHILDREN}
                </RadioGroup>
            </div>
        );
    }
}
