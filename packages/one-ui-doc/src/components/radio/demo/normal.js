import React, {PureComponent} from 'react';
import {Radio} from '@baidu/one-ui';

const RadioGroup = Radio.Group;

const ALL_OPTIONS = ['普通单选1', '普通单选2'];

const RadioNormal = () => {
    return <RadioGroup options={ALL_OPTIONS} defaultValue="普通单选1" />;
};

class RadioWithOnChange extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            value: '普通单选1'
        };
    }
    onChange = e => {
        this.setState({value: e.target.value});
    }
    render() {
        return <RadioGroup options={ALL_OPTIONS} value={this.state.value} onChange={this.onChange} />;
    }
};

export default () => {
    return (
        <div>
            <div>普通的单选</div>
            <br />
            <RadioNormal />
            <br />
            <br />
            <div>使用onChange的单选</div>
            <br />
            <RadioWithOnChange />
            <br />
            <br />
            文字换行
            <br />
            <br />
            <Radio style={{width: 200}}>
                文字换行文字换行文行文字换行文字换行文字换行文字换行文字换行文字换行行文字换行文字换行文字换行
                文字换行文字换行文行文字换行文字换行文字换行文字换行文字换行文字换行行文字换行文字换行文字换行
            </Radio>
        </div>
    );
};
