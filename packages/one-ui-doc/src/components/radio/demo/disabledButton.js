import React from 'react';
import {Popover, Radio} from '@baidu/one-ui';

const RadioGroup = Radio.Group;
const RadioButton = Radio.Button;

const CHILDREN = [
    <Popover content="为什么禁用提示" key="tip">
        <RadioButton value={1} key={1} disabled>加强单选1</RadioButton>
    </Popover>,
    <RadioButton value={2} key={2} disabled>加强单选2</RadioButton>,
    <RadioButton value={3} key={3}>加强单选3</RadioButton>
];

export default () => {
    return (
        <div>
            <br />
            <div>默认尺寸加强单选 - disabled状态</div>
            <br />
            <RadioGroup defaultValue={1}>
                {CHILDREN}
            </RadioGroup>
            <br />
            <RadioGroup type="simple" defaultValue={1}>
                {CHILDREN}
            </RadioGroup>
            <br />
        </div>
    );
};
