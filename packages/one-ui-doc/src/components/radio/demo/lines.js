import {Radio} from '@baidu/one-ui';

const options = Array.from(Array(20)).map((_, i) => `按钮选项${i + 1}`);

export default () => (
    <div>
        strong / small
        <br />
        <br />
        <Radio.Group options={options} size="small" type="strong" />
        <br />
        <br />
        strong / medium
        <br />
        <br />
        <Radio.Group options={options} type="strong" />
        <br />
        <br />
        <br />
        simple / small
        <br />
        <br />
        <Radio.Group options={options} size="small" type="simple" />
        <br />
        <br />
        simple / medium
        <br />
        <br />
        <Radio.Group options={options} type="simple" />
    </div>
);
