import React from 'react';
import {Radio} from '@baidu/one-ui';

const RadioGroup = Radio.Group;
const RadioButton = Radio.Button;

const BASIC_CHILDREN = [
    <RadioButton value={1} key={1}>按钮</RadioButton>,
    <RadioButton value={2} key={2}>按钮2</RadioButton>,
    <RadioButton value={3} key={3}>按钮单选3</RadioButton>
];

export default () => {
    return (
        <div>
            <div>开启最小宽</div>
            <br />
            <RadioGroup
                size="small"
                defaultValue={1}
                style={{'--one-checkbox-strong-min-width': 'initial'}}
            >
                {BASIC_CHILDREN}
            </RadioGroup>
            <br />
            <RadioGroup
                defaultValue={1}
                style={{'--one-checkbox-strong-min-width': 'initial'}}
            >
                {BASIC_CHILDREN}
            </RadioGroup>
        </div>
    );
};
