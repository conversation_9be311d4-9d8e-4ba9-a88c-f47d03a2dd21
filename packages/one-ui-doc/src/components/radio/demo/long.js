import React from 'react';
import {Radio} from '@baidu/one-ui';

const RadioGroup = Radio.Group;

const spanStyle = {
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    width: '90px',
    display: 'inline-block',
    verticalAlign: 'middle'
};

const getAllOptions = num => {
    const options = [];
    for (let i=0; i<num; i++) {
        options.push(<Radio value={i} style={spanStyle} key={i}>{`普通单选${i}`}</Radio>);
    }
    return options;
};

const ALL_OPTIOSN = getAllOptions(24);

const groupStyle = {
    lineHeight: 1.5
};

export default () => {
    return (
        <RadioGroup defaultValue={0} style={groupStyle}>
            {ALL_OPTIOSN}
        </RadioGroup>
    );
};
