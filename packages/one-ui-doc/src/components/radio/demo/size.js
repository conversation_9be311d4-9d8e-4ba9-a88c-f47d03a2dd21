import React, {PureComponent} from 'react';
import {Radio} from '@baidu/one-ui';

const RadioGroup = Radio.Group;

const ALL_OPTIONS = ['普通单选1', '普通单选2'];

const RadioNormal = props => {
    return <RadioGroup size={props.size} options={ALL_OPTIONS} defaultValue="普通单选1" />;
};

class RadioWithOnChange extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            value: '普通单选1'
        };
    }

    onChange = e => {
        this.setState({value: e.target.value});
    }

    render() {
        return <RadioGroup size={this.props.size} options={ALL_OPTIONS} value={this.state.value} onChange={this.onChange} />;
    }
}

export default () => {
    return (
        <div>
            <div>普通的单选 - small尺寸</div>
            <br />
            <RadioNormal size="small" />
            <br />
            <br />
            <div>使用onChange的单选 - small尺寸</div>
            <br />
            <RadioWithOnChange size="small" />
            <br />
            <br />
            <div>普通的单选</div>
            <br />
            <RadioNormal />
            <br />
            <br />
            <div>使用onChange的单选</div>
            <br />
            <RadioWithOnChange />
        </div>
    );
};
