import React from 'react';
import {Radio} from '@baidu/one-ui';

const RadioGroup = Radio.Group;
const RadioButton = Radio.Button;

const BASIC_CHILDREN = [
    <RadioButton value={1} key={1}>简单按钮单选1</RadioButton>,
    <RadioButton value={2} key={2}>简单按钮单选2</RadioButton>,
    <RadioButton value={3} key={3}>简单按钮单选3</RadioButton>
];

export default () => {
    return (
        <div>
            <div>small</div>
            <br />
            <RadioGroup type="simple" size="small" defaultValue={1}>
                {BASIC_CHILDREN}
            </RadioGroup>
            <br />
            <br />
            <div>medium</div>
            <br />
            <RadioGroup type="simple" defaultValue={1}>
                {BASIC_CHILDREN}
            </RadioGroup>
            <br />
        </div>
    );
};
