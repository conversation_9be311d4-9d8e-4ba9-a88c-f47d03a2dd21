import React from 'react';
import {Radio} from '@baidu/one-ui';

const RadioGroup = Radio.Group;

const SIMPLE_OPTION_LIST = ['Apple', 'Pear', 'Orange'];
const COMPLEX_OPTION_LIST = [{
    label: '苹果',
    value: 'Apple'
},
{
    label: '梨',
    value: 'Pear',
    disabled: true
},
{
    label: '桔子',
    value: 'Orange'
}];

export default () => {
    return (
        <div>
            <div>options基础使用</div>
            <br />
            <RadioGroup options={SIMPLE_OPTION_LIST} defaultValue="Apple" />
            <br />
            <br />
            <div>options定义value，label，disabled</div>
            <br />
            <RadioGroup options={COMPLEX_OPTION_LIST} defaultValue="Apple" />
        </div>
    );
};
