import React, {useState} from 'react';
import {Radio, Switch} from '@baidu/one-ui';

const RadioGroup = Radio.Group;
const RadioButton = Radio.Button;

const LIST = Array(6).fill(0).map((_, index) => ({key: index, label: `按钮单选${index + 1}`}));

export default () => {

    const [isEven, setIsEven] = useState(true);

    return (
        <div>
            <div>
                是否均分
                <Switch checked={isEven} onChange={setIsEven} />
            </div>
            <br />
            <RadioGroup
                defaultValue={0}
                distribution={isEven ? 'even' : 'auto'}
            >
                {
                    LIST.map(({key, label}) => {
                        return (
                            <RadioButton value={key} key={key}>{label}</RadioButton>
                        );
                    })
                }
            </RadioGroup>
        </div>
    );
};
