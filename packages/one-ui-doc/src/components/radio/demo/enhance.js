import React from 'react';
import {Radio} from '@baidu/one-ui';

const RadioGroup = Radio.Group;
const RadioButton = Radio.Button;

const CHILDREN = [
    <RadioButton value={1} key={1}>加强单选1</RadioButton>,
    <RadioButton value={2} key={2}>加强单选2</RadioButton>,
    <RadioButton value={3} key={3}>加强单选3</RadioButton>
];

export default () => {
    return (
        <div>
            <div>小尺寸加强单选</div>
            <br />
            <RadioGroup size="small" defaultValue={1}>
                {CHILDREN}
            </RadioGroup>
            <br />
            <br />
            <div>小尺寸加强禁用</div>
            <br />
            <RadioGroup disabled size="small" defaultValue={1}>
                {CHILDREN}
            </RadioGroup>
            <br />
            <div>默认尺寸加强单选</div>
            <br />
            <RadioGroup defaultValue={1}>
                {CHILDREN}
            </RadioGroup>
            <br />
        </div>
    );
};
