import React, {useState} from 'react';
import {Button, Radio, Stack} from '@baidu/one-ui';

export default () => {
    const [value, setValue] = useState('Apple')
    return (
        <Stack direction="column" gap="medium" align="start">
            <Radio.Group
                options={['Apple', 'Pear', 'Orange']}
                value={value}
                onChange={e => setValue(e.target.value)}
            />
            <Radio.Group
                options={['Apple', 'Pear', 'Orange']}
                value={value}
                onChange={e => setValue(e.target.value)}
                type="strong"
            />
            <Radio.Group
                options={['Apple', 'Pear', 'Orange']}
                value={value}
                onChange={e => setValue(e.target.value)}
                type="simple"
            />
            <Button onClick={() => setValue(undefined)}>清空</Button>
        </Stack>
    );
};
