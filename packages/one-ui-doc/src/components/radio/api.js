export default {
    Radio: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义样式名',
            option: '',
            default: ''
        },
        {
            param: 'checked',
            type: 'boolean',
            desc: '是否选中',
            option: '',
            default: 'false'
        },
        {
            param: 'disabled',
            type: 'boolean',
            desc: '是否不可操作',
            option: '',
            default: 'false'
        },
        {
            param: 'value',
            type: 'number、string',
            desc: '值',
            option: '',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: '型号大小，目前支持小号 or 中号，默认中号',
            option: 'small、medium',
            default: 'medium'
        },
        {
            param: 'onChange',
            type: 'Function(e) => checked: e.target.checked',
            desc: '变化时回调函数',
            option: '',
            default: ''
        }
    ],
    RadioButton: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义样式名',
            option: '',
            default: ''
        },
        {
            param: 'checked',
            type: 'boolean',
            desc: '是否选中',
            option: '',
            default: 'false'
        },
        {
            param: 'value',
            type: 'number、string',
            desc: '如果有此value值，以外部传入的value为准',
            option: '',
            default: ''
        }
    ],
    RadioGroup: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义样式名',
            option: '',
            default: ''
        },
        {
            param: 'disabled',
            type: 'boolean',
            desc: '是否不可操作',
            option: '',
            default: 'false'
        },
        {
            param: 'size',
            type: 'string',
            desc: '型号大小，目前支持小号 or 中号，默认中号',
            option: 'small、medium',
            default: 'medium'
        },
        {
            param: 'options',
            type: 'Array<string | number | {label: string, value: string | number, disabled: bool}>',
            desc: '指定可选项',
            option: '',
            default: '[]'
        },
        {
            param: 'value',
            type: 'number、string',
            desc: '指定选中项',
            option: '',
            default: ''
        },
        {
            param: 'direction',
            type: 'string',
            desc: '下属Radio水平排列还是垂直排列',
            option: 'row、column',
            default: 'row'
        },
        {
            param: 'onChange',
            type: 'Function(e:Event)',
            desc: '变化时回调函数<br>获取选中的单选值：{e.target.value}',
            option: '',
            default: ''
        },
        {
            param: 'type',
            type: 'string',
            desc: '类型普通、加强按钮、简单按钮',
            option: 'normal | strong | simple',
            default: ''
        }
    ]
};
