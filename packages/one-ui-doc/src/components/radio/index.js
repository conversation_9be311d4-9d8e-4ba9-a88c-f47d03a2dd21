import BaseComponent from '../base';

export default {
    radio: {
        value: BaseComponent,
        label: 'Radio 单选框',
        demos: [
            {
                title: '基本使用',
                desc: '各种状态的单选 {默认状态} {悬浮状态} {点选状态}',
                source: 'normal'
            },
            {
                title: '尺寸',
                desc: '各种状态的单选 {默认状态} {悬浮状态} {点选状态}',
                source: 'size'
            },
            {
                title: '禁用',
                desc: '各种状态的不可用单选 {不可用} {选中不可用}',
                source: 'disabled'
            },
            {
                title: 'Group 基础',
                desc: '简单模式使用{options}，自定义dom结构使用{Radio}',
                source: 'groupBase'
            },
            {
                title: 'Group 清空',
                desc: '清空已选的值',
                source: 'clear'
            },
            {
                title: 'Group options',
                desc: 'Array<string | number | {label: string, value: string | number, disabled: bool}>',
                descFlag: false,
                source: 'groupOptions'
            },
            {
                title: 'Group 垂直',
                desc: '默认{水平展示}，可以设置为{垂直展示}',
                source: 'direction'
            },
            {
                title: 'Button 加强',
                desc: '有两种尺寸{small} {medium}',
                source: 'enhance'
            },
            {
                title: 'Button 简单',
                desc: '有两种尺寸{small} {medium}',
                source: 'simpleButton'
            },
            {
                title: 'Button 换行',
                desc: '选项过多时，自动换行',
                source: 'lines'
            },
            {
                title: 'Button 禁止',
                desc: '存在禁止状态的加强单选',
                source: 'disabledButton'
            },
            {
                title: 'Button 条件选中',
                desc: '',
                source: 'checkedWithCondition'
            },
            {
                title: 'Button 最小宽',
                desc: '--one-checkbox-strong-min-width: initial | length | percentage',
                source: 'minWidth'
            },
            {
                title: 'Button 空间分布',
                desc: '',
                source: 'Distribution'
            }
        ],
        apis: [
            {
                apiKey: 'Radio',
                title: 'Radio'
            },
            {
                apiKey: 'RadioButton',
                title: 'Radio.Button'
            },
            {
                apiKey: 'RadioGroup',
                title: 'Radio.Group'
            }
        ]
    }
};
