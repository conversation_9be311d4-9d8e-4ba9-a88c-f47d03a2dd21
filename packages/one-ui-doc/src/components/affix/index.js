import BaseComponent from '../base';

export default {
    affix: {
        value: BaseComponent,
        label: 'Affix 固钉',
        desc: 'fix过程会引起页面dom的变化导致重绘，建议自行使用sticky实现吸顶或者吸底',
        demos: [
            {
                title: '偏移量',
                desc: '可通过`offsetTop`和`offsetBottom`设置固定位置相对顶部或底部的偏移量。',
                source: 'offset'
            },
            {
                title: '状态回调',
                desc: '可通过`onChange`回调捕获是否固定`affixed`状态。',
                source: 'onChange'
            },
            {
                title: '滚动容器',
                desc: '可通过`target` 设置 Affix 需要监听其滚动事件的容器，默认为 window。',
                source: 'target'
            }
        ],
        apis: [
            {
                apiKey: 'Affix',
                title: 'Affix'
            }
        ]
    }
};
