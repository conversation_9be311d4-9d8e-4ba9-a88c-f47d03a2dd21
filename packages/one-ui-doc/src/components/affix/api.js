export default {
    Affix: [
        {
            param: 'offsetBottom',
            type: 'number',
            desc: '距离窗口底部达到指定偏移量后触发',
            option: '',
            default: ''
        },
        {
            param: 'offsetTop',
            type: 'numbrt',
            desc: '距离窗口顶部达到指定偏移量后触发',
            option: '',
            default: ''
        },
        {
            param: 'target',
            type: '() => HTMLElement',
            desc: '设置 Affix 需要监听其滚动事件的元素，值为一个返回对应 DOM 元素的函数',
            option: '',
            default: '() => window'
        },
        {
            param: 'onChange',
            type: 'Function(affixed)',
            desc: '固定状态改变时触发的回调函数',
            option: '',
            default: ''
        },
        {
            param: 'zIndex',
            type: 'number',
            desc: 'Affix的z-index',
            option: '',
            default: '1'
        }
    ]
};
