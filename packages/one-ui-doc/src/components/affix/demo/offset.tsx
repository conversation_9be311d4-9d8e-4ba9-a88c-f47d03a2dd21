import React, {useState} from 'react';
import {Affix, Button} from '@baidu/one-ui';

export default () => {
    const [top, setTop] = useState(200);
    const [bottom, setBottom] = useState(200);

    return (
        <>
            <Affix offsetTop={top}>
                <Button onClick={() => setTop(top + 10)}>Top: {top}</Button>
            </Affix>
            <br />
            <Affix offsetBottom={bottom}>
                <Button onClick={() => setBottom(bottom + 10)}>Bottom: {bottom}</Button>
            </Affix>
        </>
    );
};
