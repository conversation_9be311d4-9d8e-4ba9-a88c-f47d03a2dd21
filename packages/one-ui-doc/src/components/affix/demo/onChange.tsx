import React, {useState} from 'react';
import {Affix, Button} from '@baidu/one-ui';

const AffixDemo = () => {
    const [affixed, setAffixed] = useState(false);

    const handleAffixChange = (affixed) => {
        setAffixed(affixed);
    };

    console.log('affixed:', affixed);

    return (
        <Affix offsetTop={100} onChange={handleAffixChange}>
            <Button>状态回调</Button>
        </Affix>
    );
};

export default AffixDemo;
