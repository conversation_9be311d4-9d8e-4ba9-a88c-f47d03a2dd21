import React, {useRef} from 'react';
import {Affix, Button} from '@baidu/one-ui';

export default () => {
    const containerRef = useRef(null);

    return (
        <div style={{width: 400, height: 100, overflow: 'scroll'}} ref={containerRef}>
            <div style={{height: 500, background: '#dbdbdb', paddingTop: 60}}>
                <Affix target={() => containerRef.current}>
                    <Button>容器中固定</Button>
                </Affix>
            </div>
        </div>
    );
};
