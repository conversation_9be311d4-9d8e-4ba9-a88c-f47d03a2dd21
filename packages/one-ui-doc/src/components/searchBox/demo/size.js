import React, {PureComponent} from 'react';
import {SearchBox} from '@baidu/one-ui';

const options = [
    {
        label: '操作命令1',
        value: 'lll',
        disabled: true
    },
    {
        label: '操作命令2',
        value: 'zzz'
    }
];
export default class Normal extends PureComponent {
    state = {
        options: []
    };

    search = value => {
        console.log(value);
    };

    onChange = value => {
        console.log(value);
        this.setState({
            options
        });
    }

    onVisibleChange = visible => {
        console.log('visible', visible);
    }

    render() {
        return (
            <div>
                <div>超小尺寸</div>
                <br />
                <SearchBox options={this.state.options} size="xsmall" defaultValue="" onSearch={this.search} onChange={this.onChange} onVisibleChange={this.onVisibleChange} />
                <br />
                <br />
                <div>小尺寸</div>
                <br />
                <SearchBox options={this.state.options} size="small" defaultValue="" onSearch={this.search} onChange={this.onChange}/>
                <br />
                <br />
                <div>中尺寸</div>
                <br />
                <SearchBox options={this.state.options} size="medium" defaultValue="" onSearch={this.search} onChange={this.onChange}/>
                <br />
                <br />
                <div>大尺寸</div>
                <br />
                <SearchBox options={this.state.options} size="large" defaultValue="" onSearch={this.search} onChange={this.onChange}/>
                <br />
                <br />
                <div>超小尺寸</div>
                <br />
                <br />
                <SearchBox options={this.state.options} size="xsmall" defaultValue="" onSearch={this.search} searchIconType="button" onChange={this.onChange}/>
                <br />
                <br />
                <div>小尺寸</div>
                <br />
                <SearchBox options={this.state.options} size="small" defaultValue="" onSearch={this.search} searchIconType="button" onChange={this.onChange}/>
                <br />
                <br />
                <div>中尺寸</div>
                <br />
                <SearchBox options={this.state.options} size="medium" defaultValue="" onSearch={this.search} searchIconType="button" onChange={this.onChange}/>
                <br />
                <br />
                <div>大尺寸</div>
                <br />
                <SearchBox options={this.state.options} size="large" defaultValue="" onSearch={this.search} searchIconType="button" onChange={this.onChange}/>
                <br />
            </div>
        );
    }
}
