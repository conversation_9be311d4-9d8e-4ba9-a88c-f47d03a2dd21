export default {
    SearchBox: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义样式名',
            option: '',
            default: ''
        },
        {
            param: 'disabled',
            type: 'boolean',
            desc: '是否不可操作',
            option: '',
            default: 'false'
        },
        {
            param: 'showSearchIcon',
            type: 'boolean',
            desc: '是否展示搜索按钮',
            option: '',
            default: 'true'
        },
        {
            param: 'showCloseIcon',
            type: 'boolean',
            desc: '是否展示关闭按钮（4.0.0-alpha-60）',
            option: '',
            default: 'true'
        },
        {
            param: 'width',
            type: 'number',
            desc: '指定宽度',
            option: '',
            default: 300
        },
        {
            param: 'value',
            type: 'string',
            desc: '搜索框值',
            option: '',
            default: ''
        },
        {
            param: 'placeholder',
            type: 'string',
            desc: '在用户输入值之前显示的提示信息',
            option: '',
            default: ''
        },
        {
            param: 'options',
            type: 'array',
            desc: '下拉框中数据源，{key：value}数组',
            option: '',
            default: '[]'
        },
        {
            param: 'isShowDropDown',
            type: 'boolean',
            desc: '是否展现下拉框',
            option: '',
            default: ''
        },
        {
            param: 'overlay',
            type: 'node',
            desc: '自定义下拉面板',
            option: '',
            default: ''
        },
        {
            param: 'searchIconType',
            type: 'string',
            desc: '搜索功能的按钮样式，(icon/button)',
            option: '',
            default: ''
        },
        {
            param: 'onClearClick',
            type: 'function',
            desc: '点击清空按钮时回调函数',
            option: '',
            default: ''
        },
        {
            param: 'onChange',
            type: 'function',
            desc: '输入框值改变时回调函数，暴露的是e，通过e.target.value取值',
            option: '',
            default: ''
        },
        {
            param: 'handleMenuClick',
            type: 'function',
            desc: '点击下拉选择的时候的方法，传出e，通过e.key取值',
            option: '',
            default: ''
        },
        {
            param: 'onSearch',
            type: 'function',
            desc: '点击搜索按钮/回车时回调函数',
            option: '',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: 'xsmall, small, medium, large',
            option: '',
            default: 'medium'
        },
        {
            param: 'onVisibleChange',
            type: 'func(visible)',
            desc: '弹层面板visible change的时候的回调',
            option: '',
            default: ''
        }
    ]
};
