export default {
    NumberInput: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义样式名',
            option: '',
            default: ''
        },
        {
            param: 'mode',
            type: 'string',
            desc: '数字输入框类型',
            option: 'basic|strong',
            default: 'basic'
        },
        {
            param: 'disabled',
            type: 'boolean',
            desc: '是否不可操作',
            option: '',
            default: 'false'
        },
        {
            param: 'width',
            type: 'number',
            desc: '指定宽度',
            option: '',
            default: 160
        },
        {
            param: 'value',
            type: 'string、number',
            desc: '输入框值，必填',
            option: '',
            default: ''
        },
        {
            param: 'placeholder',
            type: 'string',
            desc: '在用户输入值之前显示的提示信息',
            option: '',
            default: ''
        },
        {
            param: 'type',
            type: 'string',
            desc: '输入数字的类型',
            option: 'float、int',
            default: 'float'
        },
        {
            param: 'max',
            type: 'string、number',
            desc: '最大值',
            option: '',
            default: ''
        },
        {
            param: 'min',
            type: 'string、number',
            desc: '最小值',
            option: '',
            default: ''
        },
        {
            param: 'step',
            type: 'number',
            desc: '调节步长',
            option: '',
            default: '1'
        },
        {
            param: 'fixed',
            type: 'number',
            desc: '保留的小数位数，只有当type为float时有效',
            option: '',
            default: 'Number.POSITIVE_INFINITY'
        },
        {
            param: 'showTip',
            type: 'boolean',
            desc: '是否展示提示信息',
            option: '',
            default: 'true'
        },
        {
            param: 'tipText',
            type: 'ReactNode',
            desc: '提示信息',
            option: '',
            default: ''
        },
        {
            param: 'errorMessage',
            type: 'ReactNode',
            desc: '错误信息',
            option: '',
            default: ''
        },
        {
            param: 'location',
            type: 'string',
            desc: '位置信息',
            option: 'right、layer、bottom',
            default: 'right'
        },
        {
            param: 'errorLocation',
            type: 'string',
            desc: '错误信息位置',
            option: 'right、layer、bottom',
            default: 'location的值'
        },
        {
            param: 'tipLocation',
            type: 'string',
            desc: '提示信息位置',
            option: 'right、layer、bottom',
            default: 'location的值'
        },
        {
            param: 'tailLabel',
            type: 'string',
            desc: '固定在数字输入框后面的信息，一般为单位信息',
            option: '',
            default: ''
        },
        {
            param: 'onChange',
            type: 'Function(e:Event)',
            desc: '变化时回调函数<br>获取数字输入框的值：{e.target.value}',
            option: '',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: '尺寸-小号、中号、超小号',
            option: 'xsmall、small、medium',
            default: 'medium'
        },
        {
            param: 'readOnly',
            type: 'bool',
            desc: '是否只是可读',
            option: '',
            default: 'false'
        }, {
            param: 'showErrorWithoutErrorMessage',
            type: 'bool',
            desc: '是否报错，在没有errorMessage的情况下',
            option: '',
            default: 'false'
        }, {
            param: 'showErrorMessage',
            type: 'bool',
            desc: '错误信息；当不传此值时，会内部校验是否非空、最短字符、最长字符；当传此值时，错误信息以此值为准',
            option: '',
            default: 'true'
        }
    ]
};
