import React, {useCallback, useState} from 'react';
import {NumberInput, Radio, Stack} from '@baidu/one-ui';

export default () => {
    const [value, setValue] = useState(1.1);
    const [errorLocation, setErrorLocation] = useState('right');

    const onChange = useCallback(e => {
        const value = e.target.value;
        console.log(`[${typeof value}]`, value);
        setValue(value);
    }, []);

    // @type {NumberInputProps}
    const props = {
        tailLabel: '元',
        max: 9.99,
        min: 0.01,
        step: 0.01,
        placeholder: '请输入',
        value,
        onChange,
        errorLocation,
        errorMessage: '错误提示演示'
    };

    return (
        <Stack direction="column" gap={40}>
            <Radio.Group
                size="small"
                type="strong"
                value={errorLocation}
                onChange={e => setErrorLocation(e.target.value)}
                options={['right', 'bottom', 'layer']}
            />
            <NumberInput {...props} />
            <NumberInput {...props} mode="strong" />
        </Stack>
    );
};