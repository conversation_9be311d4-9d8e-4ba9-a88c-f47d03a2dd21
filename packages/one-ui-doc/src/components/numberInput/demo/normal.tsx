import React, {useCallback, useState} from 'react';
import {NumberInput, Stack} from '@baidu/one-ui';

export default () => {
    const [value, setValue] = useState(1.1);

    const onChange = useCallback(e => {
        const value = e.target.value;
        console.log(`[${typeof value}]`, value);
        setValue(value);
    }, []);

    const props = {
        tailLabel: '元',
        max: 9.99,
        min: 0.01,
        step: 0.01,
        placeholder: '请输入',
        value,
        onChange
    };

    return (
        <Stack direction="column" gap="large">
            <NumberInput {...props} />
            <NumberInput {...props} mode="strong" />
        </Stack>
    );
};