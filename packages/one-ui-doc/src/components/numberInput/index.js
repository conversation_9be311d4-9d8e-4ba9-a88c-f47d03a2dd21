import BaseComponent from '../base';

export default {
    numberInput: {
        value: BaseComponent,
        label: 'NumberInput 数字输入框',
        demos: [
            {
                title: '基础',
                desc: '两个变体，{normal} 和 {strong}',
                source: 'normal'
            },
            {
                title: '尺寸',
                desc: '尺寸-支持超小号，小号，中号',
                source: 'size'
            },
            {
                title: '禁用',
                desc: '',
                source: 'disabled'
            },
            {
                title: '只读',
                desc: '',
                source: 'readonly'
            },
            {
                title: '前后缀',
                desc: '通过{prefix}与{suffix}进行前后缀设置，注意前后缀会影响输入区的宽度',
                source: 'affix'
            },
            {
                title: '提示信息位置',
                desc: '{右侧} {下方} {浮层}',
                source: 'tips'
            },
            {
                title: '错误信息位置',
                desc: '{右侧} {下方} {浮层}',
                source: 'errors'
            }
        ],
        apis: [
            {
                apiKey: 'NumberInput',
                title: 'NumberInput'
            }
        ]
    }
};
