import React, {PureComponent} from 'react';
import {Sortable} from '@baidu/one-ui';

export default class NormalDraggable extends PureComponent {
    state = {
        options: [
            '1. 在那山的那边海的那边',
            '2. 有一群蓝精灵',
            '3. 他们活泼又聪明',
            '4. 他们调皮又伶俐',
            '5. 他们自由自在生活在那',
            '6. 绿色的大森林',
            '7. 他们善良勇敢相互都欢喜',
            '8. Ou',
            '9. 可爱的蓝精灵',
            '10. Ou',
            '11. 可爱的蓝精灵',
            '12. 他们齐心合力开动脑筋',
            '13. 斗败了格格巫',
            '14. 他们唱歌跳舞快乐多欢喜',
            '15. 在那山的那边海的那边',
            '16. 有一群蓝精灵',
            '17. 他们活泼又聪明',
            '18. 他们调皮又灵敏',
            '19. 他们自由自在生活在那',
            '20. 绿色的大森林'
        ].map(key => ({key, label: key}))
    };

    render() {
        const options = this.state.options;
        return (
            <Sortable
                options={options}
                onChange={options => {
                    this.setState({options});
                }}
                renderOption={option => (
                    <div
                        key={option.key}
                        style={{
                            display: 'inline-block',
                            background: '#fff',
                            border: '1px solid pink',
                            borderRadius: 3,
                            margin: '0 10px 8px 0',
                            padding: '1px 2px',
                            width: 200,
                            fontSize: 12
                        }}
                    >
                        <div>{option.label}</div>
                    </div>
                )}
                style={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    alignItems: 'center',
                    color: '#282c33'
                }}
            />
        );
    }
}
