import BaseComponent from '../base';

export default {
    sortable: {
        value: BaseComponent,
        label: 'Sortable 拖拽排序',
        demos: [
            {
                title: 'Axis: X',
                desc: '',
                source: 'normal'
            },
            {
                title: 'Axis: X等宽',
                desc: '',
                source: 'fixedWidth'
            },
            {
                title: 'Axis: Y',
                desc: '',
                source: 'column'
            }
        ],
        apis: [
            {
                apiKey: 'Sortable',
                title: 'Sortable'
            },
            {
                apiKey: 'SortableHandle',
                title: 'Sortable.Handle'
            }
        ]
    }
};
