export default {
    Sortable: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义类名',
            option: '',
            default: ''
        },
        {
            param: 'options',
            type: 'Array',
            desc: '数据源',
            option: '',
            default: ''
        },
        {
            param: 'renderOption',
            type: 'func(option, index)',
            desc: '渲染可拖拽项',
            option: '',
            default: ''
        },
        {
            param: 'onChange',
            type: 'func(options, oldIndex, newIndex)',
            desc: '拖拽变化回调',
            option: '',
            default: ''
        },
        {
            param: 'tag',
            type: 'string',
            desc: '标签',
            option: '',
            default: 'div'
        },
        {
            param: 'useHandle',
            type: 'bool',
            desc: '是否使用handle定义拖拽区',
            option: '',
            default: 'false'
        }
    ],
    Handle: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义类名',
            option: '',
            default: ''
        },
        {
            param: 'tag',
            type: 'string',
            desc: '标签',
            option: '',
            default: 'span'
        }
    ]
};
