import React from 'react';
import {Popover, Button} from '@baidu/one-ui';

const positions = [
    [
        null,
        'topLeft',
        'top',
        'topRight',
        null
    ],
    [
        'leftTop',
        null,
        null,
        null,
        'rightTop'
    ],
    [
        'left',
        null,
        null,
        null,
        'right'
    ],
    [
        'leftBottom',
        null,
        null,
        null,
        'rightBottom'
    ],
    [
        null,
        'bottomLeft',
        'bottom',
        'bottomRight',
        null
    ]
];

export default () => {
    const content = (
        <div>
            <p>Content</p>
            <p>Content</p>
        </div>
    );
    return (
        <div style={{width: 600, display: 'grid', gap: 8, gridTemplateColumns: 'repeat(5, 1fr)'}}>
            {
                positions.map(row => (
                    row.map(placement => (!placement
                        ? <div />
                        : (
                            <Popover
                                placement={placement}
                                content={content}
                            >
                                <Button>{placement}</Button>
                            </Popover>
                        )))
                ))
            }
        </div>
    );
};
