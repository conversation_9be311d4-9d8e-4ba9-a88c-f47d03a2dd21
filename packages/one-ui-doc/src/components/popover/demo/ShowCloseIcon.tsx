import React, {useState} from 'react';
import {Popover, Button, Switch, Radio} from '@baidu/one-ui';

const RadioGroup = Radio.Group;
const RadioButton = Radio.Button;

export default function ShowCloseIcon() {

    const [showCloseIcon, setShowCloseIcon] = useState<boolean>(true);
    const [trigger, setTrigger] = useState<string>('click');

    return (
        <div>
            <div>
                显示右上角关闭Icon：<Switch checked={showCloseIcon} onChange={setShowCloseIcon} />
            </div>
            <div style={{display: 'flex'}}>
                <span>触发方式：</span>
                <RadioGroup
                    value={trigger}
                    options={[
                        {label: 'click', value: 'click'},
                        {label: 'hover', value: 'hover'}
                    ]}
                    onChange={e => setTrigger(e.target.value)}
                >
                    <RadioButton value="click" key="click">click</RadioButton>,
                    <RadioButton value="hover" key="hover">hover</RadioButton>,
                </RadioGroup>
            </div>
            <br />
            <Popover
                title="标题"
                content={(
                    <div>
                        <p>Content</p>
                        <p>Content</p>
                    </div>
                )}
                showCloseIcon={showCloseIcon}
                trigger={trigger}
            >
                <Button>按钮</Button>
            </Popover>
        </div>
    );
}
