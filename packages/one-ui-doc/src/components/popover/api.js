export default {
    Popover: [
        {
            param: 'content',
            type: 'ReactNode',
            desc: '气泡弹层的内容',
            option: '',
            default: ''
        },
        {
            param: 'overlayStyle',
            type: 'obj',
            desc: '气泡弹层的样式',
            option: '',
            default: ''
        },
        {
            param: 'placement',
            type: 'string',
            desc: '气泡框位置，可选 top left right bottom topLeft topRight bottomLeft bottomRight leftTop leftBottom rightTop rightBottom',
            option: 'bottom',
            default: ''
        },
        {
            param: 'title',
            type: 'ReactNode',
            desc: '气泡弹层的标题',
            option: '',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: 'small or medium， popover尺寸',
            option: '',
            default: 'medium'
        },
        {
            param: 'trigger',
            type: 'string',
            desc: '气泡弹层打开的触发模式',
            option: 'hover | click',
            default: 'hover'
        },
        {
            param: 'visible',
            type: 'boolean',
            desc: '气泡弹层是否可见',
            option: '',
            default: ''
        },
        {
            param: '其余属性',
            type: '',
            desc: '可参照tooltip',
            option: '',
            default: ''
        }
    ]
};
