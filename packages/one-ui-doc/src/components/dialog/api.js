export default {
    Dialog: [
        {
            param: 'className',
            type: 'String',
            desc: '自定义类名',
            option: '',
            default: ''
        },
        {
            param: 'buttonPosition',
            type: 'string',
            desc: '按钮的位置',
            option: 'left, center, right',
            default: 'left'
        },
        {
            param: 'buttonSize',
            type: 'string',
            desc: '按钮的类型',
            option: 'small, medium',
            default: 'medium'
        },
        {
            param: 'confirmLoading',
            type: 'boolean',
            desc: 'confirm点击的时候是否需要loading，仅Dialog.confirm生效',
            option: 'bool',
            default: 'false'
        },
        {
            param: 'destroyOnClose',
            type: 'bool',
            desc: '关闭以后弹窗是否销毁',
            option: '',
            default: 'false'
        },
        {
            param: 'footer',
            type: 'Button []',
            desc: 'footer为一个数组，数组里面是button，可自己定义footer',
            option: '',
            default: ''
        },
        {
            param: 'mask',
            type: 'boolean',
            desc: '是否展示遮罩',
            option: '',
            default: 'true'
        },
        {
            param: 'maskClosable',
            type: 'boolean',
            desc: '点击遮罩是否可以关闭',
            option: '',
            default: 'false'
        },
        {
            param: 'cancelText',
            type: 'string',
            desc: '取消的文案',
            option: '',
            default: ''
        },
        {
            param: 'okText',
            type: 'string',
            desc: '确定按钮的文案',
            option: '',
            default: ''
        },
        {
            param: 'okType',
            type: 'string',
            desc: '确定按钮的type',
            option: '',
            default: '参照button的Type'
        },
        {
            param: 'onCancel',
            type: 'Function(e)',
            desc: '弹窗点击取消/弹窗点击X的回调',
            option: '',
            default: ''
        },
        {
            param: 'onOk',
            type: 'Function(e)',
            desc: '弹窗点击按钮',
            option: '',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: '弹窗的尺寸',
            option: 'small | medium',
            default: 'medium'
        },
        {
            param: 'title',
            type: 'string|ReactNode',
            desc: '弹窗标题',
            option: '',
            default: ''
        },
        {
            param: 'visible',
            type: 'boolean',
            desc: '弹窗是否可见',
            option: '',
            default: ''
        },
        {
            param: 'width',
            type: 'number | string',
            desc: '弹窗宽度， small为400， meidum为600，large为800',
            option: 'small | medium | large | 自定义宽度',
            default: 'medium'
        },
        {
            param: 'zIndex',
            type: 'number',
            desc: '弹窗的z-index',
            option: '',
            default: ''
        },
        {
            param: 'needCloseIcon',
            type: 'bool',
            desc: '是否需要右上角关闭Icon',
            option: '',
            default: 'true'
        },
        {
            param: 'fullScreen',
            type: 'bool',
            desc: 'dialog全屏模式',
            option: '',
            default: 'false'
        }
    ],
    DialogConfirm: [{
        param: 'buttonPosition',
        type: 'string',
        desc: 'confirm按钮的位置',
        option: 'left | center | right',
        default: 'left'
    }, {
        param: 'okCancel',
        type: 'bool',
        desc: '是否有取消按钮',
        option: '',
        default: 'true'
    }, {
        param: 'content',
        type: 'reactNode | string',
        desc: 'confirm的内容',
        option: '',
        default: ''
    }, {
        param: 'content',
        type: 'reactNode | string',
        desc: 'confirm的内容',
        option: '',
        default: ''
    }, {
        param: 'okType',
        type: 'string',
        desc: '确认按钮的类型',
        option: '',
        default: '参照button type设置'
    }, {
        param: 'onOk',
        type: 'func',
        desc: '点击确认后的回调',
        option: '',
        default: ''
    }, {
        param: 'okProps',
        type: 'object',
        desc: '确认按钮的属性',
        option: '',
        default: '可参照button的属性'
    }, {
        param: 'cancelProps',
        type: 'object',
        desc: '取消按钮的属性',
        option: '',
        default: '可参照button的属性'
    }, {
        param: 'onCancel',
        type: 'func',
        desc: '点击取消的时候的回调',
        option: '',
        default: ''
    }, {
        param: 'size',
        type: 'string',
        desc: 'confirm弹窗的尺寸',
        option: '',
        default: '参照dialog的size'
    }, {
        param: 'okText',
        type: 'string',
        desc: '确认按钮的话术',
        option: '',
        default: '确定'
    }, {
        param: 'okType',
        type: 'string',
        desc: '确认按钮的类型',
        option: '参照button的type',
        default: 'primary'
    }, {
        param: 'cancelText',
        type: 'string',
        desc: '取消按钮的话术',
        option: '',
        default: '取消'
    }, {
        param: 'cancelType',
        type: 'string',
        desc: '取消按钮的类型',
        option: '参照button的type',
        default: 'normal'
    }, {
        param: 'title',
        type: 'string|ReactNode',
        desc: 'confirm的标题',
        option: '',
        default: ''
    }, {
        param: 'buttonSize',
        type: 'string',
        desc: '按钮的统一size的设置，也可以去okProps, cancelProps里面进行设置',
        option: '',
        default: '参照button的size'
    }, {
        param: 'width',
        type: 'number | string',
        desc: '弹窗宽度， small为400， meidum为600，large为800',
        option: 'small | medium | large | 自定义宽度',
        default: 'small'
    }, {
        param: 'iconType',
        type: 'string',
        desc: '支持带信息的一些图标类型',
        option: 'success | waring | fail | info， 3.0.14-beta-53后支持',
        default: ''
    }, {
        param: 'icon',
        type: 'ReactNode',
        desc: '支持自定义icon，icon优先级高于iconType， 3.0.14-beta-53后支持',
        option: '',
        default: ''
    }, {
        param: 'keyboard',
        type: 'bool',
        desc: '是否支持键盘 esc 关闭',
        option: '',
        default: 'true'
    }],
    DialogFooter: []
};
