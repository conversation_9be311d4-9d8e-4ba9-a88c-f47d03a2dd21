import BaseComponent from '../base';

export default {
    dialog: {
        value: BaseComponent,
        label: 'Dialog 弹出式面板',
        demos: [
            {
                title: '尺寸',
                desc: '{size}可选值: {small} {medium}',
                source: 'size'
            },
            {
                title: '宽度',
                desc: '{width}可选值: {small} {medium} {large} {具体值}',
                source: 'width'
            },
            {
                title: '高度',
                desc: '默认高度随内容自动撑起。可以通过{height}设置具体高度，也可以在内区自行设置高度。',
                source: 'height'
            },
            {
                title: '操作区',
                desc: '{footer}可自定义。如果只是修改按钮文本或者属性，可以直接用{okText} {okProps}等属性',
                source: 'button'
            },
            {
                title: '操作按钮对齐',
                desc: '{buttonPosition}可选对齐方式: {left} {center} {right}',
                source: 'buttonPosition'
            },
            {
                title: '关闭',
                desc: '{needCloseIcon}隐藏右上角关闭按钮；{maskClosable}点击遮罩可关闭',
                source: 'close'
            },
            {
                title: 'alert',
                desc: '满足提示类场景{Dialog.alert}。通常直接界面操作关闭，也可通过该方法返回对象的{destroy}方法进行关闭',
                source: 'alert'
            },
            {
                title: 'confirm',
                desc: '满足确认类场景{Dialog.confirm}。通常直接界面操作关闭，也可通过该方法返回对象的{destroy}方法进行关闭',
                source: 'confirm'
            },
            {
                title: '其他场景',
                desc: '一些业务场景需求：如协议，比较多的内容提示。',
                source: 'scenes'
            }
        ],
        apis: [
            {
                apiKey: 'Dialog',
                title: 'Dialog'
            },
            {
                apiKey: 'DialogAlert',
                title: 'Dialog.confirm(.alert)',
                desc: 'API方式关闭：const dialog = Dialog.confirm({title: \'test\'}); dialog.destroy();'
            },
            {
                apiKey: 'DialogFooter',
                title: 'footer',
                desc: 'dialog中与footer按钮相关的参数（loading之类的），只对默认footer生效，如果你传入了footer请在传入的footer上处理'
            }
        ]
    }
};
