import React, {useState} from 'react';
import {Dialog, Button} from '@baidu/one-ui';

export default () => {
    const [visible, setVisible] = useState(false);

    return (
        <>
            <div className="demo-controls">
                <Button
                    size="small"
                    onClick={
                        () => {
                            setVisible(true);
                        }
                    }
                >
                    不展示关闭Icon & 点击遮罩可关闭
                </Button>
            </div>
            <Dialog
                title="Basic Dialog"
                visible={visible}
                onOk={() => setVisible(false)}
                onCancel={() => setVisible(false)}
                destroyOnClose
                okText="主按钮"
                cancelText="辅助按钮"
                maskClosable
                needCloseIcon={false}
            >
                <div style={{background: '#eee', height: 200}}>
                    自定义区域
                </div>
            </Dialog>
        </>
    );
};
