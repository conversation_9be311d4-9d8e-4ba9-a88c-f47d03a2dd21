import React, {useState} from 'react';
import {Dialog, Button} from '@baidu/one-ui';

export default () => {
    const [visible, setVisible] = useState(false);
    const [innerVisible, setInnerVisible] = useState(false);

    return (
        <>
            <Button onClick={() => setVisible(true)}>
                Open Dialog
            </Button>
            <Dialog
                title="Basic Dialog"
                visible={visible}
                onOk={() => setVisible(false)}
                onCancel={() => setVisible(false)}
                destroyOnClose
                width="large"
                fullScreen
            >
                <div style={{background: '#eee', height: 200}}>
                    <Button onClick={() => setInnerVisible(true)}>
                        Open Dialog
                    </Button>
                    <Dialog
                        title="Basic Dialog inner"
                        visible={innerVisible}
                        onOk={() => setInnerVisible(false)}
                        onCancel={() => setInnerVisible(false)}
                        destroyOnClose
                        width="large"
                    >
                        <div style={{background: '#eee', height: 1200}}>
                            内部弹窗
                        </div>
                    </Dialog>
                </div>
            </Dialog>
        </>
    );
};
