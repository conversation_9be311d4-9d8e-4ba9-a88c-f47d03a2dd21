import React from 'react';
import {Dialog, Button} from '@baidu/one-ui';
import {IconCalendar} from 'dls-icons-react';

const Btn = ({type, label, ...props}) => (
    <Button
        size="small"
        onClick={() => {
            Dialog[type]({
                title: 'Do you Want to delete these items?',
                content: `Lorem ipsum dolor sit amet,
                consectetur adipisicing elit,sed do eiusmod tempor incididunt ut labore et doloremagna aliqua.
                Ut enim ad minim veniam,
                quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
                Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
                Excepteur sint occaecat cupidatat non proident,
                sunt in culpa qui officia deserunt mollit anim id est laborum.`,
                onOk() {
                    console.log('OK');
                },
                onCancel() {
                    console.log('Cancel');
                },
                ...props
            });
        }}
    >
        {label || type}
    </Button>
);

export default () => {
    return (
        <>
            <div className="demo-controls">
                <Btn
                    type="confirm"
                    label="协议类样式"
                    title={<center>协议对话框</center>}
                    buttonPosition="center"
                    okText="同意"
                    cancelText="不同意"
                />
                <Btn
                    type="confirm"
                    label="帮助类样式"
                    needCloseIcon
                    mask={false}
                    footer={[]}
                />
            </div>
        </>
    );
};
