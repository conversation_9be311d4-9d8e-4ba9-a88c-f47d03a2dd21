import React, {useState} from 'react';
import {Dialog, Button, Radio} from '@baidu/one-ui';

export default () => {
    const [visible, setVisible] = useState(false);
    const [width, setWidth] = useState('medium');

    return (
        <>
            <div className="demo-controls">
                {['small', 'medium', 'large', '666px'].map(width => (
                    <Button
                        key={width}
                        size="small"
                        onClick={
                            () => {
                                setWidth(width);
                                setVisible(true);
                            }
                        }
                    >
                        {width}
                    </Button>
                ))}
            </div>
            <Dialog
                title="Basic Dialog"
                visible={visible}
                onOk={() => setVisible(false)}
                onCancel={() => setVisible(false)}
                destroyOnClose
                okText="主按钮"
                cancelText="辅助按钮"
                maskClosable
                width={width}
            >
                <div style={{background: '#eee', height: 200}}>
                    自定义区域
                </div>
            </Dialog>
        </>
    );
};
