import React, {useEffect, useState} from 'react';
import {Dialog, Button} from '@baidu/one-ui';

export default () => {
    const [visible, setVisible] = useState(false);
    const [contents, setContents] = useState([]);
    const [height, setHeight] = useState('auto');

    useEffect(() => {
        if (contents.length > 1000) {
            return;
        }
        const handler = setTimeout(() => {
            setContents(contents.concat(<p key={contents.length}>消息消息</p>));
        }, 1000);
        return () => {
            clearTimeout(handler);
        };
    }, [contents]);

    return (
        <>
            <div className="demo-controls">
                {['auto', '300px'].map(height => (
                    <Button
                        key={height}
                        size="small"
                        onClick={
                            () => {
                                setHeight(height);
                                setContents([]);
                                setVisible(true);
                            }
                        }
                    >
                        {height}
                    </Button>
                ))}
            </div>
            <Dialog
                title="Basic Dialog"
                visible={visible}
                onOk={() => setVisible(false)}
                onCancel={() => setVisible(false)}
                destroyOnClose
                okText="主按钮"
                cancelText="辅助按钮"
                height={height}
            >
                {contents}
            </Dialog>
        </>
    );
};
