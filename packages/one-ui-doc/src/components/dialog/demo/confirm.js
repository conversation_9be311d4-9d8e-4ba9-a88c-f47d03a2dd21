import React from 'react';
import {Dialog, Button} from '@baidu/one-ui';
import {IconCalendar} from 'dls-icons-react';

const Btn = ({label, async, ...props}) => (
    <Button
        size="small"
        onClick={() => {
            Dialog.confirm({
                title: 'Do you Want to delete these items?',
                content: `Lorem ipsum dolor sit amet, consectetur adipisicing elit,
                sed do eiusmod tempor incididunt ut labore et doloremagna aliqua.
                Ut enim ad minim veniam,
                quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
                Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
                Excepteur sint occaecat cupidatat non proident,
                sunt in culpa qui officia deserunt mollit anim id est laborum.`,
                onOk() {
                    console.log('OK');
                    if (async) {
                        return new Promise((resolve, reject) => {
                            setTimeout(Math.random() > 0.5 ? resolve : reject, 1000);
                        }).catch(() => console.log('Oops errors!'));
                    }
                },
                onCancel() {
                    console.log('Cancel');
                },
                ...props
            });
        }}
    >
        {label}
    </Button>
);

export default () => {
    return (
        <>
            <div className="demo-controls">
                <Btn label="confirm" />
                <Btn label="异步(点确定按钮)" async />
                <span>icon:</span>
                {['success', 'warning', 'info', 'fail', 'custom'].map(iconType => (
                    <Btn
                        key={iconType}
                        label={iconType}
                        iconType={iconType}
                        icon={iconType === 'custom' ? <IconCalendar /> : null}
                    />
                ))}
            </div>
        </>
    );
};
