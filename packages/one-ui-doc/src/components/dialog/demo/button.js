import React, {useState} from 'react';
import {Dialog, Button} from '@baidu/one-ui';

export default () => {
    const [visible, setVisible] = useState(false);
    const [footer, setFooter] = useState(null);

    return (
        <>
            <div className="demo-controls">
                <Button
                    size="small"
                    onClick={
                        () => {
                            setFooter(null);
                            setVisible(true);
                        }
                    }
                >
                    无操作
                </Button>
                <Button
                    size="small"
                    onClick={
                        () => {
                            setFooter([
                                <Button key="ok" onClick={() => setVisible(false)} type="primary">同意</Button>
                            ]);
                            setVisible(true);
                        }
                    }
                >
                    自定义
                </Button>
            </div>
            <Dialog
                title="Basic Dialog"
                visible={visible}
                onOk={() => setVisible(false)}
                onCancel={() => setVisible(false)}
                destroyOnClose
                okText="主按钮"
                cancelText="辅助按钮"
                maskClosable
                footer={footer}
            >
                <div style={{background: '#eee', height: 200}}>
                    自定义区域
                </div>
            </Dialog>
        </>
    );
};
