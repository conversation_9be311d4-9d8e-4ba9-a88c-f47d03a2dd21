import React, {useState} from 'react';
import {Dialog, Button, Radio} from '@baidu/one-ui';

export default () => {
    const [visible, setVisible] = useState(false);
    const [size, setSize] = useState('medium');

    return (
        <>
            <div className="demo-controls">
                {['small', 'medium'].map(size => (
                    <Button
                        key={size}
                        size="small"
                        onClick={
                            () => {
                                setSize(size);
                                setVisible(true);
                            }
                        }
                    >
                        {size}
                    </Button>
                ))}
            </div>
            <Dialog
                title="Basic Dialog"
                visible={visible}
                onOk={() => setVisible(false)}
                onCancel={() => setVisible(false)}
                destroyOnClose
                okText="主按钮"
                cancelText="辅助按钮"
                maskClosable
                size={size}
            >
                <div style={{background: '#eee', height: 200}}>
                    自定义区域
                </div>
            </Dialog>
        </>
    );
};
