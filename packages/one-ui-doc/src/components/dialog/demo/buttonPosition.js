import React, {useState} from 'react';
import {Dialog, Button} from '@baidu/one-ui';

export default () => {
    const [visible, setVisible] = useState(false);
    const [position, setPosition] = useState('left');

    return (
        <>
            <div className="demo-controls">
                {['left', 'center', 'right'].map(position => (
                    <Button
                        key={position}
                        size="small"
                        onClick={
                            () => {
                                setPosition(position);
                                setVisible(true);
                            }
                        }
                    >
                        {position}
                    </Button>
                ))}
            </div>
            <Dialog
                title="Basic Dialog"
                visible={visible}
                onOk={() => setVisible(false)}
                onCancel={() => setVisible(false)}
                destroyOnClose
                okText="主按钮"
                cancelText="辅助按钮"
                maskClosable
                buttonPosition={position}
            >
                <div style={{background: '#eee', height: 200}}>
                    自定义区域
                </div>
            </Dialog>
        </>
    );
};
