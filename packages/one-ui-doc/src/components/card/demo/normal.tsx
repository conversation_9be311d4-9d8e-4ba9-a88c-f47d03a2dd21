import React from 'react';
import {
    Card,
    But<PERSON>,
    Stack
} from '@baidu/one-ui';
import {IconBulbSolid} from 'dls-icons-react';

export default () => (
    <Stack gap="large" wrap>
        <Card
            header={(
                <Stack gap="xxsmall">
                    <IconBulbSolid
                        style={{
                            fontSize: 20,
                            color: '#0052cc',
                            '-webkit-mask': 'linear-gradient(to bottom, transparent, #000)'
                        }}
                    />
                    <span>标题</span>
                </Stack>
            )}
            style={{width: 400}}
            headerExtra={<Button type="text-strong">More</Button>}
            footer="底部自定义"
        >
            Lorem ipsum, dolor sit amet consectetur adipisicing elit.
            Magni asperiores minus vitae voluptatibus enim perspiciatis exercitationem placeat cumque molestias,
            labore cum, sunt, id veritatis excepturi ipsum facere quis blanditiis neque?
        </Card>
        <Card
            header="标题"
            headerLine={false}
            style={{width: 400, backgroundColor: '#f6f7fa'}}
            headerExtra={<Button type="text-strong">More</Button>}
            bordered={false}
            footer="底部自定义"
            footerLine={false}
        >
            Lorem ipsum, dolor sit amet consectetur adipisicing elit.
            Magni asperiores minus vitae voluptatibus enim perspiciatis exercitationem placeat cumque molestias,
            labore cum, sunt, id veritatis excepturi ipsum facere quis blanditiis neque?
        </Card>
    </Stack>
);
