import React, { useState } from 'react';
import {
    Card,
    Stack,
    Button,
    Radio
} from '@baidu/one-ui';

const shadowOptions = [
    { label: '无', value: null },
    { label: 'hover', value: 'hover' },
    { label: 'always', value: 'always' }
];

export default () => {
    const [shadow, setShadow] = useState('always');
    return (
        <Stack direction="column" gap="large">
            <Radio.Group
                value={shadow}
                onChange={e => setShadow(e.target.value)}
                shadow="small"
                type="strong"
                options={shadowOptions}
            />
            <Card
                shadow={shadow}
                header="阴影"
                style={{width: 400}}
                headerExtra={<Button type="text-strong" shadow={shadow}>More</Button>}
                footer="底部自定义"
            >
                Lorem ipsum, dolor sit amet consectetur adipisicing elit.
                Magni asperiores minus vitae voluptatibus enim perspiciatis exercitationem placeat cumque molestias,
                labore cum, sunt, id veritatis excepturi ipsum facere quis blanditiis neque?
            </Card>
        </Stack>
    );
}
