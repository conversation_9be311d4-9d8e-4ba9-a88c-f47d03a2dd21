import React from 'react';
import {
    <PERSON>,
    <PERSON>ack,
    Button,
    Breadcrumb,
    <PERSON>ge,
    Tabs,
    SearchBox,
    Table,
    Pagination
} from '@baidu/one-ui';

export default () => (
    <Card style={{backgroundColor: '#f6f7fa'}} size="xlarge" bordered={false}>
        <Stack direction="column" gap="large">
            <Card bordered={false} size="xlarge">
                <Stack gap="small" direction="column">
                    <Breadcrumb>
                        <Breadcrumb.Item>账户 - searchlab</Breadcrumb.Item>
                        <Breadcrumb.Item>推广业务 - 测试</Breadcrumb.Item>
                        <Breadcrumb.Item>计划 - 2023</Breadcrumb.Item>
                    </Breadcrumb>
                    <Stack gap="large">
                        <Stack gap="small"><b>状态</b><Badge type="success" text="有效" /></Stack>
                        <Stack gap="small"><b>预算</b><span>不限定预算</span></Stack>
                        <Stack gap="small"><b>地域</b><span>账户地域</span></Stack>
                        <Stack gap="small"><b>时段</b><span>全部时段</span></Stack>
                        <Stack gap="small"><b>否定关键词</b><span>短语：1</span></Stack>
                    </Stack>
                </Stack>
            </Card>
            <Card bordered={false} size="xlarge">
                <Stack gap="medium" direction="column">
                    <Tabs hideSpace style={{'--dls-tab-menu-padding': '0px'}}>
                        <Tabs.TabPane tab="创意管理" key="创意管理" />
                        <Tabs.TabPane tab="创意图片管理" key="创意图片管理" />
                        <Tabs.TabPane tab="创意视频管理" key="创意视频管理" />
                        <Tabs.TabPane tab="动态创意屏蔽" key="动态创意屏蔽" />
                    </Tabs>
                    <Stack gap="small">
                        <Button type="primary">新建</Button>
                        <SearchBox placeholder="请搜索单元" style={{width: 160}} />
                        <Button>筛选</Button>
                    </Stack>
                    <Table size="small" columns={['单元名称', '计划ID', '推广计划', '状态'].map(header => ({header, dataIndex: header}))} />
                    <Pagination />
                </Stack>
            </Card>
        </Stack>
    </Card>
);
