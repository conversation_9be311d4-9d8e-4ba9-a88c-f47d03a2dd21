import React, { useState } from 'react';
import {
    <PERSON>,
    Stack,
    Button,
    Switch
} from '@baidu/one-ui';

export default () => {
    const [bordered, setBordered] = useState(true);
    const [headerLine, setHeaderLine] = useState(true);
    const [footerLine, setFooterLine] = useState(true);
    return (
        <Stack direction="column" gap="large">
            <Stack gap="small">
                <Stack gap="xxsmall">
                    <Switch size="xsmall" checked={bordered} onChange={bordered => setBordered(bordered)} />
                    <span>边框</span>
                </Stack>
                <Stack gap="xxsmall">
                    <Switch size="xsmall" checked={headerLine} onChange={line => setHeaderLine(line)} />
                    <span>标题区分割线</span>
                </Stack>
                <Stack gap="xxsmall">
                    <Switch size="xsmall" checked={footerLine} onChange={line => setFooterLine(line)} />
                    <span>底部区分割线</span>
                </Stack>
            </Stack>
            <Card style={{backgroundColor: '#f6f7fa'}} size="xlarge" bordered={false}>
                <Stack align="center" justify="center">
                    <Card
                        header="标题"
                        style={{width: 400}}
                        headerExtra={<Button type="text-strong">More</Button>}
                        footer="底部自定义"
                        bordered={bordered}
                        headerLine={headerLine}
                        footerLine={footerLine}
                    >
                        Lorem ipsum, dolor sit amet consectetur adipisicing elit.
                        Magni asperiores minus vitae voluptatibus enim perspiciatis exercitationem placeat cumque molestias,
                        labore cum, sunt, id veritatis excepturi ipsum facere quis blanditiis neque?
                    </Card>
                </Stack>
            </Card>
        </Stack>
    );
};
