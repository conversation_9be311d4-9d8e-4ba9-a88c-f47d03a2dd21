import React, { useState } from 'react';
import {
    Card,
    Stack,
    Button,
    Radio,
    CardProps
} from '@baidu/one-ui';

const sizeOptions = [
    { label: 'S', value: 'small' },
    { label: 'M', value: 'medium' },
    { label: 'L', value: 'large' },
    { label: 'XL', value: 'xlarge' }
];

export default () => {
    const [size, setSize] = useState('medium');
    return (
        <Stack direction="column" gap="large">
            <Radio.Group
                value={size}
                onChange={e => setSize(e.target.value)}
                size="small"
                type="strong"
                options={sizeOptions}
            />
            <Card
                size={size}
                header={`尺寸(${size})`}
                style={{width: 400}}
                headerExtra={<Button type="text-strong" size={size}>More</Button>}
                footer="底部自定义"
            >
                Lorem ipsum, dolor sit amet consectetur adipisicing elit.
                Magni asperiores minus vitae voluptatibus enim perspiciatis exercitationem placeat cumque molestias,
                labore cum, sunt, id veritatis excepturi ipsum facere quis blanditiis neque?
            </Card>
        </Stack>
    );
}
