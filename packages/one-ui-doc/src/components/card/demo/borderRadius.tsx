import React, { useState } from 'react';
import { Stack, Radio, Card, Button, NumberInput } from '@baidu/one-ui';

const borderRadiusOptions = [
    { label: 'S', value: 'small' },
    { label: 'M', value: 'medium' },
    { label: 'L', value: 'large' },
    { label: 'XL', value: 'xlarge' },
    { label: '自定义', value: 'custom' }
];

export default () => {
    const [borderRadius, setBorderRadius] = useState('medium');
    const [borderRadiusPx, setBorderRadiusPx] = useState(5);
    return (
        <Stack direction="column" gap="large">
            <Stack gap="small">
                <Radio.Group
                    value={borderRadius}
                    onChange={e => setBorderRadius(e.target.value)}
                    size="small"
                    type="strong"
                    options={borderRadiusOptions}
                />
                {
                    borderRadius === 'custom'
                        ? (
                            <NumberInput
                                size="small"
                                min={0}
                                width={70}
                                value={borderRadiusPx}
                                suffix="px"
                                onChange={e => setBorderRadiusPx(+e.target.value)}
                            />
                        )
                        : null
                }
            </Stack>
            <Card
                borderRadius={borderRadius === 'custom' ? borderRadiusPx : borderRadius}
                header="标题"
                style={{width: 400}}
                headerExtra={<Button type="text-strong">More</Button>}
                footer="底部自定义"
            >
                Lorem ipsum, dolor sit amet consectetur adipisicing elit.
                Magni asperiores minus vitae voluptatibus enim perspiciatis exercitationem placeat cumque molestias,
                labore cum, sunt, id veritatis excepturi ipsum facere quis blanditiis neque?
            </Card>
        </Stack>
    );
};
