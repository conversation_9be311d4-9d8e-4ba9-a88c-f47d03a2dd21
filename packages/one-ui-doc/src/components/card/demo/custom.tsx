import React, { useState } from 'react';
import {
    Card,
    Stack,
    Button,
    Switch
} from '@baidu/one-ui';

export default () => {
    const [header, setHeader] = useState(true);
    const [headerExtra, setheaderExtra] = useState(true);
    const [footer, setFooter] = useState(true);
    return (
        <Stack direction="column" gap="large">
            <Stack gap="small">
                <Stack gap="xxsmall">
                    <Switch size="xsmall" checked={header} onChange={header => setHeader(header)} />
                    <span>标题区</span>
                </Stack>
                <Stack gap="xxsmall">
                    <Switch size="xsmall" checked={headerExtra} onChange={headerExtra => setheaderExtra(headerExtra)} />
                    <span>右上角区</span>
                </Stack>
                <Stack gap="xxsmall">
                    <Switch size="xsmall" checked={footer} onChange={footer => setFooter(footer)} />
                    <span>底部区</span>
                </Stack>
            </Stack>
            <Card
                style={{width: 400}}
                header={header ? '标题' : null}
                headerExtra={headerExtra ? <Button type="text-strong">More</Button> : null}
                footer={footer ? '底部自定义' : null}
            >
                Lorem ipsum, dolor sit amet consectetur adipisicing elit.
                Magni asperiores minus vitae voluptatibus enim perspiciatis exercitationem placeat cumque molestias,
                labore cum, sunt, id veritatis excepturi ipsum facere quis blanditiis neque?
            </Card>
        </Stack>
    );
}
