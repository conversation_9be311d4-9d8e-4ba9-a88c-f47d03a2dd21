import React, { useState } from 'react';
import {
    Card,
    Button,
    Stack,
    Empty
} from '@baidu/one-ui';

export default () => {
    const [count, setCount] = useState(2);

    return (
        <Card
            header="卡片列表"
            style={{width: 400}}
            headerExtra={<Button type="text-strong">More</Button>}
            footer={<Button type="text-strong" onClick={() => setCount(count => count + 1)}>+ 新增卡片</Button>}
        >
            <Stack gap="medium" direction="column" align="center">
                {count
                    ? Array.from({length: count}).fill(1).map((_, index) => (
                        <Card
                            header={`卡片${index}`}
                            headerExtra={<Button type="text-strong" onClick={() => setCount(count => count - 1)}>删除</Button>}
                        >
                            Lorem ipsum, dolor sit amet consectetur adipisicing elit.
                            Magni asperiores minus vitae voluptatibus enim perspiciatis exercitationem placeat cumque molestias,
                            labore cum, sunt, id veritatis excepturi ipsum facere quis blanditiis neque?
                        </Card>
                    ))
                    : <Empty size="small" description="没有卡片，请新增" />
                }
            </Stack>
        </Card>
    );
};
