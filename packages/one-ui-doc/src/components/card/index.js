import BaseComponent from '../base';

export default {
    card: {
        value: BaseComponent,
        label: 'Card 卡片',
        demos: [
            {
                title: '基本',
                desc: '',
                source: 'normal'
            },
            {
                title: '简洁',
                desc: '',
                source: 'simple'
            },
            {
                title: '尺寸',
                desc: '',
                source: 'size'
            },
            {
                title: '嵌套',
                desc: '',
                source: 'nesting'
            },
            {
                title: '圆角',
                desc: '不同尺寸的圆角有默认值，也可通过{borderRadius}单独控制',
                source: 'borderRadius'
            },
            {
                title: '阴影',
                desc: '通过 {shadow} 控制阴影展示，常显 {always} 或者 {hover} 展示',
                source: 'shadow'
            },
            {
                title: '边框与分割线',
                desc: '',
                source: 'bordered'
            },
            {
                title: '自定义区',
                desc: '',
                source: 'custom'
            },
            {
                title: '布局样例',
                desc: '',
                source: 'layout'
            }
        ],
        apis: [
            {
                apiKey: 'Card',
                title: 'Card'
            }
        ]
    }
};
