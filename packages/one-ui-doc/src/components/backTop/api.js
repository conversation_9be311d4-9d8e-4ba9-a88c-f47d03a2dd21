export default {
    BackTop: [
        {
            param: 'className',
            type: 'string',
            desc: '可以自定义backTop的className',
            option: '',
            default: ''
        },
        {
            param: 'target',
            type: '(e)=>void',
            desc: '设置需要监听其滚动事件的元素, 值为一个返回对应 DOM 元素的函数',
            option: '',
            default: '() => window'
        },
        {
            param: 'visible',
            type: 'boolean',
            desc: '是否显示BackTop图标, 使用此值则监听visibilityHeight滚动高度失效',
            option: '',
            default: 'false'
        },
        {
            param: 'visibilityHeight',
            type: 'number',
            desc: '滚动高度达到此参数值才出现 BackTop',
            option: '',
            default: '400'
        },
        {
            param: 'onClick',
            type: '(e)=>void',
            desc: '点击按钮的回调函数',
            option: '',
            default: ''
        }
    ]
};
