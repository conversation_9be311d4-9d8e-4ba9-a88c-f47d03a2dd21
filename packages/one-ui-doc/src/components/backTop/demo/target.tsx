import React, {useLayoutEffect, useRef, useState} from 'react';
import {BackTop} from '@baidu/one-ui';

export default () => {
    const containerRef = useRef(null);
    const [container, setContainer] = useState(null);
    useLayoutEffect(() => {
        setContainer(containerRef.current);
    }, []);
    return (
        <div style={{position: 'relative'}}>
            <div ref={containerRef} style={{height: 200, overflow: 'auto'}}>
                <div style={{height: 500, background: 'linear-gradient(#ccc, #aaa)'}} />
                {container
                    && (
                        <BackTop
                            style={{position: 'absolute'}}
                            visibilityHeight={100}
                            target={() => container}
                        />
                    )
                }
            </div>
        </div>
    );
}
