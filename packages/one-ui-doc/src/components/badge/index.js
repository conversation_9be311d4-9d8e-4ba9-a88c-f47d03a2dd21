import BaseComponent from '../base';

export default {
    badge: {
        value: BaseComponent,
        label: 'Badge 飘角',
        demos: [
            {
                title: '普通飘角',
                desc: '简单的徽章展示，当 count 为 0 时，默认不显示，但是可以使用 showZero 修改为显示。',
                source: 'normal'
            },
            {
                title: '封顶数字',
                desc: '超过 overflowCount 的会显示为 ${overflowCount}+，默认的 overflowCount 为 99。',
                source: 'overNumber'
            },
            {
                title: '小圆点',
                desc: '不展示数字的小圆点',
                source: 'dot'
            },
            {
                title: '状态小圆点',
                desc: '表示状态的时候用于展示的小圆点',
                source: 'statusDot'
            },
            {
                title: '动态展示飘角',
                desc: '通过开关动态展示飘角',
                source: 'dynamic'
            },
            {
                title: '自定义内容',
                desc: '自定义飘角内容',
                source: 'custom'
            }
        ],
        apis: [
            {
                apiKey: 'Badge',
                title: 'Badge'
            }
        ]
    }
};
