export default {
    Badge: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义className',
            option: '',
            default: ''
        },
        {
            param: 'color',
            type: 'string',
            desc: '可自定义圆点的颜色',
            option: '',
            default: ''
        },
        {
            param: 'count',
            type: 'number',
            desc: '展示的数字',
            option: '',
            default: 'null'
        },
        {
            param: 'isDot',
            type: 'bool',
            desc: '不展示数字，只展示小圆点',
            option: '',
            default: 'false'
        },
        {
            param: 'offset',
            type: 'array [number, number]',
            desc: '设置状态点的位置偏移，格式为 [x, y]',
            option: '',
            default: '-'
        },
        {
            param: 'overflowCount',
            type: 'number',
            desc: '设置封顶的数字，比如overflowCount为99，当count > 99的时候，显示 99+',
            option: '',
            default: '99'
        },
        {
            param: 'showZero',
            type: 'bool',
            desc: '当count为0的时候，是否还要展示badge',
            option: '',
            default: 'false'
        },
        {
            param: 'text',
            type: 'string',
            desc: '设置了type情况下，小圆点旁边展示的文字',
            option: '',
            default: ''
        },
        {
            param: 'type',
            type: 'string',
            desc: '小圆点的状态值，enum {success, warning, error, process, default}',
            option: '',
            default: ''
        },
        {
            param: 'visible',
            type: 'bool',
            desc: '是否展示badge',
            option: '',
            default: 'true'
        },
        {
            param: 'textContent',
            type: 'string|ReactNode',
            desc: '自定义内容， 如果传入这个，count会失效',
            option: '',
            default: ''
        }
    ]
};
