import React, {PureComponent} from 'react';
import {Badge, Button} from '@baidu/one-ui';

const colors = [
    'pink',
    'red',
    'yellow',
    'orange',
    'cyan',
    'green',
    'blue',
    'purple'
];

export default class NormalBadge extends PureComponent {
    render() {
        return (
            <div>
                <h4 style={{marginBottom: 16}}>主色:</h4>
                <div>
                    {colors.map(color => (
                        <div key={color}>
                            <span style={{marginRight: '30px'}}>
                                <Badge isDot color={color} text={color} />
                            </span>
                            <span style={{marginBottom: '10px', display: 'inline-block'}}>
                                <Badge count={100} color={color} text={color}>
                                    <Button>默认按钮</Button>
                                </Badge>
                            </span>
                        </div>
                    ))}
                </div>
                <h4 style={{margin: '16px 0'}}>自定义:</h4>
                <div>
                    <Badge isDot color="#f50" text="#f50" />
                    <br />
                    <Badge isDot color="#2db7f5" text="#2db7f5" />
                    <br />
                    <Badge isDot color="#87d068" text="#87d068" />
                    <br />
                    <Badge isDot color="#108ee9" text="#108ee9" />
                </div>
            </div>
        );
    }
}
