import React, {PureComponent} from 'react';
import {Switch, Badge, Button} from '@baidu/one-ui';

export default class NormalBadge extends PureComponent {
    state = {
        visible: true
    };

    onChange = checked => {
        this.setState({
            visible: !!checked
        });
    }

    render() {
        const visible = this.state.visible;
        return (
            <div>
                <div>
                    <Switch onChange={this.onChange} checked={visible}/>
                </div>
                <div style={{display: 'flex', alignItems: 'center', marginTop: 24}}>
                    <div style={{marginRight: '40px'}}>
                        <Badge count={40} visible={visible}>
                            <Button>默认按钮</Button>
                        </Badge>
                    </div>
                </div>
            </div>
        );
    }
}
