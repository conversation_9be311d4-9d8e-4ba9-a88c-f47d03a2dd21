import React, {useState} from 'react';
import Highlight from 'react-highlight';
import Title from '../../common/title';
import {<PERSON><PERSON>, Drawer, But<PERSON>, Alert} from '@baidu/one-ui';
import Demo from '../../common/demo';
import {menu} from '../../config';

export default props => {
    const id = props.id;
    const {label: title} = menu[id] || {};
    const [dialogVisible, setDialogVisible] = useState(false);
    const [drawerVisible, setDrawerVisible] = useState(false);
    const demo = (
        <Demo
            title={title}
            id="theme"
            source="normal"
            inline
        />
    );

    return (
        <div>
            <Title title={title} />
            <div className="doc">
                <div>
                    <Alert
                        showIcon
                        title="Toast主题切换说明"
                        content={
                            <>
                                因Toast使用方式为静态函数式调用，`ConfigProvider`设置的主题仅对context内、实例化的ONE UI组件生效，<b>对Toast无效</b>。
                            </>
                        }
                    />
                    <br />
                    Toast切换主题应使用:
                    <Highlight>
                        {
                            '全局生效: Toast.config({theme})'
                        }
                    </Highlight>
                    或：
                    <Highlight>
                        {
                            '当前一个Toast生效: Toast.info({content, theme})'
                        }
                    </Highlight>
                    <Alert
                        showIcon
                        title="主题与版本说明"
                        content={
                            <>
                                light-d20: 组件库默认主题，无需特别指定
                                <br />
                                light-d22: 无边框主题，从 4.19.0 版本支持
                                <br />
                                light-ai: AI风格主题(默认开启无边框并对齐AI场景视觉规范)，从 4.34.0 版本支持
                            </>
                        }
                    />
                    <br />
                    可以通过`ConfigProvider`让预期范围内组件生效
                    <Highlight>
                        {
                            '<ConfigProvider theme="light-d22"><Input /></ConfigProvider>'
                        }
                    </Highlight>
                    <Alert
                        type="warning"
                        content={
                            <>
                                使用light-ai主题，需要引入`@baidu/one-ui/lib/index.ai.css`，并通过`ConfigProvider`指定`theme`
                                <br />
                                light-ai通过重新定义classPrefix方式实现，可与light-d20共存
                            </>
                        }
                    />
                    <Highlight>
                        {
                            '<ConfigProvider theme="light-ai"><Input /></ConfigProvider>'
                        }
                    </Highlight>
                    支持的组件：
                    <Highlight className="markdown">
                        Button Input NumberInput SearchBox Dropdown Select Cascader
                        Radio.Button Checkbox.Button DatePicker
                        TimePicker TextArea  Pagination Uploader
                    </Highlight>
                    支持的容器组件：
                    <Highlight className="markdown">
                        Form Dialog Drawer Overlay Popover
                    </Highlight>
                    <Alert
                        content="非以上列表的不生效，如：Transfer内的SearchBox；其他不生效场景，可以通过`ConfigProvider`再次指定"
                        type="warning"
                    />
                    <br />
                    展示方式：
                    <Button size="small" type="primary" onClick={() => setDialogVisible(true)}>对话框</Button>
                    {' '}
                    <Button size="small" type="primary" onClick={() => setDrawerVisible(true)}>抽屉</Button>
                </div>
                {demo}
                <Dialog
                    visible={dialogVisible}
                    onCancel={() => setDialogVisible(false)}
                    title="对话框中展示"
                    width={800}
                    footer={[]}
                >
                    {demo}
                </Dialog>
                <Drawer
                    visible={drawerVisible}
                    onClose={() => setDrawerVisible(false)}
                    title="抽屉中展示"
                    width={800}
                >
                    {demo}
                </Drawer>
            </div>
        </div>
    );
};
