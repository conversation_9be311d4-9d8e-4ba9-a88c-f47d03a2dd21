import React, {useState} from 'react';
import {
    Form,
    Button,
    Toast,
    Transfer,
    Input,
    NumberInput,
    DatePicker,
    Select,
    Cascader,
    SearchBox,
    TimePicker,
    TextArea,
    Checkbox,
    Radio,
    Uploader,
    Dropdown,
    Pagination,
    ConfigProvider
} from '@baidu/one-ui';

const stores = [
    {
        key: '1',
        title: '门店1'
    },
    {
        key: '2',
        title: '门店2'
    },
    {
        key: '10',
        title: '门店3',
        children: [{
            key: '11',
            title: '门店31'
        },
        {
            key: '12',
            title: '门店32'
        }]
    }
];

const StoreItem = ({itemKey, title}) => (
    <div style={{display: 'flex'}} key={itemKey}>
        <div style={{width: 60}}>{title}</div>
        <Form.Field
            name={`store[${itemKey}]`}
            abstract
            rules={[
                {required: true, message: `请输入【${title}】地址`}
            ]}
        >
            <Input size="small" width={120} />
        </Form.Field>
    </div>
);

const Interest = React.forwardRef(({onChange, ...props}, ref) => (
    <SearchBox
        ref={ref}
        {...props}
        onChange={e => onChange(e.target.value)}
        handleMenuClick={e => onChange(e.key)}
        onClearClick={() => onChange('')}
        options={[{label: '修bug', value: '修bug'}, {label: '写bug', value: '写bug'}]}
    />
));

const themeOptions = [{label: 'default', value: ''}, {label: 'Light-D22', value: 'light-d22'}];

export default () => {

    const [loading, setLoading] = useState(false);
    const onSubmit = () => setLoading(true);
    const onFinish = values => (console.log(values), setLoading(false), Toast.success({content: '提交成功'}));
    const onFinishFailed = (errors, values) => (console.log(errors, values), setLoading(false));
    const [mode, onModeChange] = useState('D20');
    const [disabled, setDisabled] = useState(false);
    const theme = ({
        D20: 'light-d20',
        D22: 'light-d22',
        AI: 'light-ai'
    })[mode];

    return (
        <ConfigProvider theme={theme}>
            <Form
                onSubmit={onSubmit}
                onFinish={onFinish}
                onFinishFailed={onFinishFailed}
                scrollToFirstError
            >
                <Form.Field
                    label="设置"
                    hidden
                >
                    <Radio.Group
                        options={['D20', 'D22', 'AI']}
                        value={mode}
                        onChange={e => onModeChange(e.target.value)}
                        type="strong"
                        style={{background: '#fff', marginRight: 20}}
                    />
                    <Checkbox checked={disabled} onChange={() => setDisabled(!disabled)}>禁用态</Checkbox>
                </Form.Field>
                <Form.Field
                    label="用户名"
                    tip="提示：用户名即登录账号"
                    name="account"
                    rules={[
                        {required: true, message: '请输入用户名'},
                        {min: 5, message: '建议不少于5个字符', invalidType: 'warning'},
                        {max: 10, message: '最长10个字符'}
                    ]}
                >
                    <Input disabled={disabled} placeholder="Name" />
                </Form.Field>
                <Form.Field
                    label="年龄"
                    help="周岁"
                    helpPosition="side"
                    name="age"
                    rules={[
                        {required: true, message: '请输入年龄'},
                        {pattern: /^\d+$/, message: '请输入数字'}
                    ]}
                >
                    <NumberInput
                        disabled={disabled}
                        placeholder="Age"
                        min={18}
                        max={120}
                        showErrorMessage={false}
                    />
                </Form.Field>
                <Form.Field
                    label="出生日期"
                    name="birthday"
                    rules={[
                        {required: true, message: '请选择日期'}
                    ]}
                >
                    <DatePicker disabled={disabled} placeholder="Birthday" />
                </Form.Field>
                <Form.Field
                    label="时间"
                    name="time"
                    rules={[
                        {required: true, message: '请选择时间'}
                    ]}
                >
                    <TimePicker disabled={disabled} />
                </Form.Field>
                <Form.Field
                    label="简介"
                    name="intro"
                    rules={[
                        {required: true, message: '请输入简介'}
                    ]}
                >
                    <TextArea disabled={disabled} placeholder="Intro" />
                </Form.Field>
                <Form.Field
                    label="职业"
                    name="job"
                    rules={[
                        {required: true, message: '请选择职业'}
                    ]}
                >
                    <Select disabled={disabled} placeholder="请选择职业">
                        <Select.Option value="FE">FE</Select.Option>
                        <Select.Option value="UE">UE</Select.Option>
                        <Select.Option value="RD">RD</Select.Option>
                        <Select.Option value="PM">PM</Select.Option>
                    </Select>
                </Form.Field>
                <Form.Field
                    label="职业(多选)"
                    name="jobs"
                    rules={[
                        {required: true, message: '请选择职业'}
                    ]}
                >
                    <Select disabled={disabled} placeholder="请选择职业" mode="tags">
                        <Select.Option value="FE">FE</Select.Option>
                        <Select.Option value="UE">UE</Select.Option>
                        <Select.Option value="RD">RD</Select.Option>
                        <Select.Option value="PM">PM</Select.Option>
                    </Select>
                </Form.Field>
                <Form.Field
                    label="兴趣爱好"
                    name="interest"
                    rules={[
                        {required: true, message: '请选择兴趣爱好'}
                    ]}
                >
                    <Interest disabled={disabled} placeholder="Interest" />
                </Form.Field>
                <Form.Field
                    label="地域"
                    name="district"
                    rules={[
                        {required: true, message: '请选择地域'}
                    ]}
                >
                    <Cascader
                        placeholder="请选择地域"
                        disabled={disabled}
                        options={
                            [
                                {
                                    label: '北京',
                                    value: 'bj',
                                    children: [{label: '海淀', value: 'hd'}, {label: '朝阳', value: 'cy'}]
                                },
                                {
                                    label: '上海',
                                    value: 'sh',
                                    children: [{label: '浦东', value: 'pd'}, {label: '黄浦', value: 'hp'}]
                                }
                            ]
                        }
                    />
                </Form.Field>
                <Form.Field
                    label="地域(多选)"
                    name="districts"
                    rules={[
                        {required: true, message: '请选择地域'}
                    ]}
                >
                    <Cascader
                        placeholder="请选择地域"
                        multiple
                        disabled={disabled}
                        options={
                            [
                                {
                                    label: '北京',
                                    value: 'bj',
                                    children: [{label: '海淀', value: 'hd'}, {label: '朝阳', value: 'cy'}]
                                },
                                {
                                    label: '上海',
                                    value: 'sh',
                                    children: [{label: '浦东', value: 'pd'}, {label: '黄浦', value: 'hp'}]
                                }
                            ]
                        }
                    />
                </Form.Field>
                <Form.Field
                    label="性别"
                    name="gender"
                    rules={[
                        {required: true, message: '请选择性别'}
                    ]}
                >
                    <Radio.Group>
                        <Radio.Button disabled={disabled} value="male">男</Radio.Button>
                        <Radio.Button disabled={disabled} value="female">女</Radio.Button>
                    </Radio.Group>
                </Form.Field>
                <Form.Field
                    label="颜色"
                    name="color"
                    rules={[
                        {required: true, message: '请选择颜色'}
                    ]}
                >
                    <Checkbox.Group
                        disabled={disabled}
                        type="strong"
                        options={['红', '橙', '黄', '绿', '青', '蓝', '紫']}
                    />
                </Form.Field>
                <Form.Field
                    label="详细地址"
                    help="分别填写街道与门牌号详细地址"
                    helpPosition="top"
                >
                    <Input.Group>
                        <Form.Field
                            name="street"
                            abstract
                            rules={[{required: true, message: '请输入街道'}]}
                        >
                            <Input placeholder="Street" width={150} disabled={disabled} />
                        </Form.Field>
                        <Form.Field
                            name="no"
                            abstract
                            rules={[{required: true, message: '请输入门牌号'}]}
                        >
                            <Input placeholder="No." width={150} disabled={disabled} />
                        </Form.Field>
                        <Button disabled={disabled}>按钮</Button>
                        <Button disabled={disabled}>按钮</Button>
                    </Input.Group>
                </Form.Field>
                <Form.Field
                    label="异步校验长文本换行长文本换行"
                    help="通常用于请求服务端进行校验"
                    name="pwd"
                    rules={[
                        {required: true, message: '请输入'},
                        {pattern: /^123/, message: '请输入123'},
                        {
                            asyncValidator: (rule, value) => {
                                return new Promise((resolve, reject) => {
                                    if (value.trim() === '1234') {
                                        setTimeout(() => resolve(), 3000);
                                    }
                                    if (value.trim() === '123') {
                                        setTimeout(() => reject(['服务端校验错误，请输入1234']), 3000);
                                    }
                                });
                            },
                            successMessage: '服务端校验成功'
                        }
                    ]}
                >
                    <Input placeholder="Async" disabled={disabled} />
                </Form.Field>
                <Form.Field
                    label="照片"
                >
                    <Uploader listType="image" size="small" disabled={disabled} />
                </Form.Field>
                <Form.Field
                    label="门店"
                    name="stores"
                    rules={[
                        {required: true, type: 'array', message: '请选择门店'}
                    ]}
                >
                    <Transfer SelectedItem={StoreItem} dataSource={stores} />
                </Form.Field>
                <Form.Field actions label="">
                    <Button
                        type="primary"
                        htmlType="submit"
                        loading={loading}
                    >
                        提交
                    </Button>
                    <Button
                        htmlType="reset"
                        disabled={loading}
                        style={{marginLeft: 8}}
                    >
                        重置
                    </Button>
                </Form.Field>
            </Form>
            <Pagination
                total={100}
                pageNo={1}
            />
        </ConfigProvider>
    );
};
