export default {
    Overlay: [
        {
            param: 'trigger',
            type: 'string',
            desc: '下拉弹层的触发方式',
            option: 'click | hover',
            default: 'hover'
        },
        {
            param: 'disabled',
            type: 'bool',
            desc: '是否禁用',
            option: '',
            default: ''
        },
        {
            param: 'visible',
            type: 'bool',
            desc: '弹层是否可视，传入的话可视为受控控制',
            option: '',
            default: ''
        },
        {
            param: 'children',
            type: 'ReactNode',
            desc: 'target，如果传入children为自定义target',
            option: '',
            default: ''
        },
        {
            param: 'header',
            type: 'ReactNode',
            desc: 'target，传入的话为带按钮形式的target',
            option: '',
            default: ''
        },
        {
            param: 'onVisibleChange',
            type: 'func(visible)',
            desc: 'visible改变的时候触发的函数',
            option: '',
            default: ''
        },
        {
            param: 'dropdownMatchSelectWidth',
            type: 'bool',
            desc: '下拉弹层和target同宽',
            option: '',
            default: 'true'
        },
        {
            param: 'getPopupContainer',
            type: 'Function(triggerNode)',
            desc: '弹层渲染父节点。默认渲染到 body 上',
            option: '',
            default: '() => document.body'
        },
        {
            param: 'overlay',
            type: 'reactNode',
            desc: '下拉弹层的Dom',
            option: '',
            default: ''
        }
    ]
};
