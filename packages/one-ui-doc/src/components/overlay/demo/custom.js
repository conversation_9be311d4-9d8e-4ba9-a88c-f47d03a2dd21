import React, {PureComponent} from 'react';
import {Overlay, Button} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {
        list: [{
            label: '操作命令111111111',
            value: 11111111111111111
        }]
    }

    onVisibleChange = visible => {
        const arr = [];
        for (let i = 1; i <= Math.ceil(Math.random() * 20); i++) {
            arr.push({
                label: `操作的点点滴滴多多多的点点滴滴多多多多多多多多多多多多多多多命令${i}`,
                value: i
            });
        }
        if (!visible) {
            this.setState({
                list: arr
            });
        }
    }

    render() {
        return (
            <Overlay
                onVisibleChange={this.onVisibleChange}
                dropdownMatchSelectWidth={false}
                overlay={(
                    <div style={{
                        padding: '20px'
                    }}
                    >
                        {
                            this.state.list.map(item => {
                                return (
                                    <div key={item.value}>{item.label}</div>
                                );
                            })
                        }
                    </div>
                )}
            >
                <Button>这是一个案例</Button>
            </Overlay>
        );
    }
}
