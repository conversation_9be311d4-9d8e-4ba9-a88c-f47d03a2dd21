import React, {PureComponent} from 'react';
import {Overlay, Button} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {
        list: [{
            label: '操作命令111111111',
            value: 11111111111111111
        }],
        visible: false
    }

    onVisibleChange = visible => {
        const arr = [];
        for (let i = 1; i <= Math.ceil(Math.random() * 20); i++) {
            arr.push({
                label: `操作的点点滴滴多多多的点点滴滴多多多多多多多多多多多多多多多命令${i}`,
                value: i
            });
        }
        if (!visible) {
            this.setState({
                list: arr
            });
        }
        this.setState({
            visible
        });
    }

    render() {
        return (
            <Overlay
                onVisibleChange={this.onVisibleChange}
                trigger="click"
                dropdownMatchSelectWidth
                visible={this.state.visible}
                overlay={(
                    <div style={{
                        padding: '20px'
                    }}
                    >
                        {
                            this.state.list.map(item => {
                                return (
                                    <div
                                        key={item.value}
                                    >
                                        <div style={{
                                            marginBottom: '10px'
                                        }}
                                        >
                                            {item.label}
                                        </div>
                                        <Button onClick={
                                            () => {
                                                this.setState({
                                                    visible: false
                                                });
                                            }
                                        }
                                        >
                                            关闭按钮
                                        </Button>
                                    </div>
                                );
                            })
                        }
                    </div>
                )}
                header="这是一个下拉弹层"
            />
        );
    }
}
