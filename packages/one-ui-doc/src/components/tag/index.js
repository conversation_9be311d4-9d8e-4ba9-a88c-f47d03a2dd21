import BaseComponent from '../base';

export default {
    tag: {
        value: BaseComponent,
        label: 'Tag 标签',
        demos: [
            {
                title: '基础使用',
                desc: '普通的标签',
                source: 'normal'
            },
            {
                title: '小尺寸',
                desc: '小号的尺寸',
                source: 'medium'
            },
            {
                title: '超小尺寸',
                desc: '最紧凑的尺寸',
                source: 'xsmall'
            },
            {
                title: '状态',
                desc: '提示tag',
                source: 'tipTag'
            },
            {
                title: '禁用',
                desc: '禁用',
                source: 'disabled'
            },
            {
                title: '可选择',
                desc: '可点击标签',
                source: 'checkableTag'
            },
            {
                title: '单选组',
                desc: '单选组',
                source: 'singleGroup'
            },
            {
                title: '多选组',
                desc: '多选组',
                source: 'multipleGroup'
            },
            {
                title: '可编辑',
                desc: '可编辑tag',
                source: 'editable'
            },
            {
                title: 'normalized',
                desc: '主要移除默认margin，可使用ConfigProvider进行全局上下文配置',
                source: 'normalized'
            }
        ],
        apis: [
            {
                apiKey: 'Tag',
                title: 'Tag'
            },
            {
                apiKey: 'TagGroup',
                title: 'Tag.Group'
            },
            {
                apiKey: 'TagEditableGroup',
                title: 'Tag.EditableGroup'
            }
        ]
    }
};
