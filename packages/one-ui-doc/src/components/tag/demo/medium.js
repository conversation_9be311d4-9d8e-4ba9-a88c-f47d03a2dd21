import React, {PureComponent} from 'react';
import {Tag} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    log = () => {
        alert('关闭一个标签');
    }

    preventDefault = e => {
        e.preventDefault();
        alert('Clicked! But prevent default.');
    }

    render() {
        return (
            <div>
                <Tag size="small">Tag 1</Tag>
                <Tag size="small">
                    <a href="www.baidu.com">Link</a>
                </Tag>
                <Tag closable onClose={this.log} size="small">
                    Tag 2
                </Tag>
                <Tag closable onClose={this.preventDefault} size="small">
                    Prevent Default
                </Tag>
                <br />
                <br />
                <Tag size="small" bordered={false}>Tag 1</Tag>
                <Tag size="small" bordered={false}>
                    <a href="www.baidu.com">Link</a>
                </Tag>
                <Tag closable onClose={this.log} size="small" bordered={false}>
                    Tag 2
                </Tag>
                <Tag closable onClose={this.preventDefault} size="small" bordered={false}>
                    Prevent Default
                </Tag>
            </div>
        );
    }
}
