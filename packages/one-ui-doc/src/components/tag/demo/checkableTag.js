import React, {PureComponent} from 'react';
import {Tag} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {
        checked: false
    };

    onChange = checked => {
        this.setState({checked});
    }

    render() {
        const checked = this.state.checked;
        const checkedProps = {
            checked,
            checkable: true,
            onChange: this.onChange
        };
        return (
            <div>
                <h4 style={{marginBottom: '16px'}}>主色:</h4>
                <div>
                    <Tag tipTag="success" {...checkedProps}>success</Tag>
                    <Tag tipTag="error" {...checkedProps}>error</Tag>
                    <Tag tipTag="warning" {...checkedProps}>warning</Tag>
                    <Tag tipTag="info" {...checkedProps}>info</Tag>
                </div>
                <br />
                <br />
                <br />
                <Tag {...checkedProps}>
                    这是一个可点击标签
                </Tag>
            </div>
        );
    }
}
