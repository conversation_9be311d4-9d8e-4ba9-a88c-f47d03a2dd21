import React, {PureComponent} from 'react';
import {Tag} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    log = () => {
        alert('关闭一个标签');
    }

    preventDefault = e => {
        e.preventDefault();
        alert('Clicked! But prevent default.');
    }

    render() {
        return (
            <div>
                <Tag>Tag 1</Tag>
                <Tag>
                    <a href="www.baidu.com">Link</a>
                </Tag>
                <Tag closable onClose={this.log}>
                    Tag 2
                </Tag>
                <Tag closable onClose={this.preventDefault}>
                    Prevent Default
                </Tag>
                <Tag closable disabled>
                    Disabled
                </Tag>
                <br />
                <br />
                <Tag bordered={false}>Tag 1</Tag>
                <Tag bordered={false}>
                    <a href="www.baidu.com">Link</a>
                </Tag>
                <Tag closable onClose={this.log} bordered={false}>
                    Tag 2
                </Tag>
                <Tag closable onClose={this.preventDefault} bordered={false}>
                    Prevent Default
                </Tag>
            </div>
        );
    }
}
