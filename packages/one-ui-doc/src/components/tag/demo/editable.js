import React, {PureComponent} from 'react';
import {Tag} from '@baidu/one-ui';

const dataSource = [{
    label: '标签1',
    value: '111'
}, {
    label: '标签2',
    value: '222'
}, {
    label: '标签3',
    value: '333'
}];

export default class SingleGroup extends PureComponent {

    state = {
        dataSource
    };

    onClose = (dataSource, value) => {
        this.setState({
            dataSource: dataSource.filter(tag => tag.value !== value)
        });
    };

    onInputConfirm = (dataSource, InputValue) => {
        dataSource.push({
            value: InputValue,
            label: InputValue
        });
        console.log(dataSource);
        this.setState({
            dataSource
        });
    }

    render() {
        return (
            <div>
                <div>
                    <Tag.EditableGroup defaultDataSource={dataSource} />
                </div>
                <br />
                <br />
                <br />
                <div>
                    <Tag.EditableGroup dataSource={this.state.dataSource} onClose={this.onClose} onInputConfirm={this.onInputConfirm} />
                </div>
            </div>
        );
    }
}
