import React, {PureComponent} from 'react';
import {Tag} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    render() {
        const checkedProps = {
            checked: true,
            checkable: true
        };
        return (
            <div>
                <h4 style={{marginBottom: '16px'}}>主色:</h4>
                <div>
                    <Tag tipTag="success" disabled >success</Tag>
                    <Tag tipTag="error" disabled>error</Tag>
                    <Tag tipTag="warning" disabled >warning</Tag>
                    <Tag tipTag="info" disabled >info</Tag>
                </div>
                <br />
                <div>
                    <Tag tipTag="success" disabled bordered={false}>success</Tag>
                    <Tag tipTag="error" disabled bordered={false} >error</Tag>
                    <Tag tipTag="warning" disabled bordered={false} >warning</Tag>
                    <Tag tipTag="info" disabled bordered={false} >info</Tag>
                </div>
                <br />
                <br />
                <Tag disabled>
                    这是一个禁用标签
                </Tag>
                <br />
                <br />
                <div>
                    <Tag tipTag="success" disabled {...checkedProps}>success</Tag>
                    <Tag tipTag="error" disabled {...checkedProps}>error</Tag>
                    <Tag tipTag="warning" disabled {...checkedProps} >warning</Tag>
                    <Tag tipTag="info" disabled {...checkedProps} >info</Tag>
                </div>
                <br />
                <br />
                <br />
                <Tag disabled {...checkedProps}>
                    这是一个禁用标签
                </Tag>
            </div>
        );
    }
}
