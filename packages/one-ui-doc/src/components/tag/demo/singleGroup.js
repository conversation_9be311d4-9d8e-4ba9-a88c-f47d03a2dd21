import React, {PureComponent} from 'react';
import {Tag} from '@baidu/one-ui';

export default class SingleGroup extends PureComponent {
    state = {
        value: ['111']
    };

    onChange = selectedValue => {
        this.setState({
            value: selectedValue
        });
    }

    render() {
        const dataSource = [{
            label: '标签1',
            value: '111'
        }, {
            label: '标签2',
            value: '222'
        }, {
            label: '标签3',
            value: '333'
        }];
        return (
            <div>
                <div>
                    <Tag.Group dataSource={dataSource} defaultValue={['111']}/>
                </div>
                <br />
                <br />
                <br />
                <div>
                    <Tag.Group dataSource={dataSource} value={this.state.value} onChange={this.onChange}/>
                </div>
            </div>
        );
    }
}
