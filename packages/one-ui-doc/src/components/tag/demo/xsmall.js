import React, {PureComponent} from 'react';
import {IconAcademicCap, IconCar, IconWoman, IconMedicalKit, IconShoppingCart} from 'dls-icons-react';
import {Tag} from '@baidu/one-ui';

export default class Normal extends PureComponent {

    render() {
        const checkedProps = {
            fillStyle: 'outline',
            size: 'xsmall'
        };
        const mediumProps = {
            fillStyle: 'outline',
            size: 'medium'
        };
        const smallProps = {
            fillStyle: 'outline',
            size: 'small'
        };
        return (
            <div>
                <div>
                    <Tag {...checkedProps} icon={<IconAcademicCap style={{color: '#49a8c5'}} />}>课校通</Tag>
                    <Tag {...checkedProps} icon={<IconCar style={{color: '#4b84f5'}} />}>车校通</Tag>
                    <Tag {...checkedProps} icon={<IconWoman style={{color: '#de5877'}} />}>美品汇</Tag>
                    <Tag {...checkedProps} icon={<IconMedicalKit style={{color: '#76c5aa'}} />}>医品汇</Tag>
                    <Tag {...checkedProps} icon={<IconShoppingCart style={{color: '#f17c72'}} />}>爱采购</Tag>
                    <Tag {...checkedProps} style={{color: '#06c'}}>关键词方案</Tag>
                    <Tag {...checkedProps} style={{color: '#06c'}}>提示词方案</Tag>
                    <Tag {...checkedProps}>旧版推广计划</Tag>
                </div>
                <div>
                    <Tag {...smallProps} icon={<IconAcademicCap style={{color: '#49a8c5'}} />}>课校通</Tag>
                    <Tag {...smallProps} icon={<IconCar style={{color: '#4b84f5'}} />}>车校通</Tag>
                    <Tag {...smallProps} icon={<IconWoman style={{color: '#de5877'}} />}>美品汇</Tag>
                    <Tag {...smallProps} icon={<IconMedicalKit style={{color: '#76c5aa'}} />}>医品汇</Tag>
                    <Tag {...smallProps} icon={<IconShoppingCart style={{color: '#f17c72'}} />}>爱采购</Tag>
                    <Tag {...smallProps} style={{color: '#06c'}}>关键词方案</Tag>
                    <Tag {...smallProps} style={{color: '#06c'}}>提示词方案</Tag>
                    <Tag {...smallProps}>旧版推广计划</Tag>
                </div>
                <div>
                    <Tag {...mediumProps} icon={<IconAcademicCap style={{color: '#49a8c5'}} />}>课校通</Tag>
                    <Tag {...mediumProps} icon={<IconCar style={{color: '#4b84f5'}} />}>车校通</Tag>
                    <Tag {...mediumProps} icon={<IconWoman style={{color: '#de5877'}} />}>美品汇</Tag>
                    <Tag {...mediumProps} icon={<IconMedicalKit style={{color: '#76c5aa'}} />}>医品汇</Tag>
                    <Tag {...mediumProps} icon={<IconShoppingCart style={{color: '#f17c72'}} />}>爱采购</Tag>
                    <Tag {...mediumProps} style={{color: '#06c'}}>关键词方案</Tag>
                    <Tag {...mediumProps} style={{color: '#06c'}}>提示词方案</Tag>
                    <Tag {...mediumProps}>旧版推广计划</Tag>
                </div>

                <br />
            </div>
        );
    }
}
