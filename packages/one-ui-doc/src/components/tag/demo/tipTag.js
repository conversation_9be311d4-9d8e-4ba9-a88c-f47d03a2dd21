import React, {PureComponent} from 'react';
import {Tag} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {
        checked: false
    };

    onChange = checked => {
        this.setState({checked});
    }

    render() {
        const checked = this.state.checked;
        const checkedProps = {
            checked,
            checkable: true,
            onChange: this.onChange
        };
        return (
            <div>
                <h4 style={{marginBottom: '16px'}}>提示tag</h4>
                <div>
                    <Tag tipTag="success" {...checkedProps}>success</Tag>
                    <Tag tipTag="error" {...checkedProps}>error</Tag>
                    <Tag tipTag="warning" {...checkedProps}>warning</Tag>
                    <Tag tipTag="info" {...checkedProps}>info</Tag>
                </div>
                <div>
                    <Tag tipTag="success" {...checkedProps} bordered={false}>success</Tag>
                    <Tag tipTag="error" {...checkedProps} bordered={false}>error</Tag>
                    <Tag tipTag="warning" {...checkedProps} bordered={false}>warning</Tag>
                    <Tag tipTag="info" {...checkedProps} bordered={false}>info</Tag>
                </div>
                <br />
            </div>
        );
    }
}
