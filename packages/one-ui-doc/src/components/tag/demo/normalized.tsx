import React, {PureComponent} from 'react';
import {Tag, ConfigProvider} from '@baidu/one-ui';

export default () => {
    const dataSource = [{
            label: 'Apple',
            value: 1
        }, {
            label: 'Orange',
            value: 2
        }, {
            label: 'Pear',
            value: 3
        }];
    return (
        <>
            <Tag>原 Tag & Group</Tag>
            <Tag.Group dataSource={dataSource} />
            <br />
            <Tag normalized style={{marginRight: 8}}>Normalized Tag & Group</Tag>
            <Tag.Group normalized dataSource={dataSource} />
            <br />
            <ConfigProvider normalized>
                <Tag style={{marginRight: 8}}>全局上下文配置Normalized Tag & Group</Tag>
                <Tag.Group dataSource={dataSource} />
            </ConfigProvider>
        </>
    );
}
