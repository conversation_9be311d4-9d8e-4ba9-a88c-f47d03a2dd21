export default {
    Tag: [
        {
            param: 'className',
            type: 'String',
            desc: '自定义类名',
            option: '',
            default: ''
        },
        {
            param: 'closable',
            type: 'boolean',
            desc: '展示标签可否关闭的X',
            option: '',
            default: 'false'
        },
        {
            param: 'color',
            type: 'string',
            desc: '颜色，可以传入#XXX形式自定义or主色[pink, red, orange, yellow, cyan, green, blue, purple]',
            option: '',
            default: 'false'
        },
        {
            param: 'onClose',
            type: 'Function(e)',
            desc: '点击X关闭标签的回调',
            option: '',
            default: 'false'
        },
        {
            param: 'disabled',
            type: 'bool',
            desc: '禁用态',
            option: '',
            default: 'false'
        },
        {
            param: 'checkable',
            type: 'bool',
            desc: '标签是否可点击',
            option: '',
            default: 'false'
        },
        {
            param: 'checked',
            type: 'bool',
            desc: 'checkable为true时候，表示标签是否被选中状态',
            option: '',
            default: 'false'
        },
        {
            param: 'onChange',
            type: 'func(checked)',
            desc: 'checkable为true时候，点击标签的回调',
            option: '',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: '标签尺寸',
            option: 'medium | small',
            default: 'medium'
        }
    ],
    TagGroup: [{
        param: 'value',
        type: 'array | dataSource里面的value',
        desc: '选中的value值，传入即为受控',
        option: '',
        default: ''
    }, {
        param: 'defaultValue',
        type: 'array | dataSource里面的value',
        desc: '选中的defaultValue值，非受控使用',
        option: '',
        default: ''
    }, {
        param: 'mode',
        type: 'string',
        desc: '模式，区分单选还是多选',
        option: 'unique | multiple',
        default: 'unique'
    }, {
        param: 'dataSource',
        type: 'Object',
        desc: '数据源',
        option: 'dataSource {label, value, tagProps}',
        default: ''
    }, {
        param: 'onChange',
        type: 'func(selectedValue)| selectedValue 为选中的[]',
        desc: '选择的时候调用',
        option: '',
        default: ''
    }, {
        param: 'size',
        type: 'string',
        desc: '标签尺寸',
        option: 'medium | small',
        default: 'medium'
    }],
    TagEditable: [{
        param: 'dataSource',
        type: 'Object',
        desc: '数据源 | 传入即受控',
        option: 'dataSource {label, value, tagProps}',
        default: ''
    }, {
        param: 'defaultDataSource',
        type: 'Object',
        desc: '数据源 | 非受控使用',
        option: 'defaultDataSource {label, value, tagProps}',
        default: ''
    }, {
        param: 'size',
        type: 'string',
        desc: '标签尺寸',
        option: 'medium | small',
        default: 'medium'
    }, {
        param: 'onClose',
        type: 'func(dataSource, value)',
        desc: '关闭标签，传出dataSource，第二个参数为当前关闭tag的value',
        option: '',
        default: ''
    }, {
        param: 'onInputConfirm',
        type: 'func(dataSource, value)',
        desc: 'input输入完成后，传出dataSource，第二个参数为当前input输入值',
        option: '',
        default: ''
    }]
};
