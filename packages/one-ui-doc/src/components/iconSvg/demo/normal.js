import React, {PureComponent} from 'react';
import {IconSvg, tools} from '@baidu/one-ui';

const {
    iconSvgMap
} = tools.iconSvg;

export default class Normal extends PureComponent {
    render() {
        return (
            <div>
                {
                    Object.keys(iconSvgMap).map(key => {
                        return (
                            <span style={{display: 'inline-block', marginRight: '60px', marginBottom: '20px', textAlign: 'center'}}>
                                <IconSvg type={key} key={key} />
                                <br />
                                {key}
                            </span>
                        );
                    })
                }
            </div>
        );
    }
}
