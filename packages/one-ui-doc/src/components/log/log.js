import React from 'react';

const logs = [{
    version: '3.0.14-beta-40',
    date: '2019-10-28',
    content: (
        <div>
            <div>
                <div>datePicker</div>
                <div>
                    <div>
                        新增日期选择器组件
                    </div>
                </div>
            </div>
        </div>
    )
}, {
    version: '3.0.12',
    date: '2019-07-03',
    content: (
        <div>
            <div>
                <div>drawer</div>
                <div>
                    <div>
                        升级rc-drawer至2.0.1
                    </div>
                </div>
            </div>
            <div>
                <div>message</div>
                <div>
                    <div>
                        fix loading状态时 icon与文字对不齐
                    </div>
                </div>
            </div>
            <div>
                <div>modal</div>
                <div>
                    <div>
                        fix buttonPosition 不生效
                    </div>
                </div>
            </div>
            <div>
                <div>transfer</div>
                <div>
                    <div>
                        fix searchValue必须受控的问题，调用handleSelect时候会把点击的key暴露出来
                    </div>
                </div>
            </div>
            <div>
                <div>checkbox, radio</div>
                <div>
                    <div>
                        default变为了medium，使用default不会有问题，但是会报warning，请注意修改
                    </div>
                </div>
            </div>
        </div>
    )
}, {
    version: '3.0.5',
    date: '2019-07-02',
    content: (
        <div>
            <div>
                <div>input,numberbox,textArea,search</div>
                <div>
                    <div>
                        升级符合DLS规范皮肤
                    </div>
                </div>
            </div>
        </div>
    )
}, {
    version: '3.0.2',
    date: '2019-06-20',
    content: (
        <div>
            <div>
                <div>checkbox</div>
                <div>
                    <div>
                        升级符合DLS规范皮肤, 更新了checkbox的size，移除large尺寸，增加small尺寸
                    </div>
                </div>
            </div>
            <div>
                <div>radio</div>
                <div>
                    <div>
                        升级符合DLS规范皮肤, 更新了radio的size，移除large尺寸，增加small尺寸
                    </div>
                </div>
            </div>
            <div>
                <div>switch</div>
                <div>
                    <div>
                        升级符合DLS皮肤规范，增加small尺寸，然后每个尺寸高度和宽度都有所调整(变小)
                    </div>
                </div>
            </div>
            <div>
                <div>pagination</div>
                <div>
                    <div>
                        pageSize发生改变的时候，非受控情况下，统一更改为pageNo置为1，跳回第一页
                    </div>
                </div>
            </div>
            <div>
                <div>modal</div>
                <div>
                    <div>
                        升级依赖rc-dialog，至7.4.0，react16以上版本，创建modal的时候使用createPortal
                    </div>
                </div>
            </div>
        </div>
    )
}, {
    version: '3.0.1',
    date: '2019-06-17',
    content: (
        <div>
            <div>
                <div>button</div>
                <div>
                    <div>
                        符合DLS规范，更新了button的type，从3.0版本开始type只支持normal、strong、primary、translucent、link五种类型，同时向下兼容类型
    button尺寸由原来的3种变成了5种，增加xsmall，xlarge
                    </div>
                </div>
            </div>
            <div>
                <div>textLink</div>
                <div>
                    <div>
                        <div>符合DLS规范，更新了textlink的type，从3.0版本开始type只支持normal、strong两种类型，同时向下兼容类型</div>
                        <div>size尺寸由原来的1种变成2种，增加small尺寸</div>
                        <div>textLink增加disabled状态</div>
                    </div>
                </div>
            </div>
        </div>
    )
}, {
    version: '2.1.11',
    date: '2019-06-14',
    content: (
        <div>
            <div>
                <div>menu</div>
                <div>
                    <div>
                        将横向menu布局方式由float改为flex布局方式，避免clear:both引起的撑大的样式问题
                    </div>
                </div>
            </div>
            <div>
                <div>badge</div>
                <div>
                    <div>
                        badge组件包裹生命周期react-lifecycles-compat的polyfill，fix react15版本使用badge时，getDerivedStateFromProps不生效的问题
                    </div>
                </div>
            </div>
            <div>
                <div>textArea</div>
                <div>
                    <div>
                        textArea的默认宽度由322px修改为标准标注300px
                    </div>
                </div>
            </div>
        </div>
    )
}, {
    version: '2.1.10',
    date: '2019-06-06',
    content: (
        <div>
            <div>
                <div>table</div>
                <div>
                    <div>
                        column里面增加defaultSortOrder表示非受控默认排序
                    </div>
                    <div>
                        column里面增加defaultFilteredValue表示非受控默认的filteredValue
                    </div>
                    <div>
                        column里面增加filterWithoutConfirm，该值为boolean类型值，表示filter不需要确认取消按钮，该条件下每次点击筛选的单选/复选会调用onFilterChange
                    </div>
                    <div>
                        column里面增加customOperate，可自定义除sort和filter以外的其他操作，例如tip
                    </div>
                </div>
            </div>
            <div>
                <div>pagination</div>
                <div>
                    增加defaultPageNo，defaultPageSize表示非受控分页器的默认页数和默认页码
                </div>
            </div>
            <div>
                <div>modal</div>
                <div>
                    增加needCloseIcon属性表示是否展示右上角的X，默认是true表示展示x
                </div>
                <div>
                    增加参数getLength 可自定义计数，具体见demo
                </div>
            </div>
            <div>
                <div>select</div>
                <div>
                    增加实验性质属性customRenderTarget（有提出这个target略生涩，所以这个名字可能会更换），放在实验性质里面建议该版本不使用，customRenderTarget, 可自定义target的展示规则，如姓名：label
                </div>
            </div>
        </div>
    )
}, {
    version: '2.1.6',
    date: '2019-05-29',
    content: (
        <div>
            <div>
                <div>transfer</div>
                <div>
                    可选的大于上限的时候，全选按钮置灰
                </div>
            </div>
            <div>
                <div>input</div>
                <div>
                    增加参数countMode 来区分中文校验算1个字符还是2个字符，具体见demo
                </div>
                <div>
                    增加参数getLength 可自定义计数，具体见demo
                </div>
            </div>
            <div>
                <div>textLine</div>
                <div>
                    增加参数countMode 来区分中文校验算1个字符还是2个字符，具体见demo
                </div>
                <div>
                    增加参数getLength 可自定义计数，具体见demo
                </div>
            </div>
            <div>
                <div>textArea</div>
                <div>
                    增加参数countMode 来区分中文校验算1个字符还是2个字符，具体见demo
                </div>
                <div>
                    增加参数getLength 可自定义计数，具体见demo
                </div>
                <div>
                    textArea支持最小行高可定义，具体见demo
                </div>
            </div>
        </div>
    )
}];

export default logs;
