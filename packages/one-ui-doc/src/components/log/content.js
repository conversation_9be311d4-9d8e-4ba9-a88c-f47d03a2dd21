import React from 'react';
import Highlight from 'react-highlight';
import {Link} from '@baidu/one-ui';
import Title from '../../common/title';
import {menu} from '../../config';
import log from '../../../../one-ui/CHANGELOG.md';
import './style.less';
export default props => {
    // eslint-disable-next-line react/prop-types
    const id = props.id;
    const {label: title} = menu[id] || {};
    return (
        <div>
            <Title title={title} />
            <div className="doc">
                <div className="demo-home-page-title">
                    <Link type="strong" target="_blank" toUrl="http://icode.baidu.com/repos/baidu/fc-fe/one-ui/blob/d20_dev:packages/one-ui/CHANGELOG.md">
                        源文档
                    </Link>
                    <Highlight className="markdown">
                        {log}
                    </Highlight>
                </div>
            </div>
        </div>
    );
};
