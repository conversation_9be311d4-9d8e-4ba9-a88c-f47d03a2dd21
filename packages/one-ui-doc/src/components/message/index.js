/**
 * @file nav 配置项
 * <AUTHOR>
 * @date 2020-04-29
 */
import BaseComponent from '../base';

export default {
    message: {
        value: BaseComponent,
        label: 'Message 信息',
        demos: [
            {
                title: '基础',
                desc: '基础使用方式',
                source: 'normal'
            },
            {
                title: '尺寸',
                desc: 'medium / small',
                source: 'size'
            },
            {
                title: '类型',
                desc: '内置类型演示',
                source: 'type'
            },
            {
                title: '弹出',
                desc: 'popup',
                source: 'popup'
            },
            {
                title: '简单',
                desc: '无icon',
                source: 'simple'
            },
            {
                title: '独立',
                desc: '仅icon有状态',
                source: 'standalone'
            },
            {
                title: '自定义Icon',
                desc: '',
                source: 'icon'
            }
        ],
        apis: [
            {
                apiKey: 'Message',
                title: 'Message'
            }
        ]
    }
};