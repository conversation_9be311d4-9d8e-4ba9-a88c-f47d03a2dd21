import React from 'react';
import {Link} from '@baidu/one-ui';
import {Link as RouterLink} from 'react-router-dom';
import {IconInfoflow} from 'dls-icons-react';
import Highlight from 'react-highlight';
import Title from '../../common/title';
import {menu} from '../../config';
import {version} from '../../../../one-ui/package.json';
import './style.less';

export default props => {
    // eslint-disable-next-line react/prop-types
    const id = props.id || 'index';
    const {label: title} = menu[id] || {};
    const str1 = 'import {Button} from \'@baidu/one-ui\';';
    const str2 = '<Button>这是一个按钮</Button>';
    return (
        <div>
            <Title title={title} />
            <div className="doc">
                <div className="demo-home-page-title">ONE UI 是基于 Light Design 设计体系的 React UI 组件库，主要用于研发企业级中后台产品。</div>
                <div className="demo-home-page-title">
                    当前版本：
                    <RouterLink to="/intro/log">
                        <Link type="strong">{version}</Link>
                    </RouterLink>
                    &nbsp;&nbsp;&nbsp;&nbsp;用户群：1668007&nbsp;<Link type="strong" toUrl="baidu://addgroup/?id=1668007" isAtag><IconInfoflow /></Link>
                </div>
                <div className="demo-home-page-title">安装：</div>
                <Highlight className="bash">
                    npm install @baidu/one-ui --registry=http://registry.npm.baidu-int.com
                </Highlight>
                <div className="demo-home-page-title">使用：</div>
                <Highlight className="javscript">
                    <div>{str1}</div>
                    <br />
                    <div>{str2}</div>
                </Highlight>
                <br />
                <div className="demo-home-page-title">
                    <Link type="strong" target="_blank" toUrl="https://console.cloud.baidu-int.com/devops/icode/repos/baidu/fc-fe/one-ui/tree/d20_dev" isAtag>组件源码</Link>
                    <br />
                    <br />
                    <Link type="strong" target="_blank" toUrl="https://console.cloud.baidu-int.com/devops/icafe/space/baidu-fc-fe-one-ui/queries/query/all" isAtag>issue</Link>
                    <br />
                    <br />
                    <Link type="strong" target="_blank" toUrl="https://console.cloud.baidu-int.com/devops/icode/repos/baidu/fc-fe/one-ui/blob/d20_dev:packages/one-ui/breakingChange.md" isAtag>4.0 breaking change</Link>
                    <br />
                    <br />
                    <Link type="strong" target="_blank" toUrl="https://github.com/ecomfe/dls-icons/tree/master/packages/dls-icons-react" isAtag>图标库(dls-icons-react)</Link>
                    <br />
                    <br />
                    <Link type="strong" target="_blank" toUrl="http://agroup.baidu.com/cpdfe/md/article/3390953" isAtag>Light Design由来与开发原则</Link>
                </div>
            </div>
        </div>
    );
};
