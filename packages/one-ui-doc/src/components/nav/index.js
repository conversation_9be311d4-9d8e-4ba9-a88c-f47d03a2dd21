/**
 * @file nav 配置项
 * <AUTHOR>
 * @date 2020-04-29
 */
import BaseComponent from '../base';

export default {
    nav: {
        value: BaseComponent,
        label: 'Nav 横向导航',
        demos: [
            {
                title: '横向导航',
                desc: '横向导航',
                source: 'normal'
            },
            {
                title: 'Badge',
                desc: '一级标题支持Badge',
                source: 'badge'
            },
            {
                title: '受控',
                desc: '横向导航 - 受控',
                source: 'controlled'
            },
            {
                title: '自定义',
                desc: '横向导航 - 可自定义下拉弹层',
                source: 'card'
            }
        ],
        apis: [
            {
                apiKey: 'Nav',
                title: 'Nav'
            }
        ]
    }
};