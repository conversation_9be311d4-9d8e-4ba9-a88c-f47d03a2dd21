/**
 * @file nav的api
 * <AUTHOR>
 * @date 2020-04-29
 */
export default {
    Nav: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义横向导航的className',
            option: '',
            default: ''
        },
        {
            param: 'datasource',
            type: 'Array [{label, key, overlay, href, disabled, target}]',
            desc: `数据源，其中label为横向菜单的node，
                可以为一个reactNode，key为横向菜单唯一键值，
                overlay如果存在则为下拉弹层属性，overlay为对象，
                其中有overlayElement为下拉弹层的node，建议传入一个menu，
                placement为展平是下拉弹层弹出位置，默认是bottom，hiddenPlacement为收纳状态下弹层弹出位置，默认为left
                overlayProps为下拉弹层的其余属性，可以参考overlay的属性值，href为nav的item跳转的a标签href, target为在何处打开链接`,
            option: '',
            default: ''
        },
        {
            param: 'defaultValue',
            type: '[String]',
            desc: '默认选中的值，非受控属性',
            option: '',
            default: ''
        },
        {
            param: 'value',
            type: '[String]',
            desc: '选中的值，受控属性',
            option: '',
            default: ''
        },
        {
            param: 'onChange',
            type: 'func(e)  key为 e.target.value',
            desc: '切换导航选项时候调用函数',
            option: '',
            default: ''
        }
    ]
};