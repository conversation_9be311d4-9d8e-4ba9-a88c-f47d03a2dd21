import React, {PureComponent, useCallback, useState} from 'react';
import {Nav} from '@baidu/one-ui';
import {IconDoubleCircle} from 'dls-icons-react';

export default function Normal(props) {

    const [hoverkey, setHoverkey] = useState(null);


    const onVisibleChange = useCallback(props => {
        if (props.visible) {
            setHoverkey(props.key);
        }
        else {
            setHoverkey(null);
        }
    }, [setHoverkey]);

    const datasource = [
        {
            label: '导航1',
            key: '1'
        },
        {
            label: '导航2',
            key: '2',
            overlay: {
                overlayElement: (
                    <div>这是一个可以自定义的大卡片</div>
                )
            },
            badge: {
                type: 'ghost',
                offset: [20, -14],
                textContent: <IconDoubleCircle style={{height: 16}} />
            }
        },
        {
            label: '导航3',
            key: '3'
        },
        {
            label: '导航4',
            key: '4'
        },
        {
            label: '导航5',
            key: '5',
            disabled: true
        },
        {
            label: '导航6',
            key: '6'
        },
        {
            label: '导航7',
            key: '7'
        },
        {
            label: '导航8',
            key: '8'
        },
        {
            label: '导航9',
            key: '9',
            overlay: {
                overlayElement: (
                    <div>这是一个可以自定义的大卡片</div>
                ),
                overlayProps: {
                    onVisibleChange,
                    visible: hoverkey === '9'
                }
            }
        }
    ];

    return (
        <div>
            <Nav type="ghost" defaultValue="3" dataSource={datasource} />
        </div>
    );
}
