/**
 * @file 横向导航demo示例
 * <AUTHOR>
 * @date 2020-04-29
 */
import React, {PureComponent} from 'react';
import {Nav} from '@baidu/one-ui';

const datasource = [
    {
        label: '导航1',
        key: '1',
        href: 'https://www.baidu.com'
    },
    {
        label: '导航2',
        key: '2',
        href: 'https://www.baidu.com'
    },
    {
        label: '导航3',
        key: '3',
        href: 'https://www.baidu.com'
    },
    {
        label: '导航4',
        key: '4',
        href: 'https://www.baidu.com'
    },
    {
        label: '导航5',
        key: '5',
        disabled: true
    },
    {
        label: '导航6',
        key: '6',
        href: 'https://www.baidu.com'
    },
    {
        label: '导航7',
        key: '7',
        href: 'https://www.baidu.com'
    },
    {
        label: '导航8',
        key: '8',
        href: 'https://www.baidu.com'
    },
    {
        label: '导航9',
        key: '9',
        href: 'https://www.baidu.com'
    }
];

export default class Normal extends PureComponent {
    render() {
        return (
            <div>
                <Nav defaultValue="1" dataSource={datasource} />
            </div>
        );
    }
}
