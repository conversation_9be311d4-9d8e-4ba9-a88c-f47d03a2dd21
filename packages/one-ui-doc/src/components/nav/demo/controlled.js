/**
 * @file 横向导航demo示例
 * <AUTHOR>
 * @date 2020-04-29
 */
import React, {PureComponent} from 'react';
import {Nav} from '@baidu/one-ui';

const datasource = [
    {
        label: '导航1',
        key: '1'
    },
    {
        label: '导航2',
        key: '2'
    },
    {
        label: '导航3',
        key: '3'
    },
    {
        label: '导航4',
        key: '4'
    },
    {
        label: '导航5',
        key: '5',
        disabled: true
    },
    {
        label: '导航6',
        key: '6'
    },
    {
        label: '导航7',
        key: '7'
    },
    {
        label: '导航8',
        key: '8'
    },
    {
        label: '导航9',
        key: '9'
    }
];

export default class Normal extends PureComponent {

    state = {
        value: '1'
    };

    onChange = e => {
        this.setState({
            value: e.target.value
        });
    }

    render() {
        return (
            <div>
                <Nav
                    value={this.state.value}
                    dataSource={datasource}
                    onChange={this.onChange}
                />
            </div>
        );
    }
}
