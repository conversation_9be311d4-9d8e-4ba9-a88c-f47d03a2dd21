/**
 * @file 横向导航demo示例
 * <AUTHOR>
 * @date 2020-04-29
 */
import React, {PureComponent} from 'react';
import {Nav} from '@baidu/one-ui';

export default class Normal extends PureComponent {

    state = {
        hoverkey: null
    };

    onVisibleChange = props => {
        if (props.visible) {
            this.setState({
                hoverkey: props.key
            });
        }
        else {
            this.setState({
                hoverkey: null
            });
        }
    };

    render() {
        const datasource = [
            {
                label: '导航1',
                key: '1'
            },
            {
                label: '导航2',
                key: '2',
                overlay: {
                    overlayElement: (
                        <div>这是一个可以自定义的大卡片</div>
                    )
                }
            },
            {
                label: '导航3',
                key: '3'
            },
            {
                label: '导航4',
                key: '4'
            },
            {
                label: '导航5',
                key: '5',
                disabled: true
            },
            {
                label: '导航6',
                key: '6'
            },
            {
                label: '导航7',
                key: '7'
            },
            {
                label: '导航8',
                key: '8'
            },
            {
                label: '导航9',
                key: '9',
                overlay: {
                    overlayElement: (
                        <div>这是一个可以自定义的大卡片</div>
                    ),
                    overlayProps: {
                        onVisibleChange: this.onVisibleChange,
                        visible: this.state.hoverkey === '9'
                    }
                }
            }
        ];
        return (
            <div>
                <Nav defaultValue="3" dataSource={datasource} />
            </div>
        );
    }
}
