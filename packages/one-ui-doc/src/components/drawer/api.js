export default {
    Drawer: [
        {
            param: 'className',
            type: 'String',
            desc: '自定义类名',
            option: '',
            default: ''
        },
        {
            param: 'closable',
            type: 'boolean',
            desc: '是否允许关闭',
            option: '',
            default: 'true'
        },
        {
            param: 'destroyOnClose',
            type: 'boolean',
            desc: '关闭时候是否销毁',
            option: '',
            default: ''
        },
        {
            param: 'footer',
            type: '[<Button>, <Button>, ...]',
            desc: '传入底部按钮',
            option: '',
            default: 'null'
        },
        {
            param: 'height',
            type: 'number',
            desc: '上下弹出时候的高度',
            option: '',
            default: '256'
        },
        {
            param: 'mask',
            type: 'boolean',
            desc: '是否有遮罩',
            option: '',
            default: 'true'
        },
        {
            param: 'maskClosable',
            type: 'boolean',
            desc: '点击遮罩是否可以关闭弹窗',
            option: '',
            default: 'true'
        },
        {
            param: 'onClose',
            type: 'function(e)',
            desc: '点击遮罩层或右上角叉或取消按钮的回调',
            option: '',
            default: ''
        },
        {
            param: 'placement',
            type: 'string',
            desc: '抽屉打开的方向',
            option: 'top, right, bottom, left',
            default: 'right'
        },
        {
            param: 'title',
            type: 'reactNode',
            desc: 'draw的标题',
            option: '',
            default: ''
        },
        {
            param: 'type',
            type: 'string',
            desc: '模式, basic模式支持内容无边距样式',
            option: 'basic | default',
            default: 'default'
        },
        {
            param: 'visible',
            type: 'boolean',
            desc: '是否可见',
            option: '',
            default: ''
        },
        {
            param: 'width',
            type: 'number',
            desc: '左右弹出的时候的宽度',
            option: '',
            default: '400'
        },
        {
            param: 'hideDefaultFooter',
            type: 'bool',
            desc: '是否隐藏默认的button',
            option: '',
            default: true
        },
        {
            param: 'onOk',
            type: 'func(e)',
            desc: '点击主操作按钮的回调（仅对默认按钮生效）',
            option: '',
            default: ''
        },
        {
            param: 'onCancel',
            type: 'func(e)',
            desc: '点击副操作按钮的回调（仅对默认按钮生效）',
            option: '',
            default: ''
        },
        {
            param: 'okText',
            type: 'string|reactNode',
            desc: '主操作默认按钮话术（仅对默认按钮生效）',
            option: '',
            default: '确定'
        },
        {
            param: 'cancelText',
            type: 'string|reactNode',
            desc: '副操作默认按钮话术（仅对默认按钮生效）',
            option: '',
            default: '取消'
        },
        {
            param: 'okProps',
            type: '{}',
            desc: '主操作默认按钮属性（仅对默认按钮生效）',
            option: '',
            default: ''
        },
        {
            param: 'cancelProps',
            type: '{}',
            desc: '副操作默认按钮属性（仅对默认按钮生效）',
            option: '',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: '抽屉的尺寸',
            option: 'small | medium',
            default: 'medium'
        }
    ]
};
