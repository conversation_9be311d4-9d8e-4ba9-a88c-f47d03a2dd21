import React, {PureComponent} from 'react';
import {Drawer, Button} from '@baidu/one-ui';

export default class NoramlDrawer extends PureComponent {
    state = {
        visible: false,
        childrenDrawer: false
    };

    showDrawer = () => {
        this.setState({
            visible: true
        });
    };

    onClose = () => {
        this.setState({
            visible: false
        });
    };

    showChildrenDrawer = () => {
        this.setState({
            childrenDrawer: true
        });
    };

    onChildrenDrawerClose = () => {
        this.setState({
            childrenDrawer: false
        });
    };

    render() {
        return (
            <div>
                <br />
                <br />
                中号
                <br />
                <br />
                <Button type="primary" onClick={this.showDrawer}>
                    Open drawer
                </Button>
                <Drawer
                    title="Multi-level drawer"
                    width={520}
                    onClose={this.onClose}
                    visible={this.state.visible}
                    size="medium"
                >
                    <Button type="primary" onClick={this.showChildrenDrawer}>
                        Two-level drawer
                    </Button>
                    <Drawer
                        title="Two-level Drawer"
                        width={320}
                        onClose={this.onChildrenDrawerClose}
                        visible={this.state.childrenDrawer}
                        onCancel={this.onChildrenDrawerClose}
                        hideDefaultFooter={false}
                        size="medium"
                    >
                        This is two-level drawer
                    </Drawer>
                    <div
                        style={{
                            position: 'absolute',
                            bottom: 0,
                            width: '100%',
                            padding: '24px',
                            textAlign: 'left',
                            left: 0,
                            background: '#fff',
                            borderRadius: '0 0 4px 4px'
                        }}
                    >
                        <Button
                            style={{
                                marginRight: 12
                            }}
                            onClick={this.onClose}
                            size="medium"
                        >
                            Cancel
                        </Button>
                        <Button onClick={this.onClose} type="primary" size="medium">
                            Submit
                        </Button>
                    </div>
                </Drawer>
            </div>
        );
    }
}
