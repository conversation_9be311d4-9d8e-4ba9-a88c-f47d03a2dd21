import React, {PureComponent} from 'react';
import {Drawer, Button} from '@baidu/one-ui';

export default class NoramlDrawer extends PureComponent {
    state = {
        visible: false
    };

    showDrawer = () => {
        this.setState({
            visible: true
        });
    };

    onClose = () => {
        this.setState({
            visible: false
        });
    };

    render() {
        return (
            <div>
                <Button type="primary" onClick={this.showDrawer}>
                    Open
                </Button>
                <Drawer
                    title="Basic Drawer"
                    placement="bottom"
                    onClose={this.onClose}
                    visible={this.state.visible}
                    destroyOnClose
                    footer={[
                        (
                            <Button key={1} onClick={this.showDrawer}>
                                自定义操作1
                            </Button>
                        ),
                        (
                            <Button key={2} onClick={this.showDrawer}>
                                自定义操作2
                            </Button>
                        )
                    ]}
                >
                    <div style={{height: '1000px', backgroundColor: '#eee'}}>
                        <p style={{marginBottom: '20px'}}>Some contents...</p>
                        <p style={{marginBottom: '20px'}}>Some contents...</p>
                        <p style={{marginBottom: '20px'}}>Some contents...</p>
                    </div>
                </Drawer>
            </div>
        );
    }
}
