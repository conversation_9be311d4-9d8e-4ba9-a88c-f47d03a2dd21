import React, {PureComponent} from 'react';
import {Drawer, Button, Checkbox, Radio} from '@baidu/one-ui';

export default class NoramlDrawer extends PureComponent {
    state = {
        visible: false,
        title: '这是标题',
        type: 'default',
        placement: 'right',
        size: 'medium',
        mask: true,
        closable: true,
        hideDefaultFooter: false
    };

    showDrawer = e => {
        // for closeDrawerByClickBody
        e.stopPropagation();
        this.setState({
            visible: true
        });
    };

    onClose = () => {
        this.setState({
            visible: false
        });
    };

    onOk = () => {
        this.setState({
            visible: false
        });
    }

    onCancel = () => {
        this.setState({
            visible: false
        });
    }

    setTitle = e => {
        const checked = e.target.checked;
        this.setState({
            title: checked ? '这是标题' : null
        });
    }

    setMask = e => {
        const mask = e.target.checked;
        this.setState({
            mask
        });
    }

    setClosable = e => {
        const closable = e.target.checked;
        this.setState({
            closable
        });
    }

    setFooter = e => {
        const hideDefaultFooter = !e.target.checked;
        this.setState({
            hideDefaultFooter
        });
    }

    setType = e => {
        const checked = e.target.checked;
        this.setState({
            type: checked ? 'basic' : 'default'
        });
    }

    setPlacement = e => {
        const placement = e.target.value;
        this.setState({
            placement
        });
    }

    setSize = e => {
        const size = e.target.value;
        this.setState({
            size
        });
    }

    render() {
        const {
            title,
            type,
            placement,
            visible,
            size,
            mask,
            closable,
            hideDefaultFooter
        } = this.state;
        return (
            <div>
                <Checkbox checked={!!title} onChange={this.setTitle}>标题</Checkbox>
                <Checkbox checked={mask} onChange={this.setMask}>遮罩</Checkbox>
                <Checkbox checked={closable} onChange={this.setClosable}>关闭按钮</Checkbox>
                <Checkbox checked={!hideDefaultFooter} onChange={this.setFooter}>底部</Checkbox>
                <Checkbox checked={type === 'basic'} onChange={this.setType}>基础模式(支持内容无边距)</Checkbox>
                <br />
                <br />
                <Radio.Group
                    size="small"
                    onChange={this.setPlacement}
                    value={placement}
                >
                    <Radio.Button value="top">top</Radio.Button>
                    <Radio.Button value="bottom">bottom</Radio.Button>
                    <Radio.Button value="left">left</Radio.Button>
                    <Radio.Button value="right">right</Radio.Button>
                </Radio.Group>
                <br />
                <Radio.Group
                    size="small"
                    onChange={this.setSize}
                    value={size}
                >
                    <Radio.Button value="small">small</Radio.Button>
                    <Radio.Button value="medium">medium</Radio.Button>
                </Radio.Group>
                <br />
                <br />
                <Button onClick={this.showDrawer}>
                    Open
                </Button>
                <Drawer
                    key={placement}
                    title={title}
                    type={type}
                    placement={placement}
                    onClose={this.onClose}
                    visible={visible}
                    size={size}
                    mask={mask}
                    destroyOnClose
                    hideDefaultFooter={hideDefaultFooter}
                    onOk={this.onOk}
                    onCancel={this.onCancel}
                    closeDrawerByClickBody
                    closable={closable}
                >
                    <div style={{height: '1000px', backgroundColor: '#eee'}}>
                        <p style={{marginBottom: '20px'}}>Some contents...</p>
                        <p style={{marginBottom: '20px'}}>Some contents...</p>
                        <p style={{marginBottom: '20px'}}>Some contents...</p>
                    </div>
                </Drawer>
            </div>
        );
    }
}
