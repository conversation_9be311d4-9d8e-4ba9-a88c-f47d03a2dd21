import React, {PureComponent} from 'react';
import {Drawer, Button} from '@baidu/one-ui';

export default class NoramlDrawer extends PureComponent {
    state = {
        visible: false
    };

    showDrawer = e => {
        e.stopPropagation();
        this.setState({
            visible: true
        });
    };

    onClose = () => {
        this.setState({
            visible: false
        });
    };

    onOk = () => {
        this.setState({
            visible: false
        });
    }

    onCancel = () => {
        this.setState({
            visible: false
        });
    }

    render() {
        return (
            <div>
                <Button onClick={this.showDrawer}>
                    Open
                </Button>
                <Drawer
                    title="Basic Drawer"
                    placement="right"
                    onClose={this.onClose}
                    visible={this.state.visible}
                    destroyOnClose
                    hideDefaultFooter={false}
                    onOk={this.onOk}
                    onCancel={this.onCancel}
                    closeDrawerByClickBody
                    mask={false}
                >
                    <div style={{height: '1000px', backgroundColor: '#eee'}}>
                        <p style={{marginBottom: '20px'}}>Some contents...</p>
                        <p style={{marginBottom: '20px'}}>Some contents...</p>
                        <p style={{marginBottom: '20px'}}>Some contents...</p>
                    </div>
                </Drawer>
            </div>
        );
    }
}
