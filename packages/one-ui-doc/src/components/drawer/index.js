import BaseComponent from '../base';

export default {
    drawer: {
        value: BaseComponent,
        label: 'Drawer 抽屉',
        demos: [
            {
                title: '普通抽屉',
                desc: '普通抽屉',
                source: 'normal'
            },
            {
                title: '自定义按钮',
                desc: '普通抽屉-自定义按钮',
                source: 'customFooter'
            },
            {
                title: '底部滑出',
                desc: '底部滑出',
                source: 'positionBottom'
            },
            {
                title: '顶部滑出',
                desc: '顶部滑出',
                source: 'positionTop'
            },
            {
                title: '右部滑出',
                desc: '右部滑出',
                source: 'positionRight'
            },
            {
                title: '左部滑出',
                desc: '左部滑出',
                source: 'positionLeft'
            },
            {
                title: '中号尺寸',
                desc: '普通抽屉 - 中号尺寸',
                source: 'size'
            },
            {
                title: '多层抽屉(小号)',
                desc: '普通抽屉 - 多层抽屉 - 小号',
                source: 'multiple'
            },
            {
                title: '多层抽屉(中号)',
                desc: '普通抽屉 - 多层抽屉 - 中号',
                source: 'multiple2'
            },
            {
                title: '点击body关闭',
                desc: '普通抽屉 - 支持无遮罩场景下可以点击body关闭，并且可以点击某个按钮或其他地方不关闭',
                source: 'closeDrawerByClickBody'
            }
        ],
        apis: [
            {
                apiKey: 'Drawer',
                title: 'Drawer'
            }
        ]
    }
};
