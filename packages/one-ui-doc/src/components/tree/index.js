import BaseComponent from '../base';

export default {
    tree: {
        value: BaseComponent,
        label: 'Tree 树形控件',
        demos: [
            {
                title: '基础使用',
                desc: '不可勾选的树',
                source: 'normal'
            },
            {
                title: '可勾选',
                desc: '可勾选checkbox的树',
                source: 'checkable'
            },
            {
                title: '异步加载',
                desc: '模拟异步加载效果，带loading',
                source: 'loading'
            },
            {
                title: '高亮',
                desc: '展示高亮部分',
                source: 'highLight'
            },
            {
                title: '带图标',
                desc: '带图标的树',
                source: 'icon'
            },
            {
                title: '组合示例',
                desc: '',
                source: 'noname'
            },
            {
                title: '点文字勾选',
                desc: '无点击文字被勾选效果，这样点击文字就直接选中checkbox',
                source: 'unselectable'
            },
            {
                title: '禁用',
                desc: '整棵树被disabled',
                source: 'disabled'
            },
            {
                title: '搜索',
                desc: 'Tree 拼接 search',
                source: 'search'
            },
            {
                title: '虚拟滚动',
                desc: 'Tree 虚拟滚动',
                source: 'virtualList'
            },
            {
                title: '自动展开父级',
                desc: 'Tree 传入子的expandedkey，自动展开父亲级别',
                source: 'autoExpandKey'
            }
        ],
        apis: [
            {
                apiKey: 'Tree',
                title: 'Tree'
            },
            {
                apiKey: 'TreeNode',
                title: 'TreeNode'
            },
            {
                apiKey: 'VirtualTreeNode',
                title: 'VirtualTreeNode',
                desc: '虚拟滚动请使用这个作为节点'
            }
        ]
    }
};
