export default {
    Tree: [
        {
            param: 'className',
            type: 'string',
            desc: '可以自定义tree的className',
            option: '',
            default: ''
        },
        {
            param: 'style',
            type: 'object',
            desc: '可以自定义tree的style',
            option: '',
            default: ''
        },
        {
            param: 'autoExpandParent',
            type: 'boolean',
            desc: '是否展开父级所有节点（常于搜索情景搭配使用）',
            option: '',
            default: 'false'
        },
        {
            param: 'checkable',
            type: 'bool',
            desc: '是否有checkbox',
            option: '',
            default: 'false'
        },
        {
            param: 'checkedKeys',
            type: 'string[]',
            desc: 'checkbox选中的树的节点的key，受控属性',
            option: '',
            default: '[]'
        },
        {
            param: 'defaultCheckedKeys',
            type: 'string[]',
            desc: 'checkbox默认选中的树的节点的key，非受控属性',
            option: '',
            default: '[]'
        },
        {
            param: 'defaultExpandedKeys',
            type: 'string[]',
            desc: '默认展开的树节点的key，非受控属性',
            option: '',
            default: '[]'
        },
        {
            param: 'expandedKeys',
            type: 'string[]',
            desc: '展开的树节点的key，受控属性',
            option: '',
            default: '[]'
        },
        {
            param: 'defaultSelectedKeys',
            type: 'string[]',
            desc: '默认选中的树节点的key，非受控属性',
            option: '',
            default: '[]'
        },
        {
            param: 'selectedKeys',
            type: 'string[]',
            desc: '选中的树节点的key，受控属性，注意文字选中目前只支持单选，数组传多个值时，取第一个值',
            option: '',
            default: '[]'
        },
        {
            param: 'filterTreeNode',
            type: 'func',
            desc: '筛选高亮，具体用法可以参考示例',
            option: '',
            default: ''
        },
        {
            param: 'onExpand',
            type: 'function(expandedKeys, {expanded: bool, node})',
            desc: '展开/收起节点时触发',
            option: '',
            default: ''
        },
        {
            param: 'loadData',
            type: 'function(node)',
            desc: '异步加载数据',
            option: '',
            default: ''
        },
        {
            param: 'selectable',
            type: 'boolean',
            desc: '是否可以被文字选中',
            option: '',
            default: 'true'
        },
        {
            param: 'showIcon',
            type: 'bool',
            desc: '是否每个节点前展示图标，为true的时候，需要节点自己传入Icon',
            option: '',
            default: ''
        },
        {
            param: 'onCheck',
            type: 'func(checkedKeys, info)',
            desc: '勾选checkbox的回调',
            option: '',
            default: ''
        },
        {
            param: 'onSelect',
            type: 'func(selectedKeys, info)',
            desc: '选中文字的回调',
            option: '',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: '尺寸',
            option: 'small | medium',
            default: 'medium'
        },
        {
            param: 'disabled',
            type: 'bool',
            desc: '整树禁用，这样的话树会被收起，如果想各个节点disabled，但是树打开请在节点上设置disabled',
            option: '',
            default: 'false'
        },
        {
            param: 'parentContainerHeight',
            type: 'number',
            desc: '传入父容器的高度，默认为开启虚拟滚动',
            option: '',
            default: ''
        },
        {
            param: 'treeNodeHeight',
            type: 'number',
            desc: '可以指定虚拟滚动开启时，每个tree节点的高度',
            option: '',
            default: ''
        }
    ],
    TreeNode: [
        {
            param: 'disabled',
            type: 'boolean',
            desc: '禁止选中节点，展开依旧生效',
            option: '',
            default: 'false'
        },
        {
            param: 'icon',
            type: 'ReactNode/Function(props):ReactNode',
            desc: '自定义图标。可接收组件，props 为当前节点 props',
            option: '',
            default: ''
        },
        {
            param: 'key',
            type: 'string',
            desc: `
            树的 (default)ExpandedKeys / (default)CheckedKeys / 
            (default)SelectedKeys 属性所用。注意：整个树范围内的所有节点的 key 值不能重复！`,
            option: '',
            default: '内部计算出的节点位置'
        },
        {
            param: 'title',
            type: 'string|ReactNode',
            desc: '标题',
            option: '',
            default: ''
        },
        {
            param: 'disableCheckbox',
            type: 'bool',
            desc: '该节点的checkbox可以置灰',
            option: '',
            default: 'false'
        },
        {
            param: 'checkable',
            type: 'bool',
            desc: '该节点是否可以勾选checkbox',
            option: '',
            default: ''
        },
        {
            param: 'selectable',
            type: 'bool',
            desc: '该节点是否可以被选中',
            option: '',
            default: 'true'
        }
    ],
    VirtualTreeNode: [
        {
            param: 'disabled',
            type: 'boolean',
            desc: '禁止选中节点，展开依旧生效',
            option: '',
            default: 'false'
        },
        {
            param: 'icon',
            type: 'ReactNode/Function(props):ReactNode',
            desc: '自定义图标。可接收组件，props 为当前节点 props',
            option: '',
            default: ''
        },
        {
            param: 'key',
            type: 'string',
            desc: `
            树的 (default)ExpandedKeys / (default)CheckedKeys / 
            (default)SelectedKeys 属性所用。注意：整个树范围内的所有节点的 key 值不能重复！`,
            option: '',
            default: '内部计算出的节点位置'
        },
        {
            param: 'title',
            type: 'string|ReactNode',
            desc: '标题',
            option: '',
            default: ''
        },
        {
            param: 'disableCheckbox',
            type: 'bool',
            desc: '该节点的checkbox可以置灰',
            option: '',
            default: 'false'
        },
        {
            param: 'checkable',
            type: 'bool',
            desc: '该节点是否展示checkbox',
            option: '',
            default: ''
        },
        {
            param: 'selectable',
            type: 'bool',
            desc: '该节点是否可以被文字选中',
            option: '',
            default: 'true'
        }
    ]
};
