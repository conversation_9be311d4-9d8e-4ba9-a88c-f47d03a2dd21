import React, {PureComponent} from 'react';
import {Tree} from '@baidu/one-ui';

const {TreeNode} = Tree;

export default class Demo extends PureComponent {
    onSelect = (selectedKeys, info) => {
        console.log('selected', selectedKeys, info);
    };

    onCheck = (checkedKeys, info) => {
        console.log('onCheck', checkedKeys, info);
    };

    render() {
        return (
            <div>
                <div>
                    <br />
                    <br />
                    带checkbox选中
                    <br />
                    <br />
                    <Tree
                        defaultExpandedKeys={['0-0-0', '0-0-1']}
                        defaultSelectedKeys={['0-0-0', '0-0-1']}
                        defaultCheckedKeys={['0-0-0']}
                        onSelect={this.onSelect}
                        onCheck={this.onCheck}
                        checkable
                    >
                        <TreeNode title="计划1" key="0-0">
                            <TreeNode title="单元1" key="0-0-0">
                                <TreeNode title="关键词1" key="0-0-0-0" />
                                <TreeNode title="关键词1" key="0-0-0-1" />
                            </TreeNode>
                            <TreeNode title="单元2" key="0-0-1">
                                <TreeNode title="关键词3" key="0-0-1-0" />
                            </TreeNode>
                        </TreeNode>
                    </Tree>
                </div>
                <br />
                <br />
                中号尺寸
                <br />
                <br />
                <div>
                    <br />
                    <br />
                    带checkbox选中
                    <br />
                    <br />
                    <Tree
                        defaultExpandedKeys={['0-0-0', '0-0-1']}
                        defaultSelectedKeys={['0-0-0', '0-0-1']}
                        defaultCheckedKeys={['0-0-0']}
                        onSelect={this.onSelect}
                        onCheck={this.onCheck}
                        checkable
                        size="medium"
                    >
                        <TreeNode title="计划1" key="0-0" size="medium">
                            <TreeNode title="单元1" key="0-0-0" size="medium">
                                <TreeNode title="关键词1" key="0-0-0-0" size="medium" />
                                <TreeNode title="关键词1" key="0-0-0-1" size="medium" />
                            </TreeNode>
                            <TreeNode title="单元2" key="0-0-1" size="medium">
                                <TreeNode title="关键词3" key="0-0-1-0" size="medium" />
                            </TreeNode>
                        </TreeNode>
                    </Tree>
                </div>

            </div>
        );
    }
}
