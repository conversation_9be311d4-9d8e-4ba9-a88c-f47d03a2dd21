import React, {PureComponent} from 'react';
import {Tree, SearchBox} from '@baidu/one-ui';

const {TreeNode} = Tree;

export default class Demo extends PureComponent {
    state = {
        searchValue: ''
    }

    onSelect = (selectedKeys, info) => {
        console.log('selected', selectedKeys, info);
    };

    onCheck = (checkedKeys, info) => {
        console.log('onCheck', checkedKeys, info);
    };

    filterTreeNode = that => {
        const title = that.props.title;
        const searchValue = this.state.searchValue;
        if (title.indexOf(searchValue) > -1) {
            return searchValue;
        }
        return false;
    }

    onChange = e => {
        console.log(e.target.value);
        this.setState({
            searchValue: e.target.value
        });
    }

    render() {
        return (
            <div>
                <br />
                <br />
                    该例仅展示搜索高亮，如果筛选可以自己在外部进行受控筛选
                <br />
                <br />
                <div>
                    <SearchBox onChange={this.onChange} placeholder="请输入关键词" />
                    <div style={{marginBottom: '8px'}} />
                    <Tree
                        defaultExpandedKeys={['0-0-0', '0-0-1']}
                        defaultSelectedKeys={['0-0-0', '0-0-1']}
                        defaultCheckedKeys={['0-0-0', '0-0-1']}
                        onSelect={this.onSelect}
                        onCheck={this.onCheck}
                        filterTreeNode={this.filterTreeNode}
                    >
                        <TreeNode title="计划1,啦啦啦" key="0-0">
                            <TreeNode title="单元1" key="0-0-0">
                                <TreeNode title="关键词1" key="0-0-0-0" />
                                <TreeNode title="关键词1" key="0-0-0-1" />
                            </TreeNode>
                            <TreeNode title="单元2" key="0-0-1">
                                <TreeNode title="关键词3" key="0-0-1-0" />
                            </TreeNode>
                        </TreeNode>
                    </Tree>
                </div>
            </div>
        );
    }
}
