import {Tree} from '@baidu/one-ui';
import React from 'react';

const {TreeNode} = Tree;

export default class Demo extends React.Component {
    state = {
        treeData: [
            {title: 'Expand to load', key: '0'},
            {title: 'Expand to load', key: '1'},
            {title: 'Tree Node', key: '2', isLeaf: true}
        ]
    };

    onLoadData = treeNode => new Promise(resolve => {
        if (treeNode.props.children) {
            resolve();
            return;
        }
        setTimeout(() => {
            treeNode.props.dataRef.children = [
                {title: 'Child Node', key: `${treeNode.props.eventKey}-0`},
                {title: 'Child Node', key: `${treeNode.props.eventKey}-1`}
            ];
            this.setState({
                treeData: [...this.state.treeData]
            });
            resolve();
        }, 2000);
    });

    renderTreeNodes = data => data.map(item => {
        if (item.children) {
            return (
                <TreeNode title={item.title} key={item.key} dataRef={item}>
                    {this.renderTreeNodes(item.children)}
                </TreeNode>
            );
        }
        return <TreeNode key={item.key} {...item} dataRef={item} />;
    });

    render() {
        return <Tree loadData={this.onLoadData}>{this.renderTreeNodes(this.state.treeData)}</Tree>;
    }
}
