import React, {PureComponent} from 'react';
import {Tree} from '@baidu/one-ui';

const {TreeNode} = Tree;

export default class Demo extends PureComponent {
    onSelect = (selectedKeys, info) => {
        console.log('selected', selectedKeys, info);
    };

    onCheck = (checkedKeys, info) => {
        console.log('onCheck', checkedKeys, info);
    };

    onExpand = (expanedKeys, info) => {
        console.log('expanded', expanedKeys, info);
    }

    render() {
        return (
            <div>
                <br />
                <br />
                <div>
                    <Tree
                        autoExpandParent
                        defaultExpandedKeys={['0-0-0-0']}
                        onSelect={this.onSelect}
                        onCheck={this.onCheck}
                        size="medium"
                    >
                        <TreeNode title="计划1" key="0-0" size="medium">
                            <TreeNode title="单元1" key="0-0-0" size="medium">
                                <TreeNode title="关键词1" key="0-0-0-0" size="medium">
                                    <TreeNode title="关键词1-3" key="0-0-1-0-0" size="medium" />
                                    <TreeNode title="关键词1-4" key="0-0-1-0-1" size="medium" />
                                    <TreeNode title="关键词1-5" key="0-0-1-0-2" size="medium" />
                                </TreeNode>
                                <TreeNode title="关键词1" key="0-0-0-1" size="medium" />
                            </TreeNode>
                            <TreeNode title="单元2" key="0-0-1" size="medium">
                                <TreeNode title="关键词3" key="0-0-1-0" size="medium" />
                            </TreeNode>
                        </TreeNode>
                    </Tree>
                </div>
            </div>
        );
    }
}
