import React, {PureComponent} from 'react';
import {Tree} from '@baidu/one-ui';

const {TreeNode} = Tree;

export default class Demo extends PureComponent {
    onSelect = (selectedKeys, info) => {
        console.log('selected', selectedKeys, info);
    };

    onCheck = (checkedKeys, info) => {
        console.log('onCheck', checkedKeys, info);
    };

    render() {
        return (
            <div>
                <div>
                    <Tree
                        defaultExpandedKeys={['0-0-0', '0-0-1']}
                        defaultSelectedKeys={['0-0-0', '0-0-1']}
                        defaultCheckedKeys={['0-0-0']}
                        onSelect={this.onSelect}
                        onCheck={this.onCheck}
                        checkable
                        selectable={false}
                    >
                        <TreeNode title="计划1" key="0-0">
                            <TreeNode title="单元1" key="0-0-0">
                                <TreeNode title="关键词1" key="0-0-0-0" />
                                <TreeNode title="关键词1" key="0-0-0-1" />
                            </TreeNode>
                            <TreeNode title="单元2" key="0-0-1">
                                <TreeNode title="关键词3" key="0-0-1-0" />
                            </TreeNode>
                        </TreeNode>
                    </Tree>
                </div>
            </div>
        );
    }
}
