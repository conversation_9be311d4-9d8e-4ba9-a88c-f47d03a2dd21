import React, {PureComponent} from 'react';
import {Tree, IconSvg} from '@baidu/one-ui';

const {TreeNode} = Tree;

export default class Demo extends PureComponent {
    state = {
        expandedKeys: ['0-0-1'],
        selectedKeys: ['0-0-1'],
        checkedKeys: ['0-0-1']
    }

    onSelect = (selectedKeys, info) => {
        console.log('selected', selectedKeys, info);
        this.setState({
            selectedKeys
        });
    };

    onCheck = (checkedKeys, info) => {
        console.log('onCheck', checkedKeys, info);
        this.setState({
            checkedKeys
        });
    };

    onExpand = (expandedKeys, info) => {
        console.log('onExpanded', expandedKeys, info);
        this.setState({
            expandedKeys
        });
    }

    renderFolderIcon = key => {
        const expandedKeys = this.state.expandedKeys;
        if (expandedKeys.indexOf(key) > -1) {
            return <IconSvg type="folder-open" />;
        }
        return <IconSvg type="folder" />;
    }

    render() {
        return (
            <div>
                <br />
                <br />
                图标均为自定义，这个示例只是一个示例
                <br />
                <br />
                <div>
                    <Tree
                        expandedKeys={this.state.expandedKeys}
                        selectedKeys={this.state.selectedKeys}
                        checkedKeys={this.state.checkedKeys}
                        onSelect={this.onSelect}
                        onCheck={this.onCheck}
                        onExpand={this.onExpand}
                        showIcon
                    >
                        <TreeNode title="计划1" key="0-0" icon={this.renderFolderIcon('0-0')}>
                            <TreeNode title="单元1" key="0-0-0" icon={this.renderFolderIcon('0-0-0')}>
                                <TreeNode title="关键词1" key="0-0-0-0" icon={<IconSvg type="file" />} />
                                <TreeNode title="关键词1" key="0-0-0-1" icon={<IconSvg type="file" />} />
                            </TreeNode>
                            <TreeNode title="单元2" key="0-0-1" icon={this.renderFolderIcon('0-0-1')}>
                                <TreeNode title="关键词3" key="0-0-1-0" icon={<IconSvg type="file" />} />
                            </TreeNode>
                        </TreeNode>
                    </Tree>
                </div>
                <br />
                <br />
            </div>
        );
    }
}
