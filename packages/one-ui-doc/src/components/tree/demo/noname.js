import React, {PureComponent} from 'react';
import {Tree} from '@baidu/one-ui';

const {TreeNode} = Tree;

export default class Demo extends PureComponent {
    onSelect = (selectedKeys, info) => {
        console.log('selected', selectedKeys, info);
    };

    onCheck = (checkedKeys, info) => {
        console.log('onCheck', checkedKeys, info);
    };

    render() {
        return (
            <div>
                <div>
                    <br />
                    <br />
                    节点的checkable，selectable, disableCheckbox混合使用
                    <br />
                    <br />
                    <Tree
                        defaultExpandedKeys={['0-0-0', '0-0-1']}
                        defaultSelectedKeys={['0-0-0', '0-0-1']}
                        defaultCheckedKeys={['0-0-0']}
                        onSelect={this.onSelect}
                        onCheck={this.onCheck}
                        checkable
                    >
                        <TreeNode title="计划1" key="0-0">
                            <TreeNode title="单元1" key="0-0-0" >
                                <TreeNode title="关键词1" key="0-0-0-0" disableCheckbox />
                                <TreeNode title="关键词1" key="0-0-0-1" />
                            </TreeNode>
                            <TreeNode title="单元2" key="0-0-1">
                                <TreeNode title="关键词3" key="0-0-1-0" selectable={false} />
                            </TreeNode>
                            <TreeNode title="单元3" key="0-0-3">
                                <TreeNode title="关键词4" key="0-0-3-1" selectable={false} />
                                <TreeNode title="关键词5" key="0-0-3-2" selectable={false} />
                                <TreeNode title="关键词6" key="0-0-3-3" selectable={false} />
                            </TreeNode>
                            <TreeNode title="单元4" key="0-0-4" >
                                <TreeNode title="关键词1" key="0-0-4-0" disableCheckbox />
                                <TreeNode title="关键词1" key="0-0-4-1" />
                            </TreeNode>
                            <TreeNode title="单元5" key="0-0-5">
                                <TreeNode title="关键词3" key="0-0-5-0" selectable={false} />
                            </TreeNode>
                            <TreeNode title="单元3" key="0-0-6">
                                <TreeNode title="关键词4" key="0-0-6-1" selectable={false} disableCheckbox />
                                <TreeNode title="关键词5" key="0-0-6-2" selectable={false} />
                                <TreeNode title="关键词6" key="0-0-6-3" selectable={false} />
                            </TreeNode>
                            <TreeNode title="单元1" key="0-0-7" >
                                <TreeNode title="关键词1" key="0-0-7-0" disableCheckbox />
                                <TreeNode title="关键词1" key="0-0-7-1" />
                            </TreeNode>
                            <TreeNode title="单元2" key="0-0-8">
                                <TreeNode title="关键词3" key="0-0-8-0" selectable={false} />
                            </TreeNode>
                            <TreeNode title="单元3" key="0-0-9">
                                <TreeNode title="关键词4" key="0-0-9-1" selectable={false} disableCheckbox />
                                <TreeNode title="关键词5" key="0-0-9-2" selectable={false} />
                                <TreeNode title="关键词6" key="0-0-9-3" selectable={false} />
                            </TreeNode>
                        </TreeNode>
                    </Tree>
                </div>

            </div>
        );
    }
}
