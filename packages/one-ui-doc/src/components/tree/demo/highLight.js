import React, {PureComponent} from 'react';
import {Tree} from '@baidu/one-ui';

const {TreeNode} = Tree;

export default class Demo extends PureComponent {
    onSelect = (selectedKeys, info) => {
        console.log('selected', selectedKeys, info);
    };

    onCheck = (checkedKeys, info) => {
        console.log('onCheck', checkedKeys, info);
    };

    filterTreeNode = that => {
        const title = that.props.title;
        if (title.indexOf('计划1') > -1) {
            return '计划1';
        }
        return false;
    }

    render() {
        return (
            <div>
                <br />
                <br />
                filterTreeNode是一个函数，传出每一个TreeNode, 返回true或者非空字符串，可以给节点加上current-filter-node类名, 可以自定义样式节点隐藏或显示
                如果title是字符串并且filterTreeNode返回的是非空字符串，则会有高亮样式title中的filterTreeNode返回的部分
                <br />
                <br />
                <div>
                    <Tree
                        defaultExpandedKeys={['0-0-0', '0-0-1']}
                        defaultSelectedKeys={['0-0-0', '0-0-1']}
                        defaultCheckedKeys={['0-0-0', '0-0-1']}
                        onSelect={this.onSelect}
                        onCheck={this.onCheck}
                        filterTreeNode={this.filterTreeNode}
                    >
                        <TreeNode title="计划1,啦啦啦" key="0-0">
                            <TreeNode title="单元1" key="0-0-0">
                                <TreeNode title="关键词1" key="0-0-0-0" />
                                <TreeNode title="关键词1" key="0-0-0-1" />
                            </TreeNode>
                            <TreeNode title="单元2" key="0-0-1">
                                <TreeNode title="关键词3" key="0-0-1-0" />
                            </TreeNode>
                        </TreeNode>
                    </Tree>
                </div>
            </div>
        );
    }
}
