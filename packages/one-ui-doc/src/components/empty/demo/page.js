import React, {useState} from 'react';
import {Empty, Button, Checkbox, Radio} from '@baidu/one-ui';
import {IllustrationNotFound} from 'dls-illustrations-react';

export default () => {
    const [image, setImage] = useState(true);
    const [title, setTitle] = useState(true);
    const [description, setDescription] = useState(true);
    const [actions, setActions] = useState(true);

    return (
        <>
            <div className="demo-controls">
                <Checkbox checked={image} onChange={e => setImage(e.target.checked)}>插图</Checkbox>
                <Checkbox checked={title} onChange={e => setTitle(e.target.checked)}>标题</Checkbox>
                <Checkbox checked={description} onChange={e => setDescription(e.target.checked)}>描述</Checkbox>
                <Checkbox checked={actions} onChange={e => setActions(e.target.checked)}>按钮</Checkbox>
            </div>
            <div style={{display: 'flex', height: 400, alignItems: 'center', justifyContent: 'center'}}>
                <Empty
                    title={title && '找不到该页面'}
                    description={description && '当前页面无法访问，请检查地址栏中的网址，确保您访问的是正确的网址。'}
                    actions={
                        actions
                            && (
                                <>
                                    <Button type="primary">确定</Button>
                                    <Button>取消</Button>
                                </>
                            )
                    }
                >
                    {image && <IllustrationNotFound />}
                </Empty>
            </div>
        </>
    );
};
