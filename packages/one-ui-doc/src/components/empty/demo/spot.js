import React, {useState} from 'react';
import {Empty, Button, Checkbox, Radio} from '@baidu/one-ui';
import {IllustrationSpotNoAccess} from 'dls-illustrations-react';

export default () => {
    const [image, setImage] = useState(true);
    const [title, setTitle] = useState(true);
    /** @type {['medium' | 'small', React.Dispatch<React.SetStateAction<'medium' | 'small'>>]} */
    const [size, setSize] = useState('medium');
    const [description, setDescription] = useState(true);
    const [actions, setActions] = useState(true);

    return (
        <>
            <div className="demo-controls">
                <Checkbox checked={image} onChange={e => setImage(e.target.checked)}>插图</Checkbox>
                <Checkbox checked={title} onChange={e => setTitle(e.target.checked)}>标题</Checkbox>
                <Checkbox checked={description} onChange={e => setDescription(e.target.checked)}>描述</Checkbox>
                <Checkbox checked={actions} onChange={e => setActions(e.target.checked)}>按钮</Checkbox>
                <Radio.Group
                    options={['medium', 'small']}
                    value={size}
                    size="small"
                    type="strong"
                    onChange={e => setSize(e.target.value)}
                />
            </div>
            <div style={{display: 'flex', height: 300, alignItems: 'center', justifyContent: 'center'}}>
                <Empty
                    title={title && '您没有访问权限'}
                    description={description && '请与网络管理员联系，申请访问权限。'}
                    size={size}
                    actions={
                        actions
                            && (
                                <>
                                    <Button type="primary" size={size}>确定</Button>
                                    <Button size={size}>取消</Button>
                                </>
                            )
                    }
                >
                    {image && <IllustrationSpotNoAccess />}
                </Empty>
            </div>
        </>
    );
};
