import React from 'react';
import {Empty} from '@baidu/one-ui';
import {
    IllustrationBlank,
    IllustrationClientError,
    IllustrationForbidden,
    IllustrationNoResults,
    IllustrationNotFound,
    IllustrationReviewError,
    IllustrationReviewPending,
    IllustrationReviewSuccess,
    IllustrationServerError
} from 'dls-illustrations-react';

const illustrations =  [
    IllustrationBlank,
    IllustrationClientError,
    IllustrationForbidden,
    IllustrationNoResults,
    IllustrationNotFound,
    IllustrationReviewError,
    IllustrationReviewPending,
    IllustrationReviewSuccess,
    IllustrationServerError
];

export default () =>
    illustrations.map(Illustration => (
        <Empty
            description={Illustration.displayName}
            key={Illustration.displayName}
        >
            <Illustration />
        </Empty>
    ));