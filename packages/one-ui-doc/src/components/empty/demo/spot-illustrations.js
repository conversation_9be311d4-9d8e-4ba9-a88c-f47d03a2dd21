import React, {useState} from 'react';
import {Empty, Radio} from '@baidu/one-ui';
import {
    IllustrationSpotCleared,
    IllustrationSpotImageError,
    IllustrationSpotLoadError,
    IllustrationSpotMap,
    IllustrationSpotNetworkError,
    IllustrationSpotNoAccess,
    IllustrationSpotNoContent,
    IllustrationSpotNoFavorites,
    IllustrationSpotNoHistory,
    IllustrationSpotNoImage,
    IllustrationSpotNoInternet,
    IllustrationSpotNoMessages,
    IllustrationSpotNoResults,
    IllustrationSpotNoStore,
    IllustrationSpotNoVideo,
    IllustrationSpotPresentation,
    IllustrationSpotServerError,
    IllustrationSpotTryLater,
    IllustrationSpotVideoError,
    IllustrationSpotBarChart,
    IllustrationSpotFunnelChart,
    IllustrationSpotGaugeChart,
    IllustrationSpotLineChart,
    IllustrationSpotPieChart,
    IllustrationSpotRadarChart,
    IllustrationSpotRelationshipChart,
    IllustrationSpotWordCloud
} from 'dls-illustrations-react';

const illustrations =  [
    IllustrationSpotCleared,
    IllustrationSpotImageError,
    IllustrationSpotLoadError,
    IllustrationSpotMap,
    IllustrationSpotNetworkError,
    IllustrationSpotNoAccess,
    IllustrationSpotNoContent,
    IllustrationSpotNoFavorites,
    IllustrationSpotNoHistory,
    IllustrationSpotNoImage,
    IllustrationSpotNoInternet,
    IllustrationSpotNoMessages,
    IllustrationSpotNoResults,
    IllustrationSpotNoStore,
    IllustrationSpotNoVideo,
    IllustrationSpotPresentation,
    IllustrationSpotServerError,
    IllustrationSpotTryLater,
    IllustrationSpotVideoError,
    IllustrationSpotBarChart,
    IllustrationSpotFunnelChart,
    IllustrationSpotGaugeChart,
    IllustrationSpotLineChart,
    IllustrationSpotPieChart,
    IllustrationSpotRadarChart,
    IllustrationSpotRelationshipChart,
    IllustrationSpotWordCloud
];

export default () => {
    /** @type {['medium' | 'small', React.Dispatch<React.SetStateAction<'medium' | 'small'>>]} */
    const [size, setSize] = useState('medium');
    return (
        <>
            <Radio.Group
                options={['medium', 'small']}
                value={size}
                size="small"
                type="strong"
                onChange={e => setSize(e.target.value)}
            />
            <br />
            {
                illustrations.map(Illustration => (
                    <Empty
                        description={Illustration.displayName}
                        key={Illustration.displayName}
                        size={size}
                        style={{width: 200, margin: 10}}
                    >
                        <Illustration />
                    </Empty>
                ))
            }
        </>
    );
};
