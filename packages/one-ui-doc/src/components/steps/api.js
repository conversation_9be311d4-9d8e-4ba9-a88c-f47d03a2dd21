export default {
    Steps: [
        {
            param: 'className',
            type: 'string',
            desc: '可以自定义steps的className',
            option: '',
            default: ''
        },
        {
            param: 'style',
            type: 'object',
            desc: '可以自定义steps的style',
            option: '',
            default: ''
        },
        {
            param: 'current',
            type: 'number',
            desc: '当前的步骤',
            option: '',
            default: '0'
        },
        {
            param: 'direction',
            type: 'string',
            desc: '当前步骤条的类型，有[horizontal，vertical]',
            option: 'horizontal | vertical',
            default: 'horizontal'
        },
        {
            param: 'placement',
            type: 'string',
            desc: '气泡框位置，可选 top left right bottom topLeft topRight bottomLeft bottomRight leftTop leftBottom rightTop rightBottom',
            option: 'top left right bottom topLeft topRight bottomLeft bottomRight leftTop leftBottom rightTop rightBottom',
            default: 'bottom'
        },
        {
            param: 'initialStep',
            type: 'number',
            desc: 'initialStep 初始化的步骤条',
            option: '',
            default: '0'
        },
        {
            param: 'labelPlacement',
            type: 'string',
            desc: '描述文案放置的位置',
            option: 'horizontal | vertical',
            default: 'horizontal'
        },
        {
            param: 'onClickStep',
            type: 'Function(step) step为当前点击的step',
            desc: 'onClickStep 暴露点击step的函数',
            option: '',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: '步骤条的尺寸',
            option: 'small | medium',
            default: 'medium'
        }
    ],
    StepsStep: [{
        param: 'className',
        type: 'string',
        desc: '可以自定义当前step的className',
        option: '',
        default: ''
    },
    {
        param: 'style',
        type: 'object',
        desc: '可以自定义当前step的style',
        option: '',
        default: ''
    }, {
        param: 'description',
        type: 'string|ReactNode',
        desc: '步骤的详情描述',
        option: '',
        default: ''
    }, {
        param: 'status',
        type: 'string',
        desc: '指定状态。当不配置该属性时，会使用 Steps 的 current 来自动指定状态。可选：wait process finish error',
        option: 'wait',
        default: ''
    }, {
        param: 'title',
        type: 'string',
        desc: '标题',
        option: '',
        default: ''
    }, {
        param: 'status',
        type: 'string',
        desc: '当前步骤的状态， wait, process,finish, error',
        option: 'wait, process,finish, error',
        default: 'process'
    },
    {
        param: 'showTipWhenHover',
        type: 'bool',
        desc: 'hover时是否展示tooltip',
        option: '',
        default: 'true'
    }, {
        param: 'icon',
        type: 'reactNode',
        desc: 'step的icon',
        option: '',
        default: ''
    }, {
        param: 'icons',
        type: 'object',
        desc: '{finish, error}可以自定义状态为finish和error的icon',
        option: '',
        default: ''
    }, {
        param: 'hoverTip',
        type: 'string',
        desc: '自定义hover的tip话术',
        option: '',
        default: ''
    }]
};
