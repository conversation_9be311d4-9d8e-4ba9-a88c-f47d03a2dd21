import React, {useState} from 'react';
import {Steps, Button, Checkbox, Radio} from '@baidu/one-ui';

const Step = Steps.Step;
const MAX_STEPS = 5;

export default () => {
    const [step, setStep] = useState(0);
    const [vertical, setVertical] = useState(false);
    const [dot, setDot] = useState(true);
    const [size, setSize] = useState('medium');
    return (
        <>
            <div className="demo-controls">
                <Checkbox checked={vertical} onChange={e => setVertical(e.target.checked)}>纵向</Checkbox>
                <Checkbox checked={dot} onChange={e => setDot(e.target.checked)}>点状</Checkbox>
                <Radio.Group
                    type="strong"
                    size="small"
                    options={['small', 'medium']}
                    value={size}
                    onChange={e => setSize(e.target.value)}
                />
                <Button onClick={() => setStep(step - 1)} disabled={step === 0} size="xsmall">上一步</Button>
                <Button onClick={() => setStep(step + 1)} disabled={step === MAX_STEPS - 1} size="xsmall">下一步</Button>
            </div>
            <br />
            <Steps
                current={step}
                onChange={step => setStep(step)}
                type={dot ? 'dot' : 'default'}
                direction={vertical ? 'vertical' : 'horizontal'}
                size={size}
            >
                {Array.from({length: MAX_STEPS}).map((_, index) => (
                    <Step
                        key={index}
                        title={`设置步骤${index + 1}`}
                        description={`描述文案${index + 1}`}
                        status={(index + 1) % 2 === 0 ? 'error' : undefined}
                    />
                ))}
            </Steps>
        </>
    );
};
