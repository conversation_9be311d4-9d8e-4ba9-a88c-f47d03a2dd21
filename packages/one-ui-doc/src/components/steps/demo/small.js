import React, {PureComponent} from 'react';
import {Steps, Button} from '@baidu/one-ui';

const Step = Steps.Step;

export default class Normal extends PureComponent {
    state = {step: 0};

    handleAdd = () => {
        this.setState(prevState => ({
            step: prevState.step + 1
        }));
    }

    handleMinus = () => {
        this.setState(prevState => ({
            step: prevState.step - 1
        }));
    }

    onClickStep = step => {
        --step;
        this.setState({
            step
        });
    }

    render() {
        return (
            <div>
                <Steps current={this.state.step} size="small" onClickStep={this.onClickStep}>
                    <Step title="设置推广计划" description="描述文案" />
                    <Step title="设置推广单元" description="描述文案二" />
                    <Step title="提交和预览" description="描述文案三" />
                </Steps>
                <br />
                <br />
                <br />
                <Steps current={this.state.step} size="small" onClickStep={this.onClickStep} labelPlacement="vertical">
                    <Step title="设置推广计划" description="描述文案" />
                    <Step title="设置推广单元" description="描述文案二" />
                    <Step title="提交和预览" description="描述文案三" />
                </Steps>
                <div style={{marginTop: 60}}>
                    {
                        this.state.step < 3 && (
                            <div style={{marginRight: 20, display: 'inline-block'}}>
                                <Button onClick={this.handleAdd}>下一步</Button>
                            </div>
                        )
                    }
                    {
                        this.state.step > 0 && (
                            <Button onClick={this.handleMinus}>前一步</Button>
                        )
                    }
                </div>
            </div>
        );
    }
}
