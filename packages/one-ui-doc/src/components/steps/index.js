import BaseComponent from '../base';

export default {
    steps: {
        value: BaseComponent,
        label: 'Steps 步骤条',
        demos: [
            {
                title: '基本用法',
                desc: '普通步骤条',
                source: 'normal'
            },
            {
                title: '标签位置',
                desc: '普通步骤条, 通过labelPlacement来区分放置的位置',
                source: 'title'
            },
            {
                title: '小尺寸',
                desc: '普通步骤条, size为small的迷你版本',
                source: 'small'
            },
            {
                title: '纵向',
                desc: '普通步骤条, direction用来区分是横着的还是纵轴的步骤条',
                source: 'vertical'
            },
            {
                title: '状态',
                desc: '普通步骤条, 通过status来区分不同状态',
                source: 'status'
            },
            {
                title: '类型',
                desc: '类型分为：默认、点状',
                source: 'type'
            }
        ],
        apis: [
            {
                apiKey: 'Steps',
                title: 'Steps'
            },
            {
                apiKey: 'Step',
                title: 'Steps.Step'
            }
        ]
    }
};
