import BaseComponent from '../base';

export default {
    grid: {
        value: BaseComponent,
        label: 'Grid 栅格',
        demos: [
            {
                title: '基础栅格',
                desc: '基础栅格',
                source: 'normal'
            },
            {
                title: '间隔',
                desc: '基础栅格-间隔',
                source: 'gutter'
            },
            {
                title: '偏移',
                desc: '基础栅格-偏移，左右偏移 列偏移。 使用 offset 可以将列向右侧偏。'
                    + '例如，offset={4} 将元素向右侧偏移了 4 个列（column）的宽度。',
                source: 'offset'
            },
            {
                title: 'Flex布局',
                desc: 'Flex 布局 使用 row-flex 定义 flex 布局，其子元素根据不同的值 '
                    + 'start,center,end,space-between,space-around，分别定义其在父节点里面的排版方式。',
                source: 'flex'
            },
            {
                title: 'Flex布局-对齐',
                desc: 'Flex 对齐 Flex 子元素垂直对齐。 使用align',
                source: 'align'
            },
            {
                title: 'Flex布局-顺序',
                desc: '通过 Flex 布局的 Order 来改变元素的排序。',
                source: 'order'
            }
        ],
        apis: [
            {
                apiKey: 'GridRow',
                title: 'Grid.Row'
            },
            {
                apiKey: 'GridCol',
                title: 'Grid.Col'
            }
        ]
    }
};
