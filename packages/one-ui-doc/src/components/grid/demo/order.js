import React, {PureComponent} from 'react';
import {Grid} from '@baidu/one-ui';

const Row = Grid.Row;
const Col = Grid.Col;

export default class Normal extends PureComponent {
    render() {
        const rowStyle = {
            height: '50px',
            marginBottom: '10px',
            textAlign: 'center',
            color: 'white',
            lineHeight: '50px'
        };
        return (
            <div>
                <Row type="flex" style={rowStyle}>
                    <Col span={4} style={{background: '#00a0e9', height: '100%'}} order={4}>order-4-first-line</Col>
                    <Col span={4} style={{background: 'rgba(0,160,233,0.7)', height: '100%'}} order={3}>order-3-second-line</Col>
                    <Col span={4} style={{background: '#00a0e9', height: '100%'}} order={2}>order-2-third-line</Col>
                    <Col span={4} style={{background: 'rgba(0,160,233,0.7)', height: '100%'}} order={1}>order-1-forth-line</Col>
                </Row>
            </div>
        );
    }
}
