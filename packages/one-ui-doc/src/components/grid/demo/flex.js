import React, {PureComponent} from 'react';
import {Grid} from '@baidu/one-ui';

const Row = Grid.Row;
const Col = Grid.Col;

export default class Normal extends PureComponent {
    render() {
        const rowStyle = {
            height: '50px',
            marginBottom: '10px',
            textAlign: 'center',
            color: 'white',
            lineHeight: '50px'
        };
        return (
            <div>
                <Row type="flex" justify="start" style={rowStyle}>
                    <Col span={4} style={{background: '#00a0e9', height: '100%'}}>col-4</Col>
                    <Col span={4} style={{background: 'rgba(0,160,233,0.7)', height: '100%'}}>col-4</Col>
                    <Col span={4} style={{background: '#00a0e9', height: '100%'}}>col-4</Col>
                    <Col span={4} style={{background: 'rgba(0,160,233,0.7)', height: '100%'}}>col-4</Col>
                </Row>

                <Row type="flex" justify="center" style={rowStyle}>
                    <Col span={4} style={{background: '#00a0e9', height: '100%'}}>col-4</Col>
                    <Col span={4} style={{background: 'rgba(0,160,233,0.7)', height: '100%'}}>col-4</Col>
                    <Col span={4} style={{background: '#00a0e9', height: '100%'}}>col-4</Col>
                    <Col span={4} style={{background: 'rgba(0,160,233,0.7)', height: '100%'}}>col-4</Col>
                </Row>

                <Row type="flex" justify="end" style={rowStyle}>
                    <Col span={4} style={{background: '#00a0e9', height: '100%'}}>col-4</Col>
                    <Col span={4} style={{background: 'rgba(0,160,233,0.7)', height: '100%'}}>col-4</Col>
                    <Col span={4} style={{background: '#00a0e9', height: '100%'}}>col-4</Col>
                    <Col span={4} style={{background: 'rgba(0,160,233,0.7)', height: '100%'}}>col-4</Col>
                </Row>

                <Row type="flex" justify="space-between" style={rowStyle}>
                    <Col span={4} style={{background: '#00a0e9', height: '100%'}}>col-4</Col>
                    <Col span={4} style={{background: 'rgba(0,160,233,0.7)', height: '100%'}}>col-4</Col>
                    <Col span={4} style={{background: '#00a0e9', height: '100%'}}>col-4</Col>
                    <Col span={4} style={{background: 'rgba(0,160,233,0.7)', height: '100%'}}>col-4</Col>
                </Row>

                <Row type="flex" justify="space-around" style={rowStyle}>
                    <Col span={4} style={{background: '#00a0e9', height: '100%'}}>col-4</Col>
                    <Col span={4} style={{background: 'rgba(0,160,233,0.7)', height: '100%'}}>col-4</Col>
                    <Col span={4} style={{background: '#00a0e9', height: '100%'}}>col-4</Col>
                    <Col span={4} style={{background: 'rgba(0,160,233,0.7)', height: '100%'}}>col-4</Col>
                </Row>
            </div>
        );
    }
}
