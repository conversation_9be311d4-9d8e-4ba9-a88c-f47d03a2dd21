import React, {PureComponent} from 'react';
import {Grid} from '@baidu/one-ui';

const Row = Grid.Row;
const Col = Grid.Col;

export default class Normal extends PureComponent {
    render() {
        const rowStyle = {
            height: '50px',
            marginBottom: '10px',
            textAlign: 'center',
            color: 'white',
            lineHeight: '50px'
        };
        return (
            <div>
                <p>Align Top</p>
                <Row type="flex" justify="center" align="top" style={rowStyle}>
                    <Col span={4}><div style={{background: '#00a0e9', height: '40px'}}>col-4</div></Col>
                    <Col span={4}><div style={{background: 'rgba(0,160,233,0.7)', height: '60px'}}>col-4</div></Col>
                    <Col span={4}><div style={{background: '#00a0e9', height: '70px'}}>col-4</div></Col>
                    <Col span={4}><div style={{background: 'rgba(0,160,233,0.7)', height: '50px'}}>col-4</div></Col>
                </Row>

                <p>Align Center</p>
                <Row type="flex" justify="space-around" align="middle" style={rowStyle}>
                    <Col span={4} style={{background: '#00a0e9', height: '40px'}}><div>col-4</div></Col>
                    <Col span={4} style={{background: 'rgba(0,160,233,0.7)', height: '60px'}}><div>col-4</div></Col>
                    <Col span={4} style={{background: '#00a0e9', height: '80px'}}><div>col-4</div></Col>
                    <Col span={4} style={{background: 'rgba(0,160,233,0.7)', height: '50px'}}><div>col-4</div></Col>
                </Row>

                <p>Align Bottom</p>
                <Row type="flex" justify="space-between" align="bottom" style={rowStyle}>
                    <Col span={4} style={{background: '#00a0e9', height: '40px'}}><div value={100}>col-4</div></Col>
                    <Col span={4} style={{background: 'rgba(0,160,233,0.7)', height: '80px'}}><div value={50}>col-4</div></Col>
                    <Col span={4} style={{background: '#00a0e9', height: '60px'}}><div value={120}>col-4</div></Col>
                    <Col span={4} style={{background: 'rgba(0,160,233,0.7)', height: '70px'}}><div value={80}>col-4</div></Col>
                </Row>
            </div>
        );
    }
}
