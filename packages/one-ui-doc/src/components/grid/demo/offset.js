import React, {PureComponent} from 'react';
import {Grid} from '@baidu/one-ui';

const Row = Grid.Row;
const Col = Grid.Col;

export default class Normal extends PureComponent {
    render() {
        const rowStyle = {
            height: '50px',
            marginBottom: '10px',
            textAlign: 'center',
            color: 'white',
            lineHeight: '50px'
        };
        return (
            <div>
                <Row style={rowStyle}>
                    <Col span={8} style={{background: '#00a0e9', height: '100%'}}>col-8</Col>
                    <Col span={8} offset={8} style={{background: '#00a0e9', height: '100%'}}>col-8 col-offset-8</Col>
                </Row>
                <Row style={rowStyle}>
                    <Col span={6} offset={6} style={{background: '#00a0e9', height: '100%'}}>col-6 col-offset-6</Col>
                    <Col span={6} offset={6} style={{background: '#00a0e9', height: '100%'}}>col-6 col-offset-6</Col>
                </Row>
                <Row style={rowStyle}>
                    <Col span={12} offset={6} style={{background: '#00a0e9', height: '100%'}}>col-12 col-offset-6</Col>
                </Row>
            </div>
        );
    }
}
