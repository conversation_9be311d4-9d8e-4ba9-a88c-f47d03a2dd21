import React, {PureComponent} from 'react';
import {Grid} from '@baidu/one-ui';

const Row = Grid.Row;
const Col = Grid.Col;

export default class Normal extends PureComponent {
    render() {
        const rowStyle = {
            height: '50px',
            marginBottom: '10px',
            textAlign: 'center',
            color: 'white',
            lineHeight: '50px'
        };
        return (
            <div>
                <Row style={rowStyle} gutter={16}>
                    <Col span={4}>
                        <div style={{background: '#00a0e9', height: '100%'}}>col-6</div>
                    </Col>
                    <Col span={4}>
                        <div style={{background: '#00a0e9', height: '100%'}}>col-6</div>
                    </Col>
                    <Col span={4}>
                        <div style={{background: '#00a0e9', height: '100%'}}>col-6</div>
                    </Col>
                    <Col span={4}>
                        <div style={{background: '#00a0e9', height: '100%'}}>col-6</div>
                    </Col>
                </Row>
            </div>
        );
    }
}
