import React, {PureComponent} from 'react';
import {Grid} from '@baidu/one-ui';

const Row = Grid.Row;
const Col = Grid.Col;

export default class Normal extends PureComponent {
    render() {
        const rowStyle = {
            height: '50px',
            marginBottom: '10px',
            textAlign: 'center',
            color: 'white',
            lineHeight: '50px'
        };
        return (
            <div>
                <div>
                    <Row style={rowStyle}>
                        <Col span={12} style={{background: '#00a0e9', height: '100%'}}>col-12</Col>
                        <Col span={12} style={{background: 'rgba(0,160,233,0.7)', height: '100%'}}>col-12</Col>
                    </Row>
                    <Row style={rowStyle}>
                        <Col span={8} style={{background: '#00a0e9', height: '100%'}}>col-8</Col>
                        <Col span={8} style={{background: 'rgba(0,160,233,0.7)', height: '100%'}}>col-8</Col>
                        <Col span={8} style={{background: '#00a0e9', height: '100%'}}>col-8</Col>
                    </Row>
                    <Row style={rowStyle}>
                        <Col span={6} style={{background: '#00a0e9', height: '100%'}}>col-6</Col>
                        <Col span={6} style={{background: 'rgba(0,160,233,0.7)', height: '100%'}}>col-6</Col>
                        <Col span={6} style={{background: '#00a0e9', height: '100%'}}>col-6</Col>
                        <Col span={6} style={{background: 'rgba(0,160,233,0.7)', height: '100%'}}>col-6</Col>
                    </Row>
                </div>
            </div>
        );
    }
}
