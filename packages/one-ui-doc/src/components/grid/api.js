export default {
    GridCol: [
        {
            param: 'className',
            type: 'String',
            desc: '自定义类名',
            option: '',
            default: ''
        },
        {
            param: 'offset',
            type: 'number',
            desc: '栅格左侧的偏移间隔格数，间隔内不可以有栅格',
            option: '',
            default: '0'
        },
        {
            param: 'order',
            type: 'number',
            desc: '栅格顺序，flex 布局模式下有效',
            option: '',
            default: '0'
        },
        {
            param: 'span',
            type: 'number',
            desc: '栅格占位格数，为 0 时相当于 display: none',
            option: '',
            default: ''
        },
        {
            param: 'push',
            type: 'number',
            desc: '栅格向右移动格数',
            option: '',
            default: ''
        },
        {
            param: 'pull',
            type: 'number',
            desc: '栅格向左移动格数',
            option: '',
            default: ''
        }
    ],
    GridRow: [
        {
            param: 'className',
            type: 'String',
            desc: '自定义类名',
            option: '',
            default: ''
        },
        {
            param: 'align',
            type: 'string',
            desc: 'flex 布局下的垂直对齐方式：top middle bottom',
            option: 'top middle bottom',
            default: 'top'
        },
        {
            param: 'gutter',
            type: 'number',
            desc: '栅格间隔，可以写成像素值',
            option: '',
            default: '0'
        },
        {
            param: 'justify',
            type: 'string',
            desc: 'flex 布局下的水平排列方式：start end center space-around space-between',
            option: 'start end center space-around space-between',
            default: 'start'
        },
        {
            param: 'type',
            type: 'string',
            desc: '布局模式，可选 flex',
            option: 'flex',
            default: ''
        }
    ]
};
