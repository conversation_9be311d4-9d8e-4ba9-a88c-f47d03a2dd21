import BaseComponent from '../base';

export default {
    link: {
        value: BaseComponent,
        label: 'Link 文字链',
        demos: [
            {
                title: '文字链',
                desc: '普通文字链',
                source: 'normal'
            },
            {
                title: '尺寸',
                desc: '普通文字链',
                source: 'size'
            },
            {
                title: '禁用',
                desc: '普通文字链',
                source: 'disabled'
            },
            {
                title: '结合react-router',
                desc: '普通文字链',
                source: 'aTag'
            }
        ],
        apis: [
            {
                apiKey: 'Link',
                title: 'Link'
            }
        ]
    }
};
