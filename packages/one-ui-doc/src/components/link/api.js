export default {
    Link: [
        {
            param: 'type',
            type: 'string',
            desc: '设置按钮类型，共有normal（普通），strong（加强）两种，默认normal',
            option: 'strong|normal',
            default: 'normal'
        },
        {
            param: 'target',
            type: 'string',
            desc: "文字链的链接打开类型，原a标签html属性，默认'_self'，取值有'_self'、'_blank'、'_parent'、'top'",
            option: "'_self'、'_blank'、'_parent'、'top'",
            default: '_self'
        },
        {
            param: 'className',
            type: 'string',
            desc: '可自定义按钮的className',
            option: '',
            default: ''
        },
        {
            param: 'toUrl',
            type: 'string',
            desc: '文字链的链接地址',
            option: '',
            default: 'isRequired'
        },
        {
            param: 'size',
            type: 'string',
            desc: '文字链的尺寸',
            option: '当前支持两个尺寸，中号和小尺寸，默认中号尺寸|small、medium',
            default: 'medium'
        },
        {
            param: 'isAtag',
            type: 'bool',
            desc: '设置link是否是a标签',
            option: '',
            default: 'false'
        },
        {
            param: 'disabled',
            type: 'boolean',
            desc: '禁用状态',
            option: '',
            default: 'false'
        }
    ]
};
