export default {
    Tooltip: [
        {
            param: 'getPopupContainer',
            type: 'Function(triggerNode)',
            desc: '浮层渲染父节点，默认渲染到 body 上',
            option: '',
            default: '() => document.body'
        },
        {
            param: 'overlayClassName',
            type: 'string',
            desc: '弹层类名',
            option: '',
            default: ''
        },
        {
            param: 'title',
            type: 'ReactNode | string',
            desc: '弹层的内容',
            option: '',
            default: ''
        },
        {
            param: 'placement',
            type: 'string',
            desc: '气泡框位置，可选 top left right bottom topLeft topRight bottomLeft bottomRight leftTop leftBottom rightTop rightBottom',
            option: '',
            default: 'top'
        },
        {
            param: 'trigger',
            type: 'string',
            desc: '触发行为，可选 hover|click',
            option: 'hover|click',
            default: 'hover'
        },
        {
            param: 'visible',
            type: 'boolean',
            desc: '用于手动控制浮层显隐',
            option: 'hover|click',
            default: 'false'
        },
        {
            param: 'onVisibleChange',
            type: 'Function(visible)',
            desc: '显示隐藏的回调',
            option: '',
            default: ''
        },
        {
            param: 'type',
            type: 'string',
            desc: 'tooltip类型， 分为深色系和浅色系',
            option: 'light | dark',
            default: 'light'
        }
    ]
};
