import React, {PureComponent} from 'react';
import {Tooltip, Button} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    render() {
        const text = <span>prompt text</span>;
        const buttonWidth = 70;
        return (
            <div>
                <div style={{marginLeft: buttonWidth, whiteSpace: 'nowrap'}}>
                    <Tooltip placement="topLeft" title={text} type="dark">
                        <Button>TL</Button>
                    </Tooltip>
                    <Tooltip placement="top" title={text} type="dark">
                        <Button>Top</Button>
                    </Tooltip>
                    <Tooltip placement="topRight" title={text} type="dark">
                        <Button>TR</Button>
                    </Tooltip>
                </div>
                <div style={{width: buttonWidth, float: 'left'}}>
                    <Tooltip placement="leftTop" title={text} type="dark">
                        <Button>LT</Button>
                    </Tooltip>
                    <Tooltip placement="left" title={text} type="dark">
                        <Button>Left</Button>
                    </Tooltip>
                    <Tooltip placement="leftBottom" title={text} type="dark">
                        <Button>LB</Button>
                    </Tooltip>
                </div>
                <div style={{width: buttonWidth, marginLeft: (buttonWidth * 4) + 24}}>
                    <Tooltip placement="rightTop" title={text} type="dark">
                        <Button>RT</Button>
                    </Tooltip>
                    <Tooltip placement="right" title={text} type="dark">
                        <Button>Right</Button>
                    </Tooltip>
                    <Tooltip placement="rightBottom" title={text} type="dark">
                        <Button>RB</Button>
                    </Tooltip>
                </div>
                <div style={{marginLeft: buttonWidth, clear: 'both', whiteSpace: 'nowrap'}}>
                    <Tooltip placement="bottomLeft" title={text} type="dark">
                        <Button>BL</Button>
                    </Tooltip>
                    <Tooltip placement="bottom" title={text} type="dark">
                        <Button>Bottom</Button>
                    </Tooltip>
                    <Tooltip placement="bottomRight" title={text} type="dark">
                        <Button>BR</Button>
                    </Tooltip>
                </div>
            </div>
        );
    }
}
