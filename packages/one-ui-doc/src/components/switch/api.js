export default {
    Switch: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义样式名',
            option: '',
            default: ''
        },
        {
            param: 'checked',
            type: 'boolean',
            desc: '是否选中-受控',
            option: '',
            default: 'false'
        },
        {
            param: 'defaultChecked',
            type: 'boolean',
            desc: '是否选中-非受控',
            option: '',
            default: 'false'
        },
        {
            param: 'disabled',
            type: 'boolean',
            desc: '是否不可操作',
            option: '',
            default: 'false'
        },
        {
            param: 'loading',
            type: 'boolean',
            desc: '是否正在加载',
            option: '',
            default: 'false'
        },
        {
            param: 'size',
            type: 'string',
            desc: '型号大小',
            option: 'xsmall、small、medium',
            default: 'medium'
        },
        {
            param: 'onChange',
            type: 'Function(checked: boolean)',
            desc: '变化时回调函数',
            option: '',
            default: ''
        }
    ]
};
