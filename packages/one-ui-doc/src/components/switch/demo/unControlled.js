import React, {PureComponent} from 'react';
import {Switch} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {
        checked: true
    }

    onChange = checked => {
        this.setState({
            checked
        });
    }

    render() {
        return (
            <div>
                <div>标准尺寸开关，开启状态、关闭状态</div>
                <br />
                <br />
                非受控使用
                <br />
                <br />
                <Switch defaultChecked/>
                <br />
                <br />
                受控使用
                <br />
                <br />
                <Switch checked={this.state.checked} onChange={this.onChange} />
                <br />
                <br />
            </div>
        );
    }
}
