import BaseComponent from '../base';

export default {
    switch: {
        value: BaseComponent,
        label: 'Switch 开关',
        demos: [
            {
                title: '非受控',
                desc: '非受控开关',
                source: 'unControlled'
            },
            {
                title: '尺寸',
                desc: '开关分三个尺寸{xsmall} {small} {medium}; 两种状态 {开启状态} {关闭状态}',
                source: 'normal'
            },
            {
                title: '禁用',
                desc: '各种状态的不可用开关 {开启中且不可用} {关闭中且不可用}',
                source: 'disabled'
            },
            {
                title: '加载中',
                desc: '加载中开关 {开启且加载中} {关闭且加载中}',
                source: 'loading'
            }
        ],
        apis: [
            {
                apiKey: 'Switch',
                title: 'Switch'
            }
        ]
    }
};
