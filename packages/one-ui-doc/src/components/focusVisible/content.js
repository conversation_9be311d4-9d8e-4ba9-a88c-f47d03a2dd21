import React from 'react';
import Highlight from 'react-highlight';
import {Link} from '@baidu/one-ui';

import Title from '../../common/title';
import {menu} from '../../config';
// import './style.less';

export default props => {
    // eslint-disable-next-line react/prop-types
    const id = props.id;
    const {label: title} = menu[id] || {};
    return (
        <div>
            <Title title={title} />
            <div className="doc">
                <div className="demo-home-page-title">
                    <div>
                        通常我们会为交互元素定义 :focus 状态下的样式，以增强键盘可访问性。但在使用鼠标进行点击时，多数浏览器会使
                        button 等元素处于 :focus 状态。在多个按钮并列时，这容易使人产生「按钮被选中」的错觉。
                        <Link type="strong" target="_blank" toUrl="https://drafts.csswg.org/selectors-4/#the-focusvisible-pseudo">
                            CSS Selectors Level 4 草案中的 :focus-visible 伪类选择器
                        </Link>
                        为我们提供了选择更精确的聚焦状态的能力。
                    </div>
                    <div>
                        Chrome 浏览器在默认状态下对 button 元素就有类似的处理。
                    </div>
                    <Link type="strong" target="_blank" toUrl="https://github.com/WICG/focus-visible/blob/master/explainer.md">
                        :focus-visible 详细说明
                    </Link>
                </div>
                <div className="demo-home-page-title" style={{marginTop: '40px'}}>
                    <div style={{fontSize: '20px', fontWeight: 'bolder'}}>
                        使用
                    </div>
                    <div>
                        one-ui 的默认样式 依赖 :focus-visible 的 polyfill 才能提供最好的交互效果。使用时，需要自行在项目中进行引入：
                    </div>
                    <Highlight className="javascript">
                        {'import \'focus-visible\';'}
                    </Highlight>
                    <div style={{fontSize: '20px', fontWeight: 'bolder'}}>
                        兼容性
                    </div>
                    <div>
                        当需要支持 IE9 时，由于 WICG 的 polyfill 不会自行引入其它 polyfill，故还需要引入 classList 的兼容脚本（需自行安装）：
                    </div>
                    <Highlight className="bash">
                        $ npm i --save classlist-polyfill
                    </Highlight>
                    <Highlight className="javascript">
                        {'import \'classlist-polyfill\';'}
                    </Highlight>
                    <div>
                        使用了 polyfill 以后 :focus 样式在鼠标、键盘激活时均会展现，而使用了 .focus-visible 样式仅在通过键盘激活时展现。
                        需注意对 :focus-visible 的处理在 Firefox 和 Safari 下无效，目前在这两个浏览器下点击不会使按钮处于 :focus 状态。
                    </div>
                </div>
            </div>
        </div>
    );
};
