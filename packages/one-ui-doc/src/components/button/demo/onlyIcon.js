import React, {PureComponent} from 'react';
import {Button} from '@baidu/one-ui';
import {
    IconHeartCancel
} from 'dls-icons-react';


export default class Normal extends PureComponent {
    render() {
        return (
            <div>
                <div style={{marginTop: '20px'}}>
                    <Button type="normal" icon={IconHeartCancel} size="large" style={{marginRight: '20px'}} />
                    <Button type="normal" icon={IconHeartCancel} size="medium" style={{marginRight: '20px'}} />
                    <Button type="normal" icon={IconHeartCancel} size="small" style={{marginRight: '20px'}} />
                </div>
                <div style={{marginTop: '20px'}}>
                    <Button type="basic" icon={IconHeartCancel} size="large" style={{marginRight: '20px'}} />
                    <Button type="basic" icon={IconHeartCancel} size="medium" style={{marginRight: '20px'}} />
                    <Button type="basic" icon={IconHeartCancel} size="small" style={{marginRight: '20px'}} />
                </div>
                <div style={{marginTop: '20px'}}>
                    <Button type="strong" icon={IconHeartCancel} size="large" style={{marginRight: '20px'}} />
                    <Button type="strong" icon={IconHeartCancel} size="medium" style={{marginRight: '20px'}} />
                    <Button type="strong" icon={IconHeartCancel} size="small" style={{marginRight: '20px'}} />
                </div>
                <div style={{marginTop: '20px'}}>
                    <Button type="primary" icon={IconHeartCancel} size="large" style={{marginRight: '20px'}} />
                    <Button type="primary" icon={IconHeartCancel} size="medium" style={{marginRight: '20px'}} />
                    <Button type="primary" icon={IconHeartCancel} size="small" style={{marginRight: '20px'}} />
                </div>
                <div style={{marginTop: '20px'}}>
                    <Button type="translucent" icon={IconHeartCancel} size="large" style={{marginRight: '20px'}} />
                    <Button type="translucent" icon={IconHeartCancel} size="medium" style={{marginRight: '20px'}} />
                    <Button type="translucent" icon={IconHeartCancel} size="small" style={{marginRight: '20px'}} />
                </div>
                <div style={{marginTop: '20px'}}>
                    <Button type="text" icon={IconHeartCancel} size="large" style={{marginRight: '20px'}} />
                    <Button type="text" icon={IconHeartCancel} size="medium" style={{marginRight: '20px'}} />
                    <Button type="text" icon={IconHeartCancel} size="small" style={{marginRight: '20px'}} />
                </div>
                <div style={{marginTop: '20px'}}>
                    <Button type="text-strong" icon={IconHeartCancel} size="large" style={{marginRight: '20px'}} />
                    <Button type="text-strong" icon={IconHeartCancel} size="medium" style={{marginRight: '20px'}} />
                    <Button type="text-strong" icon={IconHeartCancel} size="small" style={{marginRight: '20px'}} />
                </div>
                <div style={{marginTop: '20px'}}>
                    <Button type="text-aux" icon={IconHeartCancel} size="large" style={{marginRight: '20px'}} />
                    <Button type="text-aux" icon={IconHeartCancel} size="medium" style={{marginRight: '20px'}} />
                    <Button type="text-aux" icon={IconHeartCancel} size="small" style={{marginRight: '20px'}} />
                </div>
                <div style={{marginTop: '20px'}}>
                    纯图标按钮-disabled状态
                    <div style={{marginTop: '20px'}}>
                        <Button type="normal" icon={IconHeartCancel} disabled />
                    </div>
                    <div style={{marginTop: '20px'}}>
                        <Button type="basic" icon={IconHeartCancel} disabled />
                    </div>
                    <div style={{marginTop: '20px'}}>
                        <Button type="strong" icon={IconHeartCancel} disabled />
                    </div>
                    <div style={{marginTop: '20px'}}>
                        <Button type="primary" icon={IconHeartCancel} disabled />
                    </div>
                    <div style={{marginTop: '20px'}}>
                        <Button type="translucent" icon={IconHeartCancel} disabled />
                    </div>
                    <div style={{marginTop: '20px'}}>
                        <Button type="text" icon={IconHeartCancel} disabled />
                    </div>
                    <div style={{marginTop: '20px'}}>
                        <Button type="text-strong" icon={IconHeartCancel} disabled />
                    </div>
                    <div style={{marginTop: '20px'}}>
                        <Button type="text-aux" icon={IconHeartCancel} disabled />
                    </div>
                </div>
            </div>
        );
    }
}
