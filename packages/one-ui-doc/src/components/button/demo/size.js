import React, {useState} from 'react';
import {Button, Radio} from '@baidu/one-ui';

const sizes = ['xlarge', 'large', 'medium', 'small', 'xsmall'];
const typeGroups = [
    ['normal', 'basic', 'strong', 'primary', 'translucent', 'text', 'text-aux', 'text-strong', 'text-reverse'],
    ['ghost', 'ghost-aux', 'ghost-strong', 'ghost-reverse', 'ghost-light']
];

export default () => {
    const [size, setSize] = useState('medium');

    return (
        <>
            <Radio.Group
                type="strong"
                options={sizes}
                value={size}
                onChange={e => setSize(e.target.value)}
                size="small"
            />
            <br />
            {
                typeGroups.map((types, index) => (
                    <div
                        key={index}
                        style={{display: 'flex', gap: 16, flexWrap: 'wrap', marginBottom: 16, alignItems: 'center'}}
                    >
                        {
                            types.map(type => (
                                type === 'ghost-reverse' || type === 'text-reverse'
                                    ? (
                                        <div
                                            key={type}
                                            style={{padding: 8, backgroundColor: '#0052CC', borderRadius: 4}}
                                        >
                                            <Button
                                                type={type}
                                                size={size}
                                            >
                                                {type}
                                            </Button>
                                        </div>
                                    )
                                    : type === 'ghost-light'
                                        ? (
                                            <div
                                                key={type}
                                                style={{padding: 8, backgroundColor: '#d4e3ff', borderRadius: 4}}
                                            >
                                                <Button
                                                    type={type}
                                                    size={size}
                                                >
                                                    {type}
                                                </Button>
                                            </div>
                                        )
                                        : (
                                            <Button
                                                type={type}
                                                key={type}
                                                size={size}
                                            >
                                                {type}
                                            </Button>
                                        )
                            ))
                        }
                    </div>
                ))
            }
        </>
    );
};
