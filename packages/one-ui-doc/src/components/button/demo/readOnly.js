import React, {PureComponent} from 'react';
import {Button} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    render() {
        return (
            <div>
                <Button type="normal" readOnly>
                    普通按钮
                </Button>
                <div style={{marginTop: '20px'}}>
                    <Button type="basic" readOnly>
                        基本按钮
                    </Button>
                </div>
                <div style={{marginTop: '20px'}}>
                    <Button type="strong" readOnly>
                        加强按钮
                    </Button>
                </div>
                <div style={{marginTop: '20px'}}>
                    <Button type="primary" readOnly>
                        重要按钮
                    </Button>
                </div>
                <div style={{marginTop: '20px'}}>
                    <Button type="translucent" readOnly>
                        半透明按钮
                    </Button>
                </div>
                <div style={{marginTop: '20px'}}>
                    <Button type="text" readOnly>
                        文字链按钮
                    </Button>
                </div>
                <div style={{marginTop: '20px'}}>
                    <Button type="text-strong" readOnly>
                        加强文字链按钮
                    </Button>
                </div>
                <div style={{marginTop: '20px'}}>
                    <Button type="text-aux" readOnly>
                        减弱文字链按钮
                    </Button>
                </div>
            </div>
        );
    }
}
