import React, {useState} from 'react';
import {Button, Radio} from '@baidu/one-ui';

const typeGroups = [
    ['normal', 'basic', 'strong', 'primary', 'translucent', 'text', 'text-aux', 'text-strong'],
    ['ghost', 'ghost-aux', 'ghost-strong', 'ghost-reverse', 'ghost-light']
];

export default () => (
    <>
        {
            typeGroups.map((types, index) => (
                <div
                    key={index}
                    style={{display: 'flex', gap: 16, flexWrap: 'wrap', marginBottom: 16, alignItems: 'center'}}
                >
                    {
                        types.map(type => (
                            type === 'ghost-reverse'
                                ? (
                                    <div key={type} style={{padding: 8, backgroundColor: '#0052CC', borderRadius: 4}}>
                                        <Button
                                            type={type}
                                            disabled
                                        >
                                            {type}
                                        </Button>
                                    </div>
                                )
                                : type === 'ghost-light'
                                    ? (
                                        <div key={type} style={{padding: 8, backgroundColor: '#d4e3ff', borderRadius: 4}}>
                                            <Button
                                                type={type}
                                                disabled
                                            >
                                                {type}
                                            </Button>
                                        </div>
                                    )
                                    : (
                                        <Button
                                            type={type}
                                            key={type}
                                            disabled
                                        >
                                            {type}
                                        </Button>
                                    )
                        ))
                    }
                </div>
            ))
        }
    </>
);
