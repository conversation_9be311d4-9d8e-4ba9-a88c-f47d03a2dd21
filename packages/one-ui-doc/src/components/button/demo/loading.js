import React, {PureComponent} from 'react';
import {Button} from '@baidu/one-ui';

export default class Normal extends PureComponent {

    onClick = e => {
        console.log(e);
    }

    render() {
        return (
            <div>
                <Button type="normal" loading onClick={this.onClick}>
                    普通按钮
                </Button>
                <div style={{marginTop: '20px'}}>
                    <Button type="basic" loading>
                        基本按钮
                    </Button>
                </div>
                <div style={{marginTop: '20px'}}>
                    <Button type="strong" loading>
                        加强按钮
                    </Button>
                </div>
                <div style={{marginTop: '20px'}}>
                    <Button type="primary" loading>
                        重要按钮
                    </Button>
                </div>
                <div style={{marginTop: '20px'}}>
                    <Button type="translucent" loading>
                        半透明按钮
                    </Button>
                </div>
                <div style={{marginTop: '20px'}}>
                    <Button type="text" loading>
                        文字链按钮
                    </Button>
                </div>
                <div style={{marginTop: '20px'}}>
                    <Button type="text-strong" loading>
                        文字加强链按钮
                    </Button>
                </div>
                <div style={{marginTop: '20px'}}>
                    <Button type="text-aux" loading>
                        文字减弱链按钮
                    </Button>
                </div>
                <div style={{marginTop: '20px'}}>
                    纯图标loading状态
                    <div style={{marginTop: '20px'}}>
                        <Button type="normal" icon="calendar" loading />
                    </div>
                    <div style={{marginTop: '20px'}}>
                        <Button type="basic" icon="calendar" loading />
                    </div>
                    <div style={{marginTop: '20px'}}>
                        <Button type="strong" icon="calendar" loading />
                    </div>
                    <div style={{marginTop: '20px'}}>
                        <Button type="primary" icon="calendar" loading />
                    </div>
                    <div style={{marginTop: '20px'}}>
                        <Button type="translucent" icon="calendar" loading />
                    </div>
                    <div style={{marginTop: '20px'}}>
                        <Button type="text" icon="calendar" loading />
                    </div>
                    <div style={{marginTop: '20px'}}>
                        <Button type="text-strong" icon="calendar" loading />
                    </div>
                    <div style={{marginTop: '20px'}}>
                        <Button type="text-aux" icon="calendar" loading />
                    </div>
                </div>
            </div>
        );
    }
}
