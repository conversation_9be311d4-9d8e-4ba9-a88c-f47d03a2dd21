import React, {PureComponent} from 'react';
import {Button} from '@baidu/one-ui';
import {
    IconDoubleCircle
} from 'dls-icons-react';

export default class Normal extends PureComponent {
    render() {
        return (
            <div>
                <Button type="normal" icon={IconDoubleCircle}>
                    普通按钮
                </Button>
                <div style={{marginTop: '20px'}}>
                    <Button type="basic" icon={IconDoubleCircle}>
                        基本按钮
                    </Button>
                </div>
                <div style={{marginTop: '20px'}}>
                    <Button type="strong" icon={IconDoubleCircle}>
                        加强按钮
                    </Button>
                </div>
                <div style={{marginTop: '20px'}}>
                    <Button type="primary" icon={IconDoubleCircle}>
                        重要按钮
                    </Button>
                </div>
                <div style={{marginTop: '20px'}}>
                    <Button type="translucent" icon={IconDoubleCircle}>
                        半透明按钮
                    </Button>
                </div>
            </div>
        );
    }
}
