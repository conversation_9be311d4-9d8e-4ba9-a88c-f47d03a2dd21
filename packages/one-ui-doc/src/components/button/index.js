import BaseComponent from '../base';

export default {
    button: {
        value: BaseComponent,
        label: 'Button 按钮',
        demos: [
            {
                title: '尺寸',
                desc: '普通按钮，DLS规范的按钮-根据size定义按钮尺寸， '
                    + '普通按钮分为五个尺寸，xsmall、small、default、large、xlarge，'
                    + '纯文字和纯图标按钮只有small、default、large三种尺寸',
                source: 'size'
            },
            {
                title: '禁用',
                desc: '普通按钮，DLS规范的按钮-禁用',
                source: 'disabled'
            },
            {
                title: '只读',
                desc: '普通按钮，DLS规范的按钮-只读',
                source: 'readOnly'
            },
            {
                title: '图文',
                desc: '普通按钮，DLS规范的按钮-图文按钮',
                source: 'icon'
            },
            {
                title: '图标',
                desc: '普通按钮，DLS规范的按钮-只有图标按钮',
                source: 'onlyIcon'
            },
            {
                title: '加载状态',
                desc: '普通按钮，DLS规范的按钮-加载状态',
                source: 'loading'
            }
        ],
        apis: [
            {
                apiKey: 'Button',
                title: 'Button'
            }
        ]
    }
};
