export default {
    Button: [
        {
            param: 'type',
            type: 'string',
            desc: '设置类型',
            option: `normal(普通), basic(基本), strong(加强),
                primary(重要), translucent(半透明), text(文字), text-strong(文字加强), text-aux(文字减弱)`,
            default: 'normal'
        },
        {
            param: 'name',
            type: 'string',
            desc: '按钮原生的name属性，如果传则为透传',
            option: '',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: '设置按钮大小，目前有五种尺寸',
            option: 'medium, small, large，xsmall， xlarge',
            default: 'medium'
        },
        {
            param: 'className',
            type: 'string',
            desc: '可自定义按钮的className',
            option: '',
            default: ''
        },
        {
            param: 'style',
            type: 'object',
            desc: '透传至button的style中，为样式的行内样式对象',
            option: '',
            default: ''
        },
        {
            param: 'icon',
            type: 'ReactNode',
            desc: '自定义icon，dls-icons-react的icons',
            option: '',
            default: ''
        },
        {
            param: 'htmlType',
            type: 'string',
            desc: '按钮原生的type属性，如果传则为透传',
            option: '[submit, button, reset]',
            default: 'button'
        },
        {
            param: 'disabled',
            type: 'boolean',
            desc: '禁用状态',
            option: '',
            default: 'false'
        },
        {
            param: 'loading',
            type: 'boolean',
            desc: '加载状态',
            option: '',
            default: 'false'
        },
        {
            param: 'readOnly',
            type: 'boolean',
            desc: '按钮是否只能可读',
            option: '',
            default: 'false'
        },
        {
            param: 'onClick',
            type: '(e)=>void',
            desc: '加载状态',
            option: '',
            default: ''
        }
    ]
};
