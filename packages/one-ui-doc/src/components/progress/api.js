export default {
    Progress: [
        {
            param: 'className',
            type: 'string',
            desc: '类名',
            option: '',
            default: ''
        },
        {
            param: 'type',
            type: 'string',
            desc: '类型',
            option: 'line | circle',
            default: 'line'
        },
        {
            param: 'size',
            type: 'string',
            desc: '尺寸',
            option: 'medium | small',
            default: 'medium'
        },
        {
            param: 'percent',
            type: 'number',
            desc: '百分比',
            option: '',
            default: '0'
        },
        {
            param: 'format',
            type: 'function(percent)',
            desc: '内容的模板函数',
            option: '',
            default: "percent => percent + '%'"
        },
        {
            param: 'showInfo',
            type: 'bool',
            desc: '是否显示进度数值或状态图标',
            option: '',
            default: 'true'
        },
        {
            param: 'status',
            type: 'string',
            desc: '状态',
            option: 'success | exception | normal',
            default: 'normal'
        },
        {
            param: 'strokeLinecap',
            type: 'string',
            desc: '进度条边缘的形状',
            option: 'round | square',
            default: 'round'
        },
        {
            param: 'strokeColor',
            type: 'string',
            desc: '进度条颜色',
            option: '',
            default: '-'
        },
        {
            param: 'width',
            type: 'string',
            desc: '画布宽度',
            option: '',
            default: '-'
        },
        {
            param: 'onCancel',
            type: 'function',
            desc: '取消操作',
            option: '',
            default: '-'
        },
        {
            param: 'onRetry',
            type: 'function',
            desc: '刷新操作',
            option: '',
            default: '-'
        }
    ]
};
