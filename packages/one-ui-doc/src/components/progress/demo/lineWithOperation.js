import React, {PureComponent} from 'react';
import {Progress} from '@baidu/one-ui';

export default class LineWithOperation extends PureComponent {
    onCancel = () => {
        console.log('cancel sucess!');
    }

    onRetry = () => {
        console.log('retry sucess!');
    }

    render() {
        return (
            <div style={{width: 400}}>
                <Progress percent={30} onRetry={this.onRetry} onCancel={this.onCancel}/>
                <Progress percent={70} status="exception" onRetry={this.onRetry} onCancel={this.onCancel}/>
                <Progress percent={100} onRetry={this.onRetry} onCancel={this.onCancel}/>
                <Progress percent={50} showInfo={false} onRetry={this.onRetry} onCancel={this.onCancel}/>
                <Progress size="small" percent={30} onRetry={this.onRetry} onCancel={this.onCancel}/>
                <Progress size="small" percent={70} status="exception" onRetry={this.onRetry} onCancel={this.onCancel}/>
                <Progress size="small" percent={100} onRetry={this.onRetry} onCancel={this.onCancel}/>
                <Progress size="small" percent={50} showInfo={false} onRetry={this.onRetry} onCancel={this.onCancel}/>
            </div>
        );
    }
}
