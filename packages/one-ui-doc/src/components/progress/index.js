import BaseComponent from '../base';

export default {
    progress: {
        value: BaseComponent,
        label: 'Progress 进度条',
        demos: [
            {
                title: '进度条',
                desc: '简单的进度条展示，分为初始，进行中，完成，报错4种状态。',
                source: 'normalLine'
            },
            {
                title: '进度环',
                desc: '',
                source: 'normalCircle'
            },
            {
                title: '小进度条',
                desc: '',
                source: 'smallLine'
            },
            {
                title: '小进度环',
                desc: '',
                source: 'smallCircle'
            },
            {
                title: '操作项',
                desc: '可增加操作如"取消”、”刷新”，如果运行时间过长，建议提供取消操作，点击"取消"，取消进行中的任务；如果进度条报错，建议增加刷新操作，后点击"刷新"，重新执行任务。',
                source: 'lineWithOperation'
            }
        ],
        apis: [
            {
                apiKey: 'Progress',
                title: 'Progress'
            }
        ]
    }
};
