import BaseComponent from '../base';

export default {
    dropdown: {
        value: BaseComponent,
        label: 'Dropdown 下拉菜单',
        demos: [
            {
                title: '普通下拉',
                desc: '下拉菜单 - 普通下拉菜单',
                source: 'button'
            },
            {
                title: '分组下拉',
                desc: '下拉菜单 - 分组下拉菜单',
                source: 'group'
            },
            {
                title: '子菜单',
                desc: '下拉菜单 - 可展开的子菜单层级',
                source: 'sublevel'
            },
            {
                title: '按钮分离',
                desc: '下拉菜单 - 带主命令按钮的下拉菜单',
                source: 'primary'
            },
            {
                title: '尺寸',
                desc: '下拉菜单 - 不同尺寸',
                source: 'normal'
            },
            {
                title: '文字按钮',
                desc: '下拉菜单 - 文字按钮',
                source: 'textLink'
            },
            {
                title: '搜索',
                desc: '下拉菜单 - 带搜索功能的下拉菜单',
                source: 'search'
            },
            {
                title: '自定义',
                desc: '下拉菜单 - 自定义触发按钮的下拉菜单，dropdownIndependentWidth默认为true',
                source: 'custom'
            },
            {
                title: '切换卡片',
                desc: '下拉菜单 - 点击菜单切换卡片',
                source: 'customCard'
            },
            {
                title: '下拉框宽度限制',
                desc: 'false表示下拉弹窗的最小宽度等于target宽度，true表示下拉弹窗的宽度保持独立并自适应包裹内容，存在自定义触发区的情况下，默认为true，其他情况默认为false',
                source: 'dropdownIndependentWidth'
            },
            {
                title: '自定义前置icon',
                desc: '',
                source: 'icon'
            }
        ],
        apis: [
            {
                apiKey: 'Dropdown',
                title: 'Dropdown'
            },
            {
                apiKey: 'DropdownButton',
                title: 'Dropdown.Button'
            }
        ]
    }
};
