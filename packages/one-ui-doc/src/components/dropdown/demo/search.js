import React, {PureComponent} from 'react';
import {Dropdown} from '@baidu/one-ui';


export default class But<PERSON> extends PureComponent {

    handleMenuClick = e => {
        console.log(e);
    }

    onVisibleChange = e => {
        console.log(e);
    }

    onSearchChange = e => {
        console.log(e);
    }

    render() {
        return (
            <div>
                <div style={{marginBottom: '60px'}}>
                    <div>支持搜索的dropdown.Button</div>
                    <br />
                    <br />
                    <Dropdown.Button
                        options={[
                            {
                                label: '操作长长长长长长操作长长长长长长',
                                value: 'value1'
                            },
                            {
                                label: '操作2',
                                value: 'value2'
                            },
                            {
                                label: '操作3',
                                value: 'value3'
                            },
                            {
                                label: '操作4',
                                value: 'value4'
                            }
                        ]}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        onVisibleChange={this.onVisibleChange}
                        trigger={['click']}
                        showSearch
                        onSearchChange={this.onSearchChange}
                        searchPlaceholder="请输入..."
                        notFound="没有找到需要的计划"
                    />
                    <br />
                    <br />
                    <div>支持搜索的dropdown.Button - medium</div>
                    <br />
                    <br />
                    <Dropdown.Button
                        options={[
                            {
                                label: '操作长长长长长长操作长长长长长长',
                                value: 'value1'
                            },
                            {
                                label: '操作2',
                                value: 'value2'
                            },
                            {
                                label: '操作3',
                                value: 'value3'
                            },
                            {
                                label: '操作4',
                                value: 'value4'
                            }
                        ]}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        onVisibleChange={this.onVisibleChange}
                        trigger={['click']}
                        showSearch
                        onSearchChange={this.onSearchChange}
                        searchPlaceholder="请输入..."
                        notFound="没有找到需要的计划"
                        size="medium"
                    />
                    <br />
                    <br />
                    <div>支持搜索的dropdown.Button - large</div>
                    <br />
                    <br />
                    <Dropdown.Button
                        options={[
                            {
                                label: '操作长长长长长长操作长长长长长长',
                                value: 'value1'
                            },
                            {
                                label: '操作2',
                                value: 'value2'
                            },
                            {
                                label: '操作3',
                                value: 'value3'
                            },
                            {
                                label: '操作4',
                                value: 'value4'
                            }
                        ]}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        onVisibleChange={this.onVisibleChange}
                        trigger={['click']}
                        showSearch
                        onSearchChange={this.onSearchChange}
                        searchPlaceholder="请输入..."
                        notFound="没有找到需要的计划"
                        size="large"
                    />
                </div>
            </div>
        );
    }
}
