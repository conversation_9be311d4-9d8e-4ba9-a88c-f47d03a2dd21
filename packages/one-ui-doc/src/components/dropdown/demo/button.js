import React, {PureComponent} from 'react';
import {Dropdown} from '@baidu/one-ui';


export default class But<PERSON> extends PureComponent {

    handleMenuClick = e => {
        console.log(e);
    }

    render() {
        return (
            <div>
                <div style={{marginBottom: '60px'}}>
                    <div>普通Dropdown.Button - 触发方式：hover</div>
                    <br />
                    <br />
                    <Dropdown.Button
                        buttonType="primary"
                        options={[
                            {
                                label: '操作命令1',
                                value: 'operation1',
                                disabled: true
                            },
                            {
                                label: '操作命令2',
                                value: 'operation2'
                            },
                            {
                                label: '操作命令3',
                                value: 'operation3'
                            },
                            {
                                label: '操作命令1',
                                value: 'operation4'
                            }
                        ]}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                    />
                </div>
                <div style={{marginBottom: '60px'}}>
                    <div>普通Dropdown.Button - 触发方式：click</div>
                    <br />
                    <br />
                    <Dropdown.Button
                        options={[
                            {
                                label: '操作命令1',
                                value: 'operation1'
                            },
                            {
                                label: '操作命令2',
                                value: 'operation2'
                            },
                            {
                                label: '操作命令3',
                                value: 'operation3'
                            },
                            {
                                label: '操作命令1',
                                value: 'operation4'
                            }
                        ]}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        trigger={['click']}
                    />
                </div>
                <div style={{marginBottom: '60px'}}>
                    <div>普通Dropdown.Button - 整体禁用</div>
                    <br />
                    <br />
                    <Dropdown.Button
                        options={[
                            {
                                label: '操作命令1',
                                value: 'operation1'
                            },
                            {
                                label: '操作命令2',
                                value: 'operation2'
                            },
                            {
                                label: '操作命令3',
                                value: 'operation3'
                            },
                            {
                                label: '操作命令1',
                                value: 'operation4'
                            }
                        ]}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        disabled
                    />
                    <br />
                    <br />
                    <div>普通Dropdown.Button - 内部有选项禁用</div>
                    <br />
                    <br />
                    <Dropdown.Button
                        options={[
                            {
                                label: '操作命令1',
                                value: 'operation1'
                            },
                            {
                                label: '操作命令2',
                                value: 'operation2',
                                disabled: true
                            },
                            {
                                label: '操作命令3',
                                value: 'operation3'
                            },
                            {
                                label: '操作命令1',
                                value: 'operation4'
                            }
                        ]}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                    />
                </div>
                <div style={{marginBottom: '60px'}}>
                    <div>普通Dropdown.Button - 子层级字数超过下拉框</div>
                    <br />
                    <br />
                    <Dropdown.Button
                        options={[
                            {
                                label: '选项字符数超过了下拉选择框哦',
                                value: 'operation1'
                            },
                            {
                                label: '操作命令2',
                                value: 'operation2'
                            },
                            {
                                label: '操作命令3',
                                value: 'operation3'
                            },
                            {
                                label: '操作命令1',
                                value: 'operation4'
                            }
                        ]}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        disabledReason="该计划暂不可用"
                    />
                    <br />
                    <br />
                </div>
                <div style={{marginBottom: '60px'}}>
                    <div>普通Dropdown.Button - 选项超过10行的高度</div>
                    <br />
                    <br />
                    <Dropdown.Button
                        options={[
                            {
                                label: '操作命令1',
                                value: 'operation1'
                            },
                            {
                                label: '操作命令2',
                                value: 'operation2'
                            },
                            {
                                label: '操作命令3',
                                value: 'operation3'
                            },
                            {
                                label: '操作命令4',
                                value: 'operation4'
                            },
                            {
                                label: '操作命令5',
                                value: 'operation5'
                            },
                            {
                                label: '操作命令6',
                                value: 'operation6'
                            },
                            {
                                label: '操作命令7',
                                value: 'operation7'
                            },
                            {
                                label: '操作命令8',
                                value: 'operation8'
                            },
                            {
                                label: '操作命令9',
                                value: 'operation9'
                            },
                            {
                                label: '操作命令10',
                                value: 'operation11'
                            },
                            {
                                label: '操作命令12',
                                value: 'operation12'
                            },
                            {
                                label: '操作命令13',
                                value: 'operation13'
                            }
                        ]}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        disabledReason="该计划暂不可用"
                    />
                    <br />
                    <br />
                </div>
            </div>
        );
    }
}
