import React, {useState} from 'react';
import {Dropdown, Radio, Menu, Button} from '@baidu/one-ui';

const RadioGroup = Radio.Group;
const options = [
    {
        label: '操作命令1',
        value: 'operation1'
    },
    {
        label: '操作命令2',
        value: 'operation2'
    },
    {
        label: '操作命令3',
        value: 'operation3'
    },
    {
        label: '操作命令4',
        value: 'operation4'
    }
];
const dropdownWidthOptions = ['默认值', 'true', 'false'];

export default function Demo() {
    const [value, setValue] = useState(options[0].value);
    const [dropdownWidth, setDropdownWidth] = useState('默认值');

    function handleMenuClick(e) {
        setValue(e.key);
    }

    function onChangeDropdownWidth(e) {
        setDropdownWidth(e.target.value);
    }

    const dropdownWidthProps = {}
    if (dropdownWidth === 'true') {
        dropdownWidthProps.dropdownIndependentWidth = true;
    }
    else if (dropdownWidth === 'false') {
        dropdownWidthProps.dropdownIndependentWidth = false;
    }

    const menu = (
        <Menu>
            <Menu.Item>1st menu</Menu.Item>
            <Menu.Item>2nd menu</Menu.Item>
        </Menu>
    );

    return (
        <div>
            <div style={{marginBottom: '30px'}}>
                <div style={{display: 'flex', marginBottom: '30px'}}>
                    dropdownIndependentWidth： 
                    <RadioGroup
                        value={dropdownWidth}
                        options={dropdownWidthOptions}
                        onChange={onChangeDropdownWidth}
                    />
                </div>

                <div style={{marginBottom: '12px'}}>普通Dropdown.Button</div>
                <Dropdown.Button
                    options={options}
                    title="下拉菜单名称"
                    onHandleMenuClick={handleMenuClick}
                    trigger={['click']}
                    {...dropdownWidthProps}
                />

                <br />
                <br />
                已选值：{value}

                <br />
                <br />

                <div style={{marginBottom: '12px'}}>自定义触发</div>
                <Dropdown
                    overlay={menu}
                    {...dropdownWidthProps}
                >
                    <Button type="primary">自定义触发按钮</Button>
                </Dropdown>
            </div>
        </div>
    );
}
