import React, {PureComponent} from 'react';
import {Dropdown} from '@baidu/one-ui';

export default class But<PERSON> extends PureComponent {

    handleMenuClick = e => {
        console.log(e);
    }


    onClickButton = e => {
        console.log(e);
    }

    render() {
        return (
            <div>
                <div style={{marginBottom: '60px'}}>
                    <div>Dropdown.Button - primary - hover</div>
                    <br />
                    <br />
                    <Dropdown.Button
                        options={[
                            {
                                label: '操作命令1',
                                value: 'operation1'
                            },
                            {
                                label: '操作命令2',
                                value: 'operation2'
                            },
                            {
                                label: '操作命令3',
                                value: 'operation3'
                            },
                            {
                                label: '操作命令1',
                                value: 'operation4'
                            }
                        ]}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        split
                        buttonType="normal"
                    />
                    <br />
                    <br />
                    <Dropdown.Button
                        options={[
                            {
                                label: '操作命令1',
                                value: 'operation1'
                            },
                            {
                                label: '操作命令2',
                                value: 'operation2'
                            },
                            {
                                label: '操作命令3',
                                value: 'operation3'
                            },
                            {
                                label: '操作命令1',
                                value: 'operation4'
                            }
                        ]}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        split
                        buttonType="primary"
                    />
                    <br />
                    <br />
                </div>
                <div style={{marginBottom: '60px'}}>
                    <div>普通Dropdown.Button - click</div>
                    <br />
                    <br />
                    <Dropdown.Button
                        options={[
                            {
                                label: '操作命令1',
                                value: 'operation1'
                            },
                            {
                                label: '操作命令2',
                                value: 'operation2'
                            },
                            {
                                label: '操作命令3',
                                value: 'operation3'
                            },
                            {
                                label: '操作命令1',
                                value: 'operation4'
                            }
                        ]}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        trigger={['click']}
                        split
                        buttonType="normal"
                    />
                    <br />
                    <br />
                    <Dropdown.Button
                        options={[
                            {
                                label: '操作命令1',
                                value: 'operation1'
                            },
                            {
                                label: '操作命令2',
                                value: 'operation2'
                            },
                            {
                                label: '操作命令3',
                                value: 'operation3'
                            },
                            {
                                label: '操作命令1',
                                value: 'operation4'
                            }
                        ]}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        trigger={['click']}
                        split
                        buttonType="primary"
                    />
                    <br />
                    <br />
                </div>
                <div style={{marginBottom: '60px'}}>
                    <div>普通Dropdown.Button - 禁用</div>
                    <br />
                    <br />
                    <Dropdown.Button
                        options={[
                            {
                                label: '操作命令1',
                                value: 'operation1'
                            },
                            {
                                label: '操作命令2',
                                value: 'operation2'
                            },
                            {
                                label: '操作命令3',
                                value: 'operation3'
                            },
                            {
                                label: '操作命令1',
                                value: 'operation4'
                            }
                        ]}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        disabled
                        split
                        buttonType="normal"
                    />
                    <br />
                    <br />
                    <Dropdown.Button
                        options={[
                            {
                                label: '操作命令1',
                                value: 'operation1'
                            },
                            {
                                label: '操作命令2',
                                value: 'operation2'
                            },
                            {
                                label: '操作命令3',
                                value: 'operation3'
                            },
                            {
                                label: '操作命令1',
                                value: 'operation4'
                            }
                        ]}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        disabled
                        split
                        buttonType="primary"
                    />
                    <br />
                    <br />
                </div>
            </div>
        );
    }
}
