import React, {PureComponent} from 'react';
import {Dropdown} from '@baidu/one-ui';

const list = [
    {
        label: '操作命令1',
        value: 1
    },
    {
        label: '操作命令2',
        value: 2
    },
    {
        label: '操作命令3',
        value: 3
    },
    {
        label: '操作命令4',
        value: 4
    },
    {
        label: '操作命令5',
        value: 5,
        disabled: true
    },
    {
        label: '操作命令6',
        value: 6,
        children: [{
            label: '操作命令7',
            value: 7
        }, {
            label: '操作命令8',
            value: 8
        }, {
            label: '操作命令9',
            value: 9,
            disabled: true
        }]
    }
];


export default class Button extends PureComponent {
    handleMenuClick = e => {
        console.log(e);
    }

    render() {
        return (
            <div>
                <div style={{marginBottom: '60px'}}>
                    <div>普通Dropdown.Button - 超小</div>
                    <br />
                    <br />
                    <Dropdown.Button
                        options={list}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        size="xsmall"
                    />
                    <br />
                    <br />
                    <Dropdown.Button
                        options={list}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        size="xsmall"
                        type="primary"
                    />
                    <br />
                    <br />
                    <Dropdown.Button
                        options={list}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        size="xsmall"
                        type="primary"
                        primaryType="primary"
                    />
                    <br />
                    <br />
                </div>
                <div style={{marginBottom: '60px'}}>
                    <div>普通Dropdown.Button - 小型</div>
                    <br />
                    <br />
                    <Dropdown.Button
                        options={list}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        size="small"
                    />
                    <br />
                    <br />
                    <Dropdown.Button
                        options={list}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        size="small"
                        type="primary"
                    />
                    <br />
                    <br />
                    <Dropdown.Button
                        options={list}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        size="small"
                        type="primary"
                        primaryType="primary"
                    />
                    <br />
                    <br />
                </div>
                <div style={{marginBottom: '60px'}}>
                    <div>普通Dropdown.Button - 中型</div>
                    <br />
                    <br />
                    <Dropdown.Button
                        options={list}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        size="medium"
                    />
                    <br />
                    <br />
                    <Dropdown.Button
                        options={list}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        size="medium"
                        type="primary"
                    />
                    <br />
                    <br />
                    <Dropdown.Button
                        options={list}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        size="medium"
                        type="primary"
                        primaryType="primary"
                    />
                    <br />
                    <br />
                </div>
                <div style={{marginBottom: '60px'}}>
                    <div>普通Dropdown.Button - 大型</div>
                    <br />
                    <br />
                    <Dropdown.Button
                        options={list}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        size="large"
                    />
                    <br />
                    <br />
                    <Dropdown.Button
                        options={list}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        size="large"
                        type="primary"
                    />
                    <br />
                    <br />
                    <Dropdown.Button
                        options={list}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        size="large"
                        type="primary"
                        primaryType="primary"
                    />
                    <br />
                    <br />
                </div>
            </div>
        );
    }
}
