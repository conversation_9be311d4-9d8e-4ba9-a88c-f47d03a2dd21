import React, {PureComponent} from 'react';
import {Dropdown} from '@baidu/one-ui';


export default class But<PERSON> extends PureComponent {

    handleMenuClick = e => {
        console.log(e);
    }

    render() {
        return (
            <div>
                <div style={{marginBottom: '60px'}}>
                    <div>普通Dropdown.Button - 展开子层级</div>
                    <br />
                    <br />
                    <Dropdown.Button
                        options={[
                            {
                                label: '操作命令1',
                                value: 'operation1',
                                children: [{
                                    label: '子层级1',
                                    value: 'sublevel-1'
                                }, {
                                    label: '子层级2',
                                    value: 'sublevel-2'
                                }, {
                                    label: '子层级3',
                                    value: 'sublevel-3'
                                }, {
                                    label: '子层级4',
                                    value: 'sublevel-4'
                                }]
                            },
                            {
                                label: '操作命令2',
                                value: 'operation2',
                                children: [{
                                    label: '子层级2-1',
                                    value: 'sublevel-1-2'
                                }, {
                                    label: '子层级2-2',
                                    value: 'sublevel-2-2'
                                }, {
                                    label: '子层级3-3',
                                    value: 'sublevel-3-3'
                                }, {
                                    label: '子层级4-4',
                                    value: 'sublevel-4-4'
                                }]
                            }
                        ]}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        trigger={['click']}
                    />
                </div>
            </div>
        );
    }
}
