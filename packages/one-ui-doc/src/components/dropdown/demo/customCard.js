import React, {PureComponent} from 'react';
import {Dropdown, Menu, Button} from '@baidu/one-ui';

export default class DropdownCustomCard extends PureComponent {
    state = {
        visible: false,
        type: 'menu'
    }

    onVisibleChange = visible => {
        console.log('visible', visible);
        this.setState({
            visible
        });
    }

    handleMenuClick = node => {
        this.setState({
            type: 'card',
            visible: true,
            menuKey: node.key
        });
    }

    handleBackClick = () => {
        this.setState({
            type: 'menu'
        });
    }

    renderMenu() {
        return (
            <div
                style={{
                    maxHeight: 300,
                    overflow: 'auto',
                    display: this.state.type === 'menu' ? 'block' : 'none'
                }}
            >
                <Menu
                    onClick={this.handleMenuClick}
                    type="basic"
                    key={this.state.type}
                >
                    <Menu.Item key="1">menu item 1</Menu.Item>
                    <Menu.Item key="2">menu item 2</Menu.Item>
                    <Menu.Divider />
                    <Menu.ItemGroup title="分组1">
                        <Menu.Item key="3">menu item 3</Menu.Item>
                        <Menu.Item key="4">menu item 4</Menu.Item>
                    </Menu.ItemGroup>
                    <Menu.SubMenu title="xth menu item" type="basic">
                        <Menu.Item key="5">menu item 5</Menu.Item>
                        <Menu.Item key="6">menu item 6</Menu.Item>
                    </Menu.SubMenu>
                    <Menu.Divider />
                    <Menu.ItemGroup title="分组2">
                        <Menu.Item key="7">menu item 7</Menu.Item>
                        <Menu.Item key="8">menu item 8</Menu.Item>
                    </Menu.ItemGroup>
                    <Menu.ItemGroup title="分组2">
                        <Menu.Item key="71">menu item 7</Menu.Item>
                        <Menu.Item key="81">menu item 8</Menu.Item>
                    </Menu.ItemGroup>
                    <Menu.ItemGroup title="分组2">
                        <Menu.Item key="72">menu item 7</Menu.Item>
                        <Menu.Item key="82">menu item 8</Menu.Item>
                    </Menu.ItemGroup>
                    <Menu.Item key="73">menu item 7</Menu.Item>
                    <Menu.Item key="83">menu item 8</Menu.Item>
                </Menu>
            </div>
        );
    }

    renderCard() {
        if (this.state.type === 'menu') {
            return null;
        }
        return (
            <div style={{width: 200, padding: 10}}>
                点击了menu item {this.state.menuKey}
                <br />
                <br />
                <br />
                <Button onClick={this.handleBackClick}>&lt; 返回</Button>
            </div>
        );
    }

    renderOverlay() {
        return (
            <div>
                {this.renderMenu()}
                {this.renderCard()}
            </div>
        );
    }

    render() {
        return (
            <div>
                <Dropdown
                    transparent={false}
                    overlay={this.renderOverlay()}
                    visible={this.state.visible}
                    onVisibleChange={this.onVisibleChange}
                >
                    <Button type="primary">自定义触发按钮</Button>
                </Dropdown>
            </div>
        );
    }
}
