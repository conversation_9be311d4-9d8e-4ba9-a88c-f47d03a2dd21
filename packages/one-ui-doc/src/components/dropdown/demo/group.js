import React, {PureComponent} from 'react';
import {Dropdown} from '@baidu/one-ui';


export default class But<PERSON> extends PureComponent {

    handleMenuClick = e => {
        console.log(e);
    }

    render() {
        return (
            <div>
                <div style={{marginBottom: '60px'}}>
                    <div>分组下拉菜单 - 带分割线的分组下拉菜单</div>
                    <br />
                    <br />
                    <Dropdown.Button
                        options={[
                            {
                                label: '操作命令1',
                                value: 'operation1'
                            },
                            {
                                label: '操作命令2',
                                value: 'operation2'
                            },
                            {
                                divider: true
                            },
                            {
                                label: '操作命令3',
                                value: 'operation3'
                            },
                            {
                                label: '操作命令1',
                                value: 'operation4'
                            }
                        ]}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        trigger={['click']}
                    />
                </div>
                <div style={{marginBottom: '60px'}}>
                    <div>分组下拉菜单 - 带分割线以及分组名称的下拉菜单</div>
                    <br />
                    <br />
                    <Dropdown.Button
                        options={[
                            {
                                value: '1',
                                label: '分类一',
                                groupChildren: [{
                                    label: '操作命令1',
                                    value: 'operation1'
                                }, {
                                    label: '操作命令2',
                                    value: 'operation2'
                                }]
                            },
                            {
                                divider: true
                            },
                            {
                                value: '2',
                                label: '分类二',
                                groupChildren: [{
                                    label: '操作命令3',
                                    value: 'operation3'
                                }, {
                                    label: '操作命令4',
                                    value: 'operation4'
                                }]
                            }
                        ]}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        trigger={['click']}
                    />
                </div>
            </div>
        );
    }
}
