import React, {PureComponent} from 'react';
import {Dropdown, Menu, Button, Checkbox, Radio, Toast} from '@baidu/one-ui';

const {SubMenu} = Menu;

export default class ButtonNormal extends PureComponent {
    state = {
        visible: false,
        transparent: false,
        title: false,
        showConfirm: false,
        size: 'medium'
    }

    onVisibleChange = visible => {
        this.setState({
            visible
        });
    }

    render() {
        const {
            transparent,
            title,
            showConfirm,
            size
        } = this.state;
        const menu = (
            <Menu size={size}>
                <Menu.Item>1st menu item</Menu.Item>
                <Menu.Item>2nd menu item</Menu.Item>
                <SubMenu title="sub menu" size={size}>
                    <Menu.Item>3rd menu item</Menu.Item>
                    <Menu.Item>4th menu item</Menu.Item>
                </SubMenu>
                <SubMenu title="disabled sub menu" disabled>
                    <Menu.Item>5d menu item</Menu.Item>
                    <Menu.Item>6th menu item</Menu.Item>
                </SubMenu>
            </Menu>
        );
        return (
            <div>
                <Radio.Group size="small" defaultValue={size} onChange={e => this.setState({size: e.target.value})}>
                    {
                        ['xsmall', 'small', 'medium', 'large'].map(
                            size => <Radio.Button value={size} key={size}>{size}</Radio.Button>
                        )
                    }
                </Radio.Group>
                <br />
                <Checkbox checked={transparent} onChange={e => this.setState({transparent: e.target.checked})}>
                    透明背景
                </Checkbox>
                <Checkbox checked={title} onChange={e => this.setState({title: e.target.checked})}>标题</Checkbox>
                <Checkbox checked={showConfirm} onChange={e => this.setState({showConfirm: e.target.checked})}>
                    应用/取消
                </Checkbox>
                <br />
                <br />
                <br />
                <Dropdown
                    title={title ? '测试标题' : null}
                    showConfirm={showConfirm}
                    transparent={transparent}
                    size={size}
                    overlay={menu}
                    visible={this.state.visible}
                    onVisibleChange={this.onVisibleChange}
                    onOk={() => {
                        Toast.success({
                            content: 'Success！',
                            duration: 3
                        });
                    }}
                >
                    <Button type="primary">自定义触发按钮</Button>
                </Dropdown>
                <br />
                <br />
                Dropdown.Button 自定义下拉面板
                <br />
                <br />
                <Dropdown.Button
                    type="primary"
                    overlay={menu}
                    title="测试标题"
                />
            </div>
        );
    }
}
