import React from 'react';
import {Dropdown} from '@baidu/one-ui';
import {IconDoubleCircle} from 'dls-icons-react';

const options = [
    {
        label: '操作命令1',
        value: 'operation1'
    },
    {
        label: '操作命令2',
        value: 'operation2'
    },
    {
        label: '操作命令3',
        value: 'operation3'
    },
    {
        label: '操作命令4',
        value: 'operation4'
    }
];

export default function Demo() {

    return (
        <Dropdown.Button
            buttonType="primary"
            icon={<IconDoubleCircle />}
            options={options}
            title="下拉菜单名称"
            split
        />
    );
}
