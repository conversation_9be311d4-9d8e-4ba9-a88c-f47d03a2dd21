import React, {PureComponent} from 'react';
import {Dropdown} from '@baidu/one-ui';


export default class But<PERSON> extends PureComponent {

    handleMenuClick = e => {
        console.log(e);
    }

    render() {
        return (
            <div>
                <div style={{display: 'flex', flexDirection: 'column', gap: 20}}>
                    <Dropdown.Button
                        options={[
                            {
                                label: '操作命令1',
                                value: 'operation1'
                            },
                            {
                                label: '操作命令2',
                                value: 'operation2'
                            },
                            {
                                label: '操作命令3',
                                value: 'operation3'
                            },
                            {
                                label: '操作命令1',
                                value: 'operation4'
                            }
                        ]}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        size="small"
                        buttonType="text"
                    />
                    <Dropdown.Button
                        options={[
                            {
                                label: '操作命令1',
                                value: 'operation1'
                            },
                            {
                                label: '操作命令2',
                                value: 'operation2'
                            },
                            {
                                label: '操作命令3',
                                value: 'operation3'
                            },
                            {
                                label: '操作命令1',
                                value: 'operation4'
                            }
                        ]}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        size="medium"
                        buttonType="text"
                    />
                    <Dropdown.Button
                        options={[
                            {
                                label: '操作命令1',
                                value: 'operation1'
                            },
                            {
                                label: '操作命令2',
                                value: 'operation2'
                            },
                            {
                                label: '操作命令3',
                                value: 'operation3'
                            },
                            {
                                label: '操作命令1',
                                value: 'operation4'
                            }
                        ]}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        size="large"
                        buttonType="text"
                    />
                    <Dropdown.Button
                        options={[
                            {
                                label: '操作命令1',
                                value: 'operation1'
                            },
                            {
                                label: '操作命令2',
                                value: 'operation2'
                            },
                            {
                                label: '操作命令3',
                                value: 'operation3'
                            },
                            {
                                label: '操作命令1',
                                value: 'operation4'
                            }
                        ]}
                        title="下拉菜单名称"
                        onHandleMenuClick={this.handleMenuClick}
                        buttonType="text"
                        disabled
                    />
                </div>
            </div>
        );
    }
}
