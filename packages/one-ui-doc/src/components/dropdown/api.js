export default {
    Dropdown: [
        {
            param: 'className',
            type: 'String',
            desc: '自定义类名',
            option: '',
            default: ''
        },
        {
            param: 'disabled',
            type: 'boolean',
            desc: '禁用',
            option: '',
            default: ''
        },
        {
            param: 'getPopupContainer',
            type: 'Function(triggerNode)',
            desc: '弹层渲染父节点。默认渲染到 body 上',
            option: '',
            default: '() => document.body'
        },
        {
            param: 'onVisibleChange',
            type: 'Function(visible)',
            desc: '弹窗visible改变时触发',
            option: '',
            default: ''
        },
        {
            param: 'overlay',
            type: 'Menu',
            desc: '弹层菜单',
            option: '',
            default: ''
        },
        {
            param: 'placement',
            type: 'string',
            desc: '弹层位置',
            option: 'topLeft, topCenter, topRight, bottomLeft, bottomCenter, bottomRight',
            default: 'bottomLeft'
        },
        {
            param: 'trigger',
            type: '[]',
            desc: '触发方式',
            option: 'click|hover|contextMenu',
            default: '[`hover`]'
        },
        {
            param: 'visible',
            type: 'boolean',
            desc: '弹层是否可见',
            option: '',
            default: ''
        },
        {
            param: 'width',
            type: 'number',
            desc: 'dropdown的宽度',
            option: '',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: 'dropdown的尺寸',
            option: 'xsmall | small | medium | large',
            default: 'medium'
        },
        {
            param: 'overlayClassName',
            type: 'string',
            desc: 'dropdown的下拉className(3.0.14-beta-41后生效)',
            option: '',
            default: ''
        },
        {
            param: 'title',
            type: 'ReactNode',
            desc: '标题',
            option: '',
            default: ''
        },
        {
            param: 'showConfirm',
            type: 'bool',
            desc: '是否展示应用/取消',
            option: 'true | false',
            default: 'false'
        },
        {
            param: 'onOk',
            type: 'func',
            desc: '点击应用后回调',
            option: '',
            default: ''
        },
        {
            param: 'onCancel',
            type: 'func',
            desc: '点击取消后回调',
            option: '',
            default: ''
        },
        {
            param: 'okProps',
            type: 'object',
            desc: '应用按钮属性，可参考Button属性',
            option: '',
            default: ''
        },
        {
            param: 'cancelProps',
            type: 'object',
            desc: '取消按钮属性，可参考Button属性',
            option: '',
            default: ''
        },
        {
            param: 'okText',
            type: 'string',
            desc: '应用按钮文字',
            option: '',
            default: ''
        },
        {
            param: 'cancelText',
            type: 'string',
            desc: '取消按钮文字',
            option: '',
            default: ''
        },
        {
            param: 'transparent',
            type: 'bool',
            desc: '默认为`true`（兼容默认下拉背景透明的实现方式），`false`则携带背景/圆角/阴影',
            option: 'true | false',
            default: 'true'
        }
    ],
    DropdownButton: [
        {
            param: 'disabled',
            type: 'boolean',
            desc: '禁用',
            option: '',
            default: ''
        },
        {
            param: 'onVisibleChange',
            type: 'Function(visible)',
            desc: '弹窗visible改变时触发',
            option: '',
            default: ''
        },
        {
            param: 'placement',
            type: 'string',
            desc: '弹层位置',
            option: 'topLeft, topCenter, topRight, bottomLeft, bottomCenter, bottomRight',
            default: 'bottomLeft'
        },
        {
            param: 'trigger',
            type: '[]',
            desc: '触发方式',
            option: 'click|hover|contextMenu',
            default: '[`hover`]'
        },
        {
            param: 'visible',
            type: 'boolean',
            desc: '弹层是否可见',
            option: '',
            default: ''
        },
        {
            param: 'width',
            type: 'number',
            desc: 'dropdown的宽度',
            option: '',
            default: ''
        },
        {
            param: 'options',
            type: '[{value, label, divider, disabled, onClick, children}]',
            desc: '传入dropdown.Button 渲染的options',
            option: '',
            default: ''
        },
        {
            param: 'showSearch',
            type: 'boolean',
            desc: 'dropdown是否需要支持搜索',
            option: '',
            default: 'false'
        },
        {
            param: 'onSearchChange',
            type: 'Function(value)',
            desc: 'dropdown支持搜索时候，输入Change的回调函数',
            option: '',
            default: ''
        },
        {
            param: 'onClearSearch',
            type: 'Function(value)',
            desc: 'dropdown支持搜索时候，点击清空的回调函数',
            option: '',
            default: ''
        },
        {
            param: 'onHandleMenuClick',
            type: 'Function(e)  e.key为Item的key，e.item为点击的Item，e.keyPath []为点击时候的key路径',
            desc: 'dropdown点击菜单时候的回调',
            option: '',
            default: ''
        },
        {
            param: 'searchPlaceholder',
            type: 'string',
            desc: 'search的Placeholder',
            option: '',
            default: '请输入需要搜索的内容'
        },
        {
            param: 'notFound',
            type: 'string',
            desc: '无搜索内容的时候的回调',
            option: '',
            default: 'notFound'
        },
        {
            param: 'size',
            type: 'string',
            desc: 'dropdown的尺寸',
            option: 'xsmall | small | medium | large',
            default: 'medium'
        },
        {
            param: 'type',
            type: 'string(normal | primary)',
            desc: 'normal为普通的dropdownButton，primary为带命令按钮的dropdownButton',
            option: 'normal | primary',
            default: 'normal'
        },
        {
            param: 'onClickButton',
            type: 'func(e)',
            desc: 'type为primary的时候，click主命令button的时候触发',
            option: '',
            default: ''
        },
        {
            param: 'buttonProps',
            type: 'object',
            desc: '自定义按钮的一些属性，参照button',
            option: '',
            default: '{}'
        },
        {
            param: 'overlayClassName',
            type: 'string',
            desc: 'dropdown的下拉className',
            option: '',
            default: ''
        },
        {
            param: 'primaryType',
            type: 'string',
            desc: '带主命令按钮类型分为normal和primary，对应button样式',
            option: 'normal|primary',
            default: 'normal'
        },
        {
            param: 'textLink',
            type: 'bool',
            desc: '是否是文字链按钮',
            option: '',
            default: 'false'
        },
        {
            param: 'searchControlled',
            type: 'bool',
            desc: '搜索结果是否受控，受控则内部不进行筛选，不受控则内部进行筛选',
            option: '',
            default: 'false'
        }
    ]
};
