import React, {PureComponent} from 'react';
import {Toast, Button, Radio, Switch} from '@baidu/one-ui';

const themes = ['light-d20', 'light-d22', 'light-ai'];
const styles = ['info', 'error', 'loading', 'success', 'warning'];

const options = {
    content: '哈哈哈哈或或或或或或或或或或或或或或或或或或或或哈哈哈哈或或或或或或或或或或或或或或或或或或或或哈哈哈哈或或或或或或或或或或或或或或或或或或或或',
    duration: 3,
    showCloseIcon: false
};

export default class Normal extends PureComponent {
    state = {
        method: 'info',
        options: options
    }

    info = () => {
        Toast[this.state.method](options);
    };

    switchTitle = e => {
        if (e) {
            this.setState({
                options: {
                    ...this.state.options,
                    title: '这是一个带着标题的Toast'
                }
            });
        }
        else {
            this.setState({
                options: {
                    ...this.state.options,
                    title: undefined
                }
            });
        }
    }

    render() {
        return (
            <div>
                <Radio.Group
                    type="strong"
                    onChange={e => Toast.config({theme: e.target.value})}
                    options={themes}
                />
                <br />
                <Radio.Group
                    type="strong"
                    onChange={e => this.setState({method: e.target.value})}
                    options={styles}
                />
                <br />
                <Switch onChange={this.switchTitle} /> 标题
                <br />
                <br />
                <div>
                    <Button onClick={this.info}>Display {this.state.method} Toast</Button>
                </div>
            </div>
        );
    }
}
