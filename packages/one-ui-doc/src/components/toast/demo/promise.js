import React, {PureComponent} from 'react';
import {Toast, Button} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    info = () => {
        Toast
            .loading({
                content: '正在启动中...'
            })
            .then(() => Toast.success({content: '启动完成'}))
            .then(() => Toast.info({content: '启动结束'}));
    };

    render() {
        return (
            <div>
                <div>
                    <Button onClick={this.info}>Display loading Toast</Button>
                </div>
            </div>
        );
    }
}
