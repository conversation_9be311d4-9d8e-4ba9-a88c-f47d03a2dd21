import BaseComponent from '../base';

export default {
    toast: {
        value: BaseComponent,
        label: 'Toast 消息提示',
        demos: [
            {
                title: '基本使用',
                desc: '主题、样式演示。可以通过`Toast.config`切换主题，对之后创建的Toast生效',
                source: 'basic'
            },
            {
                title: '消息提示',
                desc: '普通消息提示',
                source: 'info'
            },
            {
                title: '小尺寸',
                desc: '普通消息提示-尺寸-small',
                source: 'size'
            },
            {
                title: '错误',
                desc: '错误消息提示',
                source: 'error'
            },
            {
                title: 'Loading',
                desc: '加载消息提示',
                source: 'loading'
            },
            {
                title: '成功',
                desc: '成功消息提示',
                source: 'success'
            },
            {
                title: '警告',
                desc: '警告消息提示',
                source: 'warning'
            },
            {
                title: 'Promise',
                desc: 'promise用法',
                source: 'promise'
            },
            {
                title: '带标题',
                desc: '带标题的消息提示',
                source: 'title'
            }
        ],
        apis: [
            {
                apiKey: 'ToastDoc',
                title: 'Toast.info'
            },
            {
                apiKey: 'ToastDoc',
                title: 'Toast.success'
            },
            {
                apiKey: 'ToastDoc',
                title: 'Toast.error'
            },
            {
                apiKey: 'ToastDoc',
                title: 'Toast.warning'
            },
            {
                apiKey: 'ToastDoc',
                title: 'Toast.loading'
            },
            {
                apiKey: 'ToastConfig',
                title: 'Toast.config'
            }
        ]
    }
};
