export default {
    ToastInfo: [
        {
            param: 'content',
            type: 'string|ReactNode',
            desc: '提示内容',
            option: '',
            default: ''
        },
        {
            param: 'duration',
            type: 'number',
            desc: '展示多少时间后关闭，单位为秒(s)，如果有毫秒，可以输入小数',
            option: '',
            default: '3'
        },
        {
            param: 'onClose',
            type: 'Function()',
            desc: '关闭的时候的回调',
            option: '',
            default: ''
        },
        {
            param: 'showCloseIcon',
            type: 'bool',
            desc: '是否展示关闭icon',
            option: '',
            default: 'true'
        }
    ],
    ToastSuccess: [
        {
            param: 'content',
            type: 'string|ReactNode',
            desc: '提示内容',
            option: '',
            default: ''
        },
        {
            param: 'duration',
            type: 'number',
            desc: '展示多少时间后关闭，单位为秒(s)，如果有毫秒，可以输入小数',
            option: '',
            default: '3'
        },
        {
            param: 'onClose',
            type: 'Function()',
            desc: '关闭的时候的回调',
            option: '',
            default: ''
        },
        {
            param: 'showCloseIcon',
            type: 'bool',
            desc: '是否展示关闭icon',
            option: '',
            default: 'true'
        }
    ],
    ToastError: [
        {
            param: 'content',
            type: 'string|ReactNode',
            desc: '提示内容',
            option: '',
            default: ''
        },
        {
            param: 'duration',
            type: 'number',
            desc: '展示多少时间后关闭，单位为秒(s)，如果有毫秒，可以输入小数',
            option: '',
            default: '3'
        },
        {
            param: 'onClose',
            type: 'Function()',
            desc: '关闭的时候的回调',
            option: '',
            default: ''
        },
        {
            param: 'showCloseIcon',
            type: 'bool',
            desc: '是否展示关闭icon',
            option: '',
            default: 'true'
        }
    ],
    ToastWarning: [
        {
            param: 'content',
            type: 'string|ReactNode',
            desc: '提示内容',
            option: '',
            default: ''
        },
        {
            param: 'duration',
            type: 'number',
            desc: '展示多少时间后关闭，单位为秒(s)，如果有毫秒，可以输入小数',
            option: '',
            default: '3'
        },
        {
            param: 'onClose',
            type: 'Function()',
            desc: '关闭的时候的回调',
            option: '',
            default: ''
        },
        {
            param: 'showCloseIcon',
            type: 'bool',
            desc: '是否展示关闭icon',
            option: '',
            default: 'true'
        }
    ],
    ToastLoading: [
        {
            param: 'content',
            type: 'string|ReactNode',
            desc: '提示内容',
            option: '',
            default: ''
        },
        {
            param: 'duration',
            type: 'number',
            desc: '展示多少时间后关闭，单位为秒(s)，如果有毫秒，可以输入小数',
            option: '',
            default: '3'
        },
        {
            param: 'onClose',
            type: 'Function()',
            desc: '关闭的时候的回调',
            option: '',
            default: ''
        },
        {
            param: 'showCloseIcon',
            type: 'bool',
            desc: '是否展示关闭icon',
            option: '',
            default: 'true'
        }
    ],
    ToastConfig: [
        {
            param: 'duration',
            type: 'number',
            desc: '默认自动关闭延时，单位秒',
            option: '',
            default: '3'
        },
        {
            param: 'top',
            type: '30%',
            desc: '消息距离顶部的位置',
            option: '',
            default: ''
        }
    ]
};
