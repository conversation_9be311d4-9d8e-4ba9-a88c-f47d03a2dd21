export default {
    DatePicker: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义样式名',
            option: '',
            default: ''
        },
        {
            param: 'value',
            type: 'string (2019/08/11)',
            desc: '传入的日期，受控属性',
            option: '',
            default: ''
        },
        {
            param: 'defaultValue',
            type: 'string (2019/08/11)',
            desc: '传入的日期，非受控属性',
            option: '',
            default: ''
        },
        {
            param: 'dateFormat',
            type: 'string',
            desc: '日期格式， 可以被dayjs识别的日期格式 eg: YYYY/MM/DD',
            option: '',
            default: 'YYYY/MM/DD'
        },
        {
            param: 'disabled',
            type: 'boolean',
            desc: '日期选择器是否被禁用',
            option: '',
            default: ''
        },
        {
            param: 'getPopupContainer',
            type: 'Function(triggerNode)',
            desc: '弹层渲染父节点。默认渲染到 body 上',
            option: '',
            default: '() => document.body'
        },
        {
            param: 'visible',
            type: 'boolean',
            desc: '日期选择器面板是否可视 - 受控',
            option: '',
            default: ''
        },
        {
            param: 'defaultVisible',
            type: 'boolean',
            desc: '日期选择器面板默认是否可视 - 非受控属性',
            option: '',
            default: ''
        },
        {
            param: 'onVisibleChange',
            type: 'Func(visible)',
            desc: '面板可视变化的时候触发',
            option: '',
            default: ''
        },
        {
            param: 'onChange',
            type: 'Func(value)  value为触发的date(String)',
            desc: '选中日期时触发',
            option: '',
            default: ''
        },
        {
            param: 'validateMinDate',
            type: 'String',
            desc: '可选日期的最小日期',
            option: '',
            default: ''
        },
        {
            param: 'validateMaxDate',
            type: 'String',
            desc: '可选日期的最大日期',
            option: '',
            default: ''
        },
        {
            param: 'onClickButton',
            type: 'Func(e)',
            desc: '点击日期面板的时候触发',
            option: '',
            default: ''
        },
        {
            param: 'popupPlacement',
            type: 'String',
            desc: '弹层位置',
            option: 'bottomLeft | bottomRight | topLeft | topRight',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: '日期选择器的尺寸',
            option: 'small | medium | large',
            default: 'medium'
        },
        {
            param: 'showDeleteIcon',
            type: 'boolean',
            desc: '日期选择器是否有清空功能',
            option: '',
            default: 'false'
        },
        {
            param: 'onDelete',
            type: 'func',
            desc: '点击日期选择器的清空的回调',
            option: '',
            default: ''
        },
        {
            param: 'validateDisabled',
            type: 'func({dayItem, timeStamp, timeStampFunc})',
            desc: '日期是否disabled校验，dayItem：日期，timeStamp：时间戳，timeStampFunc：时间戳函数',
            option: '',
            default: ''
        },
        {
            param: 'hideInput',
            type: 'boolean',
            desc: '是否隐藏日期面板顶部的输入框',
            option: '',
            default: 'false'
        }
    ],
    RangePicker: [
        {
            param: 'value',
            type: 'Array ["2019/08/11", "2019/11/11"]',
            desc: '传入的日期，受控属性，第一位为开始日期，第二位为结束日期',
            option: '',
            default: ''
        },
        {
            param: 'defaultValue',
            type: 'Array ["2019/08/11", "2019/11/11"]',
            desc: '默认传入的日期，非受控属性，第一位为开始日期，第二位为结束日期',
            option: '',
            default: ''
        },
        {
            param: 'dateFormat',
            type: 'string',
            desc: '日期格式， 可以被dayjs识别的日期格式 eg: YYYY/MM/DD',
            option: '',
            default: 'YYYY/MM/DD'
        },
        {
            param: 'disabled',
            type: 'boolean',
            desc: '日期选择器是否被禁用',
            option: '',
            default: ''
        },
        {
            param: 'getPopupContainer',
            type: 'Function(triggerNode)',
            desc: '弹层渲染父节点。默认渲染到 body 上',
            option: '',
            default: '() => document.body'
        },
        {
            param: 'visible',
            type: 'boolean',
            desc: '日期选择器面板是否可视 - 受控',
            option: '',
            default: ''
        },
        {
            param: 'defaultVisible',
            type: 'boolean',
            desc: '日期选择器面板默认是否可视 - 非受控属性',
            option: '',
            default: ''
        },
        {
            param: 'onVisibleChange',
            type: 'Func(visible)',
            desc: '面板可视变化的时候触发',
            option: '',
            default: ''
        },
        {
            param: 'onChange',
            type: 'Func(value)  value为触发的date(String)',
            desc: '选中日期时触发',
            option: '',
            default: ''
        },
        {
            param: 'validateMinDate',
            type: 'String',
            desc: '可选日期的最小日期',
            option: '',
            default: ''
        },
        {
            param: 'validateMaxDate',
            type: 'String',
            desc: '可选日期的最大日期',
            option: '',
            default: ''
        },
        {
            param: 'onClickButton',
            type: 'Func(e)',
            desc: '点击日期面板的时候触发',
            option: '',
            default: ''
        },
        {
            param: 'shortcuts',
            type: 'Array',
            desc: `自定义快捷方式，shortcuts 列表项中的 from 及 to 字段格式相同，分别用来设置开始结束日期的计算方式。格式为 number|Object，默认值为 0。
            类型为 number 时，表示以「今天」为基准，偏移指定天数，比如 -1 等价于 { startOf: 'day', days: -1 }，即「昨天」；
            类型为 Object 时，支持的格式为：{startOf: string=, days: number=, weeks: number=, months: number=, }。
            字段详情:
            计算方式是先根据 startOf 设置基准，然后根据其它字段进行偏移量的叠加，具体请见示例`,
            option: '',
            default: ''
        },
        {
            param: 'popupPlacement',
            type: 'String',
            desc: '弹层位置',
            option: 'bottomLeft | bottomRight | topLeft | topRight',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: '日期选择器的尺寸',
            option: 'small | medium | large',
            default: 'medium'
        },
        {
            param: 'showDeleteIcon',
            type: 'boolean',
            desc: '日期选择器是否有清空功能',
            option: '',
            default: 'false'
        },
        {
            param: 'onDelete',
            type: 'func',
            desc: '点击日期选择器的清空的回调',
            option: '',
            default: ''
        },
        {
            param: 'validateDisabled',
            type: 'func({dayItem, timeStamp, timeStampFunc, selectedValue})',
            desc: '日期是否disabled校验，dayItem：日期，timeStamp：时间戳，timeStampFunc：时间戳函数，selectedValue：已选时间',
            option: '',
            default: ''
        },
        {
            param: 'hideInput',
            type: 'boolean',
            desc: '是否隐藏日期面板顶部的输入框',
            option: '',
            default: 'false'
        }
    ],
    MonthPicker: [
        {
            param: 'value',
            type: 'string (2019/08)',
            desc: '传入的日期，受控属性',
            option: '',
            default: ''
        },
        {
            param: 'defaultValue',
            type: 'string (2019/08)',
            desc: '传入的日期，非受控属性',
            option: '',
            default: ''
        },
        {
            param: 'dateFormat',
            type: 'string',
            desc: '日期格式， 可以被dayjs识别的日期格式 eg: YYYY/MM',
            option: '',
            default: 'YYYY/MM'
        },
        {
            param: 'disabled',
            type: 'boolean',
            desc: '日期选择器是否被禁用',
            option: '',
            default: ''
        },
        {
            param: 'getPopupContainer',
            type: 'Function(triggerNode)',
            desc: '弹层渲染父节点。默认渲染到 body 上',
            option: '',
            default: '() => document.body'
        },
        {
            param: 'visible',
            type: 'boolean',
            desc: '日期选择器面板是否可视 - 受控',
            option: '',
            default: ''
        },
        {
            param: 'defaultVisible',
            type: 'boolean',
            desc: '日期选择器面板默认是否可视 - 非受控属性',
            option: '',
            default: ''
        },
        {
            param: 'onVisibleChange',
            type: 'Func(visible)',
            desc: '面板可视变化的时候触发',
            option: '',
            default: ''
        },
        {
            param: 'onChange',
            type: 'Func(value)  value为触发的date(String)',
            desc: '选中日期时触发',
            option: '',
            default: ''
        },
        {
            param: 'validateMinDate',
            type: 'String',
            desc: '可选日期的最小日期',
            option: '',
            default: ''
        },
        {
            param: 'validateMaxDate',
            type: 'String',
            desc: '可选日期的最大日期',
            option: '',
            default: ''
        },
        {
            param: 'onClickButton',
            type: 'Func(e)',
            desc: '点击日期面板的时候触发',
            option: '',
            default: ''
        },
        {
            param: 'popupPlacement',
            type: 'String',
            desc: '弹层位置',
            option: 'bottomLeft | bottomRight | topLeft | topRight',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: '日期选择器的尺寸',
            option: 'small | medium | large',
            default: 'medium'
        },
        {
            param: 'customButtonTitle',
            type: 'reactNode',
            desc: '自定义标题文案',
            option: '',
            default: ''
        },
        {
            param: 'showDeleteIcon',
            type: 'boolean',
            desc: '日期选择器是否有清空功能',
            option: '',
            default: 'false'
        },
        {
            param: 'onDelete',
            type: 'func',
            desc: '点击日期选择器的清空的回调',
            option: '',
            default: ''
        },
        {
            param: 'hideInput',
            type: 'boolean',
            desc: '是否隐藏日期面板顶部的输入框',
            option: '',
            default: 'false'
        }
    ],
    Calendar: [
        {
            param: 'hideInput',
            type: 'boolean',
            desc: '是否隐藏日期面板顶部的输入框',
            option: '',
            default: 'false'
        },
        {
            param: 'CustomHeader',
            type: 'Component',
            desc: '自定义渲染日历顶部',
            option: '',
            default: ''
        },
        {
            param: 'CustomFooter',
            type: 'Component',
            desc: '自定义渲染日历底部部',
            option: '',
            default: ''
        },
        {
            param: 'CustomDayRender',
            type: 'Component',
            desc: '自定义渲染日期',
            option: '',
            default: ''
        }
    ]
};
