import BaseComponent from '../base';

export default {
    datePicker: {
        value: BaseComponent,
        label: 'DatePicker 日期选择',
        demos: [
            {
                title: '普通日期选择',
                desc: '日期选择',
                source: 'normal'
            },
            {
                title: '可选范围',
                desc: '如果不传的话，默认为当前年份的前十年和后十年',
                source: 'minAndMax'
            },
            {
                title: '区间选择',
                desc: '日期选择 - 选择时间段',
                source: 'multiple'
            },
            {
                title: '区间-自然周',
                desc: '通过{mode}设置{week}，选择自然周',
                source: 'week'
            },
            {
                title: '尺寸',
                desc: '',
                source: 'size'
            },
            {
                title: '快捷操作',
                desc: '日期选择 - 选择时间段 - 快捷操作',
                source: 'shortCut'
            },
            {
                title: '选择月份',
                desc: '日期选择 - 选择月份',
                source: 'monthPicker'
            },
            {
                title: '自定义禁用',
                desc: '日期选择 - 支持自定义校验一些disabled日期',
                source: 'disabled'
            },
            {
                title: '自定义触发区',
                desc: '',
                source: 'children'
            },
            {
                title: '隐藏输入区',
                desc: '是否隐藏日期面板顶部的输入框, 如果隐藏则日期面板不会覆盖触发元素',
                source: 'hideInput'
            }
        ],
        apis: [
            {
                apiKey: 'DatePicker',
                title: 'DatePicker'
            },
            {
                apiKey: 'RangePicker',
                title: 'DatePicker.RangePicker'
            },
            {
                apiKey: 'MonthPicker',
                title: 'DatePicker.MonthPicker'
            },
            {
                apiKey: 'Calendar',
                title: 'Calendar'
            }
        ]
    }
};
