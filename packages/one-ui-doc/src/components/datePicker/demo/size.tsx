import React, {useState} from 'react';
import {DatePicker, Radio} from '@baidu/one-ui';
import type {DatePickerProps} from '@baidu/one-ui';

export default () => {
    type Size = DatePickerProps['size'];
    const [size, setSize] = useState<Size>('medium');
    return (
        <>
            <Radio.Group
                type="strong"
                size="small"
                value={size}
                options={['xsmall', 'small', 'medium', 'large']}
                onChange={e => setSize(e.target.value as Size)}
            />
            <br />
            <DatePicker size={size} />
            <br />
            <br />
            <DatePicker.RangePicker size={size} />
            <br />
            <br />
            <DatePicker.MonthPicker size={size} placeholder="请选择月份" />
        </>
    );
}
