import React, {PureComponent} from 'react';
import {DatePicker} from '@baidu/one-ui';

export default class NormalDatePicker extends PureComponent {
    state = {
        value: '2019/09/01'
    }

    onChange = value => {
        this.setState({
            value
        });
    }

    render() {
        return (
            <div>
                无初始值
                <br />
                <br />
                <DatePicker />
                <br />
                <br />
                <br />
                默认初始值
                <br />
                <br />
                <DatePicker defaultValue="2019-09-01" />
                <br />
                <br />
                <br />
                受控状态
                <br />
                <br />
                <DatePicker value={this.state.value} onChange={this.onChange} />
                <br />
                <br />
                小号日期选择器
                <br />
                <br />
                <DatePicker size="small" />
                <br />
                <br />
                展示关闭按钮
                <br />
                <br />
                <DatePicker showDeleteIcon />
                <br />
                <br />
                禁止态
                <br />
                <br />
                <DatePicker disabled />
                <br />
                <br />
            </div>
        );
    }
}
