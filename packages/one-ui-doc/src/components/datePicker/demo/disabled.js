import React, {PureComponent} from 'react';
import {DatePicker} from '@baidu/one-ui';

export default class NormalDatePicker extends PureComponent {
    state = {
        value: ['2019/05/11', '2019/06/13']
    }

    onChange = value => {
        this.setState({
            value
        });
    }

    validateDisabled = props => {
        const beginDataStamp = props.getTimeStamp('2019/05/11');
        const endDataStamp = props.getTimeStamp('2019/06/13');

        const currentDataStamp = props.getTimeStamp(props.dayItem);

        if (currentDataStamp > beginDataStamp && currentDataStamp < endDataStamp) {
            return true;
        }
        return false;
    };

    render() {
        return (
            <div>
                <DatePicker.RangePicker
                    value={this.state.value}
                    onChange={this.onChange}
                    validateMinDate="2019/03/11"
                    validateMaxDate="2019/11/21"
                    validateDisabled={this.validateDisabled}
                />
            </div>
        );
    }
}
