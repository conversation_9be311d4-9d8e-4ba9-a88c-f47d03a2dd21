import React, {PureComponent} from 'react';
import {DatePicker} from '@baidu/one-ui';

export default class NormalDatePicker extends PureComponent {
    state = {
        value: ['2019/05/11', '2019/06/13']
    }

    onChange = value => {
        this.setState({
            value
        });
    }

    render() {
        return (
            <div>
                无初始值
                <br />
                <br />
                <DatePicker.RangePicker />
                <br />
                <br />
                <br />
                默认初始值
                <br />
                <br />
                <DatePicker.RangePicker
                    defaultValue={['2020/08/23', '2020/08/29']}
                />
                <br />
                <br />
                <br />
                受控状态
                <br />
                <br />
                <DatePicker.RangePicker value={this.state.value} onChange={this.onChange} />
                <br />
                <br />
                <br />
                存在最大日期和最小日期
                <br />
                <br />
                <DatePicker.RangePicker
                    value={this.state.value}
                    onChange={this.onChange}
                    validateMinDate="2019/05/11"
                    validateMaxDate="2019/11/21"
                />
                <br />
                <br />
                小号尺寸
                <br />
                <br />
                <DatePicker.RangePicker size="small" value={this.state.value} onChange={this.onChange} />
                <br />
                <br />
                展示可关闭按钮
                <br />
                <br />
                <DatePicker.RangePicker showDeleteIcon />
            </div>
        );
    }
}
