import React, { useState } from 'react';
import {DatePicker, Switch, Button} from '@baidu/one-ui';

export default () => {
    const [hideInput, setHideInput] = useState<boolean>(true);

    return (
        <>
            是否隐藏日期面板顶部的输入框
            <Switch checked={hideInput} onChange={setHideInput} />
            <br />
            <br />
            <DatePicker hideInput={hideInput} size="xsmall" />
            <br />
            <br />
            <DatePicker.RangePicker hideInput={hideInput} size="small" />
            <br />
            <br />
            <DatePicker.MonthPicker hideInput={hideInput} />
            <br />
            <br />
            <DatePicker hideInput={hideInput} size="large">
                <Button type="ghost-strong">选择日期</Button>
            </DatePicker>
        </>
    );
}
