import React, {PureComponent} from 'react';
import {DatePicker} from '@baidu/one-ui';

export default class NormalDatePicker extends PureComponent {
    state = {
        value: '2019/09'
    }

    onChange = value => {
        this.setState({
            value
        });
    }

    render() {
        return (
            <div>
                无初始值
                <br />
                <br />
                <DatePicker.MonthPicker />
                <br />
                <br />
                <br />
                默认初始值
                <br />
                <br />
                <DatePicker.MonthPicker defaultValue="2019/09" />
                <br />
                <br />
                <br />
                受控状态
                <br />
                <br />
                <DatePicker.MonthPicker value={this.state.value} onChange={this.onChange} />
                <br />
                <br />
                存在最大日期和最小日期
                <br />
                <br />
                <DatePicker.MonthPicker
                    value={this.state.value}
                    onChange={this.onChange}
                    validateMinDate="2019/03"
                    validateMaxDate="2019/11"
                />
                <br />
                <br />
                小号尺寸
                <br />
                <br />
                <DatePicker.MonthPicker defaultValue="2019/09" size="small" />
                <br />
                <br />
                展示可关闭按钮
                <br />
                <br />
                <DatePicker.MonthPicker defaultValue="2019/09" showDeleteIcon />
            </div>
        );
    }
}
