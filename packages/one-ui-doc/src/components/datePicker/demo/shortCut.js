import React, {PureComponent} from 'react';
import {DatePicker} from '@baidu/one-ui';

export default class NormalDatePicker extends PureComponent {
    state = {
        value: ['2019/09/01', '2019/11/11']
    }

    onChange = value => {
        this.setState({
            value
        });
    }

    render() {
        return (
            <div>
                <br />
                快捷方式
                <br />
                <br />
                <DatePicker.RangePicker
                    value={this.state.value}
                    onChange={this.onChange}
                    shortcuts={[
                        {
                            label: '今天',
                            to: 0,
                            from: 0
                        },
                        {
                            label: '昨天',
                            to: -1,
                            from: -1
                        },
                        {
                            label: '最近7天',
                            to: 0,
                            from: -6
                        },
                        {
                            label: '最近14天',
                            to: 0,
                            from: -13
                        },
                        {
                            label: '最近30天',
                            to: 0,
                            from: -29
                        },
                        {
                            label: '上个月',
                            from: {
                                startOf: 'month',
                                months: -1
                            },
                            to: {
                                startOf: 'month',
                                days: -1
                            }
                        },
                        {
                            label: '本月',
                            // 本月第一天
                            from: {
                                startOf: 'month'
                            },
                            // 今天
                            to: 0
                        },
                        {
                            label: '本周',
                            // 本周第一天，days 为 0 是可以省略的
                            from: {
                                startOf: 'week',
                                days: 0
                            },
                            // 今天
                            to: 0
                        },
                        {
                            label: '上周',
                            // 本周第一天，days 为 0 是可以省略的
                            from: {
                                startOf: 'week',
                                weeks: -1
                            },
                            // 今天
                            to: {
                                startOf: 'week',
                                days: -1
                            }
                        }
                    ]}
                />
            </div>
        );
    }
}
