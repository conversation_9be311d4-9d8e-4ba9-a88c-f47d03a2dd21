import React, {PureComponent} from 'react';
import {DatePicker} from '@baidu/one-ui';

export default class NormalDatePicker extends PureComponent {
    state = {
        value: '2019/09/01'
    }

    onChange = value => {
        this.setState({
            value
        });
    }

    render() {
        return (
            <div>
                存在最小日期和最大日期的日历
                <br />
                <br />
                <DatePicker
                    value={this.state.value}
                    onChange={this.onChange}
                    validateMinDate="2019/03/21"
                    validateMaxDate="2019/11/21"
                />
            </div>
        );
    }
}
