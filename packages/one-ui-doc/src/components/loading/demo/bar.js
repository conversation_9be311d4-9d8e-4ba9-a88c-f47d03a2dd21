import React, {useState} from 'react';
import {Loading, Button} from '@baidu/one-ui';

export default () => {
    const [loading, setLoading] = useState(false);

    return (
        <>
            <Button size="small" type="primary" onClick={() => setLoading(!loading)}>
                {loading ? '结束' : '开始'}
            </Button>
            <Loading.Bar size="small" loading={loading} />
            <Loading.Bar size="medium" loading={loading} />
        </>
    );
};
