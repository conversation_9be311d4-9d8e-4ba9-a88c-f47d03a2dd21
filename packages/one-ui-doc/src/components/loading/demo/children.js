import React, {PureComponent} from 'react';
import {Loading, Button, Switch} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {loading: false};
    toggle = value => {
        this.setState({loading: value});
    }
    render() {
        return (
            <div>
                <div style={{border: '1px solid', marginBottom: '10px'}}>
                    <Loading loading={this.state.loading} tip="加载中">
                        <div>loading content</div>
                        <div>children</div>
                        <div>loading content</div>
                        <div>children</div>
                        <div>loading content</div>
                        <div>children</div>
                    </Loading>
                </div>
                <div style={{border: '1px solid', marginBottom: '10px'}}>
                    <Loading loading={this.state.loading} tip="加载中" type="strong">
                        <div>loading content</div>
                        <div>children</div>
                        <div>loading content</div>
                        <div>children</div>
                        <div>loading content</div>
                        <div>children</div>
                    </Loading>
                </div>
                <div style={{border: '1px solid', marginBottom: '10px'}}>
                    <Loading loading={this.state.loading} tip="加载中" type="reverse">
                        <div>loading content</div>
                        <div>children</div>
                        <div>loading content</div>
                        <div>children</div>
                        <div>loading content</div>
                        <div>children</div>
                    </Loading>
                </div>
                <div>
                    Loading state: <Switch checked={this.state.loading} onChange={this.toggle} />
                </div>
            </div>
        );
    }
}
