export default {
    Loading: [
        {
            param: 'loading',
            type: 'boolean',
            desc: '是否展示加载',
            option: '',
            default: 'true'
        },
        {
            param: 'size',
            type: 'string',
            desc: 'loading图标大小',
            option: 'small|medium|large',
            default: 'medium'
        },
        {
            param: 'tip',
            type: 'string',
            desc: 'loading时的文案提示',
            option: '',
            default: ''
        },
        {
            param: 'type',
            type: 'string',
            desc: 'loading图标的样式',
            option: 'normal|strong|reverse',
            default: 'normal'
        },
        {
            param: 'className',
            type: 'string',
            desc: '自定义class',
            option: '',
            default: ''
        },
        {
            param: 'style',
            type: 'object',
            desc: '自定义style',
            option: '',
            default: ''
        },
        {
            param: 'textDirection',
            type: 'string',
            desc: '带文字的icon的方向，可以是垂直模式或者水平模式，参照示例',
            option: 'horizontal | vertical',
            default: 'horizontal'
        },
        {
            param: 'CustomIconNode',
            type: 'ReactNode',
            desc: '自定义loading的icon',
            option: '',
            default: ''
        }
    ]
};
