import BaseComponent from '../base';

export default {
    loading: {
        value: BaseComponent,
        label: 'Loading 加载',
        demos: [
            {
                title: '基础',
                desc: '最基础用法',
                source: 'normal'
            },
            {
                title: '尺寸',
                desc: '可以通过size属性设置图标大小',
                source: 'size'
            },
            {
                title: '遮罩',
                desc: '通过子元素实现遮罩方式',
                source: 'children'
            },
            {
                title: '方向',
                desc: '默认横向，可支持纵向`vertical`',
                source: 'direction'
            },
            {
                title: '类型',
                desc: '不同类型的loading，普通，加强和反白',
                source: 'type'
            },
            {
                title: '条形',
                desc: '',
                source: 'bar'
            }
        ],
        apis: [
            {
                apiKey: 'Loading',
                title: 'Loading'
            },
            {
                apiKey: 'LoadingBar',
                title: 'Loading.Bar'
            }
        ]
    }
};
