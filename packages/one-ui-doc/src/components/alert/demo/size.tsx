import React, {useState} from 'react';
import {Alert, Radio, AlertProps} from '@baidu/one-ui';

type AlertSize = AlertProps['size'];

export default () => {
    const [size, setSize] = useState<AlertSize>('small');
    return (
        <>
            <Radio.Group
                options={['small', 'medium']}
                value={size}
                onChange={e => setSize(e.target.value as AlertSize)}
                type="strong"
                size="small"
            />
            <br />
            <Alert size={size} content="info" />
        </>
    );
};
