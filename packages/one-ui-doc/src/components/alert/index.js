import BaseComponent from '../base';

export default {
    alert: {
        value: BaseComponent,
        label: 'Alert 警示',
        demos: [
            {
                title: '类型',
                desc: '可通过`type`设置，可选值`info` | `success` | `warning` | `error`',
                source: 'type'
            },
            {
                title: '尺寸',
                desc: '可通过`size`设置，可选值`small` | `medium`',
                source: 'size'
            },
            {
                title: '可关闭',
                desc: '可通过`closable`设置是否可关闭',
                source: 'closable'
            },
            {
                title: '图标',
                desc: '可通过`showIcon`开启图标展示，默认根据`type`展示对应类型的图标',
                source: 'showIcon'
            },
            {
                title: '翻页',
                desc: '使用`Alert.Page`可翻页展示多条信息',
                source: 'page'
            }
        ],
        apis: [
            {
                apiKey: 'Alert',
                title: 'Alert'
            },
            {
                apiKey: 'AlertPage',
                title: 'Alert.Page'
            }
        ]
    }
};
