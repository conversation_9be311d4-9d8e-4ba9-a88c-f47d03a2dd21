export default {
    Alert: [
        {
            param: 'type',
            type: 'string',
            desc: '警示的类型,成功,通知,警告,错误',
            option: 'success | info | warning | error',
            default: 'info'
        },
        {
            param: 'title',
            type: 'string | reactNode',
            desc: '标题',
            option: '',
            default: ''
        },
        {
            param: 'content',
            type: 'string | reactNode',
            desc: '内容',
            option: '',
            default: ''
        },
        {
            param: 'closable',
            type: 'bool',
            desc: '是否可被关闭',
            option: '',
            default: ''
        },
        {
            param: 'visible',
            type: 'bool',
            desc: '是否可见',
            option: '',
            default: ''
        },
        {
            param: 'showIcon',
            type: 'bool',
            desc: '是否展示icon',
            option: '',
            default: ''
        },
        {
            param: 'icon',
            type: 'string | react Node',
            desc: '展示的icon, string的话为 <Icon type="xxx" /> or icon',
            option: '',
            default: ''
        },
        {
            param: 'className',
            type: 'string',
            desc: '自定义className',
            option: '',
            default: ''
        },
        {
            param: 'style',
            type: 'object',
            desc: '自定义style',
            option: '',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: '尺寸',
            option: 'medium | small',
            default: 'medium'
        }
    ],
    AlertPage: [{
        param: 'dataSource',
        type: '[Alert]',
        desc: '数据源（受控）, [Alert] 元素必须为Alert组件',
        option: '',
        default: ''
    }, {
        param: 'defaultDataSource',
        type: '[Alert]',
        desc: '数据源（非受控）, [Alert] 元素必须为Alert组件',
        option: '',
        default: ''
    }, {
        param: 'onClose',
        type: 'func(index)',
        desc: '当其中有某一个alert的onClose触发的时候，会触发该方法',
        option: '',
        default: ''
    }, {
        param: 'initialSlide',
        type: 'number',
        desc: '（非受控）初始状态下从第几页开始（index, 第一页index: 0）',
        option: '',
        default: 0
    }, {
        param: 'slider',
        type: 'number',
        desc: '（受控）从第几页开始（index, 第一页index: 0）',
        option: '',
        default: ''
    }, {
        param: 'size',
        type: 'string',
        desc: '尺寸',
        option: 'small | medium',
        default: 'medium'
    }, {
        param: 'onPrevChange',
        type: 'func(index)',
        desc: '往前翻页的回调',
        option: '',
        default: ''
    }, {
        param: 'onNextChange',
        type: 'func(index)',
        desc: '往后翻页的回调',
        option: '',
        default: ''
    }]
};
