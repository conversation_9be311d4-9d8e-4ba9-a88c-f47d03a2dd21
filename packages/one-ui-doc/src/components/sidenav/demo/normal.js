/**
 * @file 侧导航demo示例
 * <AUTHOR>
 * @date 2020-03-12
 */
import React from 'react';
import {Sidenav, IconSvg, Button, Menu, Link} from '@baidu/one-ui';
import * as ReactRouterDOM from 'react-router-dom';
const {BrowserRouter, Link: RouterLink} = ReactRouterDOM;
const SubMenu = Menu.SubMenu;

export default class Normal extends React.PureComponent {
    state = {collapsed: false};
    render() {
        const {collapsed} = this.state;
        return (
            <>
                {/* <BrowserRouter> */}
                <Sidenav collapsed={collapsed}>
                    <Menu.Item key="1" icon={<IconSvg type="filter" />}>
                        <RouterLink to="/components/sidenav">Router Link</RouterLink>
                    </Menu.Item>
                    <Menu.Item key="2" href="/components/sidenav" icon={<IconSvg type="filter" />}>
                        Option(href)
                    </Menu.Item>
                    <Menu.Item key="3" icon={<IconSvg type="calendar" />}>
                        <Link isAtag toUrl="/components/sidenav">Link</Link>
                    </Menu.Item>
                    <Menu.Item key="4" icon={<IconSvg type="calendar" />}>
                        Option 4
                    </Menu.Item>
                    <SubMenu
                        key="sub1"
                        icon={<IconSvg type="search" />}
                        title="Navigation One"
                    >
                        <Menu.Item key="5">Option 5</Menu.Item>
                        <Menu.Item key="6">Option 6</Menu.Item>
                        <Menu.Item key="7">Option 7</Menu.Item>
                        <Menu.Item key="8">Option 8</Menu.Item>
                    </SubMenu>
                    <Menu.Divider />
                    <SubMenu
                        key="sub2"
                        icon={<IconSvg type="calendar" />}
                        title="Navigation Two"
                    >
                        <Menu.Item key="9">Option 9</Menu.Item>
                        <Menu.Item key="10">Option 10</Menu.Item>
                        <SubMenu key="sub3" title="Submenu">
                            <Menu.Item key="11">Option 11</Menu.Item>
                            <Menu.Item key="12">Option 12</Menu.Item>
                        </SubMenu>
                    </SubMenu>
                </Sidenav>
                <br />
                <br />
                <Button onClick={() => this.setState({collapsed: !collapsed})}>
                    {collapsed ? '展开' : '收起'}
                </Button>
                {/* </BrowserRouter> */}
            </>
        );
    }
}
