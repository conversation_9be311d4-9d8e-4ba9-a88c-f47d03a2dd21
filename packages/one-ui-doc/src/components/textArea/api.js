export default {
    TextArea: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义样式名',
            option: '',
            default: ''
        },
        {
            param: 'width',
            type: 'number',
            desc: '指定宽度',
            option: '',
            default: 160
        },
        {
            param: 'value',
            type: 'string',
            desc: '输入框值-受控',
            option: '',
            default: ''
        },
        {
            param: 'defaultValue',
            type: 'string',
            desc: '输入框值-非受控',
            option: '',
            default: ''
        },
        {
            param: 'placeholder',
            type: 'string',
            desc: '在用户输入值之前显示的提示信息',
            option: '',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: '尺寸',
            option: 'small|medium',
            default: 'medium'
        },
        {
            param: 'disabled',
            type: 'boolean',
            desc: '是否不可操作',
            option: '',
            default: 'false'
        },
        {
            param: 'readOnly',
            type: 'boolean',
            desc: '是否只读',
            option: '',
            default: 'false'
        },
        {
            param: 'minRows',
            type: 'number',
            desc: '最小可调节的高度',
            option: '',
            default: '2'
        },
        {
            param: 'maxRows',
            type: 'number',
            desc: '最大可调节的高度',
            option: '',
            default: '8'
        },
        {
            param: 'isRequired',
            type: 'boolean',
            desc: '是否必填',
            option: '',
            default: 'true'
        },
        {
            param: 'requiredErrorMessage',
            type: 'string',
            desc: '当为空时的错误信息',
            option: '',
            default: ''
        },
        {
            param: 'minLen',
            type: 'number',
            desc: '最短字符长度',
            option: '',
            default: ''
        },
        {
            param: 'minLenErrorMessage',
            type: 'string',
            desc: '当比最短字符短时的错误信息',
            option: '',
            default: ''
        },
        {
            param: 'maxLen',
            type: 'number',
            desc: '最长字符长度',
            option: '',
            default: ''
        },
        {
            param: 'maxLenErrorMessage',
            type: 'string',
            desc: '当比最长字符长时的错误信息',
            option: '',
            default: ''
        },
        {
            param: 'tipText',
            type: 'string',
            desc: '提示信息',
            option: '',
            default: ''
        },
        {
            param: 'errorMessage',
            type: 'string',
            desc: '错误信息',
            option: '',
            default: ''
        },
        {
            param: 'location',
            type: 'string',
            desc: '位置信息',
            option: 'right、layer、bottom',
            default: 'right'
        },
        {
            param: 'errorLocation',
            type: 'string',
            desc: '错误信息位置',
            option: 'right、layer、bottom',
            default: 'location的值'
        },
        {
            param: 'tipLocation',
            type: 'string',
            desc: '提示信息位置',
            option: 'right、layer、bottom',
            default: 'location的值'
        },
        {
            param: 'countMode',
            type: 'string',
            desc: '计数方式<br />当为cn时，一个中文字符记为{1}个字符，当为en时一个中文字符记为{2}个字符<br />当计数时，前后空格会trim掉不计数，中间的空格会计数',
            option: 'cn、en',
            default: 'cn'
        },
        {
            param: 'filterArray',
            type: 'string[]',
            desc: '某些字符不计数',
            option: '',
            default: ''
        },
        {
            param: 'getLength',
            type: 'Function(value)',
            desc: '当countMode中两种校验方式都不满足时，自定义校验方式',
            option: '',
            default: ''
        },
        {
            param: 'onChange',
            type: 'Function({value: string, errorMessage: string})',
            desc: '变化时回调函数',
            option: '',
            default: ''
        },
        {
            param: 'showErrorWithoutErrorMessage',
            type: 'bool',
            desc: '是否报错，在没有errorMessage的情况下',
            option: '',
            default: 'false'
        }, {
            param: 'showErrorMessage',
            type: 'bool',
            desc: '错误信息；当不传此值时，会内部校验是否非空、最短字符、最长字符；当传此值时，错误信息以此值为准',
            option: '',
            default: 'true'
        }, {
            param: 'originTextAreaProps',
            type: 'object',
            desc: '挂载在原生的textarea上的属性，比较粗暴，不建议使用',
            option: '',
            default: '{}'
        }
    ],
    TextAreaMethods: [
        {
            param: 'focus()',
            type: '',
            desc: '获取焦点',
            option: '',
            default: ''
        },
        {
            param: 'blur()',
            type: '',
            desc: '失去焦点',
            option: '',
            default: ''
        }
    ]
};
