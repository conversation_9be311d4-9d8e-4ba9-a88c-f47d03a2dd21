import React, {useRef} from 'react';
import {<PERSON><PERSON><PERSON>, Button, Stack} from '@baidu/one-ui';

export default function FocusOrBlur() {
    const textAreaRef = useRef(null);

    return (
        <>
            <Stack gap="small" style={{marginBottom: 16}}>
                <Button onClick={() => textAreaRef.current.focus()}>Focus</Button>
                <Button onClick={() => textAreaRef.current.blur()}>Blur</Button>
            </Stack>
            <TextArea
                ref={textAreaRef}
            />
        </>
    ); 
}
