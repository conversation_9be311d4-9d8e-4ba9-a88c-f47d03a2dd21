import React, {PureComponent} from 'react';
import {TextArea} from '@baidu/one-ui';

export default class ErrorMessage extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            errorMessage: undefined,
            value: ''
        };
    }
    onChange = e => {
        const value = e.value;
        this.setState({
            value,
            errorMessage: (value.indexOf('%') > -1 ? '含有特殊字符' : undefined)
        });
    }
    render() {
        const textAreaProps = {
            onChange: this.onChange,
            value: this.state.value,
            errorMessage: this.state.errorMessage,
            maxLen: 5
        };
        return <TextArea {...textAreaProps} />;
    }
}
