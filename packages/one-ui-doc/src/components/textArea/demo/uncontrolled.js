import React, {PureComponent} from 'react';
import {TextArea} from '@baidu/one-ui';

export default class Demo extends PureComponent {

    onChange = e => {
        console.log(e);
    };

    render() {
        const commonProps = {
            placeholder: '1~100个字符',
            maxLen: 100,
            minLen: 8,
            tipText: '我是提示信息',
            defaultValue: '这是一个非受控组件',
            onChange: this.onChange
        };

        return (
            <div>
                <div>非受控组件</div>
                <br />
                <TextArea {...commonProps}/>
                <br />
                <br />
            </div>
        );
    }
}
