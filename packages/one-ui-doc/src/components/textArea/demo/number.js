import React from 'react';
import {TextArea} from '@baidu/one-ui';

const getLength = str => {
    if(/[0-9]/i.test(str)){
        return str.match(/[0-9]/ig).length;
    }
    return 0;
};

export default () => {
    return <div>
        <div>需要显示输入内容的计数信息，默认汉字作为两个字符计数</div>
        <br />
        <TextArea maxLen={10} />
        <br />
        <br />
        <div>汉字作为1个字符计数</div>
        <br />
        <TextArea maxLen={10} countMode="en" />
        <br />
        <br />
        <div>自定义计数规则，比如只记输入数字的长度</div>
        <br />
        <TextArea maxLen={10} getLength={getLength}/>
        <br />
        <br />
        <div>当没有最大值时，不显示输入内容的计数信息</div>
        <br />
        <TextArea />
        <br />
        <br />
        <div>设置部分输入内容不计数，当输入{}时不会计数</div>
        <br />
        <TextArea maxLen={80} filterArray={["{", "}"]}/>
    </div>
};
