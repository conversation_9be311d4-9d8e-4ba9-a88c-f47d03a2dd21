import React, {PureComponent} from 'react';
import {TextArea} from '@baidu/one-ui';

export default class Demo extends PureComponent {
    state = {
        value: '这是一个受控组件'
    };

    onChange = e => {
        console.log(e.value);
        this.setState({
            value: e.value
        });
    };

    render() {
        const commonProps = {
            placeholder: '1~100个字符',
            maxLen: 100,
            minLen: 8,
            tipText: '我是提示信息',
            value: this.state.value,
            onChange: this.onChange
        };

        return (
            <div>
                <div>受控组件</div>
                <br />
                <TextArea {...commonProps} />
                <br />
                <br />
            </div>
        );
    }
}
