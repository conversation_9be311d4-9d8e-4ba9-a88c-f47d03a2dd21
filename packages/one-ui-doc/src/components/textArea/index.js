import BaseComponent from '../base';

export default {
    textArea: {
        value: BaseComponent,
        label: 'TextArea 长文本输入框',
        demos: [
            {
                title: '非受控',
                desc: '非受控组件',
                source: 'uncontrolled'
            },
            {
                title: '受控',
                desc: '受控组件',
                source: 'controlled'
            },
            {
                title: '尺寸',
                desc: '长文本输入框分两个尺寸{small} {medium}',
                source: 'size'
            },
            {
                title: '禁用',
                desc: '',
                source: 'disabled'
            },
            {
                title: '计数',
                desc: '是否展示输入内容的计数信息，当有最大可输入值时才展示输入内容的计数信息；可以设置部分输入内容不计数。',
                source: 'number'
            },
            {
                title: '设置行',
                desc: '长文本输入框默认最小高度为{2}行，最大高度为{8}行',
                source: 'rows'
            },
            {
                title: '错误信息',
                desc: '',
                source: 'errorMessage'
            },
            {
                title: '错误信息位置',
                desc: '{右方} {下方} {浮层}',
                source: 'errorLocation'
            },
            {
                title: '获取/失去焦点',
                desc: '',
                source: 'FocusOrBlur'
            }
        ],
        apis: [
            {
                apiKey: 'TextArea',
                title: 'TextArea'
            },
            {
                apiKey: 'TextAreaMethods',
                title: 'Methods'
            }
        ]
    }
};
