export default {
    Select: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义样式名',
            option: '',
            default: ''
        },
        {
            param: 'defaultValue',
            type: 'string|string[]|number|number[]',
            desc: '指定默认选中的条目',
            option: '',
            default: ''
        },
        {
            param: 'disabled',
            type: 'boolean',
            desc: '是否禁用',
            option: '',
            default: ''
        },
        {
            param: 'dropdownClassName',
            type: 'string',
            desc: '下拉选择自定义类名',
            option: '',
            default: ''
        },
        {
            param: 'dropdownStyle',
            type: 'object',
            desc: '现在选择自定义style',
            option: '',
            default: ''
        },
        {
            param: 'dropdownMatchSelectWidth',
            type: 'boolean',
            desc: '下拉弹窗是否与target同宽',
            option: '',
            default: 'true'
        },
        {
            param: 'filterOption',
            type: 'boolean or function(inputValue, option)',
            desc: '是否根据输入项进行筛选。当其为一个函数时，会接收 inputValue option 两个参数，当 option 符合筛选条件时，应返回 true，反之则返回 false',
            option: '',
            default: 'true'
        },
        {
            param: 'getPopupContainer',
            type: 'Function(triggerNode)',
            desc: '菜单渲染父节点。默认渲染到 body 上',
            option: '',
            default: '() => document.body'
        },
        {
            param: 'mode',
            type: 'string',
            desc: '下拉选择类型',
            option: 'default, multiple, combobox, tags',
            default: 'default'
        },
        {
            param: 'notFoundContent',
            type: 'string|ReactNode',
            desc: '搜索时候无内容展现',
            option: '',
            default: '无匹配结果'
        },
        {
            param: 'onBlur',
            type: 'Function(e)',
            desc: '搜索框失去焦点触发',
            option: '',
            default: ''
        },
        {
            param: 'onChange',
            type: 'function(value, option:Option/Array<Option>)',
            desc: '选中 option，或 input 的 value 变化（combobox 模式下）时，调用此函数',
            option: '',
            default: ''
        },
        {
            param: 'onDropdownVisibleChange',
            type: 'function(visible)',
            desc: 'dropdown visible改变的时候触发',
            option: '',
            default: ''
        },
        {
            param: 'onFocus',
            type: 'function',
            desc: '搜索框触发焦点',
            option: '',
            default: ''
        },
        {
            param: 'onSearch',
            type: 'function',
            desc: '搜索时触发',
            option: '',
            default: ''
        },
        {
            param: 'placeholder',
            type: 'string',
            desc: '搜索时的placeholder，只对搜索模式下生效，非搜索模式请传入selectorName',
            option: '',
            default: ''
        },
        {
            param: 'showSearch',
            type: 'boolean',
            desc: '是否可搜索',
            option: '',
            default: ''
        },
        {
            param: 'trigger',
            type: 'string',
            desc: '弹层触发的trigger',
            option: 'hover|click',
            default: 'hover'
        },
        {
            param: 'value',
            type: 'string|string[]|number|number[]',
            desc: '指定选中的条目',
            option: '',
            default: ''
        },
        {
            param: 'width',
            type: 'number',
            desc: 'select宽度',
            option: '',
            default: ''
        },
        {
            param: 'maxTagCount',
            type: 'number',
            desc: '最大标签数目，仅对mode为multiple的时候生效',
            option: '',
            default: ''
        },
        {
            param: 'multipleRenderTargetMode',
            type: 'string',
            desc: '多选支持的enum、count、list三种形式的(枚举、计数、多选tag)',
            option: '',
            default: 'list'
        },
        {
            param: 'customRenderTarget',
            type: 'Function(value) 传出选中的value',
            desc: '可自定义target的展示规则 - 版本2.1.9以后使用',
            option: '',
            version: '2.1.9',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: '尺寸',
            option: 'xsmall | small | medium | large',
            default: 'medium'
        },
        {
            param: 'selectorName',
            type: 'string',
            desc: '非搜索模式下，value为""的时候，默认展示的文案，类似搜索模式下的placeholder',
            option: '',
            default: ''
        },
        {
            param: 'defaultVisible',
            type: 'string',
            desc: '默认弹层是否展开，非受控，3.0.14-beta-33版本支持',
            option: '',
            default: ''
        },
        {
            param: 'optionLabelProp',
            type: 'string',
            desc: '可以自定义target展示的item，默认为children',
            option: '',
            default: ''
        },
        {
            param: 'allowClear',
            type: 'boolean',
            desc: '对于搜索场景下，支持清除功能',
            option: '',
            default: 'false'
        },
        {
            param: 'loading',
            type: 'boolean',
            desc: '是否加载',
            option: '',
            default: 'false'
        },
        {
            param: 'loadingText',
            type: 'string/ReactNode',
            desc: '加载文案',
            option: '',
            default: '加载中...'
        },
        {
            param: 'triggerTarget',
            type: 'string/ReactNode',
            desc: '自定义触发区',
            option: '',
            default: ''
        }
    ],
    SelectOption: [
        {
            param: 'disabled',
            type: 'boolean',
            desc: '是否禁用',
            option: '',
            default: 'false'
        },
        {
            param: 'key',
            type: 'string',
            desc: '与value一致，多选时候使用key进行枚举，所以多选下该参数必传',
            option: '',
            default: ''
        },
        {
            param: 'value',
            type: 'string',
            desc: '默认根据此属性值进行筛选',
            option: '',
            default: ''
        },
        {
            param: 'type',
            type: 'string',
            desc: 'type为custom的时候，option不会收起，可以在option里面写自定义跳转或者其他交互',
            option: '',
            default: ''
        }
    ],
    SelectOptGroup: [{
        param: 'key',
        type: 'string',
        desc: '',
        option: '',
        default: ''
    }, {
        param: 'label',
        type: 'string|ReactNode',
        desc: '组名',
        option: '',
        default: ''
    }]
};
