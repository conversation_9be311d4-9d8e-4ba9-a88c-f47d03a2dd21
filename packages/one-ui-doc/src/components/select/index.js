import BaseComponent from '../base';

export default {
    select: {
        value: BaseComponent,
        label: 'Select 下拉选择',
        demos: [
            {
                title: '基础使用',
                desc: '普通下拉选择',
                source: 'normal'
            },
            {
                title: '加载中',
                desc: '普通下拉选择 - 加载中',
                source: 'loading'
            },
            {
                title: '尺寸',
                desc: '普通下拉选择-尺寸',
                source: 'size'
            },
            {
                title: '单/复选混合',
                desc: '可在多选{mutiple}模式下(不可开启全选)，通过选项增加{exclusive}属性来进行排他选择，从而实现单/复选混合。',
                source: 'exclusive'
            },
            {
                title: '点击弹出 ',
                desc: '点击弹出',
                source: 'click'
            },
            {
                title: '全选 ',
                desc: '多选下的全选功能',
                source: 'checkall'
            },
            {
                title: '搜索',
                desc: '可搜索的下拉选择',
                source: 'search'
            },
            {
                title: '自定义Option',
                desc: '可自定义的Option',
                source: 'custom'
            },
            {
                title: '自定义Footer',
                desc: '自定义底部区域',
                source: 'footer'
            },
            {
                title: '异步加载',
                desc: '可搜索的下拉选择-ajax',
                source: 'searchAjax'
            },
            {
                title: '多选',
                desc: '多选的下拉选择',
                source: 'multiple2'
            },
            {
                title: '多选-异步加载',
                desc: '多选的下拉选择',
                source: 'multiple'
            },
            {
                title: '自定义容器',
                desc: '支持自定义target的单选',
                source: 'customRenderTarget'
            },
            {
                title: '回填方式',
                desc: 'enum、count两种形式的',
                source: 'multipleTarget'
            },
            {
                title: '多选-标签',
                desc: '多选标签-多选，可自选标签',
                source: 'tag'
            },
            {
                title: '自定义回填',
                desc: '可以自定义回填内容 - 单选',
                source: 'optionLabel1'
            },
            {
                title: '多选-自定义回填',
                desc: '可以自定义回填内容 - 多选',
                source: 'optionLabel2'
            },
            {
                title: '自定义触发区',
                desc: '存在自定义触发区的情况下，dropdownIndependentWidth默认为true，其他情况默认为false',
                source: 'triggerTarget'
            },
            {
                title: '下拉框宽度限制',
                desc: 'false表示下拉弹窗的最小宽度等于target宽度，true表示下拉弹窗的宽度保持独立并自适应包裹内容，存在自定义触发区的情况下，默认为true，其他情况默认为false',
                source: 'dropdownIndependentWidth'
            }
        ],
        apis: [
            {
                apiKey: 'Select',
                title: 'Select'
            },
            {
                apiKey: 'SelectOption',
                title: 'Select.Option'
            },
            {
                apiKey: 'SelectOptionGroup',
                title: 'Select.OptGroup'
            }
        ]
    }
};
