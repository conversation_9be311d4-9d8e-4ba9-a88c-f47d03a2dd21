import React, {PureComponent} from 'react';
import {Select} from '@baidu/one-ui';

const Option = Select.Option;
export default class Normal extends PureComponent {
    state = {
        value: undefined,
        searchValue: undefined,
        data: []
    };

    handleChange = value => {
        this.setState({
            value,
            searchValue: ''
        });
        setTimeout(() => {
            this.setState({
                data: []
            });
        }, 1000);
    }

    handleBlur = () => {
        this.setState({
            searchValue: ''
        });
    }

    handleFocus = () => {
        this.setState({
            searchValue: ''
        });
    }

    onSearch = string => {
        this.setState({
            searchValue: string
        });
        setTimeout(() => {
            const dataSource = [];
            for (let i = 0; i < 3; i++) {
                dataSource.push({
                    value: `${Math.random()}${i}`,
                    text: `计划${string}${i}鲜花`
                });
            }
            this.setState({
                data: dataSource
            });
        }, 300);
    }

    render() {
        return (
            <div>
                <div style={{marginBottom: '30px'}}>
                    <div style={{marginBottom: '30px'}}>普通下拉选择 - 搜索</div>
                    <Select
                        value={this.state.value}
                        placeholder="请选择计划"
                        defaultActiveFirstOption={false}
                        showSearch
                        filterOption={false}
                        onChange={this.handleChange}
                        onSearch={this.onSearch}
                        onBlur={this.handleBlur}
                        onFocus={this.handleFocus}
                        notFoundContent="没有可选计划"
                        width={200}
                    >
                        {
                            this.state.data.map(item => {
                                return (
                                    <Option
                                        key={item.value}
                                        value={item.value}
                                    >
                                        {item.text}
                                    </Option>
                                );
                            })
                        }
                    </Select>
                </div>
            </div>
        );
    }
}
