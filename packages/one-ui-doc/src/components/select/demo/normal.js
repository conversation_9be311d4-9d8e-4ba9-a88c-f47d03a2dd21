import React, {PureComponent} from 'react';
import {Select, Tooltip} from '@baidu/one-ui';

const Option = Select.Option;
export default class Normal extends PureComponent {
    state = {
        value: 1
    };

    handleChange = value => {
        this.setState({
            value
        });
    }

    render() {
        return (
            <div>
                <div style={{marginBottom: '30px'}}>
                    <div style={{marginBottom: '30px'}}>普通下拉选择</div>
                    <Select
                        value={this.state.value}
                        onChange={this.handleChange}
                        showSearch
                        trigger="click"
                        filterOption={
                            (input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        }
                    >
                        <Select.OptGroup key="website" label="已有业务点">
                            <Option value={0}>哈哈哈</Option>
                            <Option value={1}>嘿嘿嘿</Option>
                        </Select.OptGroup>
                        <Select.OptGroup key="website2" label="已有业务点2">
                            <Option value={2} disabled>嚯嚯嚯</Option>
                            <Option value={3}>啦啦啦</Option>
                        </Select.OptGroup>
                    </Select>
                </div>
                <div style={{marginBottom: '30px'}}>
                    <div style={{marginBottom: '30px'}}>普通下拉选择-禁用</div>
                    <Select
                        value={this.state.value}
                        onChange={this.handleChange}
                        disabled
                    >
                        <Option value={0}>哈哈哈</Option>
                        <Option value={1}>嘿嘿嘿</Option>
                        <Option value={2} disabled>嚯嚯嚯</Option>
                        <Option value={3}>啦啦啦</Option>
                    </Select>
                </div>
            </div>
        );
    }
}
