import React, {PureComponent} from 'react';
import {Select} from '@baidu/one-ui';

const Option = Select.Option;
const OptGroup = Select.OptGroup;

const maxCount = 6;

export default class Normal extends PureComponent {
    state = {
        searchValue: '',
        source: [],
        children: []
    };

    handleChange = e => {
        this.setState({
            source: [...e],
            searchValue: ''
        });
    }

    onSearch = val => {
        this.setState({
            searchValue: val
        });
        setTimeout(() => {
            const children = [];
            for (let i = 10; i < 36; i++) {
                children.push({
                    label: `${Math.round(Math.random() * 100)}鲜花${i.toString(36) + i}`,
                    value: `${Math.random()}`
                });
            }
            this.setState({
                children: [...children]
            });
        }, 1000);
    }

    render() {
        return (
            <div>
                <div style={{marginBottom: '30px'}}>
                    <div style={{marginBottom: '30px'}}>普通下拉选择-多选</div>
                    <Select
                        mode="multiple"
                        placeholder="您可选择5个业务点"
                        onChange={this.handleChange}
                        width={400}
                        maxTagCount={maxCount}
                        notFoundContent="没有找到你的业务点？"
                        defaultActiveFirstOption={false}
                        showSearch
                        onSearch={this.onSearch}
                        filterOption={false}
                    >
                        <OptGroup key="website" label="已有业务点">
                            {
                                this.state.children.map(item => {
                                    return (
                                        <Option value={item.value}>
                                            {item.label}
                                        </Option>
                                    );
                                })
                            }
                        </OptGroup>
                    </Select>
                    <br />
                    <br />
                    <div style={{marginBottom: '30px'}}>普通下拉选择-多选</div>
                    <Select
                        mode="multiple"
                        placeholder="您可选择5个业务点"
                        onChange={this.handleChange}
                        width={400}
                        maxTagCount={maxCount}
                        notFoundContent="没有找到你的业务点？"
                        defaultActiveFirstOption={false}
                        showSearch
                        onSearch={this.onSearch}
                        filterOption={false}
                        size="xsmall"
                    >
                        <OptGroup key="website" label="已有业务点">
                            {
                                this.state.children.map(item => {
                                    return (
                                        <Option value={item.value}>
                                            {item.label}
                                        </Option>
                                    );
                                })
                            }
                        </OptGroup>
                    </Select>
                    <br />
                    <br />
                    <div style={{marginBottom: '30px'}}>普通下拉选择-多选</div>
                    <Select
                        mode="multiple"
                        placeholder="您可选择5个业务点"
                        onChange={this.handleChange}
                        width={400}
                        maxTagCount={maxCount}
                        notFoundContent="没有找到你的业务点？"
                        defaultActiveFirstOption={false}
                        showSearch
                        onSearch={this.onSearch}
                        filterOption={false}
                        size="medium"
                    >
                        <OptGroup key="website" label="已有业务点">
                            {
                                this.state.children.map(item => {
                                    return (
                                        <Option value={item.value}>
                                            {item.label}
                                        </Option>
                                    );
                                })
                            }
                        </OptGroup>
                    </Select>
                    <br />
                    <br />
                    <div style={{marginBottom: '30px'}}>普通下拉选择-多选</div>
                    <Select
                        mode="multiple"
                        placeholder="您可选择5个业务点"
                        onChange={this.handleChange}
                        width={400}
                        maxTagCount={maxCount}
                        notFoundContent="没有找到你的业务点？"
                        defaultActiveFirstOption={false}
                        showSearch
                        onSearch={this.onSearch}
                        filterOption={false}
                        size="large"
                    >
                        <OptGroup key="website" label="已有业务点">
                            {
                                this.state.children.map(item => {
                                    return (
                                        <Option value={item.value}>
                                            {item.label}
                                        </Option>
                                    );
                                })
                            }
                        </OptGroup>
                    </Select>
                </div>
            </div>
        );
    }
}
