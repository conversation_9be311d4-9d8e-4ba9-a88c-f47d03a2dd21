import React from 'react';
import {Select} from '@baidu/one-ui';

const options = [
    {
        label: '多选',
        options: ['A1', 'A2', 'A3']
    },
    {
        label: '单选',
        exclusive: true,
        options: ['B1', 'B2', 'B3']
    }
];

export default () => (
    <Select
        showSearch
        mode="multiple"
    >
        {options.map(({label, exclusive, options}) => (
            <Select.OptGroup key={label} label={label}>
                {options.map(option => (
                    <Select.Option
                        key={option}
                        value={option}
                        exclusive={exclusive}
                    >
                        {option}
                    </Select.Option>
                ))}
            </Select.OptGroup>
        ))}
    </Select>
);
