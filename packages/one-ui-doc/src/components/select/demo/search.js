import React, {PureComponent} from 'react';
import {Select} from '@baidu/one-ui';

const Option = Select.Option;
export default class Normal extends PureComponent {
    state = {
        value: 1
    };

    onChange = value => {
        console.log(`selected ${value}`);
    }

    onBlur = () => {
        console.log('blur');
    }

    onFocus = () => {
        console.log('focus');
    }

    onSearch = val => {
        console.log('search:', val);
    }

    render() {
        return (
            <div>
                <div style={{marginBottom: '30px'}}>
                    <div style={{marginBottom: '30px'}}>普通下拉选择 - 搜索</div>
                    <Select
                        showSearch
                        style={{width: 200}}
                        placeholder="请选择计划"
                        onChange={this.onChange}
                        onFocus={this.onFocus}
                        onBlur={this.onBlur}
                        onSearch={this.onSearch}
                        filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                    >
                        <Option value={0}>哈哈哈</Option>
                        <Option value={1}>嘿嘿嘿</Option>
                        <Option value={2} disabled>嚯嚯嚯</Option>
                        <Option value={3}>啦啦啦</Option>
                    </Select>
                </div>
                <br />
                <br />
                <div style={{marginBottom: '30px'}}>
                    <div style={{marginBottom: '30px'}}>普通下拉选择 - 搜索 - 带清除功能</div>
                    <Select
                        showSearch
                        style={{width: 200}}
                        placeholder="请选择计划"
                        onChange={this.onChange}
                        onFocus={this.onFocus}
                        onBlur={this.onBlur}
                        onSearch={this.onSearch}
                        filterOption={(input, option) => option.props.children.toLowerCase().indexOf(input.toLowerCase()) >= 0}
                        allowClear
                    >
                        <Option value={0}>哈哈哈</Option>
                        <Option value={1}>嘿嘿嘿</Option>
                        <Option value={2} disabled>嚯嚯嚯</Option>
                        <Option value={3}>啦啦啦</Option>
                    </Select>
                </div>
            </div>
        );
    }
}
