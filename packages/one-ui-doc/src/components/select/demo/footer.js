import React from 'react';
import {Select} from '@baidu/one-ui';

const Option = Select.Option;

const children = [];
for (let i = 10; i < 36; i++) {
    children.push(<Option value={i.toString(36) + i}>{i.toString(36) + i}</Option>);
}

export default () => (
    <Select
        mode="multiple"
        showSearch
        placeholder="Please select"
        defaultValue={['a10', 'c12']}
        onChange={val => console.log(val)}
        footer="Footer自定义区"
    >
        {children}
    </Select>
);
