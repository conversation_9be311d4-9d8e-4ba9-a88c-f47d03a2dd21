import React, {PureComponent} from 'react';
import {Select, Button} from '@baidu/one-ui';

const Option = Select.Option;
const labels = ['哈哈哈', '嘿嘿嘿', '嚯嚯嚯', '啦啦啦'];

export default class Normal extends PureComponent {
    state = {
        value: labels[0]
    };

    handleChange = value => {
        this.setState({
            value
        });
    }

    render() {
        return (
            <div>
                <div style={{marginBottom: '30px'}}>
                    <div style={{marginBottom: '30px'}}>普通下拉选择</div>
                    <Select
                        value={this.state.value}
                        onChange={this.handleChange}
                        trigger="click"
                        triggerTarget={<Button type="ghost-strong">选择按钮</Button>}
                    >
                        {
                            labels.map((label) => {
                                return (
                                    <Option key={label} value={label}>{label}</Option>
                                )
                            })
                        }
                    </Select>
                    <br />
                    <br />
                    已选值：{this.state.value}
                </div>
            </div>
        );
    }
}
