import React from 'react';
import {Select} from '@baidu/one-ui';

const {
    Option,
    OptGroup
} = Select;

export default () => (
    <>
        <Select
            trigger="click"
            width={256}
            showCheckAll
            showSearch
            mode="multiple"
            placeholder="请选择"
        >
            <Option value="0">哈哈哈</Option>
            <Option value="1">嘿嘿嘿</Option>
            <Option value="2">嚯嚯嚯</Option>
            <Option value="3">啦啦啦</Option>
        </Select>
        <br />
        <br />
        <br />
        包含禁用
        <br />
        <br />
        <Select
            trigger="click"
            width={256}
            showCheckAll
            showSearch
            mode="multiple"
            placeholder="请选择"
        >
            <Option value="0">哈哈哈</Option>
            <Option value="1">嘿嘿嘿</Option>
            <Option value="2" disabled>嚯嚯嚯</Option>
            <Option value="3">啦啦啦</Option>
        </Select><br />
        <br />
        <br />
        包含已选禁用
        <br />
        <br />
        <Select
            trigger="click"
            width={256}
            showCheckAll
            defaultValue={['2']}
            showSearch
            mode="multiple"
            placeholder="请选择"
        >
            <Option value="0">哈哈哈</Option>
            <Option value="1">嘿嘿嘿</Option>
            <Option value="2" disabled>嚯嚯嚯</Option>
            <Option value="3">啦啦啦</Option>
        </Select>
        <br />
        <br />
        <br />
        分组
        <br />
        <br />
        <Select
            trigger="click"
            width={256}
            showCheckAll
            showSearch
            mode="multiple"
            placeholder="请选择"
        >
            <OptGroup label="分组一" key="1">
                <Option value="0">哈哈哈</Option>
                <Option value="1">嘿嘿嘿</Option>
                <Option value="2">嚯嚯嚯</Option>
                <Option value="3">啦啦啦</Option>
            </OptGroup>
            <OptGroup label="分组二" key="0">
                <Option value="10">哈哈哈-2</Option>
                <Option value="11">嘿嘿嘿-2</Option>
                <Option value="12">嚯嚯嚯-2</Option>
                <Option value="13">啦啦啦-2</Option>
            </OptGroup>
        </Select>

        <br />
        <br />
        <br />
        分组包含禁用
        <br />
        <br />
        <Select
            trigger="click"
            width={256}
            showCheckAll
            showSearch
            mode="multiple"
            placeholder="请选择"
        >
            <OptGroup label="分组一" key="1">
                <Option value="0">哈哈哈</Option>
                <Option value="1">嘿嘿嘿</Option>
                <Option value="2" disabled>嚯嚯嚯</Option>
                <Option value="3">啦啦啦</Option>
            </OptGroup>
            <OptGroup label="分组二" key="0">
                <Option value="10">哈哈哈-2</Option>
                <Option value="11">嘿嘿嘿-2</Option>
                <Option value="12" disabled>嚯嚯嚯-2</Option>
                <Option value="13">啦啦啦-2</Option>
            </OptGroup>
        </Select>
    </>
)
