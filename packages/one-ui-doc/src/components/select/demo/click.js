import React, {PureComponent} from 'react';
import {Select} from '@baidu/one-ui';

const Option = Select.Option;
export default class Normal extends PureComponent {
    state = {
        value: 1
    };

    handleChange = value => {
        this.setState({
            value
        });
    }

    render() {
        return (
            <div>
                <div style={{marginBottom: '30px'}}>
                    <div style={{marginBottom: '30px'}}>普通下拉选择</div>
                    <Select
                        value={this.state.value}
                        onChange={this.handleChange}
                        trigger="click"
                    >
                        <Option value={0}>哈哈哈</Option>
                        <Option value={1}>嘿嘿嘿</Option>
                        <Option value={2} disabled>嚯嚯嚯</Option>
                        <Option value={3}>啦啦啦</Option>
                    </Select>
                </div>
            </div>
        );
    }
}
