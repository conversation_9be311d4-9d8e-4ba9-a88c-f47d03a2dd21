import React, {PureComponent} from 'react';
import {Select} from '@baidu/one-ui';

const Option = Select.Option;

const children = [];
for (let i = 10; i < 36; i++) {
    children.push(<Option value={i.toString(36) + i}>{i.toString(36) + i}</Option>);
}

export default class Normal extends PureComponent {
    handleChange = value => {
        console.log(`Selected: ${value}`);
    }

    render() {
        return (
            <div>
                <Select
                    mode="multiple"
                    showSearch
                    placeholder="Please select"
                    defaultValue={['a10', 'c12']}
                    onChange={this.handleChange}
                    style={{width: '400px'}}
                >
                    {children}
                </Select>
                <br />
                <br />
                <Select
                    mode="multiple"
                    showSearch
                    placeholder="Please select"
                    defaultValue={['a10', 'c12']}
                    onChange={this.handleChange}
                    style={{width: '400px'}}
                    disabled
                >
                    {children}
                </Select>
            </div>
        );
    }
}
