import React, {PureComponent} from 'react';
import {Select} from '@baidu/one-ui';

const Option = Select.Option;
export default class Normal extends PureComponent {
    state = {
        value: ['1']
    };

    handleChange = value => {
        this.setState({
            value
        });
    }

    render() {
        return (
            <div>
                <div style={{marginBottom: '30px'}}>
                    <div style={{marginBottom: '30px'}}>普通下拉选择</div>
                    <Select
                        value={this.state.value}
                        onChange={this.handleChange}
                        mode="multiple"
                        showSearch
                        optionLabelProp="label"
                    >
                        <Option key="0" label="0-哈哈哈">哈哈哈</Option>
                        <Option key="1" label="1-哈哈哈">嘿嘿嘿</Option>
                        <Option key="2" label="2-哈哈哈">嚯嚯嚯</Option>
                        <Option key="3" label="3-哈哈哈">啦啦啦</Option>
                    </Select>
                </div>
            </div>
        );
    }
}
