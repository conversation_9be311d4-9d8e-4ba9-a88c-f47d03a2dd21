import React, {PureComponent} from 'react';
import {Select, Tooltip} from '@baidu/one-ui';

const Option = Select.Option;
export default class Normal extends PureComponent {
    state = {
        value: 1
    };

    handleChange = value => {
        this.setState({
            value
        });
    }

    render() {
        return (
            <div>
                <div style={{marginBottom: '30px'}}>
                    <div style={{marginBottom: '30px'}}>加载中</div>
                    <Select
                        value={this.state.value}
                        onChange={this.handleChange}
                        loading
                        loadingText="数据加载中"
                    >
                        <Select.OptGroup key="website" label="已有业务点">
                            <Option value={0}><Tooltip title="哈哈哈">哈哈哈</Tooltip></Option>
                            <Option value={1}>嘿嘿嘿</Option>
                        </Select.OptGroup>
                        <Select.OptGroup key="website2" label="已有业务点2">
                            <Option value={2} disabled>嚯嚯嚯</Option>
                            <Option value={3}>啦啦啦</Option>
                        </Select.OptGroup>
                    </Select>
                </div>
            </div>
        );
    }
}
