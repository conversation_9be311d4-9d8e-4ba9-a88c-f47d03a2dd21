import React, {PureComponent} from 'react';
import {Select} from '@baidu/one-ui';

const Option = Select.Option;

const children = [];
for (let i = 10; i < 36; i++) {
    children.push(<Option value={i.toString(36) + i + '111'}>{i.toString(36) + i}</Option>);
}

export default class Normal extends PureComponent {
    handleChange = value => {
        // console.log(`Selected: ${value}`);
    }

    render() {
        return (
            <div>
                <Select
                    mode="multiple"
                    showSearch
                    placeholder="Please select"
                    defaultValue={['a10111', 'c12111']}
                    onChange={this.handleChange}
                    style={{width: '400px'}}
                    selectorName="地域下拉选择"
                    multipleRenderTargetMode="enum"
                >
                    {children}
                </Select>
                <br />
                <br />
                <br />
                <Select
                    mode="multiple"
                    showSearch
                    placeholder="Please select"
                    defaultValue={['a10111', 'c12111']}
                    onChange={this.handleChange}
                    style={{width: '400px'}}
                    selectorName="地域下拉选择"
                    multipleRenderTargetMode="count"
                >
                    {children}
                </Select>
                <br />
                <br />
                <br />
                <Select
                    mode="multiple"
                    showSearch
                    placeholder="Please select"
                    defaultValue={['a10111', 'c12111']}
                    onChange={this.handleChange}
                    style={{width: '400px'}}
                    selectorName="地域下拉选择"
                    multipleRenderTargetMode="custom"
                    customRenderTarget={value => {
                        return `这是一个自定义target的下拉选择${value[0]}等${value.length}个`;
                    }}
                >
                    {children}
                </Select>
            </div>
        );
    }
}
