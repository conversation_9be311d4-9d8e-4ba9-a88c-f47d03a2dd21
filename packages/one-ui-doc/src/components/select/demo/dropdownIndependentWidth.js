import React, {useState} from 'react';
import {Select, Button, Radio} from '@baidu/one-ui';

const Option = Select.Option;
const RadioGroup = Radio.Group;
const labels = ['哈哈哈', '嘿嘿嘿', '嚯嚯嚯', '啦啦啦'];
const options = ['默认值', 'true', 'false'];

export default function Demo() {
    const [value, setValue] = useState(labels[0]);
    const [dropdownWidth, setDropdownWidth] = useState('默认值');

    function onChangeDropdownWidth(e) {
        setDropdownWidth(e.target.value);
    }

    const selectProps = {
        value: value,
        onChange: val => setValue(val),
        trigger: "click"
    };

    if (dropdownWidth === 'true') {
        selectProps.dropdownIndependentWidth = true;
    }
    else if (dropdownWidth === 'false') {
        selectProps.dropdownIndependentWidth = false;
    }

    return (
        <div>
            <div style={{marginBottom: '30px'}}>
                <div style={{display: 'flex', marginBottom: '30px'}}>
                    dropdownIndependentWidth： 
                    <RadioGroup
                        value={dropdownWidth}
                        options={options}
                        onChange={onChangeDropdownWidth}
                    />
                </div>

                <div style={{marginBottom: '12px'}}>普通下拉选择</div>
                <Select {...selectProps}>
                    {
                        labels.map((label) => {
                            return (
                                <Option key={label} value={label}>{label}</Option>
                            )
                        })
                    }
                </Select>

                <br />
                <br />
                <div style={{marginTop: '12px', marginBottom: '12px'}}>自定义触发区下拉选择按钮</div>
                <Select
                    {...selectProps}
                    triggerTarget={<Button type="ghost-strong">选择按钮</Button>}
                >
                    {
                        labels.map((label) => {
                            return (
                                <Option key={label} value={label}>{label}</Option>
                            )
                        })
                    }
                </Select>
                <br />
                <br />
                已选值：{value}
            </div>
        </div>
    );
}
