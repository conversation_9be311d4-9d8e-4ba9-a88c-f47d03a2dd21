import React, {PureComponent} from 'react';
import {Select} from '@baidu/one-ui';

const Option = Select.Option;
const labels = ['哈哈哈', '嘿嘿嘿', '嚯嚯嚯', '啦啦啦'];
export default class Normal extends PureComponent {
    state = {
        value: 1
    };

    handleChange = value => {
        this.setState({
            value
        });
    }

    customRenderTarget = value => {
        return (
            <span>
                <span>姓名:</span>
                <span>{labels[+value]}</span>
            </span>
        );
    }

    render() {
        return (
            <div>
                <div style={{marginBottom: '30px'}}>
                    <div style={{marginBottom: '30px'}}>普通下拉选择</div>
                    <Select
                        value={this.state.value}
                        onChange={this.handleChange}
                        trigger="click"
                        customRenderTarget={this.customRenderTarget}
                    >
                        <Option value={0}>哈哈哈</Option>
                        <Option value={1}>嘿嘿嘿</Option>
                        <Option value={2} disabled>嚯嚯嚯</Option>
                        <Option value={3}>啦啦啦</Option>
                    </Select>
                </div>
            </div>
        );
    }
}
