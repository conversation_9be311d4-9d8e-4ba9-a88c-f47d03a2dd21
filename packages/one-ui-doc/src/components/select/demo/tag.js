import React, {PureComponent} from 'react';
import {Select} from '@baidu/one-ui';

const Option = Select.Option;
const children = [];
for (let i = 10; i < 36; i++) {
    children.push(<Option value={i.toString(36) + i}>{i.toString(36) + i}</Option>);
}
export default class Normal extends PureComponent {

    handleChange = value => {
        console.log(`selected ${value}`);
    }

    render() {
        return (
            <div>
                <div style={{marginBottom: '30px'}}>
                    <div style={{marginBottom: '30px'}}>标签</div>
                    <Select mode="tags" placeholder="Tags Mode" onChange={this.handleChange} width={400} dropdownClassName="xxxx">
                        {children}
                    </Select>
                </div>
            </div>
        );
    }
}
