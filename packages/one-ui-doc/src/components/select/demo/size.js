import React, {PureComponent} from 'react';
import {Select} from '@baidu/one-ui';

const Option = Select.Option;
export default class Normal extends PureComponent {
    state = {
        value: 1
    };

    handleChange = value => {
        this.setState({
            value
        });
    }

    render() {
        return (
            <div>
                <div style={{marginBottom: '30px'}}>
                    <div style={{marginBottom: '30px'}}>普通下拉选择-小号</div>
                    <Select
                        value={this.state.value}
                        onChange={this.handleChange}
                    >
                        <Option value={0}>哈哈哈</Option>
                        <Option value={1}>嘿嘿嘿</Option>
                        <Option value={2} disabled>嚯嚯嚯</Option>
                        <Option value={3}>啦啦啦</Option>
                    </Select>
                </div>
                <br />
                <br />
                <div style={{marginBottom: '30px'}}>普通下拉选择-超小号</div>
                <Select
                    value={this.state.value}
                    onChange={this.handleChange}
                    size="xsmall"
                >
                    <Option value={0}>哈哈哈</Option>
                    <Option value={1}>嘿嘿嘿</Option>
                    <Option value={2} disabled>嚯嚯嚯</Option>
                    <Option value={3}>啦啦啦</Option>
                </Select>
                <br />
                <br />
                <div style={{marginBottom: '30px'}}>普通下拉选择-中号</div>
                <Select
                    value={this.state.value}
                    onChange={this.handleChange}
                    size="medium"
                >
                    <Option value={0}>哈哈哈</Option>
                    <Option value={1}>嘿嘿嘿</Option>
                    <Option value={2} disabled>嚯嚯嚯</Option>
                    <Option value={3}>啦啦啦</Option>
                </Select>
                <br />
                <br />
                <div style={{marginBottom: '30px'}}>普通下拉选择-大号</div>
                <Select
                    value={this.state.value}
                    onChange={this.handleChange}
                    size="large"
                >
                    <Option value={0}>哈哈哈</Option>
                    <Option value={1}>嘿嘿嘿</Option>
                    <Option value={2} disabled>嚯嚯嚯</Option>
                    <Option value={3}>啦啦啦</Option>
                </Select>
            </div>
        );
    }
}
