import React from 'react';
import {Checkbox} from '@baidu/one-ui';

const options = [
    {
        exclusive: true,
        options: ['不限', '男', '女']
    },
    {
        options: ['学生', '老师', '家长']
    }
];

export default () => (
    <>
        <Checkbox.Group emptyValue="不限">
            {options.map(({exclusive, options}) => (
                options.map(option => (
                    <Checkbox
                        key={option}
                        value={option}
                        exclusive={exclusive}
                    >
                        {option}
                    </Checkbox>
                ))
            ))}
        </Checkbox.Group>
        <br />
        <br />
        <Checkbox.Group emptyValue="不限">
            {options.map(({exclusive, options}) => (
                options.map(option => (
                    <Checkbox.Button
                        key={option}
                        value={option}
                        exclusive={exclusive}
                    >
                        {option}
                    </Checkbox.Button>
                ))
            ))}
        </Checkbox.Group>
    </>
);
