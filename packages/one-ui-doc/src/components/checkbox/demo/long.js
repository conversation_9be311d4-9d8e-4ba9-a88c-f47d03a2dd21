import React from 'react';
import {Checkbox} from '@baidu/one-ui';

const spanStyle = {
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    width: '48px',
    display: 'inline-block',
    verticalAlign: 'middle'
};
export default () => {
    return (
        <div>
            <Checkbox>
                <span style={spanStyle} title="超长多选超长多选超长多选超长多选超长多选超长多选超长多选">超长多选超长多选超长多选超长多选超长多选超长多选超长多选</span>
            </Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
            <Checkbox>普通多选</Checkbox>
        </div>
    );
};
