import React from 'react';
import {Checkbox} from '@baidu/one-ui';

const CheckboxGroup = Checkbox.Group;
const CheckboxButton = Checkbox.Button;

const CHILDREN = [
    <CheckboxButton value={1} key={1}>加强多选1</CheckboxButton>,
    <CheckboxButton value={2} key={2}>加强多选2</CheckboxButton>,
    <CheckboxButton value={3} key={3}>加强多选3</CheckboxButton>
];

export default () => {
    return (
        <div>
            <div>默认尺寸加强多选</div>
            <br />
            <CheckboxGroup defaultValue={[2]}>
                {CHILDREN}
            </CheckboxGroup>
            <br />
            <br />
            <div>默认尺寸加强禁用</div>
            <br />
            <CheckboxGroup disabled defaultValue={[2]}>
                {CHILDREN}
            </CheckboxGroup>
            <br />
            <br />
            <div>小尺寸加强多选</div>
            <br />
            <CheckboxGroup size="small" defaultValue={[2]}>
                {CHILDREN}
            </CheckboxGroup>
        </div>
    );
};
