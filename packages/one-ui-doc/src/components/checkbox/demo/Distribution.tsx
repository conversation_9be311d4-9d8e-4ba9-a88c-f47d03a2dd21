import React, {useState} from 'react';
import {Checkbox, Switch} from '@baidu/one-ui';

const CheckboxGroup = Checkbox.Group;
const CheckboxButton = Checkbox.Button;

const LIST = Array(6).fill(0).map((_, index) => ({key: index, label: `按钮单选${index + 1}`}));

export default () => {

    const [isEven, setIsEven] = useState(true);

    return (
        <div>
            <div>
                是否均分
                <Switch checked={isEven} onChange={setIsEven} />
            </div>
            <br />
            <CheckboxGroup
                defaultValue={[0]}
                distribution={isEven ? 'even' : 'auto'}
            >
                {
                    LIST.map(({key, label}) => {
                        return (
                            <CheckboxButton value={key} key={key}>{label}</CheckboxButton>
                        );
                    })
                }
            </CheckboxGroup>
        </div>
    );
};
