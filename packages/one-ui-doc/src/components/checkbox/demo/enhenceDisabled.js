import React from 'react';
import {Checkbox, Popover} from '@baidu/one-ui';

const CheckboxGroup = Checkbox.Group;
const CheckboxButton = Checkbox.Button;

const CHILDREN = [
    <Popover content="为什么禁用提示" key="tip">
        <CheckboxButton value={1} key={1} disabled>加强多选1</CheckboxButton>
    </Popover>,
    <CheckboxButton value={2} key={2} disabled>加强多选2</CheckboxButton>,
    <CheckboxButton value={3} key={3}>加强多选3</CheckboxButton>
];

export default () => {
    return (
        <div>
            <div>默认尺寸加强多选</div>
            <br />
            <CheckboxGroup defaultValue={[2]}>
                {CHILDREN}
            </CheckboxGroup>
            <br />
            <br />
            <div>小尺寸加强多选</div>
            <br />
            <CheckboxGroup size="small" defaultValue={[2]}>
                {CHILDREN}
            </CheckboxGroup>
            <br />
            <br />
            <div>简单样式禁用</div>
            <br />
            <CheckboxGroup type="simple" size="small" defaultValue={[2]}>
                {CHILDREN}
            </CheckboxGroup>
        </div>
    );
};
