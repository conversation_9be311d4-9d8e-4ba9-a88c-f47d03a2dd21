import React, {PureComponent} from 'react';
import {Checkbox, Dialog} from '@baidu/one-ui';

const CheckboxGroup = Checkbox.Group;

const ALL_OPTION_LIST = ['Apple', 'Pear', 'Orange'];

export default class CheckedWithCondition extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            value: []
        };
    }
    onChange = e => {
        const me = this;
        Dialog.confirm({
            title: '提示',
            content: '确认选择么？',
            onOk() {
                me.setState({value: e});
            }
        });
    }
    render() {
        return (
            <div>
                <div>当满足特定条件时才选中</div>
                <br />
                <CheckboxGroup options={ALL_OPTION_LIST} onChange={this.onChange} value={this.state.value} />
            </div>
        );
    }
}
