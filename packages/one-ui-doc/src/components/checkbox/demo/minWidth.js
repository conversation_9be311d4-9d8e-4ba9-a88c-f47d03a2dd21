import React from 'react';
import {Checkbox} from '@baidu/one-ui';

const CheckboxGroup = Checkbox.Group;
const CheckboxButton = Checkbox.Button;

const BASIC_CHILDREN = [
    <CheckboxButton value={1} key={1}>按钮</CheckboxButton>,
    <CheckboxButton value={2} key={2}>按钮2</CheckboxButton>,
    <CheckboxButton value={3} key={3}>按钮单选3</CheckboxButton>
];

export default () => {
    return (
        <div>
            <CheckboxGroup
                size="small"
                defaultValue={[1]}
                style={{'--one-checkbox-strong-min-width': 'initial'}}
            >
                {BASIC_CHILDREN}
            </CheckboxGroup>
            <br />
            <br />
            <CheckboxGroup
                defaultValue={[1]}
                style={{'--one-checkbox-strong-min-width': 'initial'}}
            >
                {BASIC_CHILDREN}
            </CheckboxGroup>
        </div>
    );
};
