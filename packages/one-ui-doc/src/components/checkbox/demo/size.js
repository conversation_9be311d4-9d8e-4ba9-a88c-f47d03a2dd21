import React, {PureComponent} from 'react';
import {Checkbox} from '@baidu/one-ui';

const CheckboxGroup = Checkbox.Group;

const ALL_OPTION_LIST = ['Apple', 'Pear', 'Orange'];

class FirstDemo extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            indeterminate: true,
            checkAll: false,
            checkedList: ['Apple']
        };
    }

    onChange = e => {
        const checkAll = e.target.checked;
        this.setState({
            indeterminate: false,
            checkAll,
            checkedList: checkAll ? ALL_OPTION_LIST : []
        });
    }

    onGroupChange = (checkedList = []) => {
        const checkedLen = checkedList.length;
        const allLen = ALL_OPTION_LIST.length;
        this.setState({
            checkedList,
            indeterminate: (!!checkedLen) && (checkedLen < allLen),
            checkAll: checkedLen === allLen
        });
    }

    render() {
        const {indeterminate, checkAll, checkedList} = this.state;
        const size = this.props.size;
        const firstCheckProps = {
            indeterminate,
            onChange: this.onChange,
            checked: checkAll,
            size
        };
        const firstCheckGroupProps = {
            options: ALL_OPTION_LIST,
            value: checkedList,
            onChange: this.onGroupChange,
            size
        };
        return (
            <div>
                <Checkbox {...firstCheckProps}>
                    部分选中态
                </Checkbox>
                <br />
                <br />
                <CheckboxGroup {...firstCheckGroupProps} />
            </div>
        );
    }
}

export default () => {
    return (
        <div>
            <div>小尺寸多选</div>
            <br />
            <br />
            <Checkbox size="small">普通多选</Checkbox>
            <br />
            <br />
            <div>标准尺寸多选</div>
            <br />
            <br />
            <Checkbox>普通多选</Checkbox>
            <br />
            <br />
            <div>部分选中态小尺寸</div>
            <br />
            <FirstDemo size="small" />
            <br />
            <br />
            <div>部分选中态标准尺寸</div>
            <br />
            <FirstDemo />
            <br />
            <br />
        </div>
    );
};
