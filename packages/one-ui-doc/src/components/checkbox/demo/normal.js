import React, {PureComponent} from 'react';
import {Checkbox} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {
        checked: true
    }

    onChange = e => {
        this.setState({
            checked: e.target.checked
        });
    }

    render() {
        return (
            <div>
                非受控使用
                <br />
                <br />
                <Checkbox>普通多选</Checkbox>
                <br />
                <br />
                受控使用
                <br />
                <br />
                <Checkbox checked={this.state.checked} onChange={this.onChange}>普通多选</Checkbox>
                <br />
                <br />
                文字换行
                <br />
                <br />
                <Checkbox style={{width: 200}}>
                    文字换行文字换行文行文字换行文字换行文字换行文字换行文字换行文字换行行文字换行文字换行文字换行
                    文字换行文字换行文行文字换行文字换行文字换行文字换行文字换行文字换行行文字换行文字换行文字换行
                </Checkbox>
            </div>
        );
    }
}
