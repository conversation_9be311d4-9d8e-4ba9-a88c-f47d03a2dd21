import React, {PureComponent} from 'react';
import {Checkbox} from '@baidu/one-ui';

const CheckboxGroup = Checkbox.Group;

const ALL_OPTION_LIST = ['Apple', 'Pear', 'Orange'];

class FirstDemo extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            indeterminate: true,
            checkAll: false,
            checkedList: ['Apple']
        };
    }

    onChange = e => {
        const checkAll = e.target.checked;
        this.setState({
            indeterminate: false,
            checkAll: checkAll,
            checkedList: checkAll ? ALL_OPTION_LIST : []
        });
    }

    onGroupChange = (checkedList = []) => {
        const checkedLen = checkedList.length;
        const allLen = ALL_OPTION_LIST.length;
        this.setState({
            checkedList: checkedList,
            indeterminate: (!!checkedLen) && (checkedLen < allLen),
            checkAll: checkedLen === allLen
        });
    }

    render() {
        const {indeterminate, checkAll, checkedList} = this.state;
        const firstCheckProps = {
            indeterminate,
            onChange: this.onChange,
            checked: checkAll
        };
        const firstCheckGroupProps = {
            options: ALL_OPTION_LIST,
            value: checkedList,
            onChange: this.onGroupChange
        };
        return (
            <div>
                <div>使用options属性，简单模式</div>
                <br />
                <div>
                    <Checkbox {...firstCheckProps}>
                        部分选中态
                    </Checkbox>
                    <br />
                    <br />
                    <CheckboxGroup {...firstCheckGroupProps} />
                </div>
                <br />
            </div>
        );
    };
}

const spanStyle = {
    marginLeft: '5px',
    color: 'red'
};

// eslint-disable-next-line react/no-multi-comp
class SecondDemo extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            indeterminate: true,
            checkAll: false,
            checkedList: ['Apple']
        };
    }

    onChange = e => {
        const checkAll = e.target.checked;
        this.setState({
            indeterminate: false,
            checkAll: checkAll,
            checkedList: checkAll ? ALL_OPTION_LIST : []
        });
    }

    onGroupChange = (checkedList = []) => {
        const checkedLen = checkedList.length;
        const allLen = ALL_OPTION_LIST.length;
        this.setState({
            checkedList: checkedList,
            indeterminate: (!!checkedLen) && (checkedLen < allLen),
            checkAll: checkedLen === allLen
        });
    }

    render() {
        const {indeterminate, checkAll, checkedList} = this.state;
        const secondCheckProps = {
            indeterminate,
            onChange: this.onChange,
            checked: checkAll
        };

        const secondCheckGroupProps = {
            value: checkedList,
            onChange: this.onGroupChange
        };
        return (
            <div>
                <div>使用Children-Checkbox，自定义dom结构</div>
                <br />
                <div>
                    <Checkbox {...secondCheckProps}>
                        部分选中态
                    </Checkbox>
                    <br />
                    <br />
                    <CheckboxGroup {...secondCheckGroupProps} >
                        <Checkbox value="Apple">Apple<span style={spanStyle}>我是特有的提示信息</span></Checkbox>
                        <Checkbox value="Pear">Pear</Checkbox>
                        <Checkbox value="Orange">Orange</Checkbox>
                    </CheckboxGroup>
                </div>
            </div>
        );
    };
}

export default () => {
    return (
        <div>
            <FirstDemo />
            <SecondDemo />
        </div>
    );
};
