import React from 'react';
import {Checkbox} from '@baidu/one-ui';

const CheckboxGroup = Checkbox.Group;
const CheckboxButton = Checkbox.Button;

const BASIC_CHILDREN = [
    <CheckboxButton value={1} key={1}>简单按钮单选1</CheckboxButton>,
    <CheckboxButton value={2} key={2}>简单按钮单选2</CheckboxButton>,
    <CheckboxButton value={3} key={3}>简单按钮单选3</CheckboxButton>
];

export default () => {
    return (
        <div>
            <div>small</div>
            <br />
            <CheckboxGroup type="simple" size="small" defaultValue={[1]}>
                {BASIC_CHILDREN}
            </CheckboxGroup>
            <br />
            <br />
            <div>medium</div>
            <br />
            <CheckboxGroup type="simple" defaultValue={[1]}>
                {BASIC_CHILDREN}
            </CheckboxGroup>
            <br />
        </div>
    );
};
