import React from 'react';
import {Checkbox} from '@baidu/one-ui';

const CheckboxGroup = Checkbox.Group;

const SIMPLE_OPTION_LIST = ['Apple', 'Pear', 'Orange'];
const COMPLEX_OPTION_LIST = [{
    label: '苹果',
    value: 'Apple'
},
{
    label: '梨',
    value: 'Pear',
    disabled: true
},
{
    label: '桔子',
    value: 'Orange'
}];

export default () => {
    return (
        <div>
            <div>options基础使用</div>
            <br />
            <CheckboxGroup options={SIMPLE_OPTION_LIST} />
            <br />
            <br />
            <div>options定义value，label，disabled</div>
            <br />
            <CheckboxGroup options={COMPLEX_OPTION_LIST} />
        </div>
    );
};
