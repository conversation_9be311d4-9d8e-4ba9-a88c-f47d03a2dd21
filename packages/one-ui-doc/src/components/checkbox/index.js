import BaseComponent from '../base';

export default {
    checkbox: {
        value: BaseComponent,
        label: 'Checkbox 多选框',
        demos: [
            {
                title: '普通多选',
                desc: '各种状态的多选 {默认状态} {悬浮状态} {点选状态}',
                source: 'normal'
            },
            {
                title: '尺寸',
                desc: '目前支持的中号和小号，分别为medium和small，默认中号为medium',
                source: 'size'
            },
            {
                title: '禁用',
                desc: '各种状态的不可用多选 {不可用} {选中不可用} {部分选中不可用}',
                source: 'disabled'
            },
            {
                title: 'Group 基础使用',
                desc: '简单模式使用{options}，自定义dom结构使用{Checkbox}',
                source: 'groupBase'
            },
            {
                title: 'Group options',
                desc: 'Array<string | number | {label: string, value: string | number, disabled: bool}>',
                descFlag: false,
                source: 'groupOptions'
            },
            {
                title: 'Group 垂直展示',
                desc: '默认{水平展示}，可以设置为{垂直展示}',
                source: 'direction'
            },
            {
                title: 'Group 条件选中',
                desc: '',
                source: 'checkedWithCondition'
            },
            {
                title: 'Button 加强',
                desc: '',
                source: 'enhance'
            },
            {
                title: 'Button 简单',
                desc: '',
                source: 'simpleButton'
            },
            {
                title: '单/复选混合',
                desc: '可通过选项增加{exclusive}属性来进行排他选择，从而实现单/复选混合。同时配合{emptyValue}可在取消选择时，填充默认选择。',
                source: 'exclusive'
            },
            {
                title: 'Button 换行',
                desc: '选项过多时，自动换行',
                source: 'lines'
            },
            {
                title: 'Button 禁用',
                desc: '加强多选-禁用',
                source: 'enhenceDisabled'
            },
            {
                title: 'Button 最小宽',
                desc: '--one-checkbox-strong-min-width: initial | length | percentage',
                source: 'minWidth'
            },
            {
                title: 'Button 空间分布',
                desc: '',
                source: 'Distribution'
            }
        ],
        apis: [
            {
                apiKey: 'Checkbox',
                title: 'Checkbox'
            },
            {
                apiKey: 'CheckboxButton',
                title: 'Checkbox.Button'
            },
            {
                apiKey: 'CheckboxGroup',
                title: 'Checkbox.Group'
            },
            {
                apiKey: 'CSS',
                title: 'CSS变量',
                children: [
                    {
                        param: '--one-checkbox-strong-min-width',
                        desc: 'strong型选项最小宽',
                        type: '<length>'
                    }
                ]
            }
        ]
    }
};
