export default {
    Checkbox: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义样式名',
            option: '',
            default: ''
        },
        {
            param: 'checked',
            type: 'boolean',
            desc: '是否选中，受控属性',
            option: '',
            default: 'false'
        },
        {
            param: 'defaultChecked',
            type: 'boolean',
            desc: '是否选中，非受控属性',
            option: '',
            default: 'false'
        },
        {
            param: 'disabled',
            type: 'boolean',
            desc: '是否不可操作',
            option: '',
            default: 'false'
        },
        {
            param: 'indeterminate',
            type: 'boolean',
            desc: '是否半选',
            option: '',
            default: 'false'
        },
        {
            param: 'size',
            type: 'string',
            desc: '型号大小，目前支持小号 or 中号，默认中号',
            option: 'small、medium',
            default: 'medium'
        },
        {
            param: 'onChange',
            type: 'Function(e) => checked: e.target.checked',
            desc: '变化时回调函数',
            option: '',
            default: ''
        }
    ],
    CheckboxButton: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义样式名',
            option: '',
            default: ''
        },
        {
            param: 'checked',
            type: 'boolean',
            desc: '是否选中',
            option: '',
            default: 'false'
        },
        {
            param: 'value',
            type: 'number、string',
            desc: '如果有此value值，以外部传入的value为准',
            option: '',
            default: ''
        }
    ],
    CheckboxGroup: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义样式名',
            option: '',
            default: ''
        },
        {
            param: 'disabled',
            type: 'boolean',
            desc: '是否不可操作',
            option: '',
            default: 'false'
        },
        {
            param: 'options',
            type: 'Array<string | number | {label: string, value: string | number, disabled: bool}>',
            desc: '指定可选项',
            option: '',
            default: '[]'
        },
        {
            param: 'value',
            type: 'Array<string | number>',
            desc: '指定选中项，跟Checkbox的value值对应',
            option: '',
            default: '[]'
        },
        {
            param: 'direction',
            type: 'string',
            desc: '下属checkbox水平排列还是垂直排列',
            option: 'row、column',
            default: 'row'
        },
        {
            param: 'onChange',
            type: 'Function(checkedList: Array)',
            desc: '变化时回调函数',
            option: '',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: '型号大小，目前支持小号 or 中号，默认中号',
            option: 'small、medium',
            default: 'medium'
        },
        {
            param: 'type',
            type: 'string',
            desc: '类型普通、加强按钮、简单按钮',
            option: 'normal | strong | simple',
            default: ''
        }
    ]
};
