import React, {useState} from 'react';
import {TimePicker, Radio} from '@baidu/one-ui';
import type {TimePickerProps} from '@baidu/one-ui';

export default () => {
    type Size = TimePickerProps['size'];
    const [size, setSize] = useState<Size>('medium');
    return (
        <>
            <Radio.Group
                type="strong"
                size="small"
                value={size}
                options={['xsmall', 'small', 'medium', 'large']}
                onChange={e => setSize(e.target.value as Size)}
            />
            <br />
            <TimePicker size={size} width={160} />
        </>
    );
}
