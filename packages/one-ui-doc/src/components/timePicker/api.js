export default {
    TimePicker: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义样式名',
            option: '',
            default: ''
        },
        {
            param: 'disabled',
            type: 'boolean',
            desc: '是否不可操作',
            option: '',
            default: 'false'
        },
        {
            param: 'getPopupContainer',
            type: 'Function(triggerNode)',
            desc: '弹层渲染父节点。默认渲染到 body 上',
            option: '',
            default: '() => document.body'
        },
        {
            param: 'width',
            type: 'number',
            desc: '指定宽度',
            option: '',
            default: 160
        },
        {
            param: 'value',
            type: 'string',
            desc: '时间选择器值，需要是对应的时间格式',
            option: '',
            default: ''
        },
        {
            param: 'placeholder',
            type: 'string',
            desc: '在用户输入值之前显示的提示信息',
            option: '',
            default: ''
        },
        {
            param: 'allowEmpty',
            type: 'boolean',
            desc: '是否允许为空',
            option: '',
            default: 'true'
        },
        {
            param: 'format',
            type: 'string',
            desc: '返回的value的时间格式，如HH:mm:ss、HH:mm等',
            option: '',
            default: 'HH:mm:ss'
        },
        {
            param: 'showHour',
            type: 'boolean',
            desc: '是否支持选择小时',
            option: '',
            default: 'true'
        },
        {
            param: 'showMinute',
            type: 'boolean',
            desc: '是否支持选择分钟',
            option: '',
            default: 'true'
        },
        {
            param: 'showSecond',
            type: 'boolean',
            desc: '是否支持选择秒',
            option: '',
            default: 'true'
        },
        {
            param: 'disabledHours',
            type: 'function',
            desc: '不可选的小时项，函数需返回不可选的小时项数组',
            option: '',
            default: ''
        },
        {
            param: 'disabledMinutes',
            type: 'function',
            desc: '不可选的分钟项，函数需返回不可选的分钟项数组',
            option: '',
            default: ''
        },
        {
            param: 'disabledSeconds',
            type: 'function',
            desc: '不可选的秒项，函数需返回不可选的秒项数组',
            option: '',
            default: ''
        },
        {
            param: 'use12Hours',
            type: 'boolean',
            desc: '是否使用12小时制',
            option: '',
            default: 'false'
        },
        {
            param: 'errorMessage',
            type: 'string',
            desc: '错误信息',
            option: '',
            default: ''
        },
        {
            param: 'errorLocation',
            type: 'string',
            desc: '错误信息位置',
            option: 'right、bottom',
            default: 'right'
        },
        {
            param: 'onChange',
            type: 'Function(value: string)',
            desc: '变化时回调函数',
            option: '',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: '时间选择器尺寸',
            option: 'small | medium',
            default: 'medium'
        }
    ]
};
