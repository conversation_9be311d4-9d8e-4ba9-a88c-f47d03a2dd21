import BaseComponent from '../base';

export default {
    slider: {
        value: BaseComponent,
        label: 'Slider 滑动条',
        demos: [
            {
                title: '分段式',
                desc: '基本滑动条-分段式',
                source: 'normal'
            },
            {
                title: '受控',
                desc: '和 数字输入框 组件保持同步。',
                source: 'input'
            },
            {
                title: '小尺寸',
                desc: '默认提供两种尺寸滑动条，small | medium，默认为medium',
                source: 'size'
            },
            {
                title: '禁用',
                desc: '',
                source: 'disabled'
            },
            {
                title: '区间',
                desc: '',
                source: 'range'
            }
        ],
        apis: [
            {
                apiKey: 'Slider',
                title: 'slider'
            }
        ]
    }
};
