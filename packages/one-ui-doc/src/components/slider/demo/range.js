import React, {PureComponent} from 'react';
import {<PERSON>lide<PERSON>} from '@baidu/one-ui';

export default class NoramlSlider extends PureComponent {

    handleDisabledChange = disabled => {
        this.setState({disabled});
    };

    render() {
        const marks = {
            0: 'A',
            20: 'B',
            40: 'C',
            60: 'D',
            80: 'E',
            100: 'F'
        };
        return (
            <div>
                <Slider defaultValue={[0, 30]} marks={marks} range />
            </div>
        );
    }
}
