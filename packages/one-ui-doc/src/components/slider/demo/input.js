import React, {PureComponent} from 'react';
import {Slider, NumberInput} from '@baidu/one-ui';

export default class SliderWithNumber extends React.Component {
    constructor(props) {
        super(props);
        this.state = {
            inputValue: 1
        };
    }

    onSliderChange = value => {
        this.setState({
            inputValue: value
        });
    };

    onChange = e => {
        this.setState({
            inputValue: +e.target.value
        });
    };

    render() {
        const {inputValue} = this.state;
        return (
            <div>
                <Slider
                    min={1}
                    max={20}
                    onChange={this.onSliderChange}
                    value={typeof inputValue === 'number' ? inputValue : 0}
                />
                <NumberInput
                    min={1}
                    max={20}
                    style={{marginLeft: 16}}
                    step={1}
                    value={inputValue}
                    onChange={this.onChange}
                />
            </div>
        );
    }
}
