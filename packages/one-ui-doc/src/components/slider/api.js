export default {
    slider: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义样式名',
            option: '',
            default: ''
        },
        {
            param: 'defaultValue',
            type: 'number|number[]',
            desc: '设置初始取值。当 range 为 false 时，使用 number，否则用 [number, number] ',
            option: '',
            default: '0 or [0, 0]'
        },
        {
            param: 'value',
            type: 'number|number[]',
            desc: '设置当前取值。当 range 为 false 时，使用 number，否则用 [number, number] ',
            option: '',
            default: ''
        },
        {
            param: 'range',
            type: 'bool',
            desc: '双滑块模式',
            option: '',
            default: 'false'
        },
        {
            param: 'max',
            type: 'number',
            desc: '最大值',
            option: '',
            default: '100'
        },
        {
            param: 'min',
            type: 'number',
            desc: '最小值',
            option: '',
            default: '0'
        },
        {
            param: 'step',
            type: 'number',
            desc: '步长，取值必须大于 0，并且可被 (max - min) 整除。当 marks 不为空对象时，可以设置 step 为 null，此时 Slider 的可选值仅有 marks 标出来的部分。 ',
            option: '',
            default: '1'
        },
        {
            param: 'marks',
            type: 'object',
            desc: '刻度标记，key 的类型必须为 number 且取值在闭区间 [min, max] 内，每个标签可以单独设置样式 ',
            option: '',
            default: '{ number: string|ReactNode } or { number: { style: object, label: string|ReactNode } }'
        },
        {
            param: 'dots',
            type: 'bool',
            desc: '是否只能拖拽到刻度上',
            option: '',
            default: 'false'
        },
        {
            param: 'included',
            type: 'bool',
            desc: 'marks 不为空对象时有效，值为 true 时表示值为包含关系，false 表示并列',
            option: '',
            default: 'true'
        },
        {
            param: 'disabled',
            type: 'bool',
            desc: '值为 true 时，滑块为禁用状态',
            option: '',
            default: 'false'
        },
        {
            param: 'onChange',
            type: 'func',
            desc: '当 Slider 的值发生改变时，会触发 onChange 事件，并把改变后的值作为参数传入。',
            option: '',
            default: ''
        },
        {
            param: 'onAfterChange',
            type: 'func',
            desc: '与 onmouseup 触发时机一致，把当前值作为参数传入。',
            option: '',
            default: ''
        },
        {
            param: 'tipFormatter',
            type: 'func',
            desc: 'Slider 会把当前值传给 tipFormatter，并在 Tooltip 中显示 tipFormatter 的返回值，若为 null，则隐藏 Tooltip。',
            option: '',
            default: ''
        },
        {
            param: 'tooltipVisible',
            type: 'bool',
            desc: '值为true时，Tooltip 将会始终显示；否则始终不显示，哪怕在拖拽及移入时。',
            option: '',
            default: ''
        },
        {
            param: 'tooltipPlacement',
            type: 'string',
            desc: '设置 Tooltip 展示位置',
            option: '',
            default: 'top'
        },
        {
            param: 'getTooltipPopupContainer',
            type: 'func',
            desc: 'Tooltip 渲染父节点，默认渲染到 body 上。',
            option: '',
            default: '() => document.body'
        }
    ]
};
