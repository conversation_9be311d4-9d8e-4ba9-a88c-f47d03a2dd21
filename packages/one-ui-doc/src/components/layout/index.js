import BaseComponent from '../base';

export default {
    layout: {
        value: BaseComponent,
        label: 'Layout 布局',
        demos: [
            {
                title: '基础用法',
                desc: '最基础用法',
                source: 'normal'
            },
            {
                title: '侧边栏',
                desc: '侧边栏属性',
                source: 'sidebar'
            },
            {
                title: '固定',
                desc: '固定',
                source: 'sticky'
            },
            {
                title: '业务示例',
                desc: '典型业务场景',
                source: 'biz'
            }
        ],
        apis: [
            {
                apiKey: 'Layout',
                title: 'Layout'
            },
            {
                apiKey: 'LayoutHeader',
                title: 'Layout.Header'
            },
            {
                apiKey: 'LayoutContent',
                title: 'Layout.Content'
            },
            {
                apiKey: 'LayoutFooter',
                title: 'Layout.Footer'
            },
            {
                apiKey: 'LayoutSidebar',
                title: 'Layout.Sidebar'
            },
            {
                apiKey: 'CSS',
                title: 'CSS变量',
                children: [
                    {
                        param: '--one-layout-header-height',
                        desc: 'header高度',
                        type: '<length>'
                    },
                    {
                        param: '--one-layout-footer-height',
                        desc: 'footer高度',
                        type: '<length>'
                    }
                ]
            }
        ]
    }
};