import React, {useState} from 'react';
import {Layout, Checkbox} from '@baidu/one-ui';
import IFrame from '../../../common/iframe';

const {
    Sidebar,
    Header,
    Footer,
    Content
} = Layout;

const Text = ({children}) =>
    <div style={{display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%'}}>{children}</div>;
const SidebarBlock = () => <div style={{backgroundColor: '#999', height: '100%'}}><Text>Sidebar</Text></div>;
const HeaderBlock = () => <div style={{backgroundColor: '#ccc'}}><Text>Header</Text></div>;
const ContentBlock = () => <div style={{backgroundColor: '#f6f7fa', height: 900}}><Text>Content</Text></div>;
const FooterBlock = () => <div style={{backgroundColor: '#ccc'}}><Text>Footer</Text></div>;

export default () => {
    const [headerSticky, setHeaderSticky] = useState(false);
    const [sidebarSticky, setSidebarSticky] = useState(false);
    const [footerSticky, setFooterSticky] = useState(false);

    return (
        <div>
            <div style={{display: 'flex', alignItems: 'center', height: 40}}>
                Header：
                <Checkbox checked={headerSticky} onChange={e => setHeaderSticky(e.target.checked)} />
                Sidebar：
                <Checkbox checked={sidebarSticky} onChange={e => setSidebarSticky(e.target.checked)} />
                Footer：
                <Checkbox checked={footerSticky} onChange={e => setFooterSticky(e.target.checked)} />
            </div>
            <br />
            侧边栏通顶
            <br />
            <br />
            <IFrame>
                <Layout>
                    <Sidebar sticky={sidebarSticky} showToggle><SidebarBlock /></Sidebar>
                    <Layout>
                        <Header sticky={headerSticky}><HeaderBlock /></Header>
                        <Content><ContentBlock /></Content>
                        <Footer sticky={footerSticky}><FooterBlock /></Footer>
                    </Layout>
                </Layout>
            </IFrame>
            <br />
            <br />
            顶部通栏
            <br />
            <br />
            <IFrame>
                <Layout>
                    <Header sticky={headerSticky}><HeaderBlock /></Header>
                    <Layout>
                        <Sidebar sticky={sidebarSticky} showToggle><SidebarBlock /></Sidebar>
                        <Content><ContentBlock /></Content>
                    </Layout>
                    <Footer sticky={footerSticky}><FooterBlock /></Footer>
                </Layout>
            </IFrame>
        </div>
    );
};
