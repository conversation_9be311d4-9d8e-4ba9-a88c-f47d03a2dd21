import React from 'react';
import {Layout} from '@baidu/one-ui';
import IFrame from '../../../common/iframe';

const {
    Sidebar,
    Header,
    Content,
    Footer
} = Layout;

const Text = ({children}) =>
    <div style={{display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%'}}>{children}</div>;
const SidebarBlock = () => <div style={{backgroundColor: '#999', height: '100%'}}><Text>Sidebar</Text></div>;
const HeaderBlock = () => <div style={{backgroundColor: '#ccc'}}><Text>Header</Text></div>;
const ContentBlock = () => <div style={{backgroundColor: '#f6f7fa', height: 200}}><Text>Content</Text></div>;
const FooterBlock = () => <div style={{backgroundColor: '#ccc'}}><Text>Footer</Text></div>;

export default () => {

    return (
        <div>
            侧边栏通顶
            <br />
            <br />
            <IFrame>
                <Layout>
                    <Sidebar><SidebarBlock /></Sidebar>
                    <Layout>
                        <Header><HeaderBlock /></Header>
                        <Content><ContentBlock /></Content>
                        <Footer><FooterBlock /></Footer>
                    </Layout>
                </Layout>
            </IFrame>
            <br />
            <br />
            顶部通栏
            <br />
            <br />
            <IFrame>
                <Layout>
                    <Header><HeaderBlock /></Header>
                    <Layout>
                        <Sidebar><SidebarBlock /></Sidebar>
                        <Layout>
                            <Content><ContentBlock /></Content>
                            <Footer><FooterBlock /></Footer>
                        </Layout>
                    </Layout>
                </Layout>
            </IFrame>
            <br />
            <br />
            侧边栏可收起
            <br />
            <br />
            <IFrame>
                <Layout>
                    <Sidebar showToggle><SidebarBlock /></Sidebar>
                    <Layout>
                        <Header><HeaderBlock /></Header>
                        <Content><ContentBlock /></Content>
                        <Footer><FooterBlock /></Footer>
                    </Layout>
                </Layout>
            </IFrame>
        </div>
    );
};
