import React, {useState} from 'react';
import {Layout, Radio, LayoutSidebarProps, Checkbox, SearchBox} from '@baidu/one-ui';
import IFrame from '../../../common/iframe';

const {
    Sidebar,
    Content
} = Layout;

const Text = ({children}) =>
    <div style={{display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%'}}>{children}</div>;
const SidebarBlock = () => <div style={{backgroundColor: '#999', height: '100%'}}><Text>Sidebar</Text></div>;
const ContentBlock = () => <div style={{backgroundColor: '#f6f7fa', height: 400}}><Text>Content</Text></div>;

export default () => {
    const [showToggle, setShowToggle] = useState(true);
    const [autoCollapse, setAutoCollapse] = useState(false);
    const [collapseMode, setMode] = useState('slim');
    const [toggleExtra, setToggleExtra] = useState(false);

    /**
     * @type LayoutSidebarProps
     */
    const props = {
        sticky: true,
        showToggle,
        collapseMode,
        autoCollapse
    };

    return (
        <div>
            <div style={{display: 'flex', alignItems: 'center', height: 40}}>
                可收起：
                <Checkbox checked={showToggle} onChange={e => setShowToggle(e.target.checked)} />
                {showToggle && (
                    <div style={{display: 'flex', alignItems: 'center', marginLeft: 15}}>
                        收起模式：
                        <Radio.Group
                            value={collapseMode}
                            options={['slim', 'hidden']}
                            onChange={e => setMode(e.target.value)}
                            type="strong"
                            size="small"
                            style={{marginRight: 15}}
                        />
                        自动：
                        <Checkbox
                            checked={autoCollapse}
                            onChange={e => setAutoCollapse(e.target.checked)}
                            style={{marginRight: 15}}
                        />
                    </div>
                )}
            </div>
            <br />
            <IFrame>
                <Layout>
                    <Sidebar {...props}><SidebarBlock /></Sidebar>
                    <Content><ContentBlock /></Content>
                </Layout>
            </IFrame>
        </div>
    );
};
