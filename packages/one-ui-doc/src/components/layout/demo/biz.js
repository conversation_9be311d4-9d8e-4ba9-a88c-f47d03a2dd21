import React, {useState} from 'react';
import {Layout, Nav, Sidenav, Menu, IconSvg, SearchBox, Affix, Button} from '@baidu/one-ui';
import IFrame from '../../../common/iframe';
import sticky from './sticky';

const {
    <PERSON>bar,
    Header,
    Content,
    Footer
} = Layout;

function navs() {
    return [
        {
            label: '导航1',
            key: '1'
        },
        {
            label: '导航2',
            key: '2'
        },
        {
            label: '导航3',
            key: '3'
        }
    ];
}

function SidenavBlock({collapsed}) {
    const {SubMenu, Item} = Menu;
    return (
        <Sidenav style={{width: 170, margin: collapsed ? 0 : '0 15px'}} collapsed={collapsed}>
            <Item key="1" icon={<IconSvg type="filter" />}>
                <span>菜单1</span>
            </Item>
            <Item key="2" icon={<IconSvg type="filter" />}>
                <span>菜单2</span>
            </Item>
            <Item key="3" icon={<IconSvg type="calendar" />}>
                <span>菜单3</span>
            </Item>
            <Item key="4" icon={<IconSvg type="calendar" />}>
                <span>菜单4</span>
            </Item>
            <Item key="5" icon={<IconSvg type="calendar" />}>
                <span>菜单5</span>
            </Item>
            <SubMenu
                key="sub"
                icon={<IconSvg type="search" />}
                title="更多"
            >
                <Item key="6">菜单6</Item>
                <Item key="7">菜单7</Item>
                <Item key="8">菜单8</Item>
                <Item key="9">菜单9</Item>
                <Item key="10">菜单10</Item>
                <Item key="11">菜单11</Item>
            </SubMenu>
        </Sidenav>
    );
}

const Logo = () => (
    <div
        style={{
            height: 64,
            width: 194,
            backgroundImage: 'url(https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png)',
            backgroundSize: 'contain',
            backgroundRepeat: 'no-repeat',
            backgroundPosition: '40% -10px'
        }}
    />
);

const FooterContent = () => (
    <div
        style={{
            padding: 24,
            lineHeight: 1,
            textAlign: 'center',
            fontSize: 12,
            color: '#999'
        }}
    >
        ©2021 Baidu 使用百度前必读 京公网安备11000002000001号 互联网信息服务许可
    </div>
);

const Title = () => (
    <div
        style={{
            padding: 24,
            lineHeight: 1,
            fontSize: 14,
            backgroundColor: '#fff',
            fontWeight: 600
        }}
    >
        标题名称
    </div>
);

const Aside = ({style}) => (
    <div
        style={{
            padding: 24,
            lineHeight: 1,
            textAlign: 'center',
            fontSize: 12,
            color: '#999',
            backgroundColor: '#fff',
            ...style
        }}
    >
        辅助信息区
    </div>
);

const Main = ({style}) => (
    <div
        style={{
            padding: 24,
            lineHeight: 1,
            textAlign: 'center',
            fontSize: 12,
            color: '#999',
            backgroundColor: '#fff',
            height: 300,
            ...style
        }}
    >
        主内容区
    </div>
);

const Toolbar = ({style, sticky}) => (
    <div
        style={{
            padding: 24,
            lineHeight: 1,
            fontSize: 12,
            color: '#999',
            backgroundColor: '#fff',
            margin: '0 20px',
            position: sticky ? 'sticky' : 'static',
            bottom: 0,
            boxShadow: sticky
                ? '0 4px 10px rgb(0 0 0 / 3%), 0 3px 9px rgb(0 0 0 / 2%), 0 2px 8px rgb(0 0 0 / 1%)'
                : 'none',
            ...style
        }}
    >
        <Button type="primary">提交</Button>
        <Button style={{marginLeft: 16}}>取消</Button>
    </div>
);

export default () => {
    const [collapsed, setCollapsed] = useState(false);
    return (
        <div>
            <IFrame>
                <Layout>
                    <Sidebar sticky>
                        <Logo />
                        <SidenavBlock />
                    </Sidebar>
                    <Layout>
                        <Header>
                            <Nav dataSource={navs()} />
                        </Header>
                        <Content>
                            <Title />
                            <Aside style={{margin: 20}} />
                            <Main style={{margin: 20}} />
                            <Toolbar />
                        </Content>
                        <Footer>
                            <FooterContent />
                        </Footer>
                    </Layout>
                </Layout>
            </IFrame>
            <br />
            <br />
            <br />
            <IFrame>
                <Layout>
                    <Header>
                        <div style={{display: 'flex', alignItems: 'center'}}>
                            <Logo />
                            <div style={{flex: 1}}>
                                <Nav dataSource={navs()} />
                            </div>
                        </div>
                    </Header>
                    <Layout>
                        <Sidebar
                            sticky
                            showToggle
                            onCollapsedChange={setCollapsed}
                            collapsed={collapsed}
                            collapseMode="slim"
                        >
                            <SidenavBlock collapsed={collapsed} />
                        </Sidebar>
                        <Layout>
                            <Content>
                                <Title />
                                <Layout style={{margin: 20, marginBottom: 0}}>
                                    <Content><Main style={{marginRight: 20}} /></Content>
                                    <Sidebar style={{boxShadow: 'none'}}><Aside /></Sidebar>
                                </Layout>
                                <Toolbar sticky />
                            </Content>
                            <Footer>
                                <FooterContent />
                            </Footer>
                        </Layout>
                    </Layout>
                </Layout>
            </IFrame>
            <br />
            <br />
            <br />
            <IFrame>
                <Layout style={{'--one-layout-footer-height': '40px'}}>
                    <Header>
                        <div style={{display: 'flex', alignItems: 'center'}}>
                            <Logo />
                            <div style={{flex: 1}}>
                                <Nav dataSource={navs()} />
                            </div>
                        </div>
                    </Header>
                    <Layout>
                        <Sidebar
                            showToggle
                            onCollapsedChange={setCollapsed}
                            collapsed={collapsed}
                            sticky
                        >
                            <SidenavBlock collapsed={collapsed} />
                        </Sidebar>
                        <Layout>
                            <Content>
                                <Title />
                                <Main style={{margin: 20, marginBottom: 0}} />
                                <Toolbar sticky style={{bottom: 40}} />
                            </Content>
                            <Footer>
                                <div
                                    style={{
                                        padding: 24,
                                        lineHeight: 1,
                                        textAlign: 'center',
                                        fontSize: 12,
                                        color: '#999'
                                    }}
                                >
                                    ©2021 Baidu 使用百度前必读 京公网安备11000002000001号 互联网信息服务许可
                                </div>
                            </Footer>
                        </Layout>
                    </Layout>
                    <Footer
                        sticky
                        style={{
                            lineHeight: '40px',
                            backgroundColor: '#fff',
                            fontSize: 12,
                            paddingLeft: 20,
                            boxShadow:
                                '0 4px 6px rgb(0 0 0 / 6%), 0 1px 10px rgb(0 0 0 / 5%), 0 2px 4px -1px rgb(0 0 0 / 1%)'
                        }}
                    >
                        今日实时数据：点击0次 消费0.00元 同比0.00% 展现0次 同比0.00%
                    </Footer>
                </Layout>
            </IFrame>
        </div>
    );
};
