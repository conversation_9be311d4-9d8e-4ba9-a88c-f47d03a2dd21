import BaseComponent from '../base';

export default {
    tabs: {
        value: BaseComponent,
        label: 'Tabs 标签页',
        demos: [
            {
                title: '线条',
                desc: '线条样式tabs - 容器左右边距为0 - tabItem超长处理',
                source: 'normalLine'
            },
            {
                title: '线条-无底边',
                desc: '线条样式tabs - 无底边',
                source: 'normalLineBorderless'
            },
            {
                title: '简洁',
                desc: 'simple样式tabs - 普通tabs',
                source: 'normalCard'
            },
            {
                title: '加强',
                desc: 'strong样式tabs - 普通tabs',
                source: 'strong'
            },
            {
                title: '删除',
                desc: '删除前确认可通过 {onDeleteBefore} 回调处理',
                source: 'closable'
            },
            {
                title: '添加',
                desc: '线条样式tabs - 带增加按钮',
                source: 'addNormal'
            },
            {
                title: '超长',
                desc: 'tabs - 超长',
                source: 'longNormal'
            },
            {
                title: '尺寸',
                desc: 'tabs - 尺寸有 {small} {medium} {large}3种 默认 {medium}，注意type为strong的样式只有medium一种样式',
                source: 'size'
            },
            {
                title: '禁用',
                desc: 'tabs - 存在disabled状态',
                source: 'disabled'
            },
            {
                title: '拖拽排序',
                desc: '拖拽Tab标题可进行选项排序，`sortable`需设置为`true`，配合`onSort`使用',
                source: 'sortable'
            },
            {
                title: '受控',
                desc: 'tabs - 受控用法',
                source: 'add2'
            },
            {
                title: '带icon',
                desc: 'tabs - 带icon',
                source: 'icon'
            },
            {
                title: '自定义tip渲染',
                desc: '自定义Tooltip或者Popover文案渲染函数，默认在tab的截断文字上显示Tooltip，其他情况(比如禁用，或者其他的补充说明等)都用Popover',
                source: 'RenderTip'
            }
        ],
        apis: [
            {
                apiKey: 'Tabs',
                title: 'Tabs'
            },
            {
                apiKey: 'TabPane',
                title: 'Tabs.TabPane'
            },
            {
                apiKey: 'CSS',
                title: 'CSS变量',
                children: [
                    {
                        param: '--dls-tab-menu-padding',
                        desc: '左右内边距',
                        type: '<length> | <percentage>'
                    }
                ]
            }
        ]
    }
};
