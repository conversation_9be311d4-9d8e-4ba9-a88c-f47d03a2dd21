import React, {useState, useCallback} from 'react';
import {Tabs, Radio} from '@baidu/one-ui';

const TabPane = Tabs.TabPane;

const tabs = Array.from({length: 5}).map((_, i) =>
    ({
        key: String(i),
        label: `Tab ${i}`
    }));
const types = ['simple', 'strong', 'line'];

export default () => {
    const [options, setOptions] = useState(tabs);
    const [type, setType] = useState('line');
    const [activeKey, setActiveKey] = useState('0');
    const onSort = useCallback(
        (fromIndex, toIndex) => {
            const option = options[fromIndex];
            options.splice(fromIndex, 1);
            options.splice(toIndex, 0, option);
            setOptions([...options]);
        },
        [options]
    );
    const onChange = useCallback(key => setActiveKey(key), []);
    const onDelete = useCallback(key => {
        setOptions(options.filter(o => o.key !== key));
        if (key === activeKey) {
            setActiveKey(null);
        }
    }, [options]);
    const onAdd = useCallback(() => {
        const key = String(Math.random());
        setActiveKey(key);
        setOptions(options.concat({key, label: key}));
    }, [options]);

    return (
        <>
            <Radio.Group
                type="strong"
                value={type}
                options={types}
                onChange={e => setType(e.target.value)}
                size="small"
            />
            <br />
            <br />
            <Tabs
                activeKey={activeKey}
                onChange={onChange}
                onAdd={onAdd}
                onDelete={onDelete}
                showAdd
                sortable
                onSort={onSort}
                type={type}
                key={type}
            >
                {options.map(({label, key}) => (
                    <TabPane tab={label} key={key} closable>Content of {label}</TabPane>
                ))}
            </Tabs>
        </>
    );
};
