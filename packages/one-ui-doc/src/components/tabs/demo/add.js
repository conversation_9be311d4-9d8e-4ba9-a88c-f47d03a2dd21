import React, {PureComponent} from 'react';
import {Tabs} from '@baidu/one-ui';

const TabPane = Tabs.TabPane;

export default class NoramlSlider extends PureComponent {
    state = {
        list: ['1', '2', '3'],
        value: '1',
        showAddDisabled: false
    };

    onChange = key => {
        this.setState({
            value: key
        });
    }

    onAdd = key => {
        const list = [...this.state.list];
        let showAddDisabled = this.state.showAddDisabled;
        const activeKey = `${key}11${Math.floor(Math.random() * 100)}`;
        list.push(activeKey);
        if (list.length >= 10) {
            showAddDisabled = true;
        }
        this.setState({
            list,
            showAddDisabled,
            value: activeKey
        });
    }

    onDelete = key => {
        let list = [...this.state.list];
        list = list.filter(item => item !== key);
        let showAddDisabled = this.state.showAddDisabled;
        if (list.length < 10) {
            showAddDisabled = false;
        }
        this.setState({
            list,
            showAddDisabled
        });
    }

    render() {
        const {list, value, showAddDisabled} = this.state;
        return (
            <div>
                <Tabs
                    activeKey={value}
                    onChange={this.onChange}
                    onAdd={this.onAdd}
                    onDelete={this.onDelete}
                    onTabClick={key => {
                        console.log('onTabClick', key);
                    }}
                    showAdd
                    showAddDisabled={showAddDisabled}
                >
                    {list.map(item => {
                        return (
                            <TabPane tab={`Tab ${item}`} key={item} closable>
                                Content of Tab Pane
                                {item}
                            </TabPane>
                        );
                    })}
                </Tabs>
            </div>
        );
    }
}
