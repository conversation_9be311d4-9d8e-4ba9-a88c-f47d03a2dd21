import React, {useState} from 'react';
import {Tabs, Input} from '@baidu/one-ui';

const TabPane = Tabs.TabPane;

const TABS = [
    {tab: 'Tab 1', key: '1', content: 'Content of Tab Pane 1'},
    {tab: 'Tab 2', key: '2', content: 'Content of Tab Pane 2', disabled: true},
    {tab: 'Tab 3', key: '3', content: 'Content of Tab Pane 3'},
    {tab: 'Tab 4', key: '4', content: 'Content of Tab Pane 4'}
];

const LONG_TABS = TABS.map(item => {
    return {
        ...item,
        tab: item.tab.repeat(5),
        disabled: false
    };
});

function renderTip({tab, overflow, disabled}) {
    if (disabled) {
        return `${tab}暂不可使用`;
    }
    if (tab === 'Tab 4') {
        return '上新求关注~';
    }
    if (overflow) {
        return tab;
    }
}

export default function RenderTip() {

    const [tabs, setTabs] = useState(LONG_TABS);
    const [firstLabel, setFirstLabel] = useState(LONG_TABS[0].tab);

    function onChangeFirstLabel(e) {
        const val = e.value;
        setFirstLabel(val);
        setTabs(tabs.map((item, index) => {
            if (!index) {
                return {...item, tab: val};
            }
            return item;
        }));
    }

    return (
        <div>
            <div>
                第一个tab的label：
                <Input onChange={onChangeFirstLabel} value={firstLabel} />
            </div>
            <br />
            <Tabs defaultActiveKey="1">
                {
                    tabs.map(({key, content, ...restProps}) => {
                        return (
                            <TabPane
                                key={key}
                                {...restProps}
                            >
                                {content}
                            </TabPane>
                        );
                    })
                }
            </Tabs>
            <br />
            <Tabs
                type="strong"
                defaultActiveKey="1"
                renderTip={renderTip}
            >
                {
                    TABS.map(({key, content, ...restProps}) => {
                        return (
                            <TabPane
                                key={key}
                                {...restProps}
                            >
                                {content}
                            </TabPane>
                        );
                    })
                }
            </Tabs>
        </div>
    );
}
