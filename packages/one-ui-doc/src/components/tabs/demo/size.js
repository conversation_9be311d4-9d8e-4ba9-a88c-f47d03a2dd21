import React from 'react';
import {Tabs} from '@baidu/one-ui';

const TabPane = Tabs.TabPane;

const array = [];

const status = ['success', 'info', 'error', 'warning'];

for (let i = 0; i < 30; i++) {
    array.push(
        <TabPane
            tab={`Tab ${i}`}
            key={String(i + 1)}
            closable
            status={i % 2 === 0 && status[Math.floor((Math.random() * 4))]}
        >
            Content of Tab Pane
            {i}
        </TabPane>
    );
}

export default () => {
    return (
        <div>
            <br />
            <br />
            small
            <br />
            <br />
            <Tabs
                defaultActiveKey="1"
                onChange={key => {
                    console.log('onChange', key);
                }}
                onAdd={key => {
                    console.log('key', key);
                }}
                onDelete={key => {
                    console.log('key', key);
                }}
                onTabClick={key => {
                    console.log('onTabClick', key);
                }}
                showAdd
                size="small"
            >
                {array.slice(0, 7)}
            </Tabs>
            <br />
            <br />
            <Tabs
                defaultActiveKey="1"
                onChange={key => {
                    console.log('onChange', key);
                }}
                onAdd={key => {
                    console.log('key', key);
                }}
                onDelete={key => {
                    console.log('key', key);
                }}
                onTabClick={key => {
                    console.log('onTabClick', key);
                }}
                showAdd
                type="simple"
                size="small"
            >
                {array.slice(0, 7)}
            </Tabs>
            <br />
            <br />
            <Tabs
                defaultActiveKey="1"
                onChange={key => {
                    console.log('onChange', key);
                }}
                onAdd={key => {
                    console.log('key', key);
                }}
                onDelete={key => {
                    console.log('key', key);
                }}
                onTabClick={key => {
                    console.log('onTabClick', key);
                }}
                showAdd
                type="strong"
                size="small"
            >
                {array.slice(0, 7)}
            </Tabs>
            <br />
            <br />
            medium
            <br />
            <br />
            <Tabs
                defaultActiveKey="1"
                onChange={key => {
                    console.log('onChange', key);
                }}
                onAdd={key => {
                    console.log('key', key);
                }}
                onDelete={key => {
                    console.log('key', key);
                }}
                onTabClick={key => {
                    console.log('onTabClick', key);
                }}
                showAdd
                size="medium"
            >
                {array}
            </Tabs>
            <br />
            <br />
            <Tabs
                defaultActiveKey="1"
                onChange={key => {
                    console.log('onChange', key);
                }}
                onAdd={key => {
                    console.log('key', key);
                }}
                onDelete={key => {
                    console.log('key', key);
                }}
                onTabClick={key => {
                    console.log('onTabClick', key);
                }}
                showAdd
                type="simple"
                size="medium"
            >
                {array}
            </Tabs>
            <br />
            <br />
            <Tabs
                defaultActiveKey="1"
                onChange={key => {
                    console.log('onChange', key);
                }}
                onAdd={key => {
                    console.log('key', key);
                }}
                onDelete={key => {
                    console.log('key', key);
                }}
                onTabClick={key => {
                    console.log('onTabClick', key);
                }}
                showAdd
                type="strong"
                size="medium"
            >
                {array}
            </Tabs>
            <br />
            <br />
            large
            <br />
            <br />
            <Tabs
                defaultActiveKey="1"
                onChange={key => {
                    console.log('onChange', key);
                }}
                onAdd={key => {
                    console.log('key', key);
                }}
                onDelete={key => {
                    console.log('key', key);
                }}
                onTabClick={key => {
                    console.log('onTabClick', key);
                }}
                showAdd
                size="large"
            >
                {array}
            </Tabs>
            <br />
            <br />
            <Tabs
                defaultActiveKey="1"
                onChange={key => {
                    console.log('onChange', key);
                }}
                onAdd={key => {
                    console.log('key', key);
                }}
                onDelete={key => {
                    console.log('key', key);
                }}
                onTabClick={key => {
                    console.log('onTabClick', key);
                }}
                showAdd
                type="simple"
                size="large"
            >
                {array}
            </Tabs>
            <br />
            <br />
            <Tabs
                defaultActiveKey="1"
                onChange={key => {
                    console.log('onChange', key);
                }}
                onAdd={key => {
                    console.log('key', key);
                }}
                onDelete={key => {
                    console.log('key', key);
                }}
                onTabClick={key => {
                    console.log('onTabClick', key);
                }}
                showAdd
                type="strong"
                size="large"
            >
                {array}
            </Tabs>
        </div>
    );
};
