import React from 'react';
import {Tabs} from '@baidu/one-ui';

const TabPane = Tabs.TabPane;

const array = [];

for (let i = 0; i < 30; i++) {
    array.push(
        <TabPane tab={`Tab title ${i}`} key={String(i + 1)} closable>
            Content of Tab Pane
            {i}
        </TabPane>
    );
}

export default () => {
    return (
        <div>
            <Tabs
                defaultActiveKey="1"
                onChange={key => {
                    console.log('onChange', key);
                }}
                onAdd={key => {
                    console.log('key', key);
                }}
                onDelete={key => {
                    console.log('key', key);
                }}
                onTabClick={key => {
                    console.log('onTabClick', key);
                }}
                showAdd
            >
                {array}
            </Tabs>
            <br />
            <br />
            <Tabs
                defaultActiveKey="1"
                onChange={key => {
                    console.log('onChange', key);
                }}
                onAdd={key => {
                    console.log('key', key);
                }}
                onDelete={key => {
                    console.log('key', key);
                }}
                onTabClick={key => {
                    console.log('onTabClick', key);
                }}
                showAdd
                type="simple"
            >
                {array}
            </Tabs>
            <br />
            <br />
            <Tabs
                defaultActiveKey="1"
                onChange={key => {
                    console.log('onChange', key);
                }}
                onAdd={key => {
                    console.log('key', key);
                }}
                onDelete={key => {
                    console.log('key', key);
                }}
                onTabClick={key => {
                    console.log('onTabClick', key);
                }}
                showAdd
                type="strong"
            >
                {array}
            </Tabs>
        </div>
    );
};
