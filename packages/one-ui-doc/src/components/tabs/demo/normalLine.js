import React from 'react';
import {Tabs, Button} from '@baidu/one-ui';

const TabPane = Tabs.TabPane;

export default () => {
    return (
        <div>
            <Tabs
                defaultActiveKey="1"
                onChange={key => {
                    console.log('onChange', key);
                }}
                onTabClick={key => {
                    console.log('onTabClick', key);
                }}
                extra={<Button type="text-strong" style={{marginLeft: 'auto'}}>extra</Button>}
                style={{'--dls-tab-menu-padding': '0px'}}
            >
                <TabPane tab="Tab 1Tab 1Tab 1Tab 1Tab 1" key="1">
                    Content of Tab Pane 1
                </TabPane>
                <TabPane tab="Tab 2" key="2">
                    Content of Tab Pane 2
                </TabPane>
                <TabPane tab="Tab 3" key="3">
                    Content of Tab Pane 3
                </TabPane>
                {/* {null}
                {false} */}
            </Tabs>
        </div>
    );
};
