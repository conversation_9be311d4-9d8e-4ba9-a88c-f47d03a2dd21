import React from 'react';
import {Tabs} from '@baidu/one-ui';

const TabPane = Tabs.TabPane;

export default () => {
    return (
        <div>
            <Tabs
                defaultActiveKey="1"
                onChange={key => {
                    console.log('onChange', key);
                }}
                onAdd={key => {
                    console.log('key', key);
                }}
                onDelete={key => {
                    console.log('key', key);
                }}
                onTabClick={key => {
                    console.log('onTabClick', key);
                }}
                showAdd
            >
                <TabPane tab="Tab 1" key="1" closable>
                    Content of Tab Pane 1
                </TabPane>
                <TabPane tab="Tab 2" key="2" closable>
                    Content of Tab Pane 2
                </TabPane>
                <TabPane tab="Tab 3" key="3" closable>
                    Content of Tab Pane 3
                </TabPane>
            </Tabs>
            <br />
            <br />
            <Tabs
                defaultActiveKey="1"
                onChange={key => {
                    console.log('onChange', key);
                }}
                onAdd={key => {
                    console.log('key', key);
                }}
                onDelete={key => {
                    console.log('key', key);
                }}
                onTabClick={key => {
                    console.log('onTabClick', key);
                }}
                showAdd
                type="simple"
            >
                <TabPane tab="Tab 1" key="1" closable>
                    Content of Tab Pane 1
                </TabPane>
                <TabPane tab="Tab 2" key="2" closable>
                    Content of Tab Pane 2
                </TabPane>
                <TabPane tab="Tab 3" key="3" closable>
                    Content of Tab Pane 3
                </TabPane>
            </Tabs>
            <br />
            <br />
            <Tabs
                defaultActiveKey="1"
                onChange={key => {
                    console.log('onChange', key);
                }}
                onAdd={key => {
                    console.log('key', key);
                }}
                onDelete={key => {
                    console.log('key', key);
                }}
                onTabClick={key => {
                    console.log('onTabClick', key);
                }}
                showAdd
                type="strong"
            >
                <TabPane tab="Tab 1" key="1" closable>
                    Content of Tab Pane 1
                </TabPane>
                <TabPane tab="Tab 2" key="2" closable>
                    Content of Tab Pane 2
                </TabPane>
                <TabPane tab="Tab 3" key="3" closable>
                    Content of Tab Pane 3
                </TabPane>
            </Tabs>
        </div>
    );
};
