export default {
    Tabs: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义类名',
            option: '',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: '尺寸, type为strong样式目前只有medium尺寸',
            option: 'small | medium | large',
            default: 'medium'
        },
        {
            param: 'activeKey',
            type: 'string',
            desc: '受控属性，当前选中的tab的key，与Tab.Pane的key对应，请传string类型值',
            option: '',
            default: ''
        },
        {
            param: 'defaultActiveKey',
            type: 'string',
            desc: '非受控属性，默认选中的tab的key，与Tab.Pane的key对应，请传string类型值',
            option: '',
            default: ''
        },
        {
            param: 'showAdd',
            type: 'string',
            desc: '是否展示添加按钮',
            option: '',
            default: ''
        },
        {
            param: 'type',
            type: 'string',
            desc: '样式类型',
            option: 'simple | strong | line',
            default: 'line'
        },
        {
            param: 'bordered',
            type: 'boolean',
            desc: '底边配置，type为line时有效',
            option: '',
            default: 'true'
        },
        {
            param: 'onChange',
            type: 'func(activeKey)',
            desc: '切换tab的时候的回调，activeKey为当前激活的key',
            option: '',
            default: ''
        },
        {
            param: 'onNextClick',
            type: 'func(e)',
            desc: '点击往后翻页按钮的回调，e为点击按钮的e',
            option: '',
            default: ''
        },
        {
            param: 'onPrevClick',
            type: 'func(e)',
            desc: '点击往前翻页按钮的回调，e为点击按钮的e',
            option: '',
            default: ''
        },
        {
            param: 'onTabClick',
            type: 'func(activeKey)',
            desc: '点击tab的回调，activeKey为当前点击的key',
            option: '',
            default: ''
        },
        {
            param: 'showAddDisabled',
            type: 'bool',
            desc: '添加按钮disabled态',
            option: '',
            default: ''
        },
        {
            param: 'onDelete',
            type: 'func(key)',
            desc: '点击删除的回调，key为当前删除tab的key',
            option: '',
            default: ''
        },
        {
            param: 'onAdd',
            type: 'func(activeKey)',
            desc: '点击添加的回调，activeKey为当前激活的key',
            option: '',
            default: ''
        },
        {
            param: 'addButtonText',
            type: 'string',
            desc: 'type为line的时候，支持配置自定义添加的文案',
            option: '',
            default: '添加标签'
        },
        {
            param: 'extra',
            type: 'ReactNode',
            desc: '右侧区域自定义',
            option: '',
            default: ''
        },
        {
            param: 'RenderTip',
            type: '(val: TabPaneProps) => ReactNode',
            desc: '自定义tab tip渲染方法',
            option: '',
            default: ''
        }
    ],
    TabPane: [{
        param: 'tab',
        type: 'ReactNode || string',
        desc: '选项卡头显示的文字',
        option: '',
        default: ''
    }, {
        param: 'Icon',
        type: 'ReactNode',
        desc: '选项文字前可展示icon',
        option: '',
        default: ''
    }, {
        param: 'closable',
        type: 'bool',
        desc: '当前选项卡是否可以关闭',
        option: '',
        default: ''
    }, {
        param: 'disabled',
        type: 'bool',
        desc: '当前选项卡是否是禁用态',
        option: '',
        default: ''
    }, {
        param: 'key',
        type: 'string',
        desc: '当前的tab的key，必传',
        option: '',
        default: ''
    }, {
        param: 'status',
        type: 'string',
        desc: '状态',
        option: 'success | info | error | warning',
        default: ''
    }]
};
