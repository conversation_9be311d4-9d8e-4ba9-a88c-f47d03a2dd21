export default {
    Table: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义className',
            option: '',
            default: ''
        },
        {
            param: 'bordered',
            type: 'boolean',
            desc: '是否表格需要bordered形式',
            option: '',
            default: 'false'
        },
        {
            param: 'headBordered',
            type: 'boolean',
            desc: '只设置表头带bordered',
            option: '',
            default: 'false'
        },
        {
            param: 'columns',
            type: '[]',
            desc: '详见下列columns',
            option: '',
            default: ''
        },
        {
            param: 'dataSource',
            type: '[] 里面对象的key和columns里面的每一个column的key对应上',
            desc: '表格的数据',
            option: '',
            default: ''
        },
        {
            param: 'defaultExpandedRowKeys',
            type: '[]',
            desc: '默认展开的子表的key，key为dataSource中的每一个数据的key，非受控属性',
            option: '',
            default: ''
        },
        {
            param: 'expandedRowKeys',
            type: '[]',
            desc: '展开的子表的key，key为dataSource中的每一个数据的key，受控属性',
            option: '',
            default: ''
        },
        {
            param: 'expandedRowRender',
            type: 'Function(record, index, indent, expanded):ReactNode',
            desc: '渲染的子表的render',
            option: '',
            default: ''
        },
        {
            param: 'loading',
            type: 'boolean',
            desc: '表格是否正在加载中',
            option: '',
            default: 'false'
        },
        {
            param: 'locale',
            type: `{
                filterTitle: '筛选',
                filterConfirm: '确定',
                filterCancel: '取消',
                emptyText: <span>暂无数据</span>,
                selectAll: '全选当页',
                selectInvert: '反选当页',
                loadingText: '加载中...'
            }`,
            desc: `
                一些默认话术 filterTitle: '筛选',
                filterConfirm: '确定',
                filterCancel: '取消',
                emptyText: 暂无数据,
                loadingText: 加载中...
            `,
            option: '',
            default: ''
        },
        {
            param: 'onClickExpandIcon',
            type: 'function',
            desc: '点击expand的icon触发的函数',
            option: '',
            default: ''
        },
        {
            param: 'onExpand',
            type: 'function',
            desc: '点击展开/关闭子母表按钮时候触发',
            option: '',
            default: ''
        },
        {
            param: 'onExpandedRowsChange',
            type: 'Function(expandedRows)',
            desc: '展开子母表行变化的时候触发 Function(expandedRows)',
            option: '',
            default: ''
        },
        {
            param: 'onFilterChange',
            type: 'Function',
            desc: '筛选改变时触发',
            option: '',
            default: ''
        },
        {
            param: 'onSortClick',
            type: 'Function',
            desc: '当存在排序icon时候，点击触发函数',
            option: '',
            default: ''
        },
        {
            param: 'pagination',
            type: 'boolean | {}',
            desc: '表格的分页器：false隐藏，true展示，Object可详细配置(参考pagination的属性)',
            option: '',
            default: ''
        },
        {
            param: 'rowKey',
            type: 'func or string',
            desc: '每一行的key进行转化，如果为函数则通过函数返回对应key值，如果为字符串则是dataSource的item对应的值（record[rowKey]）',
            option: '',
            default: 'key'
        },
        {
            param: 'rowSelection',
            type: '{}',
            desc: '详见下列rowSelection',
            option: '',
            default: ''
        },
        {
            param: 'scroll',
            type: '设置横向或纵向滚动，也可用于指定滚动区域的宽和高，可以设置为像素值，百分比, true { x: number | true, y: number }',
            desc: `
                设置横向或纵向滚动，也可用于指定滚动区域的宽和高，
                可以设置为像素值，百分比, true { x: number | true, y: number }，
                当x为数字的时候，为表格的总宽度，
                特别注意当scroll.x大于表格父亲宽度时候会出现滚动条，
                当scroll.x小于表格父亲宽度时候表格的总宽度为表格父亲宽度`,
            option: '',
            default: ''
        },
        {
            param: 'showHeader',
            type: 'boolean',
            desc: '是否展示表头',
            option: '',
            default: ''
        },
        {
            param: 'headerBottom',
            type: 'ReactNode',
            desc: '表头下插入内容',
            option: '',
            default: ''
        },
        {
            param: 'type',
            type: 'string',
            desc: '紧凑型，宽松型，正常型表格（cell padding 区别）',
            option: 'compact, loose, normal',
            default: 'normal'
        },
        {
            param: 'onDragStart',
            type: 'func',
            desc: '开始拖拽触发',
            option: '',
            default: ''
        },
        {
            param: 'onDraging',
            type: 'func({curCol, allColumns})',
            desc: '拖拽中触发',
            option: '',
            default: ''
        },
        {
            param: 'onDragEnd',
            type: 'func({allColumns})',
            desc: '拖拽完后触发',
            option: '',
            default: ''
        },
        {
            param: 'updateWidthChange',
            type: 'boolean',
            desc: '是否需要容器宽度变化的时候，内部表格宽度也跟着实时动态变化',
            option: '',
            default: 'false'
        },
        {
            param: 'autoHideOperation',
            type: 'string',
            desc: '自动隐藏相关操作，鼠标hover列头才展示。all: 全部; filter: 筛选; sort: 排序',
            option: 'all | filter | sort',
            default: ''
        },
        {
            param: 'cellLines',
            type: 'number',
            desc: '固定每行内容行高(包括表头，可以用`headCellLines`对表头微调)',
            option: '1 | 2',
            default: ''
        },
        {
            param: 'headCellLines',
            type: 'number',
            desc: '固定表头每行内容行高',
            option: '1 | 2',
            default: ''
        }
    ],
    Tablecolumn: [
        {
            param: 'colSpan',
            type: 'number',
            desc: '表头列合并,设置为 0 时，不渲染',
            option: '',
            default: ''
        },
        {
            param: 'dataIndex',
            type: 'string',
            desc: '列数据在数据项中对应的 key',
            option: '',
            default: ''
        },
        {
            param: 'filterDropdownTitle',
            type: 'ReactNode',
            desc: '可以自定义筛选标题',
            option: '',
            default: ''
        },
        {
            param: 'filterDropdownProps',
            type: 'object',
            desc: '可以自定义筛选Dropdown属性',
            option: '',
            default: ''
        },
        {
            param: 'filterDropdownVisible',
            type: 'boolean',
            desc: '用于控制自定义筛选菜单是否可见',
            option: '',
            default: ''
        },
        {
            param: 'filterMultiple',
            type: 'boolean',
            desc: '是否多选',
            option: '',
            default: 'true'
        },
        {
            param: 'filters',
            type: 'object[]',
            desc: '表头的筛选菜单项',
            option: '',
            default: ''
        },
        {
            param: 'filteredValue',
            type: 'string []',
            desc: '筛选的选中值， 如果传入改值，表示筛选完全受控',
            option: '',
            default: ''
        },
        {
            param: 'defaultFilteredValue',
            type: 'string []',
            desc: '默认筛选的选中值，用于非受控筛选',
            option: '',
            default: ''
        },
        {
            param: 'filterWithoutConfirm',
            type: 'boolean',
            desc: 'filterDropdown不需要确认按钮',
            option: '',
            default: 'false'
        },
        {
            param: 'filterIconVisible',
            type: 'boolean',
            desc: 'filterIcon是否展示',
            option: '',
            default: ''
        },
        {
            param: 'fixed',
            type: `列是否固定，可选
                true(等效于 left) 'left' 'right'`,
            desc: 'boolean|string',
            option: '',
            default: 'false'
        },
        {
            param: 'key',
            type: 'React 需要的 key，如果已经设置了唯一的 dataIndex，可以忽略这个属性',
            desc: 'string',
            option: '',
            default: ''
        },
        {
            param: 'render',
            type: 'Function(text, record, index) {}',
            desc: '生成复杂数据的渲染函数，参数分别为当前行的值，当前行数据，行索引',
            option: '',
            default: ''
        },
        {
            param: 'sorter',
            type: 'Function|boolean',
            desc: '排序函数，本地非受控排序使用一个函数(参考 Array.sort 的 compareFunction)，如果完全受控排序可设为 true',
            option: '',
            default: ''
        },
        {
            param: 'sortOrder',
            type: 'boolean|string',
            desc: `排序的受控属性，外界可用此控制列的排序，
                可设置为 'ascend' 'descend' false`,
            option: '',
            default: ''
        },
        {
            param: 'sortDirections',
            type: 'Array []',
            desc: `支持的排序方式，
                取值为 'ascend' 'descend'`,
            option: '',
            default: `
                ['ascend', 'descend']`
        },
        {
            param: 'title',
            type: 'ReactNode | ({sortOrder, filters}) => ReactNode',
            desc: '列头显示文字',
            option: '',
            default: ''
        },
        {
            param: 'desc',
            type: 'ReactNode',
            desc: '描述提示，支持`hide/show`属性方法，用于控制是否展示`desc`的`Popover`',
            option: '',
            default: ''
        },
        {
            param: 'width',
            type: '列宽度',
            desc: 'number',
            option: '',
            default: ''
        },
        {
            param: 'onFilter',
            type: '本地模式下，确定筛选的运行函数',
            desc: 'Function',
            option: '',
            default: ''
        },
        {
            param: 'onFilterDropdownVisibleChange',
            type: 'function(visible) {}',
            desc: '自定义筛选菜单可见变化时调用',
            option: '',
            default: ''
        },
        {
            param: 'customOperate',
            type: 'ReactNode []',
            desc: '可自定义每一个column的表头操作',
            option: '',
            default: ''
        },
        {
            param: 'align',
            type: 'string',
            desc: '自定义该列对齐方式',
            option: 'left|center|right',
            default: 'left'
        },
        {
            param: 'minWidth',
            type: 'number',
            desc: '可拖拽列下，该column拖拽的最小宽度',
            option: '',
            default: ''
        },
        {
            param: 'maxWidth',
            type: 'number',
            desc: '可拖拽列下，该column拖拽的最大宽度',
            option: '',
            default: ''
        },
        {
            param: 'draggable',
            type: 'boolean',
            desc: '表头是否可以拖拽',
            option: '',
            default: 'false'
        }
    ],
    TablerowSelection: [{
        param: 'fixed',
        type: 'boolean',
        desc: '把选择框列固定在左边',
        option: '',
        default: ''
    }, {
        param: 'selectedRowKeys',
        type: 'string[]',
        desc: '指定选中项的 key 数组，需要和 onChange 进行配合',
        option: '',
        default: ''
    }, {
        param: 'selections',
        type: 'object[]|boolean 见下selection',
        desc: '自定义选择项 配置项, 设为 true 时使用默认选择项（全选和反选）',
        option: '',
        default: 'true'
    }, {
        param: 'onChange',
        type: 'Function(selectedRowKeys, selectedRows)',
        desc: '选中项发生变化时的回调',
        option: '',
        default: ''
    }, {
        param: 'getCheckboxProps',
        type: 'Function(record)',
        desc: `用于设置单选/多选属性，
            disabled: 是否禁用，visible: 是否展示,
            示例：getCheckboxProps: record => return {disabled: !record.id || isEditPanel`,
        option: '',
        default: ''
    }, {
        param: 'type',
        type: 'string',
        desc: '单选or多选',
        option: 'radio | checkbox',
        default: ''
    }, {
        param: 'hideDefaultSelections',
        type: 'bool',
        desc: '去掉默认的全选和反选',
        option: '',
        default: 'false'
    }],
    Tableselection: [{
        param: 'key',
        type: 'string',
        desc: '',
        option: '',
        default: ''
    }, {
        param: 'text',
        type: '选择项显示的文字',
        desc: 'string|React.ReactNode',
        option: '',
        default: ''
    }, {
        param: 'onSelect',
        type: 'Function(changeableRowKeys)',
        desc: '选择项点击回调',
        option: '',
        default: ''
    }, {
        param: 'renderCheckbox',
        type: 'Function(record, checkboxNode)',
        desc: '自定义渲染checkbox(表头无record参数), checkboxNode为当前渲染节点',
        option: '',
        default: ''
    }]
};
