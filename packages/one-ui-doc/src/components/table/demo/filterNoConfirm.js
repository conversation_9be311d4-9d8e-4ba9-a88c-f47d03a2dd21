import React, {PureComponent} from 'react';
import {Table} from '@baidu/one-ui';

export default class Normal extends PureComponent {

    state = {
        data: [{
            key: '1',
            name: '<PERSON>',
            age: 32,
            address: 'New York No. 1 Lake Park'
        }, {
            key: '2',
            name: '<PERSON>',
            age: 42,
            address: 'London No. 1 Lake Park'
        }, {
            key: '3',
            name: '<PERSON>',
            age: 32,
            address: 'Sidney No. 1 Lake Park'
        }, {
            key: '4',
            name: '<PERSON>',
            age: 32,
            address: 'London No. 2 Lake Park'
        }],
        filteredValue: ['Jxxx']
    }

    onFilterChange = filter => {
        console.log(filter);
        const data = [];
        for (let i = 1; i < 30; i++) {
            data.push({
                key: `${i}${i}`,
                name: `${Math.floor(Math.random() * 100)}Jim <PERSON>`,
                age: 32,
                address: i % 2 === 0 ? 'London No. 2 Lake Park' : 'New York No. 1 Lake Park'
            });
        }
        this.setState({
            data,
            filteredValue: filter.name
        });
    }

    render() {
        const columns = [{
            title: '多选受控筛选',
            dataIndex: 'name',
            filters: [{
                text: '<PERSON>',
                value: 'Jxxx'
            }, {
                text: '<PERSON>',
                value: '<PERSON>xxx'
            }],
            filteredValue: this.state.filteredValue,
            filterWithoutConfirm: true
        }, {
            title: 'Age',
            dataIndex: 'age'
        }, {
            title: '单选完全不受控',
            dataIndex: 'address',
            filters: [{
                text: 'London',
                value: 'London'
            }, {
                text: 'New York',
                value: 'New York'
            }],
            filterMultiple: false,
            onFilter: (value, record) => record.address.indexOf(value) === 0,
            defaultFilteredValue: ['New York'],
            filterWithoutConfirm: true
        }];
        const data = this.state.data;
        return (
            <div style={{width: 960}}>
                <Table columns={columns} dataSource={data} onFilterChange={this.onFilterChange} />
            </div>
        );
    }
}
