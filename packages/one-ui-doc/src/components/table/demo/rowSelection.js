import React, {PureComponent} from 'react';
import {Table} from '@baidu/one-ui';

const columns = [{
    title: 'Name',
    dataIndex: 'name',
    // eslint-disable-next-line no-script-url
    render: text => <a href="https://www.baidu.com;">{text}</a>
}, {
    title: 'Age',
    dataIndex: 'age'
}, {
    title: 'Address',
    dataIndex: 'address'
}];

const data = [];
for (let i = 0; i < 100; i++) {
    data.push({
        id: `${i + 2}`,
        key: `${i + 1}`,
        name: '<PERSON>',
        age: 32,
        address: 'New York No. 1 Lake Park'
    });
}

// const data = [{
//     key: '1',
//     name: '<PERSON>',
//     age: 32,
//     address: 'New York No. 1 Lake Park'
// }, {
//     key: '2',
//     name: '<PERSON>',
//     age: 42,
//     address: 'London No. 1 Lake Park'
// }, {
//     key: '3',
//     name: '<PERSON>',
//     age: 32,
//     address: 'Sidney No. 1 Lake Park'
// }, {
//     key: '4',
//     name: 'Disabled User',
//     age: 99,
//     address: 'Sidney No. 1 Lake Park'
// }];

export default class Normal extends PureComponent {
    state = {
        selectedRowKeys: [] // Check here to configure the default column
    };

    onSelectChange = (selectedRowKeys, selectedRow) => {
        console.log('selectedRowKeys changed: ', selectedRowKeys);
        console.log('selectedRow', selectedRow);
        this.setState({selectedRowKeys});
    };

    render() {
        const hasSelected = this.state.selectedRowKeys.length > 0;
        return (
            <div style={{width: 960}}>
                <div style={{marginBottom: 16}}>
                    <span style={{marginLeft: 8}}>
                        {hasSelected ? `Selected ${this.state.selectedRowKeys.length} items` : ''}
                    </span>
                </div>
                <br />
                <br />
                多选
                <br />
                <br />
                <Table
                    rowKey='id'
                    rowSelection={{
                        selectedRowKeys: this.state.selectedRowKeys,
                        onChange: this.onSelectChange,
                        getCheckboxProps: record => {
                            return {
                                disabled: +record.id % 10 === 0
                            };
                        }
                    }}
                    columns={columns}
                    dataSource={data}
                />
                <br />
                <br />
                单选
                <br />
                <br />
                <Table
                    rowSelection={{
                        selectedRowKeys: this.state.selectedRowKeys,
                        onChange: this.onSelectChange,
                        type: 'radio'
                    }}
                    columns={columns}
                    dataSource={data}
                />
            </div>
        );
    }
}
