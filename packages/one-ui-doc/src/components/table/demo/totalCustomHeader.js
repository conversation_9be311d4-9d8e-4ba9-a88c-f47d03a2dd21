import React, {PureComponent} from 'react';
import {Table, Popover, IconSvg} from '@baidu/one-ui';

const data = [{
    key: '1',
    name: '<PERSON>',
    age: 32,
    address: 'New York No. 1 Lake Park'
}, {
    key: '2',
    name: '<PERSON>',
    age: 42,
    address: 'London No. 1 Lake Park'
}, {
    key: '3',
    name: '<PERSON>',
    age: 32,
    address: 'Sidney No. 1 Lake Park'
}, {
    key: '4',
    name: '<PERSON>',
    age: 32,
    address: 'London No. 2 Lake Park'
}];

// eslint-disable-next-line react/prefer-stateless-function
export default class Normal extends PureComponent {
    render() {
        const columns = [{
            dataIndex: 'name',
            title: (
                <span>
                    <Popover content="这是一个自定义Icon">
                        <span style={{marginRight: '5px'}}>Name</span>
                        <IconSvg type="calendar" />
                    </Popover>
                </span>
            )
        }, {
            title: 'Age',
            dataIndex: 'age'
        }, {
            title: 'Address',
            dataIndex: 'address'
        }];
        return (
            <div style={{width: 960}}>
                <Table columns={columns} dataSource={data} />
            </div>
        );
    }
}
