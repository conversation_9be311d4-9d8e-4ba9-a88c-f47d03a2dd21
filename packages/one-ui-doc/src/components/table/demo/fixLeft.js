/* eslint-disable no-script-url */
import React, {PureComponent} from 'react';
import {Table, Button} from '@baidu/one-ui';

const columns = [
    {
        title: 'Full Name', width: 100, dataIndex: 'name', key: 'name', fixed: 'left'
    },
    {
        title: 'Age', width: 100, dataIndex: 'age', key: 'age', fixed: 'left'
    },
    {
        title: 'Column 1', dataIndex: 'address', key: '1', width: 150
    },
    {
        title: 'Column 2', dataIndex: 'address', key: '2', width: 150
    },
    {
        title: 'Column 3', dataIndex: 'address', key: '3', width: 150
    },
    {
        title: 'Column 4', dataIndex: 'address', key: '4', width: 150
    },
    {
        title: 'Column 5', dataIndex: 'address', key: '5', width: 150
    },
    {
        title: 'Column 6', dataIndex: 'address', key: '6', width: 150
    },
    {
        title: 'Column 7', dataIndex: 'address', key: '7', width: 150
    },
    {
        title: 'Column 8', dataIndex: 'address', key: '8', width: 150
    },
    {
        title: 'Action',
        key: 'operation',
        fixed: 'right',
        width: 100,
        // eslint-disable-next-line no-unused-vars
        render: (text, record, index) => {
            // eslint-disable-next-line no-unused-expressions
            return <Button>Action</Button>;
        }
    }
];

const data = [];
for (let i = 0; i < 3; i++) {
    data.push({
        key: i,
        name: `Edrward ${i}`,
        age: 32,
        address: `London Park no. ${i}`
    });
}
export default class Normal extends PureComponent {

    render() {
        return (
            <div style={{width: 960}}>
                <Table columns={columns} dataSource={data} scroll={{x: 1500}} />
            </div>
        );
    }
}
