import React, {PureComponent} from 'react';
import {Table} from '@baidu/one-ui';

export default class Normal extends PureComponent {

    state = {
        data: [{
            key: '1',
            name: '<PERSON>',
            age: 32,
            address: 'New York No. 1 Lake Park'
        }, {
            key: '2',
            name: '<PERSON>',
            age: 42,
            address: 'London No. 1 Lake Park'
        }, {
            key: '3',
            name: '<PERSON>',
            age: 32,
            address: 'Sidney No. 1 Lake Park'
        }, {
            key: '4',
            name: '<PERSON>',
            age: 32,
            address: 'London No. 2 Lake Park'
        }],
        filteredValue: [],
        filterDropdownVisible: false
    }

    onFilterDropdownVisibleChange = visible => {
        console.log('filterDropdownVisible');
        this.setState({
            filterDropdownVisible: visible
        });
    }

    onFilterChange = filter => {
        console.log('onFilterChange', filter);
        const data = [];
        for (let i = 1; i < 30; i++) {
            data.push({
                key: `${i}${i}`,
                name: `${Math.floor(Math.random() * 100)}Jim <PERSON>`,
                age: 32,
                address: i % 2 === 0 ? 'London No. 2 Lake Park' : 'New York No. 1 Lake Park'
            });
        }
        this.setState({
            data,
            filteredValue: filter.name
        });
    }

    render() {
        const columns = [{
            title: '多选受控筛选',
            dataIndex: 'name',
            filters: [{
                text: 'Joe',
                value: 'Jxxx'
            }, {
                text: 'Jim',
                value: 'Jimxxx'
            }],
            filteredValue: this.state.filteredValue
        }, {
            title: 'Age',
            dataIndex: 'age',
            onFilterDropdownVisibleChange: this.onFilterDropdownVisibleChange,
            filterDropdownVisible: this.state.filterDropdownVisible,
            filters: [{
                text: 'Joe',
                value: 'Jxxx'
            }, {
                text: 'Jim',
                value: 'Jimxxx'
            }],
            filteredValue: this.state.filteredValue
        }, {
            title: '单选完全不受控',
            dataIndex: 'address',
            filters: [{
                text: 'London',
                value: 'London'
            }, {
                text: 'New York',
                value: 'New York'
            }],
            filterMultiple: false,
            onFilter: (value, record) => {
                return record.address.indexOf(value) === 0;
            },
            defaultFilteredValue: ['New York']
        }];
        const data = this.state.data;
        return (
            <div style={{width: 960}}>
                <Table columns={columns} dataSource={data} onFilterChange={this.onFilterChange} />
            </div>
        );
    }
}
