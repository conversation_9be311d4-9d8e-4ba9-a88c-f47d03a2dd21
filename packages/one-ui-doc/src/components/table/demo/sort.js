import React, {PureComponent} from 'react';
import {Table} from '@baidu/one-ui';


export default class Normal extends PureComponent {
    state = {
        sortOrder: 'descend',
        sortType: 'name',
        data: [{
            key: '1',
            name: '<PERSON>',
            age: 32,
            address: 'New York No. 1 Lake Park'
        }, {
            key: '2',
            name: '<PERSON>',
            age: 42,
            address: 'London No. 1 Lake Park'
        }, {
            key: '3',
            name: '<PERSON>',
            age: 32,
            address: 'Sidney No. 1 Lake Park'
        }, {
            key: '4',
            name: '<PERSON>',
            age: 32,
            address: 'London No. 2 Lake Park'
        }]
    }

    onSortClick = sortParams => {
        const {
            sortColumn,
            sortOrder
        } = sortParams;
        const data = [];
        for (let i = 0; i < 30; i++) {
            data.push({
                key: `${i}`,
                name: `<PERSON>{Math.floor(Math.random() * 1000)}`,
                age: i,
                address: 'London No. 2 Lake Park'
            });
        }
        this.setState({
            sortOrder,
            data,
            sortType: sortColumn && sortColumn.dataIndex
        });
    }

    render() {
        const {
            sortType,
            sortOrder
        } = this.state;

        const columns = [{
            title: `${sortOrder || ''}受控排序`,
            dataIndex: 'name',
            sorter: true,
            sortOrder: sortType === 'name' ? sortOrder : ''
        }, {
            title: '受控排序',
            dataIndex: 'age',
            sorter: true,
            sortOrder: sortType === 'age' ? sortOrder : ''
        }, {
            title: 'Address',
            dataIndex: 'address'
        }];
        return (
            <div style={{width: 960}}>
                <Table
                    columns={columns}
                    dataSource={this.state.data}
                    onSortClick={this.onSortClick}
                />
            </div>
        );
    }
}
