import React, {PureComponent} from 'react';
import {Table, TableColumnProps} from '@baidu/one-ui';

/**
 * @type TableColumnProps[]
 */
const columns = [{
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
    width: 100,
    fixed: 'left',
    filters: [{
        text: '<PERSON>',
        value: '<PERSON>'
    }, {
        text: '<PERSON>',
        value: '<PERSON>'
    }],
    onFilter: (value, record) => record.name.indexOf(value) === 0
}, {
    title: 'Other',
    children: [{
        title: 'Age',
        dataIndex: 'age',
        key: 'age',
        width: 200,
        sorter: (a, b) => a.age - b.age
    }, {
        title: 'Address',
        children: [{
            title: 'Street',
            dataIndex: 'street',
            key: 'street',
            width: 200
        }, {
            title: 'Block',
            children: [{
                title: 'Building',
                dataIndex: 'building',
                key: 'building',
                width: 100
            }, {
                title: 'Door No.',
                dataIndex: 'number',
                key: 'number',
                width: 100
            }]
        }]
    }]
}, {
    title: 'Company',
    children: [{
        title: 'Company Address',
        dataIndex: 'companyAddress',
        key: 'companyAddress'
    }, {
        title: 'Company Name',
        dataIndex: 'companyName',
        key: 'companyName'
    }]
}, {
    title: 'Gender',
    dataIndex: 'gender',
    key: 'gender',
    width: 80,
    fixed: 'right'
}];

const data = [];
for (let i = 0; i < 3; i++) {
    data.push({
        key: i,
        name: 'John Brown',
        age: i + 1,
        street: 'Lake Park',
        building: 'C',
        number: 2035,
        companyAddress: 'Lake Street 42',
        companyName: 'SoftLake Co',
        gender: 'M'
    });
}
export default class Normal extends PureComponent {

    render() {
        return (
            <div style={{width: 960}}>
                <Table
                    columns={columns}
                    dataSource={data}
                    bordered
                    scroll={{x: '130%'}}
                />
            </div>
        );
    }
}
