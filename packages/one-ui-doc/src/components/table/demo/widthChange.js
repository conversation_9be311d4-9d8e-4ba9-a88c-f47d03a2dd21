import React, {PureComponent} from 'react';
import {Table, Button} from '@baidu/one-ui';
import {findIndex} from 'lodash';

const columns = [{
    title: 'name',
    dataIndex: 'name',
    key: 'name',
    draggable: true,
    fixed: 'left',
    width: '15%',
    minWidth: 100
}, {
    title: 'Age',
    dataIndex: 'age',
    key: 'age',
    draggable: true,
    fixed: 'left',
    width: '15%',
    minWidth: 100
}, {
    title: 'Address',
    dataIndex: 'address',
    key: 'address',
    draggable: true,
    width: '20%',
    minWidth: 100
}, {
    title: 'Tags',
    key: 'tags',
    dataIndex: 'tags',
    draggable: true,
    width: '20%',
    minWidth: 100,
    render: tags => (
        <span>
            {tags.map(tag => {
                let color = tag.length > 5 ? 'geekblue' : 'green';
                if (tag === 'loser') {
                    color = 'volcano';
                }
                return <div color={color} key={tag}>{tag.toUpperCase()}</div>;
            })}
        </span>
    )
}, {
    title: 'Action',
    key: 'action',
    draggable: true,
    width: '30%',
    minWidth: 100,
    render: (text, record) => (
        <span>
            <a href="">
                Invite
                {record.name}
            </a>
            <a href="">Delete</a>
        </span>
    )
}];

const data = [];
for (let i = 0; i < 100; i++) {
    data.push({
        key: `${i}`,
        name: `John Brown ${i}`,
        age: 100 - i,
        address: `New York No. ${i + 1} Lake Park`,
        tags: ['nice', 'developer'],
        address2: `New York No. ${i + 1} Lake Park 2`
    });
}
export default class Normal extends PureComponent {

    state = {
        columns: [],
        selectedRowKeys: [],
        width: '1000px',
        left: '0px'
    }

    componentDidMount() {
        window.setTimeout(() => {
            this.setState({
                columns: columns
            });
        }, 3000);
    }

    onSelectChange = selectedRowKeys => {
        this.setState({selectedRowKeys});
    };

    getTableRef = ref => {
        this.tableContainerRef = ref;
    }

    onClick = () => {
        this.setState({
            width: '1600px',
            left: '-300px'
        });
    }

    onClickAccountTree = () => {
        this.setState({
            width: '1000px',
            left: '0'
        });
    }

    onAddNewColumn = () => {
        const newColumns = this.state.columns;
        newColumns.push({
            title: 'Address2',
            dataIndex: 'address2',
            key: 'address2',
            draggable: true,
            width: 200,
            minWidth: 100
        });
        this.setState({
            columns: [...newColumns]
        });
    }

    render() {
        return (
            <div style={{width: this.state.width, left: this.state.left}}>
                <Button onClick={this.onClick}>模拟账户树收起</Button>
                <br />
                <br />
                <Button onClick={this.onClickAccountTree}>模拟账户树打开</Button>
                <br />
                <br />
                <Button onClick={this.onAddNewColumn}>新增一列</Button>
                <Table
                    columns={this.state.columns}
                    dataSource={data}
                    showHeader
                    ref={this.getTableRef}
                    onDragStart={this.onDragStart}
                    onDraging={this.onDraging}
                    onDragEnd={this.onDragEnd}
                    headBordered
                    rowSelection={{
                        selectedRowKeys: this.state.selectedRowKeys,
                        onChange: this.onSelectChange
                    }}
                    headerFixTop={0}
                    updateWidthChange
                    useStickyFixTop
                    bottomScroll={{
                        bottom: 0
                    }}
                />
            </div>
        );
    }
}
