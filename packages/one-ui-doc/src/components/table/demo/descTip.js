import React, {PureComponent, useEffect, useState} from 'react';
import {Table} from '@baidu/one-ui';

const AsyncTip = ({hide, show}) => {
    const [desc, setDesc] = useState(null);
    useEffect(() => {
        hide();
        setTimeout(() => {
            setDesc('异步提示成功');
            show();
        }, 1000);
    }, []);
    return desc;
};

const columns = [
    {
        title: '名称(提示)',
        dataIndex: 'name',
        key: 'name',
        desc: '普通提示'
    }, {
        title: 'Age(异步提示)',
        dataIndex: 'age',
        key: 'age',
        desc: <AsyncTip />
    }, {
        title: 'Address',
        dataIndex: 'address',
        key: 'address'
    }
];

const data = [];
for (let i = 0; i < 10; i++) {
    data.push({
        key: `${i}`,
        name: `<PERSON> ${i}`,
        age: 100 - i,
        address: `New York No. ${i + 1} Lake Park`
    });
}
export default class Normal extends PureComponent {

    render() {
        return (
            <div style={{width: 960}}>
                <Table
                    columns={columns}
                    dataSource={data}
                />
            </div>
        );
    }
}
