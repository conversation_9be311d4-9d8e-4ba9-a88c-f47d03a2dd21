import React from 'react';
import {Switch, Table} from '@baidu/one-ui';
import {times} from 'lodash';

const data = times(20, (i) => ({
    key: `${i}`,
    name: `<PERSON> ${i}`,
    age: 100 - i,
    score: Math.floor(Math.random() * 100)
}));

export default () => {
    const [color, setColor] = React.useState(false);
    const border = '1px solid #fff';
    const onHeaderCell = () => ({
        style: {
            backgroundColor: color? '#ebf2ff' : 'transparent',
            border
        }
    });

    const columns = [
        {
            title: 'Name',
            dataIndex: 'name',
            key: 'name',
            onHeaderCell
        },
        {
            title: 'Age',
            dataIndex: 'age',
            key: 'age',
            onHeaderCell,
            onCell: (record) => ({
                style: {
                    color: record.age > 90 && color ? 'blue' : 'black',
                }
            })
        },
        {
            title: 'Score',
            dataIndex: 'score',
            key: 'score',
            onHeaderCell,
            onCell: (record) => ({
                style: {
                    color: record.score < 50 && color ? '#fff' : 'black',
                    backgroundColor: record.score < 50 && color ? 'red' : 'transparent',
                    border
                }
            })
        }
    ];
    return (
        <>
            着色：<Switch checked={color} onChange={() => setColor(!color)} />
            <br />
            <br />
            <Table
                columns={columns}
                dataSource={data}
                variant="basic"
                pagination={false}
            />
        </>
    )
};
