import React, {PureComponent} from 'react';
import {Table} from '@baidu/one-ui';

const columns = [{
    title: <span>啦啦啦</span>,
    dataIndex: 'name',
    key: 'name',
    // eslint-disable-next-line no-script-url
    render: text => <a href="https://www.baidu.com;">{text}</a>,
    align: 'right'
}, {
    title: 'Age',
    dataIndex: 'age',
    key: 'age'
}, {
    title: 'Address',
    dataIndex: 'address',
    key: 'address'
}, {
    title: 'Tags',
    key: 'tags',
    dataIndex: 'tags',
    render: tags => (
        <span>
            {tags.map(tag => {
                let color = tag.length > 5 ? 'geekblue' : 'green';
                if (tag === 'loser') {
                    color = 'volcano';
                }
                return <div color={color} key={tag}>{tag.toUpperCase()}</div>;
            })}
        </span>
    )
}, {
    title: 'Action',
    key: 'action',
    render: (text, record) => (
        <span>
            <a href="">
                Invite
                {record.name}
            </a>
            <a href="">Delete</a>
        </span>
    )
}];

const data = [];
for (let i = 0; i < 10; i++) {
    data.push({
        key: `${i}`,
        name: `<PERSON> ${i}`,
        age: 100 - i,
        address: `New York No. ${i + 1} Lake Park`,
        tags: ['nice', 'developer']
    });
}
// eslint-disable-next-line react/prefer-stateless-function
export default class Normal extends PureComponent {
    render() {
        return (
            <div style={{width: 960}}>
                <br />
                <br />
                宽松型
                <br />
                <br />
                <Table
                    columns={columns}
                    dataSource={data}
                    showHeader
                    type="loose"
                />
                <br />
                <br />
                紧凑型
                <br />
                <br />
                <Table
                    columns={columns}
                    dataSource={data}
                    showHeader
                    type="compact"
                />
            </div>
        );
    }
}
