import React, {PureComponent} from 'react';
import {Table, IconSvg, Popover} from '@baidu/one-ui';

const data = [{
    key: '1',
    name: '<PERSON>',
    age: 32,
    address: 'New York No. 1 Lake Park'
}, {
    key: '2',
    name: '<PERSON>',
    age: 42,
    address: 'London No. 1 Lake Park'
}, {
    key: '3',
    name: '<PERSON>',
    age: 32,
    address: 'Sidney No. 1 Lake Park'
}, {
    key: '4',
    name: '<PERSON>',
    age: 32,
    address: 'London No. 2 Lake Park'
}];

export default class Normal extends PureComponent {
    state = {
        sortOrder: 'descend'
    }


    render() {
        const columns = [{
            title: 'Name',
            dataIndex: 'name',
            customOperate: [
                (
                    <Popover key="1" content="这是一个自定义Icon">
                        <IconSvg type="calendar" />
                    </Popover>
                )
            ]
        }, {
            title: 'Age',
            dataIndex: 'age'
        }, {
            title: 'Address',
            dataIndex: 'address',
            customOperate: [
                (
                    <Popover key="1" content="这是一个自定义Icon">
                        <IconSvg type="calendar" />
                    </Popover>
                )
            ]
        }];
        return (
            <div style={{width: 960}}>
                <Table
                    columns={columns}
                    dataSource={data}
                    onSortClick={this.onSortClick}
                    onFilterChange={this.onFilterChange}
                />
            </div>
        );
    }
}
