import React, {PureComponent} from 'react';
import {Checkbox, Table, NumberInput, Radio} from '@baidu/one-ui';

const longText = '长长长长长长长长长长长长长长长长长长长长长长长长长长长';

const columns = long => [
    {
        title: <span>名字{long ? longText : ''}</span>,
        dataIndex: 'name',
        key: 'name'
    },
    {
        title: `跨列${long ? longText : ''}`,
        children: [
            {
                title: 'Age',
                dataIndex: 'age',
                key: 'age'
            }, {
                title: `Address${long ? longText : ''}`,
                dataIndex: 'address',
                key: 'address'
            }
        ]
    },
    {
        title: 'Tags',
        key: 'tags',
        dataIndex: 'tags',
        render: tags => (
            <span>
                {tags.join()}
            </span>
        )
    },
    {
        title: 'Action',
        key: 'action',
        render: (text, record) => (
            <span>
                <a href="">Delete</a>
            </span>
        )
    }];

const data = long => {
    const data = [];
    for (let i = 0; i < 10; i++) {
        data.push({
            key: `${i}`,
            name: `<PERSON> ${i}${long ? longText : ''}`,
            age: 100 - i,
            address: `New York No. ${i + 1} Lake Park`,
            tags: ['nice', 'developer']
        });
    }
    return data;
};

export default class Normal extends PureComponent {
    state = {
        cellLinesFlag: false,
        cellLines: 1,
        headCellLinesFlag: false,
        headCellLines: 1,
        long: false,
        type: 'normal'
    };

    render() {
        const {
            cellLinesFlag,
            headCellLinesFlag,
            cellLines,
            headCellLines,
            long,
            type
        } = this.state;
        const groupStyle = {
            display: 'inline-block',
            verticalAlign: 'middle',
            marginRight: 20
        };
        return (
            <div style={{width: 960}}>
                <Checkbox
                    checked={cellLinesFlag}
                    onChange={() => this.setState({cellLinesFlag: !cellLinesFlag})}
                >
                    固定占行
                </Checkbox>
                {cellLinesFlag
                    && (
                        <span style={{marginRight: 20, marginLeft: -10}}>
                            <NumberInput
                                size="small"
                                value={cellLines}
                                min={1}
                                max={2}
                                showTip={false}
                                width={60}
                                onChange={e => this.setState({cellLines: +e.target.value})}
                            />
                        </span>
                    )
                }
                <Checkbox
                    checked={headCellLinesFlag}
                    onChange={() => this.setState({headCellLinesFlag: !headCellLinesFlag})}
                >
                    列头固定占行
                </Checkbox>
                {headCellLinesFlag
                    && (
                        <span style={{marginRight: 20, marginLeft: -10}}>
                            <NumberInput
                                size="small"
                                value={headCellLines}
                                min={1}
                                max={2}
                                showTip={false}
                                width={60}
                                onChange={e => this.setState({headCellLines: +e.target.value})}
                            />
                        </span>
                    )
                }
                <Radio.Group
                    size="small"
                    value={type}
                    style={groupStyle}
                    onChange={e => this.setState({type: e.target.value})}
                >
                    <Radio.Button value="normal" key="normal">normal</Radio.Button>
                    <Radio.Button value="loose" key="loose">loose</Radio.Button>
                    <Radio.Button value="compact" key="compact">compact</Radio.Button>
                </Radio.Group>
                <Checkbox
                    checked={long}
                    onChange={() => this.setState({long: !long})}
                >
                    长内容
                </Checkbox>
                <br />
                <br />
                <Table
                    columns={columns(long)}
                    dataSource={data(long)}
                    headBordered
                    type={type}
                    cellLines={cellLinesFlag ? cellLines : undefined}
                    headCellLines={headCellLinesFlag ? headCellLines : undefined}
                />
            </div>
        );
    }
}
