import React, {PureComponent} from 'react';
import {Table} from '@baidu/one-ui';

const columns = [{
    title: 'Name',
    dataIndex: 'name'
}, {
    title: 'Age',
    dataIndex: 'age'
}, {
    title: 'Address',
    dataIndex: 'address'
}];


const data = [];
for (let i = 0; i < 5; i++) {
    data.push({
        key: `${i}`,
        name: `<PERSON> ${i}`,
        age: 32,
        address: `London, Park Lane no. ${i}`
    });
}

export default class Normal extends PureComponent {
    state = {
        selectedRowKeys: [] // Check here to configure the default column
    };

    onSelectChange = selectedRowKeys => {
        console.log('selectedRowKeys changed: ', selectedRowKeys);
        this.setState({selectedRowKeys});
    }

    render() {
        const hasSelected = this.state.selectedRowKeys.length > 0;
        return (
            <div style={{width: 960}}>
                <div style={{marginBottom: 16}}>
                    <span style={{marginLeft: 8}}>
                        {hasSelected ? `Selected ${this.state.selectedRowKeys.length} items` : ''}
                    </span>
                </div>
                <Table
                    rowSelection={{
                        selectedRowKeys: this.state.selectedRowKeys,
                        onChange: this.onSelectChange
                    }}
                    columns={columns}
                    dataSource={data}
                />
            </div>
        );
    }
}
