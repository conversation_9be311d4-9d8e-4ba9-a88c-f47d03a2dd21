import React, {useEffect, useState} from 'react';
import {Table, Checkbox, Radio, Alert} from '@baidu/one-ui';

const columns = [{
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
    fixed: 'left',
    width: 200,
    // eslint-disable-next-line no-script-url
    render: text => <a href="https://www.baidu.com;">{text}</a>
}, {
    title: 'Age',
    width: 200,
    dataIndex: 'age',
    key: 'age'
}, {
    title: 'Address',
    width: 200,
    dataIndex: 'address',
    key: 'address'
}, {
    title: 'Tags',
    width: 200,
    key: 'tags',
    dataIndex: 'tags',
    render: tags => (
        <span>
            {tags.map(tag => {
                let color = tag.length > 5 ? 'geekblue' : 'green';
                if (tag === 'loser') {
                    color = 'volcano';
                }
                return <div color={color} key={tag}>{tag.toUpperCase()}</div>;
            })}
        </span>
    )
}, {
    title: 'Action',
    width: 200,
    key: 'action',
    fixed: 'right',
    render: (text, record) => (
        <span>
            <a href="">
                Invite
                {record.name}
            </a>
            <a href="">Delete</a>
        </span>
    )
}];

const dataSource = [];
for (let i = 0; i < 5; i++) {
    dataSource.push({
        key: `${i}`,
        name: `John Brown ${i}`,
        age: 100 - i,
        address: `New York No. ${i + 1} Lake Park`,
        tags: ['nice', 'developer']
    });
}
let setTimer;
let startTime = 0;
export default () => {
    const [loading, setLoading] = useState(false);
    const [data, setData] = useState(true);
    const [loadingType, setLoadingType] = useState('bar');
    const [headerBottom, setHeaderBottom] = useState(false);
    const [yScroll, setYScroll] = useState(true);
    const [stickyHeader, setStickyHeader] = useState(false);
    const [fixed, setFixed] = useState(true);
    const [time, setTime] = useState(0);
    useEffect(() => {
        if (loading) {
            startTime = Date.now();
            setTimer = setInterval(() => {
                setTime(Math.round((Date.now() - startTime) / 1000));
            }, 50);
        }
        else {
            clearInterval(setTimer);
        }
        return () => {
            clearInterval(setTimer);
        };
    }, [loading]);

    return (
        <>
            <div className="demo-controls">
                <Checkbox
                    checked={loading}
                    onChange={e => setLoading(e.target.checked)}
                >
                    Loading
                    {loading
                        ? (
                            <span
                                style={{
                                    fontFamily: '"Baidu Number", sans-serif',
                                    fontVariantNumeric: 'tabular-nums'
                                }}
                            >
                                ({time}秒)
                            </span>
                        )
                        : null
                    }
                </Checkbox>
                <Checkbox
                    checked={data}
                    onChange={e => setData(e.target.checked)}
                >
                    数据
                </Checkbox>
                <Checkbox
                    checked={headerBottom}
                    onChange={e => setHeaderBottom(e.target.checked)}
                >
                    表头附加内容
                </Checkbox>
                <Checkbox
                    checked={yScroll}
                    onChange={e => setYScroll(e.target.checked)}
                >
                    内部滚动
                </Checkbox>
                <Checkbox
                    checked={stickyHeader}
                    onChange={e => setStickyHeader(e.target.checked)}
                >
                    表头吸顶
                </Checkbox>
                <Checkbox
                    checked={fixed}
                    onChange={e => setFixed(e.target.checked)}
                >
                    固定列
                </Checkbox>
                类型：
                <Radio.Group
                    options={['bar', 'spinner']}
                    type="strong"
                    size="small"
                    value={loadingType}
                    onChange={e => setLoadingType(e.target.value)}
                />
            </div>
            <div style={{width: 960}}>
                <Table
                    columns={columns.map((col, i) => ({...col, fixed: fixed ? col.fixed : ''}))}
                    dataSource={data ? dataSource : []}
                    loading={loading}
                    // bar 类型配置，普通loading不需要
                    loadingOption={{type: loadingType}}
                    scroll={yScroll ? {y: 200} : {}}
                    headerFixTop={stickyHeader ? 0 : undefined}
                    headerBottom={
                        headerBottom
                            ? <Alert content="一些提示" type="info" showIcon />
                            : null
                    }
                />
            </div>
        </>
    );
};
