import React, {PureComponent} from 'react';
import {Table, Menu, Dropdown, IconSvg} from '@baidu/one-ui';

const columns = [
    {title: 'Name', dataIndex: 'name', key: 'name'},
    {title: 'Platform', dataIndex: 'platform', key: 'platform'},
    {title: 'Version', dataIndex: 'version', key: 'version'},
    {title: 'Upgraded', dataIndex: 'upgradeNum', key: 'upgradeNum'},
    {title: 'Creator', dataIndex: 'creator', key: 'creator'},
    {title: 'Date', dataIndex: 'createdAt', key: 'createdAt'},
    // eslint-disable-next-line no-script-url
    {title: 'Action', key: 'operation', render: () => <a href="https://www.baidu.com;">Publish</a>}
];

const data = [];
for (let i = 0; i < 3; ++i) {
    data.push({
        key: i,
        name: '<PERSON>ree<PERSON>',
        platform: 'iOS',
        version: '10.3.4.5654',
        upgradeNum: 500,
        creator: 'Jack',
        createdAt: '2014-12-24 23:12:00'
    });
}

export default class Normal extends PureComponent {
    state = {
        expandedRowKeys: [2]
    };

    expandedRowRender = (record, index) => {
        const menu = (
            <Menu>
                <Menu.Item>
                    Action 1
                </Menu.Item>
                <Menu.Item>
                    Action 2
                </Menu.Item>
            </Menu>
        );
        const columns = [
            {
                title: 'Date', dataIndex: 'date', key: 'date'
            },
            {
                title: 'Name', dataIndex: 'name', key: 'name'
            },
            {
                title: 'Status', key: 'state', render: () => <span>Finished</span>
            },
            {
                title: 'Upgrade Status', dataIndex: 'upgradeNum', key: 'upgradeNum'
            },
            {
                title: 'Action',
                dataIndex: 'operation',
                key: 'operation',
                render: () => (
                    <span className="table-operation">
                        <a style={{marginRight: '10px'}} href="">Pause</a>
                        <a style={{marginRight: '10px'}} href="">Stop</a>
                        <Dropdown overlay={menu} transparent={false}>
                            <a href="">
                                More
                                <IconSvg type="down" />
                            </a>
                        </Dropdown>
                    </span>
                )
            }
        ];

        const data = [];
        for (let i = 0; i < 30; ++i) {
            data.push({
                key: i,
                date: '2014-12-24 23:12:00',
                name: 'This is production name',
                upgradeNum: 'Upgraded: 56'
            });
        }
        if (index === 1) {
            return null;
        }
        return (
            <Table
                columns={columns}
                dataSource={data}
            />
        );
    };

    onExpandedRowsChange = rows => {
        this.setState({
            expandedRowKeys: rows
        });
    }

    render() {
        return (
            <div style={{width: 960}}>
                受控-控制表格展开收起
                <br />
                <br />
                <Table
                    className="components-table-demo-nested"
                    columns={columns}
                    expandedRowRender={this.expandedRowRender}
                    dataSource={data}
                    expandedRowKeys={this.state.expandedRowKeys}
                    onExpandedRowsChange={this.onExpandedRowsChange}
                />
                <br />
                <br />
                非受控-控制表格展开收起
                <br />
                <br />
                <Table
                    className="components-table-demo-nested"
                    columns={columns}
                    expandedRowRender={this.expandedRowRender}
                    dataSource={data}
                    defaultExpandedRowKeys={[2]}
                />
            </div>
        );
    }
}
