import React, {PureComponent} from 'react';
import {Table, Button} from '@baidu/one-ui';
import {findIndex} from 'lodash';

const columns = [{
    title: 'name',
    dataIndex: 'name',
    key: 'name',
    draggable: true,
    fixed: 'left',
    width: '15%',
    minWidth: 100
}, {
    title: 'Age',
    dataIndex: 'age',
    key: 'age',
    draggable: true,
    fixed: 'left',
    width: '15%',
    minWidth: 100
}, {
    title: 'Address',
    dataIndex: 'address',
    key: 'address',
    draggable: true,
    width: '20%',
    minWidth: 100
}, {
    title: 'Tags',
    key: 'tags',
    dataIndex: 'tags',
    draggable: true,
    width: '20%',
    minWidth: 100,
    render: tags => (
        <span>
            {tags.map(tag => {
                let color = tag.length > 5 ? 'geekblue' : 'green';
                if (tag === 'loser') {
                    color = 'volcano';
                }
                return <div color={color} key={tag}>{tag.toUpperCase()}</div>;
            })}
        </span>
    )
}, {
    title: 'Action',
    key: 'action',
    draggable: true,
    width: '30%',
    minWidth: 100,
    render: (text, record) => (
        <span>
            <a href="">
                Invite
                {record.name}
            </a>
            <a href="">Delete</a>
        </span>
    )
}];

const data = [];
for (let i = 0; i < 50; i++) {
    data.push({
        key: `${i}`,
        name: `John Brown ${i}`,
        age: 100 - i,
        address: `New York No. ${i + 1} Lake Park`,
        tags: ['nice', 'developer']
    });
}
export default class Normal extends PureComponent {

    state = {
        columns: [],
        selectedRowKeys: []
    }

    componentDidMount() {
        window.setTimeout(() => {
            this.setState({
                columns: columns
            });
        }, 3000);
    }

    onSelectChange = selectedRowKeys => {
        this.setState({selectedRowKeys});
    };

    getTableRef = ref => {
        this.tableContainerRef = ref;
    }

    onClick = () => {
        const columns = this.state.columns;
        this.tableContainerRef.updateColumnWidths(columns);
    }

    onDragStart = () => {
        // console.log('表格开始拖拽');
    }

    onDraging = props => {
        // console.log('表格开始拖拽', props);
    }

    onDragEnd = props => {
        // console.log('拖拽完成', props);
    }

    onAdd = () => {
        const columns = [...this.state.columns];
        columns.push({
            title: 'NewColumn',
            dataIndex: 'NewColumn',
            key: 'NewColumn',
            draggable: true,
            width: '25%',
            minWidth: 100
        });
        this.setState({
            columns
        });
    }

    onDelete = () => {
        const columns = [...this.state.columns];
        columns.pop();
        this.setState({
            columns
        });
    }

    changeOrder = () => {
        const columns = [...this.state.columns];
        const c3 = columns[3];
        const c4 = columns[4];
        columns[4] = {
            ...c3
        };
        columns[3] = {
            ...c4
        };
        this.setState({
            columns
        });
    }

    render() {
        return (
            <div style={{width: 960}}>
                <Button onClick={this.onClick}>重置宽度</Button>
                <br />
                <br />
                <Button onClick={this.onAdd}>增加列</Button>
                <br />
                <br />
                <Button onClick={this.onDelete}>删除列</Button>
                <br />
                <br />
                <Button onClick={this.changeOrder}>换序</Button>
                <br />
                <br />
                <Table
                    columns={this.state.columns}
                    dataSource={data}
                    showHeader
                    ref={this.getTableRef}
                    onDragStart={this.onDragStart}
                    onDraging={this.onDraging}
                    onDragEnd={this.onDragEnd}
                    headBordered
                    rowSelection={{
                        selectedRowKeys: this.state.selectedRowKeys,
                        onChange: this.onSelectChange
                    }}
                />
            </div>
        );
    }
}
