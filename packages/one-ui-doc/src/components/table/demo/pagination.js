import React, {PureComponent} from 'react';
import {Table} from '@baidu/one-ui';

const columns = [{
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
    // eslint-disable-next-line no-script-url
    render: text => <a href="https://www.baidu.com;">{text}</a>
}, {
    title: 'Age',
    dataIndex: 'age',
    key: 'age'
}, {
    title: 'Address',
    dataIndex: 'address',
    key: 'address'
}, {
    title: 'Tags',
    key: 'tags',
    dataIndex: 'tags',
    render: tags => (
        <span>
            {tags.map(tag => {
                let color = tag.length > 5 ? 'geekblue' : 'green';
                if (tag === 'loser') {
                    color = 'volcano';
                }
                return <div color={color} key={tag}>{tag.toUpperCase()}</div>;
            })}
        </span>
    )
}, {
    title: 'Action',
    key: 'action',
    render: (text, record) => (
        <span>
            <a href="">
                Invite
                {record.name}
            </a>
            <a href="">Delete</a>
        </span>
    )
}];

const data = [];
for (let i = 0; i < 5; i++) {
    data.push({
        key: `${i}`,
        name: `<PERSON> ${i}`,
        age: 100 - i,
        address: `New York No. ${i + 1} Lake Park`,
        tags: ['nice', 'developer']
    });
}
export default class Normal extends PureComponent {
    state = {
        pageNo: 1,
        pageSize: 2,
        total: 20,
        data
    }

    onPageNoChange = e => {
        const pageNo = e.target.value;
        const pageSize = this.state.pageSize;
        const newData = [];
        for (let i = 100; i < 100 + pageSize; i++) {
            newData.push({
                key: `${i}`,
                name: `John Brown ${Math.floor(Math.random() * 100)} ${i}`,
                age: 100 - i,
                address: `${pageNo} New York No. ${i + 1} Lake Park`,
                tags: ['nice', 'developer']
            });
        }
        this.setState({
            data: newData,
            pageNo
        });
    }

    onPageSizeChange = e => {
        const pageSize = e.target.value;
        const newData = [];
        for (let i = 300; i < 300 + pageSize; i++) {
            newData.push({
                key: `${i}`,
                name: `John Brown ${Math.floor(Math.random() * 100)} ${i}`,
                age: 100 - i,
                address: `New York No. ${i + 1} Lake Park`,
                tags: ['nice', 'developer']
            });
        }
        this.setState({
            data: newData,
            pageSize
        });
    }

    render() {
        return (
            <div style={{width: 960}}>
                <Table
                    columns={columns}
                    dataSource={this.state.data}
                    pagination={{
                        pageNo: this.state.pageNo,
                        pageSize: this.state.pageSize,
                        onPageNoChange: this.onPageNoChange,
                        onPageSizeChange: this.onPageSizeChange,
                        pageSizeOptions: [2, 5, 10],
                        total: this.state.total
                    }}
                />
            </div>
        );
    }
}
