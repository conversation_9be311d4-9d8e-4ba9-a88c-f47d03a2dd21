import React, {PureComponent} from 'react';
import {Table} from '@baidu/one-ui';

const columns = [{
    title: 'Name',
    dataIndex: 'name'
}, {
    title: 'Age',
    dataIndex: 'age'
}, {
    title: 'Address',
    dataIndex: 'address'
}];

const data = [];
for (let i = 0; i < 10; i++) {
    data.push({
        key: `${i}`,
        name: `<PERSON> ${i}`,
        age: 32,
        address: `London, Park Lane no. ${i}`
    });
}

export default class Normal extends PureComponent {
    state = {
        selectedRowKeys: [] // Check here to configure the default column
    };

    onSelectChange = selectedRowKeys => {
        console.log('selectedRowKeys changed: ', selectedRowKeys);
        this.setState({selectedRowKeys});
    }

    render() {
        return (
            <div style={{width: 960}}>
                <Table
                    rowSelection={{
                        selectedRowKeys: this.state.selectedRowKeys,
                        onChange: this.onSelectChange,
                        selections: [{
                            key: 'all-data',
                            text: '全选',
                            onSelect: () => {
                                const selectedRowKeys = [];
                                for (let i = 0; i < 46; i++) {
                                    selectedRowKeys.push(`${i}`);
                                }
                                this.setState({
                                    selectedRowKeys // 0...45
                                });
                            }
                        }, {
                            key: 'odd',
                            text: '选择双列',
                            onSelect: changableRowKeys => {
                                let newSelectedRowKeys = [];
                                newSelectedRowKeys = changableRowKeys.filter((key, index) => {
                                    if (index % 2 !== 0) {
                                        return false;
                                    }
                                    return true;
                                });
                                this.setState({selectedRowKeys: newSelectedRowKeys});
                            }
                        }, {
                            key: 'even',
                            text: '选择单列',
                            onSelect: changableRowKeys => {
                                let newSelectedRowKeys = [];
                                newSelectedRowKeys = changableRowKeys.filter((key, index) => {
                                    if (index % 2 !== 0) {
                                        return true;
                                    }
                                    return false;
                                });
                                this.setState({selectedRowKeys: newSelectedRowKeys});
                            }
                        }],
                        onSelection: this.onSelection
                    }}
                    hideDefaultSelections
                    columns={columns}
                    dataSource={data}
                />
                <br />
                <br />
                默认状态
                <br />
                <br />
                <Table
                    rowSelection={{
                        selectedRowKeys: this.state.selectedRowKeys,
                        onChange: this.onSelectChange,
                        selections: true,
                        onSelection: this.onSelection
                    }}
                    columns={columns}
                    dataSource={data}
                    pagination={{
                        pageSize: 5,
                        pageSizeOptions: [2, 5, 10],
                        total: 10
                    }}
                />
            </div>
        );
    }
}
