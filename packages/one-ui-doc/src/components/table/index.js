import BaseComponent from '../base';

export default {
    table: {
        value: BaseComponent,
        label: 'Table 表格',
        demos: [
            {
                title: '基本用法',
                desc: '普通的表格',
                source: 'normal'
            },
            {
                title: '无分页',
                desc: '普通表格 - 无分页',
                source: 'noPagination'
            },
            {
                title: '无数据',
                desc: '普通表格 - 无数据',
                source: 'noData'
            },
            {
                title: '带border',
                desc: '普通表格 - 带border',
                source: 'border'
            },
            {
                title: '表头带border',
                desc: '普通表格 - 表头带border',
                source: 'headBordered'
            },
            {
                title: 'loading',
                desc: '',
                source: 'loading'
            },
            {
                title: '分页',
                desc: '普通表格 - 分页器控制（模拟ajax）',
                source: 'pagination'
            },
            {
                title: '可选择',
                desc: '普通表格 - 选择item',
                source: 'item'
            },
            {
                title: '可选择(复杂)',
                desc: '普通表格 - 可选择列表 - rowSelection',
                source: 'rowSelection'
            },
            {
                title: '基础样式',
                desc: '用于自定义样式场景',
                source: 'basic'
            },
            {
                title: '自定义选择',
                desc: '普通表格 - 自定义选项',
                source: 'custom'
            },
            {
                title: '筛选',
                desc: '普通表格 - 筛选',
                source: 'filter'
            },
            {
                title: '筛选(无确认)',
                desc: '普通表格 - 不带确认按钮的筛选',
                source: 'filterNoConfirm'
            },
            {
                title: '排序',
                desc: '普通表格 - 排序',
                source: 'sort'
            },
            {
                title: '行列合并',
                desc: '普通表格 - 表格行列合并',
                source: 'colSpan'
            },
            {
                title: '固定表头',
                desc: '普通表格 - 固定表头',
                source: 'fixHeader'
            },
            {
                title: '固定列',
                desc: '普通表格 - 固定列',
                source: 'fixLeft'
            },
            {
                title: '表头分组',
                desc: '普通表格 - 表头分组',
                source: 'colAndFixed'
            },
            {
                title: '子表',
                desc: '普通表格 - 子母表',
                source: 'parentTable'
            },
            {
                title: '子表展开受控',
                desc: '普通表格 - 受控/非受控 控制表格的子母表展开收起',
                source: 'expandedTable'
            },
            {
                title: '自定义表头',
                desc: '普通表格 - 表头可自定义',
                source: 'customHeader'
            },
            {
                title: '自定义操作',
                desc: '普通表格 - 不使用任何组件库operate，完全自定义',
                source: 'totalCustomHeader'
            },
            {
                title: '可拖拽列宽',
                desc: '普通表格 - 可拖拽列表',
                source: 'draggable'
            },
            {
                title: '类型',
                desc: '普通表格 - 紧凑型 or 宽松型',
                source: 'type'
            },
            {
                title: '树形',
                desc: '普通表格 - 树形表格',
                source: 'treeNode'
            },
            {
                title: '宽度变化',
                desc: '普通表格 - 模拟账户树收起，表格宽度变化，需要forceUpdate的情况(表头完全吸顶的情况)',
                source: 'widthChange'
            },
            {
                title: '固定占行',
                desc: '普通表格 - 固定占行',
                source: 'cellLines'
            },
            {
                title: '标题提示',
                desc: '支持`show`与`hide`，在`desc`内部控制提示展示与隐藏',
                source: 'descTip'
            }
        ],
        apis: [
            {
                apiKey: 'Table',
                title: 'Table'
            },
            {
                apiKey: 'TableColumn',
                title: 'column'
            },
            {
                apiKey: 'TablerowSelection',
                title: 'rowSelection'
            }
        ]
    }
};
