import React from 'react';
import Highlight from 'react-highlight';
import Title from '../../common/title';
import {menu} from '../../config';

export default props => {
    // eslint-disable-next-line react/prop-types
    const id = props.id;
    const {label: title} = menu[id] || {};

    const str1 = 'import {Button} from \'@baidu/one-ui\'';
    const config1 = 'import Button from \'@baidu/one-ui/lib/Button\';';

    const config2 = 'npm install babel-plugin-import --save-dev';
    // eslint-disable-next-line no-template-curly-in-string
    const str = '${name[0].toLowerCase()}${name.slice(1)}';
    const path = '${path}';
    const config3 = `plugins: [
    [
        'import', {
            libraryName: '@baidu/one-ui',
            camel2DashComponentName: false,
            camel2UnderlineComponentName: false,
            customName: name => {
                return \`@baidu/one-ui/lib/components/${str}\`;
            },
            style: path => {
                return false;
            }
        },
        'one-ui'
    ]
],`;
    return (
        <div>
            <Title title={title} />
            <div className="doc">
                <div className="demo-home-page-title">
                    通过
                    {str1}
                    方式会全量加载组件。
                </div>

                <div className="demo-home-page-title">可以通过如下方式进行按需加载：</div>
                <div>
                    <Highlight className="javascript">
                        {config1}
                    </Highlight>
                </div>
                <div>采用以上方式除了组件引用之外，每次都要多写一行对应的样式引入，比较麻烦，可以用相关插件解决这个问题。</div>

                <div className="demo-home-page-title" style={{margin: '20px 0'}}>通过babel配置：</div>
                <div>首先，安装 babel-plugin-import:</div>
                <div>
                    <Highlight className="bash">
                        {config2}
                    </Highlight>
                </div>
                <div>
                    然后，在.babelrc或babel-loader中添加以下配置：
                </div>
                <div>
                    <Highlight className="javascript">
                        {config3}
                    </Highlight>
                </div>

                <div>注意：当前阶段，按需加载不支持按需加载样式，请引全局样式</div>
            </div>
        </div>
    );
};
