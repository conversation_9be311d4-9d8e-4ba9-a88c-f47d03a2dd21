import React from 'react';
import Highlight from 'react-highlight';
import Title from '../../common/title';
import {menu} from '../../config';

export default props => {
    // eslint-disable-next-line react/prop-types
    const id = props.id;
    const {label: title} = menu[id] || {};
    const config1 = `npm install
cd packages/one-ui
npm install
cd ../..
// 退回至根目录启动服务
npm run start
`;

    const config2 = `|-doc
    |-src
        |-components
            |-组件的示例
|-packages
    |-one-ui
        |-src
            |-components
                |-button
                    |-index.jsx
                    |-style.less
                |-index.less
            |-index.js
            |-core
                |-buttonTools.js
                |-index.js
`;
    return (
        <div>
            <Title title={title} />
            <div className="doc">
                <div className="demo-home-page-title">本地开发</div>
                <div>
                    <Highlight className="bash">
                        {config1}
                    </Highlight>
                </div>
                <div className="demo-home-page-title">共建开发</div>
                <div>
                    4.0组件目前基于d20_dev分支进行开发，
                    如果需要共建，可以基于d20_dev分支拉开发分支，
                    开发完成后，进行CR评审，CR评审通过后，合回d20_dev分支后，基于d20_dev统一发版
                </div>

                <div className="demo-home-page-title">组件文件组织</div>
                <div>
                    <Highlight className="javascript">
                        {config2}
                    </Highlight>
                </div>
                <div>
                    doc为文档示例部分
                </div>
                <div>
                    src中components为各个组件的代码部分，
                    core为各个组件所依赖的方法，
                    新写组件后，
                    需要添加至顶层index.js和index.less将组件暴露出去
                </div>
            </div>
        </div>
    );
};
