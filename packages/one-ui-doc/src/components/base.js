import React, {useState} from 'react';
import {menu} from '../config';

import Title from '../common/title';
import Demo from '../common/demo';
import Api from '../common/api';
import {Drawer} from '@baidu/one-ui';
import typeApi from '../type.api';

export default props => {
    const id = props.id;
    const {label: title, desc, desginLink, demos, apis} = menu[id] || {};
    const [visible, setVisible] = useState(false);
    const [fullscreen, setFullscreen] = useState(false);
    const apiSection = apis.map(({title, desc, children, apiKey, CustomContent}, index) => (
        <Api
            key={index}
            title={title}
            desc={desc}
            list={children}
            apiKey={apiKey}
            ApiCustom={CustomContent || null}
        />
    ));
    return (
        <div className={fullscreen ? 'fullscreen' : null}>
            <Title title={title} desc={desc} desginLink={desginLink} />
            {
                demos.map((demo, index) => {
                    const {title, desc, descFlag} = demo;
                    return (
                        <Demo
                            key={`${id}-${title}-${index}`}
                            title={title}
                            desc={desc}
                            source={demo.source}
                            id={id}
                            descFlag={descFlag}
                            setFullscreen={setFullscreen}
                            showApi={setVisible}
                        />
                    );
                })
            }
            <div className="api-header-title" id="apis">API</div>
            {
                fullscreen
                    ? (
                        <Drawer
                            visible={visible}
                            placement="bottom"
                            size="small"
                            height="60vh"
                            onClose={() => setVisible(false)}
                        >
                            {
                                apiSection
                            }
                        </Drawer>
                    )
                    : apiSection
            }
        </div>
    );
};
