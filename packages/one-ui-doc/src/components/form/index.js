import React from 'react';
import BaseComponent from '../base';
import FormCreate from './code/formcreate';
import WrappedComponentRef from './code/wrappedComponentRef';
import ValidateFields from './code/validateFields';
import warn from './code/warn';

export default {
    form: {
        value: BaseComponent,
        label: 'Form 表单',
        demos: [
            {
                title: '综合示例',
                desc: '演示Form.Field属性`tip`, `help`, 异步校验、复杂组件子元素校验、`abstract`错误信息统一收集展示等',
                source: 'formField'
            },
            {
                title: '标签宽度',
                desc: 'CSS变量`--dls-form-label-width`设置标签宽度',
                source: 'labelWidth'
            },
            {
                title: '表单API',
                desc: '可通过`ref`拿到表单实例，并调用表单API(参考form属性)',
                source: 'instance'
            },
            {
                title: '字符长度校验',
                desc: '默认字符长度为实际长度，但业务通常会将中文算作两个字符长度，且Input与TextArea默认使用该规则，所以`type`增加`string-cn`用于解决此场景问题',
                source: 'stringCN'
            },
            {
                title: '标签位置',
                desc: '`top` | `left`',
                source: 'labelPosition'
            },
            {
                title: '横向',
                desc: '水平栏，常用在顶部导航栏中。',
                source: 'normal'
            },
            {
                title: '纵向',
                desc: '普通表单-登陆框，常用于提交一些信息',
                source: 'submit'
            },
            {
                title: '布局',
                desc: '',
                source: 'layout'
            },
            {
                title: '表单项撑满宽度',
                desc: '可使用css变量--dls-form-field-min-width、--dls-form-field-max-width、--dls-form-grid-column-gap进行精细化控制',
                source: 'fillWidth'
            },
            {
                title: '标签',
                desc: '普通表单-常用于提交一些信息',
                source: 'register'
            },
            {
                title: '清空',
                desc: '普通表单-带取消按钮，this.props.form.resetFields使用该方法可以清空表单',
                source: 'cancel'
            },
            {
                title: '时间表单项',
                desc: '普通表单-日期，常用于提交一些日期，时间的表单',
                source: 'date'
            },
            {
                title: '自定义表单项',
                desc: '普通表单-自定义表单，可以自己定义一个组件，放在表单里面，只要该组件遵循以下的约定：'
                    + '提供受控属性 value 或其它与 valuePropName(valuePropName: {checked: true}) 的值同名的属性。'
                    + '提供 onChange 事件或 trigger 的值同名的事件。',
                source: 'custom'
            },
            {
                title: '数据受控',
                desc: '普通表单-数据, 通过使用 onFieldsChange 与 mapPropsToFields，'
                    + '可以把表单的数据存储到上层组件或者 Redux、dva 中,mapPropsToFields '
                    + '里面返回的表单域数据必须使用 Form.createFormField 包装',
                source: 'data'
            },
            {
                title: '原始表单',
                desc: '普通表单-不使用create,使用 Form.create 处理后的表单具有自动收集数据并校验的功能，但如果不需要这个功能，'
                    + '或者默认的行为无法满足业务需求，可以选择不使用 Form.create 并自行处理数据。',
                source: 'rawForm'
            },
            {
                title: '自定义校验',
                // eslint-disable-next-line max-len
                desc: '普通表单-不使用create，自定义校验,我们提供了 validateStatus help 等属性，'
                    + '你可以不需要使用 Form.create 和 getFieldDecorator，自己定义校验的时机和内容。'
                    + 'validateStatus: 校验状态，可选 "success", "warning", "error", "validating",  help：设置校验文案。',
                source: 'customValidation'
            },
            {
                title: '设置表单项Value',
                desc: '普通表单-setFieldsValue, 使用 setFieldsValue 来动态设置其他控件的值。',
                source: 'setFieldsValue'
            },
            {
                title: '动态校验规则',
                desc: '普通表单-dynamic, 根据不同情况执行不同的校验规则。切换后重置报错信息。',
                source: 'dynamic'
            },
            {
                title: '多种表单项校验',
                desc: '普通表单-other, 以上演示没有出现的表单控件对应的校验演示。',
                source: 'other'
            }
        ],
        apis: [
            {
                apiKey: 'Form',
                title: 'Form'
            },
            {
                apiKey: 'FormField',
                desc: 'v4.14.0，代替Form.Item与getFieldDecorator，并提供更完善的校验逻辑',
                title: 'Form.Field'
            },
            {
                apiKey: 'FormFieldGroup',
                desc: '',
                title: 'Form.FieldGroup'
            },
            {
                apiKey: 'FormCreate',
                title: 'Form.create',
                desc: '废弃，Form.create(options) 使用方式如下：',
                CustomContent: FormCreate
            },
            {
                apiKey: 'WrappedComponentRef',
                title: 'wrappedComponentRef',
                desc: '经过 Form.create 之后如果要拿到 ref，可以使用 rc-form 提供的 wrappedComponentRef',
                CustomContent: WrappedComponentRef
            },
            {
                apiKey: 'PropsForm',
                title: 'form属性',
                desc: '经过 Form.create 包装的组件将会自带 this.props.form 属性，this.props.form 提供的 API 如下：'
                    + '注意：使用 getFieldsValue getFieldValue setFieldsValue 等时，'
                    + '应确保对应的 field 已经用 getFieldDecorator 注册过了。'
            },
            {
                apiKey: 'validateFields',
                title: 'validateFields/validateFieldsAndScroll',
                CustomContent: ValidateFields
            },
            {
                apiKey: 'getFieldDecoratorWarn',
                title: 'form.getFieldDecorator(id, options)',
                CustomContent: warn
            },
            {
                apiKey: 'getFieldDecorator',
                title: 'getFieldDecorator(id, options)'
            },
            {
                apiKey: 'FormItem',
                title: 'Form.Item',
                desc: '废弃，注意：一个 Form.Item 建议只放一个被 getFieldDecorator 装饰过的 child'
            },
            {
                apiKey: 'validation',
                title: 'validation',
                desc: (
                    <span>
                        校验规则，更多校验访问
                        <a href="https://github.com/yiminghe/async-validator">
                            async-validator
                        </a>
                    </span>
                )
            },
            {
                apiKey: 'CSS',
                title: 'CSS变量',
                children: [
                    {
                        param: '--dls-form-field-min-width',
                        desc: '最小表单项宽度',
                        type: '<length> | <percentage>'
                    },
                    {
                        param: '--dls-form-field-max-width',
                        desc: '最大表单项宽度',
                        type: '<length> | <percentage>'
                    },
                    {
                        param: '--dls-form-grid-column-gap',
                        desc: '网格布局列间距',
                        type: '<length> | <percentage>'
                    },
                    {
                        param: '--dls-form-label-width',
                        desc: '标签宽度',
                        type: '<length> | <percentage>'
                    },
                    {
                        param: '--dls-form-grid-column-width',
                        desc: '网格布局列宽度',
                        type: '<length> | <percentage>'
                    }
                ]
            }
        ]
    }
};
