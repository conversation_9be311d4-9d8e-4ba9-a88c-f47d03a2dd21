import React, {PureComponent} from 'react';

export default class Demo extends PureComponent {
    render() {
        return (
            <span>
                <div>经过 getFieldDecorator 包装的控件，表单控件会自动添加 value（或 valuePropName 指定的其他属性） onChange（或 trigger 指定的其他属性），数据同步将被 Form 接管，这会导致以下结果：</div>
                <div>你不再需要也不应该用 onChange 来做同步，但还是可以继续监听 onChange 等事件。</div>
                <div>你不能用控件的 value defaultValue 等属性来设置表单域的值，默认值可以用 getFieldDecorator 里的 initialValue。</div>
                <div>你不应该用 setState，可以使用 this.props.form.setFieldsValue 来动态改变表单值。</div>
            </span>
        );
    }
}
