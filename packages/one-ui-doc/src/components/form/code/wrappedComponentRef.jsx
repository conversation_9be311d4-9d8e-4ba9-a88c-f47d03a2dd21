import Highlight from 'react-highlight';
import React, {PureComponent} from 'react';

export default class Demo extends PureComponent {
    render() {
        const config = `class CustomizedForm extends React.Component { ... }
// use wrappedComponentRef
const EnhancedForm =  Form.create()(CustomizedForm);
<EnhancedForm wrappedComponentRef={(form) => this.form = form} />
this.form // => The instance of CustomizedForm
`;
        return (
            <Highlight className="javascript">
                {config}
            </Highlight>
        );
    }
}
