import Highlight from 'react-highlight';
import React, {PureComponent} from 'react';

export default class Demo extends PureComponent {
    render() {
        const config = `
const {
    form: { validateFields },
} = this.props;
validateFields((errors, values) => {
    // ...
});
validateFields(['field1', 'field2'], (errors, values) => {
    // ...
});
validateFields(['field1', 'field2'], options, (errors, values) => {
    // ...
});
`;
        return (
            <Highlight className="javascript">
                {config}
            </Highlight>
        );
    }
}
