import Highlight from 'react-highlight';
import React, {PureComponent} from 'react';

export default class Demo extends PureComponent {
    render() {
        const config = `
class CustomizedForm extends React.Component {}
CustomizedForm = Form.create({})(CustomizedForm);
// options的配置如下：
`;
        return (
            <Highlight className="javascript">
                {config}
            </Highlight>
        );
    }
}
