import React from 'react';

export default {
    Form: [
        {
            param: 'hideRequiredMark',
            type: 'Boolean',
            desc: '隐藏所有表单项的必选标记',
            option: '',
            default: 'false'
        },
        {
            param: 'labelAlign',
            type: 'left | right',
            desc: 'label 标签的文本对齐方式',
            option: '',
            default: 'right'
        },
        {
            param: 'labelCol',
            type: 'object',
            desc: 'label 标签布局，同 <Col> 组件，设置 span offset 值，如 {span: 3, offset: 12} 或 sm: {span: 3, offset: 12}',
            option: '',
            default: ''
        },
        {
            param: 'layout',
            type: 'string',
            desc: '表单布局',
            option: 'horizontal | inline',
            default: 'horizontal'
        },
        {
            param: 'onSubmit',
            type: 'Func(e)',
            desc: '数据验证成功后回调事件',
            option: '',
            default: ''
        },
        {
            param: 'wrapperCol',
            type: 'object',
            desc: '需要为输入控件设置布局样式时，使用该属性，用法同 labelCol',
            option: '',
            default: ''
        },
        {
            param: 'colon',
            type: 'boolean',
            desc: '配置 Form.Item 的 colon(冒号) 的默认值',
            option: '',
            default: 'true'
        },
        {
            param: 'size',
            type: '',
            desc: '表单的size',
            option: 'small | medium | large',
            default: 'medium'
        }
    ],
    Formcreate: [{
        param: 'mapPropsToFields',
        type: '(props) => ({ [fieldName]: FormField {value}})',
        desc: (
            <span>
                把父组件的属性映射到表单项上（如：把 Redux store 中的值读出），
                需要对返回值中的表单域数据用
                <code>
                    {
                        `Form.createFormField({
                            value: any,
                            errors?: Array<{message: string}>
                        })`
                    }
                </code>
                标记，注意表单项将变成受控组件
            </span>
        ),
        option: '',
        default: ''
    }, {
        param: 'name',
        type: '',
        desc: '设置表单域内字段 id 的前缀',
        option: '',
        default: ''
    }, {
        param: 'validateMessages',
        type: '',
        desc: (
            <span>
                默认校验信息，可用于把默认错误信息改为中文等，格式与
                <a href="https://github.com/yiminghe/async-validator/blob/master/src/messages.js">newMessages</a>
                返回值一致
            </span>
        ),
        option: 'Object {[nested.path]: String}',
        default: ''
    }, {
        param: 'onFieldsChange',
        type: '',
        desc: '当 Form.Item 子节点的值（包括 error）发生改变时触发，可以把对应的值转存到 Redux store',
        option: 'Function(props, changedFields, allFields)',
        default: ''
    }, {
        param: 'onValuesChange',
        type: '',
        desc: '任一表单域的值发生改变时的回调',
        option: '(props, changedValues, allValues) => void',
        default: ''
    }, {
        param: 'size',
        type: '',
        desc: '表单下所有子组件的size',
        option: 'small | medium | large',
        default: 'medium'
    }],
    WrappedComponentRef: [],
    PropsForm: [{
        param: 'getFieldDecorator',
        type: '',
        desc: '用于和表单进行双向绑定，详见下方描述',
        option: '',
        default: ''
    }, {
        param: 'getFieldError',
        type: 'Function(name)',
        desc: '获取某个输入控件的 Error',
        option: '',
        default: ''
    }, {
        param: 'getFieldsError',
        type: 'Function([names: string[]])',
        desc: '获取一组输入控件的 Error ，如不传入参数，则获取全部组件的 Error',
        option: '',
        default: ''
    }, {
        param: 'getFieldsValue',
        type: 'Function([fieldNames: string[]])',
        desc: '获取一组输入控件的值，如不传入参数，则获取全部组件的值',
        option: '',
        default: ''
    }, {
        param: 'getFieldValue',
        type: 'Function(fieldName: string)',
        desc: '获取一个输入控件的值',
        option: '',
        default: ''
    }, {
        param: 'isFieldValidating',
        type: 'Function(name)',
        desc: '判断一个输入控件是否在校验状态',
        option: '',
        default: ''
    }, {
        param: 'resetFields',
        type: 'Function([names: string[]])',
        desc: '重置一组输入控件的值（为 initialValue）与状态，如不传入参数，则重置所有组件',
        option: '',
        default: ''
    }, {
        param: 'setFields',
        type: '({[fieldName]: {value: any, errors: [Error]}}) => void',
        desc: '设置一组输入控件的值与错误状态',
        option: '',
        default: ''
    }, {
        param: 'setFieldsValue',
        type: '({[fieldName]: value}, callback: Function) => void',
        desc: '设置一组输入控件的值（注意：不要在 componentWillReceiveProps 内使用，否则会导致死循环',
        option: '',
        default: ''
    }, {
        param: 'setFieldsError',
        type: '({[fieldName]: Array<ReactNode>}, callback: Function) => void',
        desc: '设置错误状态提示',
        option: '',
        default: ''
    }, {
        param: 'validateFields',
        type: '([fieldNames: string[]], [options: object], callback(errors, values)) => Promise',
        desc: '校验并获取一组输入域的值与 Error，若 fieldNames 参数为空，则校验全部组件。Promise形式: then(values); catch({errors, values})',
        option: '',
        default: ''
    }, {
        param: 'validateFieldsAndScroll',
        type: '参考validateFields',
        desc: '与 validateFields 相似，但校验完后，如果校验不通过的菜单域不在可见范围内，则自动滚动进可见范围',
        option: '',
        default: ''
    }],
    validateFields: [{
        param: 'options.first',
        type: 'boolean',
        desc: '若为 true，则每一表单域的都会在碰到第一个失败了的校验规则后停止校验',
        option: '',
        default: 'false'
    }, {
        param: 'options.firstFields',
        type: 'String[]',
        desc: '指定表单域会在碰到第一个失败了的校验规则后停止校验',
        option: '',
        default: 'false'
    }, {
        param: 'options.force',
        type: 'boolean',
        desc: '对已经校验过的表单域，在 validateTrigger 再次被触发时是否再次校验',
        option: '',
        default: 'false'
    }, {
        param: 'options.scroll',
        type: 'Object',
        desc: (
            <span>
                定义 validateFieldsAndScroll 的滚动行为,详细配置见
                <a href="https://github.com/yiminghe/dom-scroll-into-view#function-parameter">dom-scroll-into-view config</a>
            </span>
        ),
        option: '',
        default: ''
    }],
    getFieldDecoratorWarn: [],
    getFieldDecorator: [{
        param: 'id',
        type: 'string',
        desc: '必填输入控件唯一标志',
        option: '',
        default: ''
    }, {
        param: 'options.getValueFromEvent',
        type: 'function(..args)',
        desc: '可以把 onChange 的参数（如 event）转化为控件的值',
        option: '',
        default: ''
    }, {
        param: 'options.initialValue',
        type: 'function(..args)',
        desc: '子节点的初始值，类型、可选值均由子节点决定',
        option: '',
        default: ''
    }, {
        param: 'options.normalize',
        type: 'function(value, prevValue, allValues)',
        desc: '转换 value 给控件',
        option: '',
        default: ''
    }, {
        param: 'options.rules',
        type: 'object[]',
        desc: '校验规则，参考下方文档',
        option: '',
        default: ''
    }, {
        param: 'options.trigger',
        type: 'string',
        desc: '子节点触发的函数名称',
        option: '',
        default: 'onChange'
    }, {
        param: 'options.valuePropName',
        type: 'string',
        desc: '子节点的值的属性，如 Switch 的是 "checked"',
        option: '',
        default: 'value'
    }, {
        param: 'options.preserve',
        type: 'string',
        desc: '即便字段不再使用，也保留该字段的值',
        option: '',
        default: ''
    }, {
        param: 'options.validateFirst',
        type: 'string',
        desc: '当某一规则校验不通过时，是否停止剩下的规则的校验',
        option: '',
        default: ''
    }, {
        param: 'options.validateTrigger',
        type: 'string',
        desc: '校验子节点值的时机',
        option: '',
        default: 'onChange'
    }],
    formItem: [{
        param: 'colon',
        type: 'boolean',
        desc: '配合 label 属性使用，表示是否显示 label 后面的冒号',
        option: '',
        default: 'true'
    }, {
        param: 'extra',
        type: 'string|ReactNode',
        desc: '额外的提示信息，和 help 类似，当需要错误信息和提示文案同时出现时，可以使用这个。',
        option: '',
        default: ''
    }, {
        param: 'help',
        type: 'string|ReactNode',
        desc: '提示信息，如不设置，则会根据校验规则自动生成',
        option: '',
        default: ''
    }, {
        param: 'label',
        type: 'string|ReactNode',
        desc: 'label 标签的文本',
        option: '',
        default: ''
    }, {
        param: 'required',
        type: 'boolean',
        desc: '是否必填，如不设置，则会根据校验规则自动生成',
        option: '',
        default: 'false'
    }, {
        param: 'validateStatus',
        type: 'string',
        desc: '校验状态，如不设置，则会根据校验规则自动生成，可选：success warning error validating',
        option: '',
        default: ''
    }, {
        param: 'labelCol',
        type: 'object',
        desc: '需要为输入控件设置布局样式时，使用该属性，用法同 labelCol。你可以通过 Form 的 wrapperCol 进行统一设置。当和 Form 同时设置时，以 FormItem 为准。',
        option: '',
        default: ''
    }, {
        param: 'extralPlacemenet',
        type: 'string',
        desc: '报错提示的位置',
        option: 'left | center | right',
        default: 'left'
    }],
    validation: [
        {
            param: 'enum',
            type: 'string',
            desc: '枚举类型',
            option: '',
            default: ''
        },
        {
            param: 'len',
            type: 'number',
            desc: '字段长度',
            option: '',
            default: ''
        },
        {
            param: 'max',
            type: 'number',
            desc: '最大长度',
            option: '',
            default: ''
        },
        {
            param: 'min',
            type: 'number',
            desc: '最小长度',
            option: '',
            default: ''
        },
        {
            param: 'message',
            type: 'string|ReactNode',
            desc: '校验文案',
            option: '',
            default: ''
        },
        {
            param: 'pattern',
            type: 'RegExp',
            desc: '正则表达式校验',
            option: '',
            default: ''
        },
        {
            param: 'required',
            type: 'boolean',
            desc: '是否必选',
            option: '',
            default: 'false'
        },
        {
            param: 'transform',
            type: 'function(value) => transformedValue',
            desc: '校验前转换字段值',
            option: '',
            default: ''
        },
        {
            param: 'type',
            type: 'function(value) => transformedValue',
            desc: (
                <span>
                    内建校验类型
                    <a href="https://github.com/yiminghe/async-validator#type">可选项</a>
                </span>
            ),
            option: '',
            default: ''
        },
        {
            param: 'validator',
            type: 'function(rule, value, callback)',
            desc: '自定义校验（注意，callback 必须被调用）',
            option: '',
            default: ''
        },
        {
            param: 'whitespace',
            type: 'boolean',
            desc: '必选时，空格是否会被视为错误',
            option: '',
            default: 'false'
        }
    ]
};
