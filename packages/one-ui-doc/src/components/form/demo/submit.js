import React, {PureComponent} from 'react';
import {Form, Input, Button, Checkbox, IconSvg} from '@baidu/one-ui';

class NormalLoginForm extends PureComponent {
    handleSubmit = e => {
        e.preventDefault();
        // eslint-disable-next-line react/prop-types
        this.props.form.validateFields((err, values) => {
            if (!err) {
                console.log('Received values of form: ', values);
            }
        });
    };

    render() {
        // eslint-disable-next-line react/prop-types
        const {getFieldDecorator} = this.props.form;
        return (
            <Form onSubmit={this.handleSubmit} className="login-form">
                <Form.Item>
                    {getFieldDecorator('username', {
                        rules: [
                            {required: true, message: 'Please input your username!'},
                            {required: true, message: 'Please input your username 2!'},
                            {required: true, message: 'Please input your username 3!'}
                        ]
                    })(
                        <Input
                            prefix={<IconSvg type="home" />}
                            placeholder="Username"
                        />,
                    )}
                </Form.Item>
                <Form.Item>
                    {getFieldDecorator('password', {
                        rules: [{required: true, message: 'Please input your Password!'}]
                    })(
                        <Input
                            prefix={<IconSvg type="file" />}
                            type="password"
                            placeholder="Password"
                        />,
                    )}
                </Form.Item>
                <Form.Item>
                    {getFieldDecorator('remember', {
                        valuePropName: 'checked',
                        initialValue: true
                    })(<Checkbox>Remember me</Checkbox>)}
                </Form.Item>
                <Form.Item>
                    <Button type="primary" htmlType="submit" className="login-form-button">
                        Log in
                    </Button>
                </Form.Item>
            </Form>
        );
    }
}

const DemoForm = Form.create({name: 'normal_login'})(NormalLoginForm);
export default () => <DemoForm />;
