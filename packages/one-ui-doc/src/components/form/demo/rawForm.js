import React, {PureComponent} from 'react';
import {Form, NumberInput} from '@baidu/one-ui';

function validatePrimeNumber(number) {
    if (number === 11) {
        return {
            validateStatus: 'success',
            errorMsg: null
        };
    }
    return {
        validateStatus: 'error',
        errorMsg: 'The prime between 8 and 12 is 11!'
    };
}

export default class RawForm extends PureComponent {
    state = {
        number: {
            value: 11
        }
    };

    handleNumberChange = e => {
        const value = +e.target.value;
        this.setState({
            number: {
                ...validatePrimeNumber(value),
                value
            }
        });
    };

    render() {
        const formItemLayout = {
            labelCol: {span: 7},
            wrapperCol: {span: 12}
        };
        const {number} = this.state;
        const tips = 'A prime is a natural number greater than 1 that has no positive divisors other than 1 and itself.';
        return (
            <Form>
                <Form.Item
                    {...formItemLayout}
                    label="Prime between 8 & 12"
                    validateStatus={number.validateStatus}
                    help={number.errorMsg || tips}
                >
                    <NumberInput min={8} max={12} value={number.value} onChange={this.handleNumberChange} />
                </Form.Item>
            </Form>
        );
    }
}
