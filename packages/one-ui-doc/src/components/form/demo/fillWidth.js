import React, {useState} from 'react';
import {
    Form,
    Input,
    Button,
    NumberInput,
    Radio,
    Switch,
    Select
} from '@baidu/one-ui';

export default () => {

    const [loading, setLoading] = useState(false);
    const onSubmit = () => setLoading(true);
    const onFinish = values => (console.log(values), setLoading(false));
    const onFinishFailed = (errors, values) => (console.log(errors, values), setLoading(false));
    const [layout, setLayout] = useState('inline');
    const [fillWidth, setFillWidth] = useState(true);
    const [labelPosition, setLabelPosition] = useState('top');
    const [minWidth, setMinWidth] = useState(304);
    const [maxWidth, setMaxWidth] = useState(354);
    const [gap, setGap] = useState(12);

    const isGridLayout = layout === 'grid';
    const widthLimitStyle = {'--dls-form-label-width': '6em'};
    if (minWidth) {
        widthLimitStyle['--dls-form-field-min-width'] = `${minWidth}px`;
    }
    if (!isGridLayout && maxWidth) {
        widthLimitStyle['--dls-form-field-max-width'] = `${maxWidth}px`;
    }
    if (isGridLayout && gap) {
        widthLimitStyle['--dls-form-grid-column-gap'] = `${gap}px`;
    }

    return (
        <>
            <div className="demo-controls">
                表单项撑满宽度：
                <Switch
                    size="small"
                    checked={fillWidth}
                    onChange={checked => setFillWidth(checked)}
                />
                方向：
                <Radio.Group
                    size="small"
                    type="strong"
                    options={['default', 'inline', 'grid']}
                    value={layout}
                    onChange={e => setLayout(e.target.value)}
                />
                标签位置：
                <Radio.Group
                    size="small"
                    type="strong"
                    options={['left', 'top']}
                    value={labelPosition}
                    onChange={e => setLabelPosition(e.target.value)}
                />
            </div>
            {
                fillWidth
                && (
                    <div className="demo-controls">
                        表单项最小宽度：
                        <NumberInput size="small" value={minWidth} onChange={e => setMinWidth(e.target.value)} />
                        {
                            isGridLayout
                                ? (
                                    <>
                                        网格布局列间距：
                                        <NumberInput size="small" value={gap} onChange={e => setGap(e.target.value)} />
                                    </>
                                )
                                : (
                                    <>
                                        表单项最大宽度：
                                        <NumberInput
                                            size="small"
                                            value={maxWidth}
                                            onChange={e => setMaxWidth(e.target.value)}
                                        />
                                    </>
                                )
                        }
                    </div>
                )
            }
            <br />
            <br />
            <Form
                onSubmit={onSubmit}
                onFinish={onFinish}
                onFinishFailed={onFinishFailed}
                scrollToFirstError
                density="compact"
                layout={layout}
                labelPosition={labelPosition}
                fillWidth={fillWidth}
                style={widthLimitStyle}
            >
                <Form.Field
                    label="用户名"
                    tip="提示：用户名即登录账号"
                    name="account"
                    rules={[{required: true, message: '请输入用户名'}]}
                >
                    <Input placeholder="用户名" width={fillWidth ? undefined : 160} />
                </Form.Field>
                <Form.Field
                    label="密码"
                    name="password"
                    rules={[{required: true, message: '请输入密码'}]}
                >
                    <Input placeholder="密码" width={fillWidth ? undefined : 160} />
                </Form.Field>
                <Form.Field
                    label="职业"
                    name="job"
                    rules={[{required: true, message: '请选择职业'}]}
                >
                    <Select
                        showSearch
                        placeholder="请选择职业"
                        width={fillWidth ? undefined : 160}
                    >
                        <Select.Option value="FE">FE</Select.Option>
                        <Select.Option value="UE">UE</Select.Option>
                        <Select.Option value="RD">RD</Select.Option>
                        <Select.Option value="PM">PM</Select.Option>
                    </Select>
                </Form.Field>
                <Form.Field
                    label="学历"
                    name="education"
                    rules={[ {required: true, message: '请选择学历'}]}
                >
                    <Select
                        placeholder="请选择学历"
                        width={fillWidth ? undefined : 160}
                    >
                        <Select.Option value="class1">高中及以下</Select.Option>
                        <Select.Option value="class2">本科</Select.Option>
                        <Select.Option value="class3">硕士</Select.Option>
                        <Select.Option value="class4">博士</Select.Option>
                    </Select>
                </Form.Field>
                <Form.Field
                    label="性别"
                    name="gender"
                    rules={[{required: true, message: '请选择性别'}]}
                >
                    <Radio.Group>
                        <Radio.Button value="male">男</Radio.Button>
                        <Radio.Button value="female">女</Radio.Button>
                    </Radio.Group>
                </Form.Field>
                {layout === 'default'
                    && (
                        <Form.Field
                            label="地址"
                        >
                            <Form.FieldGroup
                                style={{
                                    padding: 16,
                                    backgroundColor: '#F6F7FA',
                                    borderRadius: 4,
                                    '--dls-form-label-width': '4em'
                                }}
                            >
                                <Form.Field
                                    label="城市"
                                    name="city"
                                    rules={[{required: true, message: '请输入城市'}]}
                                >
                                    <Input placeholder="city" width={fillWidth ? undefined : 160} />
                                </Form.Field>
                                <Form.Field
                                    label="街道"
                                    name="street"
                                    rules={[{required: true, message: '请输入街道'}]}
                                >
                                    <Input placeholder="street" width={fillWidth ? undefined : 160} />
                                </Form.Field>
                            </Form.FieldGroup>
                        </Form.Field>
                    )
                }
                <Form.Field display="standalone" actions label={layout === 'default' ? '' : null}>
                    <Button
                        type="primary"
                        htmlType="submit"
                        loading={loading}
                    >
                        提交
                    </Button>
                </Form.Field>
            </Form>
        </>
    );
};
