import React, {PureComponent} from 'react';
import {
    Form,
    Input,
    Tooltip,
    IconSvg,
    Cascader,
    Select,
    Grid,
    Checkbox,
    Button
} from '@baidu/one-ui';

const {Option} = Select;

const residences = [
    {
        value: 'zhejiang',
        label: 'Zhejiang',
        children: [
            {
                value: 'hangzhou',
                label: 'Hangzhou',
                children: [
                    {
                        value: 'xihu',
                        label: 'West Lake'
                    }
                ]
            }
        ]
    },
    {
        value: 'jiangsu',
        label: 'Jiangsu',
        children: [
            {
                value: 'nanjing',
                label: 'Nanjing',
                children: [
                    {
                        value: 'zhonghuamen',
                        label: 'Zhong Hua Men'
                    }
                ]
            }
        ]
    }
];

class RegistrationForm extends PureComponent {
    state = {
        confirmDirty: false
    };

    handleSubmit = e => {
        e.preventDefault();
        // eslint-disable-next-line react/prop-types
        this.props.form
            .validateFieldsAndScroll((errors, values) => {
                if (!errors) {
                    console.log('callback values of form: ', values);
                }
                else {
                    console.log('callback errors of form: ', errors, values);
                }
            })
            .then(values => console.log('then', values))
            .catch(({errors, values}) => console.log('catch', errors, values));
    };

    handleConfirmBlur = e => {
        const {value} = e.target;
        // eslint-disable-next-line react/no-access-state-in-setstate
        this.setState({confirmDirty: this.state.confirmDirty || !!value});
    };

    compareToFirstPassword = (rule, value, callback) => {
        // eslint-disable-next-line react/prop-types
        const {form} = this.props;
        if (value && value !== form.getFieldValue('password')) {
            callback('Two passwords that you enter is inconsistent!');
        } else {
            callback();
        }
    };

    validateToNextPassword = (rule, value, callback) => {
        // eslint-disable-next-line react/prop-types
        const {form} = this.props;
        if (value && this.state.confirmDirty) {
            form.validateFields(['confirm'], {force: true});
        }
        callback();
    };

    render() {
        // eslint-disable-next-line react/prop-types
        const {getFieldDecorator} = this.props.form;

        const formItemLayout = {
            labelCol: {
                xs: {span: 24},
                sm: {span: 8}
            },
            wrapperCol: {
                xs: {span: 24},
                sm: {span: 16}
            }
        };
        const tailFormItemLayout = {
            wrapperCol: {
                xs: {
                    span: 24,
                    offset: 0
                },
                sm: {
                    span: 16,
                    offset: 8
                }
            }
        };
        const prefixSelector = getFieldDecorator('prefix', {
            initialValue: '86'
        })(
            <Select
                showSearch
                style={{width: 70}}
            >
                <Option value="86">+86</Option>
                <Option value="87">+87</Option>
            </Select>,
        );

        return (
            <Form {...formItemLayout} onSubmit={this.handleSubmit}>
                <Form.Item label="E-mail">
                    {getFieldDecorator('email', {
                        rules: [
                            {
                                type: 'email',
                                message: 'The input is not valid E-mail!'
                            },
                            {
                                required: true,
                                message: 'Please input your E-mail!'
                            }
                        ]
                    })(<Input />)}
                </Form.Item>
                <Form.Item label="Password">
                    {getFieldDecorator('password', {
                        rules: [
                            {
                                required: true,
                                message: 'Please input your password!'
                            },
                            {
                                validator: this.validateToNextPassword
                            }
                        ]
                    })(<Input type="password" />)}
                </Form.Item>
                <Form.Item label="Confirm Password">
                    {getFieldDecorator('confirm', {
                        rules: [
                            {
                                required: true,
                                message: 'Please confirm your password!'
                            },
                            {
                                validator: this.compareToFirstPassword
                            }
                        ]
                    })(<Input type="password" onBlur={this.handleConfirmBlur} />)}
                </Form.Item>
                <Form.Item
                    label={(
                        <span>
                            Nickname&nbsp;
                            <Tooltip title="What do you want others to call you?">
                                <IconSvg type="IconQuestionCircle" />
                            </Tooltip>
                        </span>
                    )}
                >
                    {getFieldDecorator('nickname', {
                        rules: [{required: true, message: 'Please input your nickname!', whitespace: true}]
                    })(<Input />)}
                </Form.Item>
                <Form.Item label="Habitual Residence">
                    {getFieldDecorator('residence', {
                        initialValue: ['zhejiang', 'hangzhou', 'xihu'],
                        rules: [
                            {type: 'array', required: true, message: 'Please select your habitual residence!'}
                        ]
                    })(<Cascader options={residences} style={{width: 300}} />)}
                </Form.Item>
                <Form.Item label="Phone Number">
                    {getFieldDecorator('phone', {
                        rules: [{required: true, message: 'Please input your phone number!'}]
                    })(<Input addonBefore={prefixSelector} />)}
                </Form.Item>
                <Form.Item label="Captcha" extra="We must make sure that your are a human.">
                    <Grid.Row gutter={8}>
                        <Grid.Col span={12}>
                            {getFieldDecorator('captcha', {
                                rules: [{required: true, message: 'Please input the captcha you got!'}]
                            })(<Input />)}
                        </Grid.Col>
                        <Grid.Col span={12}>
                            <Button>Get captcha</Button>
                        </Grid.Col>
                    </Grid.Row>
                </Form.Item>
                <Form.Item {...tailFormItemLayout}>
                    {getFieldDecorator('agreement', {
                        valuePropName: 'checked'
                    })(
                        <Checkbox>
                            I have read the
                            <a href="">agreement</a>
                        </Checkbox>,
                    )}
                </Form.Item>
                <Form.Item {...tailFormItemLayout}>
                    <Button type="primary" htmlType="submit">
                        Register
                    </Button>
                </Form.Item>
            </Form>
        );
    }
}

const DemoForm = Form.create({name: 'register'})(RegistrationForm);
export default () => <DemoForm />;
