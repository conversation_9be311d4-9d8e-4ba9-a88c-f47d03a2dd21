import React, {useRef, useState} from 'react';
import {Form, Input, Button, Switch} from '@baidu/one-ui';

export default function DynamicValidation() {
    const formRef = useRef(null);
    const onFinish = values => console.log(values);
    const onFinishFailed = (errors, values) => console.log(errors, values);
    const [required, setRequired] = useState(true);

    return (
        <>
            是否必填：<Switch onChange={setRequired} checked={required} />
            <br />
            <br />
            <br />
            <Form
                ref={formRef}
                onFinish={onFinish}
                onFinishFailed={onFinishFailed}
                scrollToFirstError
                style={{'--dls-form-label-width': '6em'}}
            >
                <Form.Field
                    label="用户名"
                    tip="最少5个字符、最长10个字符"
                    help="建议不少于5个字符"
                    helpPosition="top"
                    name="account"
                    rules={required ? [{required: true, message: '请输入用户名'}] : undefined}
                >
                    <Input placeholder="Name" />
                </Form.Field>
                <Form.Field actions label="">
                    <Button
                        type="primary"
                        htmlType="submit"
                    >
                        提交
                    </Button>
                    <Button
                        htmlType="reset"
                        style={{marginLeft: 8}}
                    >
                        重置
                    </Button>
                </Form.Field>
            </Form>
        </>
    );
}