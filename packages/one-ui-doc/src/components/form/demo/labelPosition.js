import React, {useState} from 'react';
import {Form, Input, Button, NumberInput} from '@baidu/one-ui';

export default () => {

    const [loading, setLoading] = useState(false);
    const onSubmit = () => setLoading(true);
    const onFinish = values => (console.log(values), setLoading(false));
    const onFinishFailed = (errors, values) => (console.log(errors, values), setLoading(false));

    return (
        <Form
            onSubmit={onSubmit}
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            scrollToFirstError
            labelPosition="top"
        >
            <Form.Field
                label="用户名"
                tip="最少5个字符、最长10个字符"
                help="建议不少于5个字符"
                helpPosition="top"
                name="account"
                rules={[
                    {required: true, message: '请输入用户名'},
                    {min: 5, message: '建议不少于5个字符', invalidType: 'warning'},
                    {max: 10, message: '最长10个字符'}
                ]}
            >
                <Input placeholder="Name" />
            </Form.Field>
            <Form.Field
                label="年龄"
                help="周岁"
                helpPosition="side"
                name="age"
                initialValue="18"
                rules={[
                    {required: true, message: '请输入年龄'},
                    {pattern: /\d+/, message: '请输入数字'}
                ]}
            >
                <NumberInput placeholder="Age" min={18} max={120} showErrorMessage={false} />
            </Form.Field>
            <Form.Field actions>
                <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                >
                    提交
                </Button>
                <Button
                    htmlType="reset"
                    disabled={loading}
                    style={{marginLeft: 8}}
                >
                    重置
                </Button>
            </Form.Field>
        </Form>
    );
}
