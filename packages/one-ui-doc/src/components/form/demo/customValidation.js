import React, {PureComponent} from 'react';
import {Form, Input, Select, Cascader, NumberInput, DatePicker} from '@baidu/one-ui';

const {Option} = Select;

const formItemLayout = {
    labelCol: {
        xs: {span: 24},
        sm: {span: 5}
    },
    wrapperCol: {
        xs: {span: 24},
        sm: {span: 12}
    }
};

export default class Demo extends PureComponent {
    render() {
        return (
            <Form {...formItemLayout}>
                <Form.Item
                    label="Fail"
                    validateStatus="error"
                    help="Should be combination of numbers & alphabets"
                >
                    <Input placeholder="unavailable choice" id="error" />
                </Form.Item>
                <Form.Item label="Warning" validateStatus="warning">
                    <Input placeholder="Warning" id="warning" />
                </Form.Item>
                <Form.Item
                    label="Validating"
                    validateStatus="validating"
                    help="The information is being validated..."
                >
                    <Input placeholder="I'm the content is being validated" id="validating" />
                </Form.Item>
                <Form.Item label="Success" validateStatus="success">
                    <Input placeholder="I'm the content" id="success" />
                </Form.Item>
                <Form.Item label="Warning" validateStatus="warning">
                    <Input placeholder="Warning" id="warning2" />
                </Form.Item>
                <Form.Item
                    label="Fail"
                    validateStatus="error"
                    help="Should be combination of numbers & alphabets"
                >
                    <Input placeholder="unavailable choice" id="error2" />
                </Form.Item>
                <Form.Item label="Error" validateStatus="error">
                    <DatePicker />
                </Form.Item>
                {/* <Form.Item label="Warning" validateStatus="warning">
                    <TimePicker />
                </Form.Item> */}
                <Form.Item label="Error" validateStatus="error">
                    <Select
                        defaultValue="1"
                        showSearch
                    >
                        <Option value="1">Option 1</Option>
                        <Option value="2">Option 2</Option>
                        <Option value="3">Option 3</Option>
                    </Select>
                </Form.Item>
                <Form.Item
                    label="Error"
                    validateStatus="error"
                    help="The information is error..."
                >
                    <Cascader defaultValue={['1']} options={[]} />
                </Form.Item>
                <Form.Item label="Error" validateStatus="error">
                    <NumberInput value={1.1} />
                </Form.Item>
            </Form>
        );
    }
}
