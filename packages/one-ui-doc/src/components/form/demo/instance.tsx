import React, {useRef} from 'react';
import {Form, Input, Button, NumberInput, Toast} from '@baidu/one-ui';

export default () => {
    const onFinish = values => console.log(values);
    const onFinishFailed = (errors, values) => console.log(errors, values);
    const formRef = useRef(null);

    return (
        <>
            <Button
                onClick={() => {
                    console.log(formRef.current);
                    Toast.success({
                        content: '请在控制台查看表单实例',
                    });
                }}
                size="small"
                type="primary"
            >
                获取表单实例
            </Button>
            <br />
            <br />
            <Form
                onFinish={onFinish}
                onFinishFailed={onFinishFailed}
                scrollToFirstError
                labelPosition="top"
                ref={formRef}
            >
                <Form.Field
                    label="用户名"
                    tip="最少5个字符、最长10个字符"
                    help="建议不少于5个字符"
                    helpPosition="top"
                    name="account"
                    rules={[
                        {required: true, message: '请输入用户名'},
                        {min: 5, message: '建议不少于5个字符', invalidType: 'warning'},
                        {max: 10, message: '最长10个字符'}
                    ]}
                >
                    <Input placeholder="Name" />
                </Form.Field>
                <Form.Field
                    label="年龄"
                    help="周岁"
                    helpPosition="side"
                    name="age"
                    initialValue="18"
                    rules={[
                        {required: true, message: '请输入年龄'},
                        {pattern: /\d+/, message: '请输入数字'}
                    ]}
                >
                    <NumberInput placeholder="Age" min={18} max={120} showErrorMessage={false} />
                </Form.Field>
                <Form.Field actions label="">
                    <Button
                        type="primary"
                        htmlType="submit"
                    >
                        提交
                    </Button>
                    <Button
                        htmlType="reset"
                        style={{marginLeft: 8}}
                    >
                        重置
                    </Button>
                </Form.Field>
            </Form>
        </>
    );
}
