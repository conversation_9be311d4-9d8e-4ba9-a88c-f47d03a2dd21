import React, {useState} from 'react';
import {
    Form,
    Input,
    Button,
    NumberInput,
    Radio,
    Switch,
    DatePicker,
    Select
} from '@baidu/one-ui';

export default () => {

    const [loading, setLoading] = useState(false);
    const onSubmit = () => setLoading(true);
    const onFinish = values => (console.log(values), setLoading(false));
    const onFinishFailed = (errors, values) => (console.log(errors, values), setLoading(false));
    const [layout, setLayout] = useState('default');
    const [density, setDensity] = useState('default');
    const [actions, setActions] = useState(true);
    const [standalone, setStandalone] = useState(false);

    return (
        <>
            <div className="demo-controls">
                方向：
                <Radio.Group
                    size="small"
                    type="strong"
                    options={['default', 'inline', 'grid']}
                    value={layout}
                    onChange={e => setLayout(e.target.value)}
                />
                紧凑：
                <Radio.Group
                    size="small"
                    type="strong"
                    options={['default', 'compact']}
                    value={density}
                    onChange={e => setDensity(e.target.value)}
                />
                操作：
                <Switch
                    size="small"
                    checked={actions}
                    onChange={checked => setActions(checked)}
                />
                {actions && layout !== 'default'
                    && (
                        <>
                            操作独占一行：
                            <Switch
                                size="small"
                                checked={standalone}
                                onChange={checked => setStandalone(checked)}
                            />
                        </>
                    )
                }
            </div>
            <br />
            <br />
            <Form
                onSubmit={onSubmit}
                onFinish={onFinish}
                onFinishFailed={onFinishFailed}
                scrollToFirstError
                layout={layout}
                density={density}
                style={{
                    '--dls-form-label-width': layout === 'inline' ? 'auto' : '6em'
                }}
            >
                <Form.Field
                    label="用户名"
                    tip="提示：用户名即登录账号"
                    name="account"
                    rules={[
                        {required: true, message: '请输入用户名'},
                        {min: 5, message: '建议不少于5个字符', invalidType: 'warning'},
                        {max: 10, message: '最长10个字符'}
                    ]}
                >
                    <Input placeholder="Name" width={160} />
                </Form.Field>
                <Form.Field
                    label="年龄"
                    help="周岁"
                    helpPosition="side"
                    name="age"
                    rules={[
                        {required: true, message: '请输入年龄'},
                        {pattern: /^\d+$/, message: '请输入数字'}
                    ]}
                >
                    <NumberInput placeholder="Age" min={18} max={120} showErrorMessage={false} />
                </Form.Field>
                <Form.Field
                    label="出生日期"
                    name="birthday"
                    rules={[
                        {required: true, message: '请选择日期'}
                    ]}
                >
                    <DatePicker placeholder="Birthday" />
                </Form.Field>
                {layout === 'default'
                    && (
                        <Form.Field
                            label="地址"
                        >
                            <Form.FieldGroup
                                style={{
                                    padding: 16,
                                    backgroundColor: '#F6F7FA',
                                    borderRadius: 4,
                                    '--dls-form-label-width': '4em'
                                }}
                            >
                                <Form.Field
                                    label="城市"
                                    name="city"
                                    rules={[
                                        {required: true, message: '请输入城市'}
                                    ]}
                                >
                                    <Input placeholder="Name" width={160} />
                                </Form.Field>
                                <Form.Field
                                    label="街道"
                                    name="street"
                                    rules={[
                                        {required: true, message: '请输入街道'}
                                    ]}
                                >
                                    <Input placeholder="Name" width={160} />
                                </Form.Field>
                            </Form.FieldGroup>
                        </Form.Field>
                    )
                }
                <Form.Field
                    label="职业"
                    name="job"
                    rules={[
                        {required: true, message: '请选择职业'}
                    ]}
                >
                    <Select
                        showSearch
                        placeholder="请选择职业"
                        width={160}
                    >
                        <Select.Option value="FE">FE</Select.Option>
                        <Select.Option value="UE">UE</Select.Option>
                        <Select.Option value="RD">RD</Select.Option>
                        <Select.Option value="PM">PM</Select.Option>
                    </Select>
                </Form.Field>
                <Form.Field
                    label="性别"
                    name="gender"
                    rules={[
                        {required: true, message: '请选择性别'}
                    ]}
                >
                    <Radio.Group>
                        <Radio.Button value="male">男</Radio.Button>
                        <Radio.Button value="female">女</Radio.Button>
                    </Radio.Group>
                </Form.Field>
                {actions
                    && (
                        <Form.Field
                            label={layout === 'default' ? '' : null}
                            actions
                            display={standalone ? 'standalone' : null}
                        >
                            <Button
                                type="primary"
                                htmlType="submit"
                                loading={loading}
                            >
                                提交
                            </Button>
                            <Button
                                htmlType="reset"
                                disabled={loading}
                                style={{marginLeft: 8}}
                            >
                                重置
                            </Button>
                        </Form.Field>
                    )
                }
            </Form>
        </>
    );
};
