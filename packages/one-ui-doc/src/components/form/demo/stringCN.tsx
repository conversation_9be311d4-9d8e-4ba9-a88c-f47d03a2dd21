import React, {useState} from 'react';
import {Form, Input, Button, NumberInput} from '@baidu/one-ui';

export default () => {
    const onFinish = values => console.log(values);
    const onFinishFailed = (errors, values) => console.log(errors, values);

    return (
        <Form
            onFinish={onFinish}
            onFinishFailed={onFinishFailed}
            scrollToFirstError
            style={{'--dls-form-label-width': '6em'}}
        >
            <Form.Field
                label="用户名"
                help="中文算2个字符长度"
                helpPosition="side"
                name="account"
                rules={[
                    {required: true, message: '请输入用户名'},
                    {type: 'string-cn', min: 5, max: 10, message: '最少5个字符、最长10个字符'}
                ]}
            >
                <Input placeholder="Name" />
            </Form.Field>
        </Form>
    );
}
