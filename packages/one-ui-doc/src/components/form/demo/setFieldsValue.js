import React, {PureComponent} from 'react';
import {Form, Select, Input, Button} from '@baidu/one-ui';

const {Option} = Select;

class App extends PureComponent {
    handleSubmit = e => {
        e.preventDefault();
        // eslint-disable-next-line react/prop-types
        this.props.form.validateFields((err, values) => {
            if (!err) {
                console.log('Received values of form: ', values);
            }
        });
    };

    handleSelectChange = value => {
        console.log(value);
        // eslint-disable-next-line react/prop-types
        this.props.form.setFieldsValue({
            note: `Hi, ${value === 'male' ? 'man' : 'lady'}!`
        });
    };

    render() {
        // eslint-disable-next-line react/prop-types
        const {getFieldDecorator} = this.props.form;
        return (
            <Form labelCol={{span: 5}} wrapperCol={{span: 12}} onSubmit={this.handleSubmit}>
                <Form.Item label="Note">
                    {getFieldDecorator('note', {
                        rules: [{required: true, message: 'Please input your note!'}]
                    })(<Input />)}
                </Form.Item>
                <Form.Item label="Gender">
                    {getFieldDecorator('gender', {
                        rules: [{required: true, message: 'Please select your gender!'}]
                    })(
                        <Select
                            showSearch
                            placeholder="Select a option and change input text above"
                            onChange={this.handleSelectChange}
                        >
                            <Option value="male">male</Option>
                            <Option value="female">female</Option>
                        </Select>,
                    )}
                </Form.Item>
                <Form.Item wrapperCol={{span: 12, offset: 5}}>
                    <Button type="primary" htmlType="submit">
                        Submit
                    </Button>
                </Form.Item>
            </Form>
        );
    }
}

const DemoForm = Form.create({name: 'coordinated'})(App);
export default () => <DemoForm />;
