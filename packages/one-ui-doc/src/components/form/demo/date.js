import React, {PureComponent} from 'react';
import {Form, DatePicker, Button} from '@baidu/one-ui';

class HorizontalLoginForm extends PureComponent {
    handleSubmit = e => {
        e.preventDefault();
        // eslint-disable-next-line react/prop-types
        this.props.form.validateFields((err, values) => {
            if (!err) {
                console.log('Received values of form: ', values);
            }
        });
    };

    render() {
        // eslint-disable-next-line react/prop-types
        const {getFieldDecorator} = this.props.form;
        const formItemLayout = {
            labelCol: {
                xs: {span: 24},
                sm: {span: 8}
            },
            wrapperCol: {
                xs: {span: 24},
                sm: {span: 16}
            }
        };
        const config = {
            rules: [{required: true, message: 'Please select date!'}]
        };
        // const rangeConfig = {
        //     rules: [{type: 'array', required: true, message: 'Please select time!'}]
        // };
        return (
            <Form {...formItemLayout} onSubmit={this.handleSubmit}>
                <Form.Item label="DatePicker">
                    {getFieldDecorator('date-picker', config)(<DatePicker />)}
                </Form.Item>
                <Form.Item label="DatePickerMulitiple">
                    {getFieldDecorator('date-picker-multiple', config)(<DatePicker.RangePicker />)}
                </Form.Item>
                <Form.Item label="MonthPicker">
                    {getFieldDecorator('date-picker-month', config)(<DatePicker.MonthPicker />)}
                </Form.Item>
                {/* <Form.Item label="TimePicker">
                    {getFieldDecorator('time-picker', {
                        rules: [{required: true, message: 'Please select time!'}]
                    })(<TimePicker />)}
                </Form.Item> */}
                <Form.Item
                    wrapperCol={{
                        xs: {span: 24, offset: 0},
                        sm: {span: 16, offset: 8}
                    }}
                >
                    <Button type="primary" htmlType="submit">
                        Submit
                    </Button>
                </Form.Item>
            </Form>
        );
    }
}
const DemoForm = Form.create({name: 'horizontal_login'})(HorizontalLoginForm);
export default () => <DemoForm />;
