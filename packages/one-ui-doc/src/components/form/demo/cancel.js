import React, {PureComponent} from 'react';
import {Form, Grid, Input, Button} from '@baidu/one-ui';

const {Row, Col} = Grid;

class AdvancedSearchForm extends PureComponent {
    // To generate mock Form.Item
    getFields() {
        // eslint-disable-next-line react/prop-types
        const {getFieldDecorator} = this.props.form;
        const children = [];
        for (let i = 0; i < 2; i++) {
            children.push(
                <Col span={12} key={i}>
                    <Form.Item label={`Field ${i}`}>
                        {getFieldDecorator(`field-${i}`, {
                            rules: [
                                {
                                    required: true,
                                    message: 'Input something!'
                                }
                            ]
                        })(<Input placeholder="placeholder" />)}
                    </Form.Item>
                </Col>,
            );
        }
        return children;
    }

    handleSearch = e => {
        e.preventDefault();
        // eslint-disable-next-line react/prop-types
        this.props.form.validateFields((err, values) => {
            console.log('Received values of form: ', values);
        });
    };

    handleReset = () => {
        // eslint-disable-next-line react/prop-types
        this.props.form.resetFields();
    };

    render() {
        return (
            <Form className="ant-advanced-search-form" onSubmit={this.handleSearch} style={{width: 960}}>
                <Row gutter={24}>{this.getFields()}</Row>
                <Row>
                    <Col span={24} style={{textAlign: 'center'}}>
                        <Button type="primary" htmlType="submit">
                            Search
                        </Button>
                        <Button style={{marginLeft: 8}} onClick={this.handleReset}>
                            Clear
                        </Button>
                    </Col>
                </Row>
            </Form>
        );
    }
}

const DemoForm = Form.create({name: 'advanced_search'})(AdvancedSearchForm);
export default () => <DemoForm />;
