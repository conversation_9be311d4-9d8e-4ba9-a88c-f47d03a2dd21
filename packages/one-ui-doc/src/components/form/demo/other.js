import React, {PureComponent} from 'react';
import {
    Form, Button, TextArea,
    Switch, Select, Radio,
    Checkbox, Uploader
} from '@baidu/one-ui';

const Option = Select.Option;

class HorizontalLoginForm extends PureComponent {
    handleSubmit = e => {
        e.preventDefault();
        // eslint-disable-next-line react/prop-types
        this.props.form.validateFields((err, values) => {
            console.log('Received values of form: ', values);
        });
    };

    render() {
        // eslint-disable-next-line react/prop-types
        const {getFieldDecorator} = this.props.form;
        const formItemLayout = {
            labelCol: {
                xs: {span: 24},
                sm: {span: 8}
            },
            wrapperCol: {
                xs: {span: 24},
                sm: {span: 16}
            }
        };
        return (
            <Form {...formItemLayout} onSubmit={this.handleSubmit}>
                <Form.Item label="长文本">
                    {getFieldDecorator('textarea', {
                        rules: [{required: true, message: 'Please say something!'}]
                    })(<TextArea />)}
                </Form.Item>
                <Form.Item label="开关">
                    {getFieldDecorator('switch', {
                        initialValue: true,
                        valuePropName: 'checked',
                        rules: [{required: true, message: 'Please say something!'}]
                    })(<Switch />)}
                </Form.Item>
                <Form.Item label="多选">
                    {getFieldDecorator('select-multiple', {
                        rules: [
                            {required: true, message: 'Please select your favourite colors!', type: 'array'}
                        ]
                    })(
                        <Select
                            showSearch
                            mode="multiple"
                            placeholder="Please select favourite colors"
                        >
                            <Option key="red" value="red">Red</Option>
                            <Option key="green" value="green">Green</Option>
                            <Option key="blue" value="blue">Blue</Option>
                        </Select>,
                    )}
                </Form.Item>
                <Form.Item label="单选组">
                    {getFieldDecorator('radio-group', {
                        rules: [
                            {required: true, message: 'Please select your favourite colors!'}
                        ]
                    })(
                        <Radio.Group>
                            <Radio value="a">item 1</Radio>
                            <Radio value="b">item 2</Radio>
                            <Radio value="c">item 3</Radio>
                        </Radio.Group>,
                    )}
                </Form.Item>
                <Form.Item label="Radio.Button">
                    {getFieldDecorator('radio-button', {
                        rules: [
                            {required: true, message: 'Please select your favourite colors!'}
                        ]
                    })(
                        <Radio.Group>
                            <Radio.Button value="a">item 1</Radio.Button>
                            <Radio.Button value="b">item 2</Radio.Button>
                            <Radio.Button value="c">item 3</Radio.Button>
                        </Radio.Group>,
                    )}
                </Form.Item>
                <Form.Item label="Checkbox.Group">
                    {getFieldDecorator('checkbox-group', {
                        initialValue: ['A', 'B']
                    })(
                        <Checkbox.Group>
                            <Checkbox value="A">A</Checkbox>
                            <Checkbox value="B">B</Checkbox>
                            <Checkbox value="C">C</Checkbox>
                            <Checkbox value="D">D</Checkbox>
                            <Checkbox value="E">E</Checkbox>
                        </Checkbox.Group>
                    )}
                </Form.Item>
                <Form.Item label="File Uploader">
                    {
                        getFieldDecorator('file-loader', {
                            initialValue: [],
                            valuePropName: 'fileList',
                            getValueFromEvent: e => {
                                if (e && e.fileList) {
                                    return e.fileList;
                                }
                                return [];
                            },
                            rules: [
                                {required: true, message: 'Please select your favourite colors!', type: 'array'}
                            ]
                        })(
                            <Uploader useInForm />
                        )
                    }
                </Form.Item>
                <Form.Item
                    wrapperCol={{
                        xs: {span: 24, offset: 0},
                        sm: {span: 16, offset: 8}
                    }}
                >
                    <Button type="primary" htmlType="submit">
                        Submit
                    </Button>
                </Form.Item>
            </Form>
        );
    }
}
const DemoForm = Form.create({name: 'horizontal_login'})(HorizontalLoginForm);
export default () => <DemoForm />;
