import React, {PureComponent} from 'react';
import {Button, Form, Input, Select} from '@baidu/one-ui';

const CustomizedForm = Form.create({
    name: 'global_state',
    onFieldsChange(props, changedFields) {
        props.onChange(changedFields);
    },
    mapPropsToFields(props) {
        return {
            username: Form.createFormField({
                ...props.username,
                value: props.username.value
            }),
            interest: Form.createFormField({
                ...props.interest,
                value: props.interest.value
            })
        };
    },
    onValuesChange(_, values) {
        console.log(values);
    }
})(props => {
    const {getFieldDecorator} = props.form;
    return (
        <Form onSubmit={
            e => {
                e.preventDefault();
                props.form.validateFields((err, values) => {
                    if (!err) {
                        console.log('values:', values);
                    }
                    else {
                        console.log('errors:', err);
                    }
                });
            }}
        >
            <Form.Item label="姓名">
                {getFieldDecorator('username', {
                    rules: [{required: true, message: '请输入姓名'}]
                })(<Input />)}
            </Form.Item>
            <Form.Item label="兴趣">
                {getFieldDecorator('interest', {
                    rules: [{required: true, message: '请选择兴趣'}]
                })(
                    <Select
                        mode="multiple"
                        showSearch
                    >
                        {['琴', '棋', '书', '画'].map(value => (
                            <Select.Option value={value} key={value}>{value}</Select.Option>
                        ))}
                    </Select>
                )}
            </Form.Item>
            <Form.Item>
                <Button htmlType="submit" type="primary">提交</Button>
            </Form.Item>
        </Form>
    );
});

export default class Demo extends PureComponent {
    state = {
        fields: {
            username: {
                value: 'huangshiming'
            },
            interest: {
                value: []
            }
        }
    };

    handleFormChange = changedFields => {
        this.setState(({fields}) => ({
            fields: {...fields, ...changedFields}
        }));
        console.log(this.state.fields);
    };

    render() {
        const {fields} = this.state;
        return (
            <div>
                <CustomizedForm {...fields} onChange={this.handleFormChange} />
            </div>
        );
    }
}
