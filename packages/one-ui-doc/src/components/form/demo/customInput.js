import React, {PureComponent} from 'react';
import {Input, Select} from '@baidu/one-ui';

const {Option} = Select;

export default class customInput extends PureComponent {
    static getDerivedStateFromProps(nextProps) {
        // Should be a controlled component.
        if ('value' in nextProps) {
            return {
                ...(nextProps.value || {})
            };
        }
        return null;
    }

    constructor(props) {
        super(props);
        // eslint-disable-next-line react/prop-types
        const value = props.value || {};
        this.state = {
            city: value.city || '',
            name: value.name || ''
        };
    }

    handleCityChange = e => {
        const city = e.value;
        if (!('value' in this.props)) {
            this.setState({city});
        }
        this.triggerChange({city});
    };

    handleNameChange = name => {
        if (!('value' in this.props)) {
            this.setState({name});
        }
        this.triggerChange({name});
    };

    triggerChange = changedValue => {
        // Should provide an event to pass value to Form.
        // eslint-disable-next-line react/prop-types
        const onChange = this.props.onChange;
        console.log(Object.assign({}, this.state, changedValue));
        if (onChange) {
            onChange(Object.assign({}, this.state, changedValue));
        }
    };

    render() {
        // eslint-disable-next-line react/prop-types
        const size = this.props.size;
        const {state} = this;
        return (
            <span>
                <Input
                    type="text"
                    size={size}
                    value={state.city}
                    onChange={this.handleCityChange}
                    width={200}
                    style={{marginRight: '15px'}}
                />
                <Select
                    value={state.name}
                    size={size}
                    width={100}
                    showSearch
                    onChange={this.handleNameChange}
                >
                    <Option value="xiaoming">黄诗铭</Option>
                    <Option value="xiaobo">张博</Option>
                </Select>
            </span>
        );
    }
}
