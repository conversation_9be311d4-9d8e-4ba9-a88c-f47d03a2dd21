export default {
    Menu: [
        {
            param: 'className',
            type: 'string',
            desc: '可以自定义menu的className',
            option: '',
            default: ''
        },
        {
            param: 'style',
            type: 'object',
            desc: '可以自定义menu的style',
            option: '',
            default: ''
        },
        {
            param: 'mode',
            type: 'string',
            desc: '导航类型垂直弹出、垂直内嵌，对应vertical, inline (水平横向导航请参考nav组件)',
            option: 'vertical, inline',
            default: 'vertical'
        },
        {
            param: 'inlineCollapsed',
            type: 'bool',
            desc: 'inline模式下，菜单是否收起状态',
            option: '',
            default: ''
        },
        {
            param: 'defaultOpenKeys',
            type: 'string []',
            desc: '菜单展开的选项对应的key值，非受控属性',
            option: '',
            default: ''
        },
        {
            param: 'openKeys',
            type: 'string []',
            desc: '菜单展开的选项对应的key值，受控属性',
            option: '',
            default: ''
        },
        {
            param: 'defaultSelectedKeys',
            type: 'string []',
            desc: '菜单选中的选项对应的key值，非受控属性',
            option: '',
            default: ''
        },
        {
            param: 'selectedKeys',
            type: 'string []',
            desc: '菜单选中的选项对应的key值，受控属性',
            option: '',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: 'menu的尺寸',
            option: 'small | medium | large',
            default: 'medium'
        },
        {
            param: 'type',
            type: 'string',
            desc: '类型, 默认为strong加强型，可以设置为basic基础型',
            option: 'basic | strong',
            default: 'strong'
        },
        {
            param: 'onClick',
            type: 'Function(e), e为对象{key, keyPath, item, domEvent}',
            desc: '点击 MenuItem 调用此函数',
            option: '',
            default: ''
        },
        {
            param: 'onOpenChange',
            type: 'Function(keyPath) keyPath为[], 为点击当前subMenu的时候，当前subMenu的路径',
            desc: 'SubMenu 展开/关闭的回调',
            option: '',
            default: ''
        },
        {
            param: 'needBorder',
            type: 'bool',
            desc: 'inline是否需要右边框',
            option: '',
            default: 'false'
        }
    ],
    MenuItem: [
        {
            param: 'disabled',
            type: 'boolean',
            desc: '当前MenuItem是否可以点击',
            option: '',
            default: 'false'
        },
        {
            param: 'key',
            type: 'string',
            desc: 'MenuItem的key',
            option: '',
            default: ''
        },
        {
            param: 'icon',
            type: 'ReactNode',
            desc: '菜单icon',
            option: '',
            default: ''
        }
    ],
    MenuSubMenu: [
        {
            param: 'disabled',
            type: 'boolean',
            desc: '当前Menu.Item是否可以点击',
            option: '',
            default: 'false'
        },
        {
            param: 'key',
            type: 'string',
            desc: 'Menu.SubMenu的key',
            option: '',
            default: ''
        },
        {
            param: 'title',
            type: 'string|ReactNode',
            desc: '子菜单的title',
            option: '',
            default: ''
        },
        {
            param: 'onTitleClick',
            type: 'Function(e) e为{key, domEvent}',
            desc: '点击子菜单的title',
            option: '',
            default: ''
        },
        {
            param: 'icon',
            type: 'ReactNode',
            desc: '子菜单支持传入一个icon',
            option: '',
            default: ''
        },
        {
            param: 'size',
            type: 'number',
            desc: 'menu的尺寸',
            option: 'small | medium | large',
            default: 'medium'
        },
        {
            param: 'type',
            type: 'string',
            desc: '类型, 默认为strong加强型，可以设置为basic基础型',
            option: 'basic | strong',
            default: 'strong'
        },
        {
            param: 'popupClassName',
            type: 'string',
            desc: '可自定义弹层的className',
            option: '',
            default: ''
        }
    ],
    MenuDivider: [{
        param: '无参数',
        type: '',
        desc: '菜单项分割线，只用在弹出菜单内',
        option: '',
        default: ''
    }]
};
