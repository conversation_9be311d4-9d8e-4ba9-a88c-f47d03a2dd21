import React, {PureComponent} from 'react';
import {Menu, IconSvg} from '@baidu/one-ui';

const SubMenu = Menu.SubMenu;
export default class Normal extends PureComponent {
    handleClick = e => {
        console.log('click', e);
    }

    render() {
        return (
            <div>
                <Menu onClick={this.handleClick} mode="vertical">
                    <SubMenu
                        key="sub1"
                        disabled
                        title={(
                            <span>Navigation One</span>
                        )}
                    >
                        <Menu.ItemGroup title="Item 1">
                            <Menu.Item key="1">Option 1</Menu.Item>
                            <Menu.Item key="2">Option 2</Menu.Item>
                        </Menu.ItemGroup>
                        <Menu.ItemGroup title="Item 2">
                            <Menu.Item key="3">Option 3</Menu.Item>
                            <Menu.Item key="4">Option 4</Menu.Item>
                        </Menu.ItemGroup>
                    </SubMenu>
                    <SubMenu
                        key="sub2"
                        title={(
                            <span>Navigation Two</span>
                        )}
                        popupClassName="xxx-xx-sd"
                    >
                        <Menu.Item key="5" disabled>Option 5</Menu.Item>
                        <Menu.Item key="6">Option 6</Menu.Item>
                        <SubMenu key="sub3" title="Submenu" disabled>
                            <Menu.Item key="7">Option 7</Menu.Item>
                            <Menu.Item key="8">Option 8</Menu.Item>
                        </SubMenu>
                    </SubMenu>
                    <SubMenu
                        key="sub4"
                        title={(
                            <span>Navigation Three</span>
                        )}
                    >
                        <Menu.Item key="9">Option 9</Menu.Item>
                        <Menu.Item key="10">Option 10</Menu.Item>
                        <Menu.Item key="11">Option 11</Menu.Item>
                        <Menu.Item key="12">Option 12</Menu.Item>
                    </SubMenu>
                </Menu>
                <br />
                <br />
                <br />
                <Menu onClick={this.handleClick} mode="vertical" size="large">
                    <SubMenu
                        key="sub1"
                        title={(
                            <span>Navigation One</span>
                        )}
                        size="large"
                    >
                        <Menu.ItemGroup title="Item 1">
                            <Menu.Item key="1">Option 1</Menu.Item>
                            <Menu.Item key="2">Option 2</Menu.Item>
                        </Menu.ItemGroup>
                        <Menu.ItemGroup title="Item 2">
                            <Menu.Item key="3">Option 3</Menu.Item>
                            <Menu.Item key="4">Option 4</Menu.Item>
                        </Menu.ItemGroup>
                    </SubMenu>
                    <SubMenu
                        key="sub2"
                        title={(
                            <span>Navigation Two</span>
                        )}
                        size="large"
                    >
                        <Menu.Item key="5">Option 5</Menu.Item>
                        <Menu.Item key="6">Option 6</Menu.Item>
                        <SubMenu key="sub3" title="Submenu" size="large">
                            <Menu.Item key="7">Option 7</Menu.Item>
                            <Menu.Item key="8">Option 8</Menu.Item>
                        </SubMenu>
                    </SubMenu>
                    <SubMenu
                        key="sub4"
                        title={(
                            <span>Navigation Three</span>
                        )}
                        size="large"
                    >
                        <Menu.Item key="9">Option 9</Menu.Item>
                        <Menu.Item key="10">Option 10</Menu.Item>
                        <Menu.Item key="11">Option 11</Menu.Item>
                        <Menu.Item key="12">Option 12</Menu.Item>
                    </SubMenu>
                </Menu>
                <br />
                <br />
                <br />
                <Menu onClick={this.handleClick} mode="vertical" size="small">
                    <SubMenu
                        key="sub1"
                        title={(
                            <span>Navigation One</span>
                        )}
                        size="small"
                    >
                        <Menu.ItemGroup title="Item 1">
                            <Menu.Item key="1">Option 1</Menu.Item>
                            <Menu.Item key="2">Option 2</Menu.Item>
                        </Menu.ItemGroup>
                        <Menu.ItemGroup title="Item 2">
                            <Menu.Item key="3">Option 3</Menu.Item>
                            <Menu.Item key="4">Option 4</Menu.Item>
                        </Menu.ItemGroup>
                    </SubMenu>
                    <SubMenu
                        key="sub2"
                        title={(
                            <span>Navigation Two</span>
                        )}
                        size="small"
                    >
                        <Menu.Item key="5">Option 5</Menu.Item>
                        <Menu.Item key="6">Option 6</Menu.Item>
                        <SubMenu key="sub3" title="Submenu" size="small">
                            <Menu.Item key="7">Option 7</Menu.Item>
                            <Menu.Item key="8">Option 8</Menu.Item>
                        </SubMenu>
                    </SubMenu>
                    <SubMenu
                        key="sub4"
                        title={(
                            <span>Navigation Three</span>
                        )}
                        size="small"
                    >
                        <Menu.Item key="9">Option 9</Menu.Item>
                        <Menu.Item key="10">Option 10</Menu.Item>
                        <Menu.Item key="11">Option 11</Menu.Item>
                        <Menu.Item key="12">Option 12</Menu.Item>
                    </SubMenu>
                </Menu>
            </div>
        );
    }
}
