import React, {PureComponent} from 'react';
import {Menu, IconSvg} from '@baidu/one-ui';

const SubMenu = Menu.SubMenu;
export default class Normal extends PureComponent {
    handleClick = e => {
        console.log(e);
    }

    render() {
        return (
            <div>
                <Menu
                    onClick={this.handleClick}
                    defaultSelectedKeys={['1']}
                    defaultOpenKeys={['sub1']}
                    mode="inline"
                >
                    <SubMenu
                        key="sub1"
                        icon={(<IconSvg type="calendar" />)}
                        title={
                            (
                                <span>
                                    <span>Navigation One</span>
                                </span>
                            )}
                    >
                        <Menu.Item key="1">
                            Option 1
                        </Menu.Item>
                        <Menu.Item key="2">
                            Option 2
                        </Menu.Item>
                        <Menu.Item key="3">Option 3</Menu.Item>
                        <Menu.Item key="4">Option 4</Menu.Item>
                    </SubMenu>
                    <SubMenu
                        key="sub2"
                        icon={(<IconSvg type="calendar" />)}
                        title={
                            (
                                <span>
                                    <span>Navigation Two</span>
                                </span>
                            )}
                    >
                        <Menu.Item key="7"><span>Option 7</span></Menu.Item>
                        <Menu.Item key="8"><span>Option 8</span></Menu.Item>
                    </SubMenu>
                </Menu>
            </div>
        );
    }
}
