import React, {PureComponent} from 'react';
import {Menu} from '@baidu/one-ui';

const SubMenu = Menu.SubMenu;
const Group = Menu.ItemGroup;
export default class Normal extends PureComponent {
    state = {
        current: '1'
    };

    render() {
        return (
            <div>
                中号
                <br />
                <br />
                <br />
                <Menu
                    onClick={this.handleClick}
                    defaultSelectedKeys={['sub1', 'sub666', '1']}
                    defaultOpenKeys={['sub1', 'sub666', '1', '3', '4']}
                    mode="inline"
                >
                    <SubMenu key="sub1" title={<span><span>一级菜单01</span></span>}>
                        <SubMenu
                        // inlineIndent={15}
                            key="sub666"
                            title="二级菜单01"
                        >
                            <Group title="分组1">
                                <Menu.Item key="1">
                                    <span>三级菜单01</span>
                                </Menu.Item>
                                <Menu.Item key="2"><span>三级菜单02</span></Menu.Item>
                            </Group>
                        </SubMenu>
                        <SubMenu key="sub2333" title="二级菜单02">
                            <Group title="分组2">
                                <Menu.Item key="3"><span>三级菜单03</span></Menu.Item>
                                <Menu.Item key="4"><span>三级菜单04</span></Menu.Item>
                            </Group>
                        </SubMenu>
                        <Menu.Item key="5"><span>三级菜单05</span></Menu.Item>
                    </SubMenu>
                    <SubMenu key="sub2" title={<span><span>一级菜单02</span></span>}>
                        <SubMenu key="sub3" title="二级菜单03">
                            <Group title="分组3">
                                <Menu.Item key="7"><span>三级菜单07</span></Menu.Item>
                                <Menu.Item key="8"><span>三级菜单08</span></Menu.Item>
                            </Group>
                        </SubMenu>
                    </SubMenu>
                </Menu>
                <br />
            </div>
        );
    }
}
