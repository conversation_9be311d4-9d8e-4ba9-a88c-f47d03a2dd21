import React, {PureComponent} from 'react';
import {Menu} from '@baidu/one-ui';

const SubMenu = Menu.SubMenu;
export default class Normal extends PureComponent {
    state = {
        current: '1'
    };

    render() {
        return (
            <div>
                中号
                <br />
                <br />
                <br />
                <Menu
                    onClick={this.handleClick}
                    defaultSelectedKeys={['2']}
                    defaultOpenKeys={['sub1']}
                    mode="inline"
                >
                    <Menu.Item key="101" href="https://www.baidu.com">
                        一级菜单01
                    </Menu.Item>
                    <SubMenu key="sub1" title={<span>这是一级菜单01</span>}>
                        <Menu.Item key="1" href="https://www.baidu.com">
                            二级菜单01
                        </Menu.Item>
                        <Menu.Item key="2" href="https://www.baidu.com">
                            <span>二级菜单02</span>
                        </Menu.Item>
                        <Menu.Item key="5" href="https://www.baidu.com">
                            <span>二级菜单03</span>
                        </Menu.Item>
                    </SubMenu>
                    <SubMenu key="sub2" disabled title={<span>这是一级菜单02</span>}>
                        <Menu.Item key="7" href="https://www.baidu.com"><span>二级菜单07</span></Menu.Item>
                        <Menu.Item key="8" href="https://www.baidu.com"><span>二级菜单08</span></Menu.Item>
                    </SubMenu>
                </Menu>
                <br />
                <br />
                <br />
                大号
                <br />
                <br />
                <br />
                <Menu
                    onClick={this.handleClick}
                    defaultSelectedKeys={['1']}
                    defaultOpenKeys={['sub1']}
                    mode="inline"
                    size="large"
                >
                    <SubMenu key="sub1" title={<span>这是一级菜单01</span>}>
                        <Menu.Item key="1">
                            <span>二级菜单01</span>
                        </Menu.Item>
                        <Menu.Item key="2">
                            <span>二级菜单02</span>
                        </Menu.Item>
                        <Menu.Item key="5">
                            <span>二级菜单03</span>
                        </Menu.Item>
                    </SubMenu>
                    <SubMenu key="sub2" title={<span>这是一级菜单02</span>}>
                        <Menu.Item key="7"><span>二级菜单07</span></Menu.Item>
                        <Menu.Item key="8"><span>二级菜单08</span></Menu.Item>
                    </SubMenu>
                </Menu>
                <br />
                <br />
                <br />
                小号
                <br />
                <br />
                <br />
                <Menu
                    onClick={this.handleClick}
                    defaultSelectedKeys={['1']}
                    defaultOpenKeys={['sub1']}
                    mode="inline"
                    size="small"
                >
                    <SubMenu key="sub1" title={<span>这是一级菜单01</span>}>
                        <Menu.Item key="1">
                            <span>二级菜单01</span>
                        </Menu.Item>
                        <Menu.Item key="2">
                            <span>二级菜单02</span>
                        </Menu.Item>
                        <Menu.Item key="5">
                            <span>二级菜单03</span>
                        </Menu.Item>
                    </SubMenu>
                    <SubMenu key="sub2" title={<span>这是一级菜单02</span>}>
                        <Menu.Item key="7"><span>二级菜单07</span></Menu.Item>
                        <Menu.Item key="8"><span>二级菜单08</span></Menu.Item>
                    </SubMenu>
                </Menu>
            </div>
        );
    }
}
