import React, {PureComponent} from 'react';
import {Menu, IconSvg, Button} from '@baidu/one-ui';

const SubMenu = Menu.SubMenu;
export default class Normal extends PureComponent {
    state = {
        collapsed: false
    };

    toggleCollapsed = () => {
        this.setState({
            collapsed: !this.state.collapsed
        });
    };

    render() {
        const type = this.state.collapsed ? 'menu-unfold' : 'menu-fold';
        return (
            <div style={{width: 256}}>
                <Button type="primary" onClick={this.toggleCollapsed} style={{marginBottom: 16}}>
                    {type}
                </Button>
                <br />
                <br />
                <Menu
                    defaultSelectedKeys={['1']}
                    defaultOpenKeys={['sub1']}
                    mode="inline"
                    theme="dark"
                    inlineCollapsed={this.state.collapsed}
                >
                    <Menu.Item key="1" icon={<IconSvg type="filter" />}>
                        <span>Option 1</span>
                    </Menu.Item>
                    <Menu.Item key="2" icon={<IconSvg type="filter" />}>
                        <span>Option 2</span>
                    </Menu.Item>
                    <Menu.Item key="3" icon={<IconSvg type="calendar" />}>
                        <span>Option 3</span>
                    </Menu.Item>
                    <SubMenu
                        key="sub1"
                        icon={<IconSvg type="search" />}
                        title={(<span>Navigation One</span>)}
                    >
                        <SubMenu
                            key="sub1-2"
                            title={(
                                <span>
                                    <span>Navigation Level One</span>
                                </span>
                            )}
                        >
                            <Menu.Item key="5">Option 5</Menu.Item>
                            <Menu.Item key="6">Option 6</Menu.Item>
                            <Menu.Item key="7">Option 7</Menu.Item>
                            <Menu.Item key="8">Option 8</Menu.Item>
                        </SubMenu>
                    </SubMenu>
                    <SubMenu
                        key="sub2"
                        icon={<IconSvg type="calendar" />}
                        title={(<span>Navigation Two</span>)}
                    >
                        <SubMenu
                            key="sub2-1"
                            title={(<span>Navigation Level Two</span>)}
                        >
                            <Menu.Item key="9">Option 9</Menu.Item>
                            <Menu.Item key="10">Option 10</Menu.Item>
                            <SubMenu key="sub3" title="Submenu">
                                <Menu.Item key="11">Option 11</Menu.Item>
                                <Menu.Item key="12">Option 12</Menu.Item>
                            </SubMenu>
                        </SubMenu>
                    </SubMenu>
                </Menu>
            </div>
        );
    }
}
