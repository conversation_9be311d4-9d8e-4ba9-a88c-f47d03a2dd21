import React, {PureComponent} from 'react';
import {Menu, Radio} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {
        size: 'medium'
    };

    render() {
        const size = this.state.size;
        return (
            <div>
                <Radio.Group size="small" defaultValue={size} onChange={e => this.setState({size: e.target.value})}>
                    {
                        ['small', 'medium', 'large'].map(
                            size => <Radio.Button value={size} key={size}>{size}</Radio.Button>
                        )
                    }
                </Radio.Group>
                <br />
                <br />
                <Menu
                    onClick={this.handleClick}
                    defaultSelectedKeys={['2']}
                    defaultOpenKeys={['sub1']}
                    type="basic"
                    size={size}
                >
                    <Menu.ItemGroup key="101" title="分组标题1">
                        <Menu.Item key="1">
                            二级菜单01
                        </Menu.Item>
                        <Menu.Item key="2">
                            <span>二级菜单02</span>
                        </Menu.Item>
                        <Menu.Item key="3">
                            <span>二级菜单03</span>
                        </Menu.Item>
                    </Menu.ItemGroup>
                    <Menu.Divider />
                    <Menu.ItemGroup key="201" title="分组标题2">
                        <Menu.Item key="4">
                            二级菜单01
                        </Menu.Item>
                        <Menu.Item key="5">
                            <span>二级菜单02</span>
                        </Menu.Item>
                        <Menu.Item key="6">
                            <span>二级菜单03</span>
                        </Menu.Item>
                        <Menu.Divider />
                        <Menu.SubMenu
                            key="301"
                            title="子菜单"
                            type="basic"
                            size={size}
                        >
                            <Menu.Item key="7">
                                二级菜单01
                            </Menu.Item>
                            <Menu.Item key="8">
                                <span>二级菜单02</span>
                            </Menu.Item>
                            <Menu.Item key="9">
                                <span>二级菜单03</span>
                            </Menu.Item>
                            <Menu.Divider />
                            <Menu.ItemGroup key="501" title="分组标题1">
                                <Menu.Item key="51">
                                    二级菜单01
                                </Menu.Item>
                                <Menu.Item key="52">
                                    <span>二级菜单02</span>
                                </Menu.Item>
                                <Menu.Item key="53">
                                    <span>二级菜单03</span>
                                </Menu.Item>
                            </Menu.ItemGroup>
                        </Menu.SubMenu>
                    </Menu.ItemGroup>
                    <Menu.SubMenu
                        key="401"
                        title="分组标题3"
                        type="basic"
                        size={size}
                    >
                        <Menu.Item key="14">
                            二级菜单01
                        </Menu.Item>
                        <Menu.Item key="15">
                            <span>二级菜单02</span>
                        </Menu.Item>
                        <Menu.Item key="16">
                            <span>二级菜单03</span>
                        </Menu.Item>
                    </Menu.SubMenu>
                </Menu>
            </div>
        );
    }
}
