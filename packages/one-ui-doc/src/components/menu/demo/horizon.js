import React, {PureComponent} from 'react';
import {Menu} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {
        current: '1'
    };

    handleClick = e => {
        this.setState({
            current: e.key
        });
    }

    render() {
        return (
            <div>
                <div style={{marginBottom: '30px'}}>
                    <div style={{marginBottom: '30px'}}>横向导航 - small</div>
                    <Menu
                        onClick={this.handleClick}
                        selectedKeys={[this.state.current]}
                        mode="horizontal"
                        size="small"
                    >
                        <Menu.Item key="1">
                            项目一
                        </Menu.Item>
                        <Menu.Item key="2">
                        项目二
                        </Menu.Item>
                        <Menu.Item key="3">
                        项目三
                        </Menu.Item>
                        <Menu.Item key="4">
                        项目四
                        </Menu.Item>
                        <Menu.Item key="5">
                        项目五
                        </Menu.Item>
                        <Menu.Item key="6" disabled>
                        项目六
                        </Menu.Item>
                    </Menu>
                </div>
                <div style={{marginBottom: '30px'}}>
                    <div style={{marginBottom: '30px'}}>横向导航 - medium</div>
                    <Menu
                        onClick={this.handleClick}
                        selectedKeys={[this.state.current]}
                        mode="horizontal"
                        size="medium"
                    >
                        <Menu.Item key="1">
                            项目一
                        </Menu.Item>
                        <Menu.Item key="2">
                        项目二
                        </Menu.Item>
                        <Menu.Item key="3">
                        项目三
                        </Menu.Item>
                        <Menu.Item key="4">
                        项目四
                        </Menu.Item>
                        <Menu.Item key="5">
                        项目五
                        </Menu.Item>
                        <Menu.Item key="6" disabled>
                        项目六
                        </Menu.Item>
                    </Menu>
                </div>
                <div style={{marginBottom: '30px'}}>
                    <div style={{marginBottom: '30px'}}>横向导航 - large</div>
                    <Menu
                        onClick={this.handleClick}
                        selectedKeys={[this.state.current]}
                        mode="horizontal"
                        size="large"
                    >
                        <Menu.Item key="1">
                            项目一
                        </Menu.Item>
                        <Menu.Item key="2">
                        项目二
                        </Menu.Item>
                        <Menu.Item key="3">
                        项目三
                        </Menu.Item>
                        <Menu.Item key="4">
                        项目四
                        </Menu.Item>
                        <Menu.Item key="5">
                        项目五
                        </Menu.Item>
                        <Menu.Item key="6" disabled>
                        项目六
                        </Menu.Item>
                    </Menu>
                </div>
            </div>
        );
    }
}
