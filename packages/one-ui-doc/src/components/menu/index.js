import BaseComponent from '../base';

export default {
    menu: {
        value: BaseComponent,
        label: 'Menu 导航',
        demos: [
            {
                title: '基础',
                desc: '基础菜单',
                source: 'basic'
            },
            {
                title: '内嵌',
                desc: '内嵌菜单',
                source: 'vertical'
            },
            {
                title: '带icon',
                desc: '内嵌菜单-带icon',
                source: 'inline-icon'
            },
            {
                title: '右边框',
                desc: '内嵌菜单-带右边框',
                source: 'border'
            },
            {
                title: '弹出式',
                desc: '竖向弹出菜单',
                source: 'inline'
            },
            {
                title: '收起',
                desc: '缩起内嵌菜单',
                source: 'collapse'
            }
        ],
        apis: [
            {
                apiKey: 'Menu',
                title: 'Menu'
            },
            {
                apiKey: 'MenuItem',
                title: 'Menu.Item'
            },
            {
                apiKey: 'SubMenu',
                title: 'Menu.SubMenu'
            },
            {
                apiKey: 'MenuDivider',
                title: 'Menu.Divider'
            }
        ]
    }
};
