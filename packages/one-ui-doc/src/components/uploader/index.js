import BaseComponent from '../base';

export default {
    uploader: {
        value: BaseComponent,
        label: 'Uploader 上传',
        demos: [
            {
                title: '文件',
                desc: '普通上传',
                source: 'regular'
            },
            {
                title: '图片',
                desc: '普通上传 - 图片',
                source: 'regularImage'
            },
            {
                title: '图片-小尺寸',
                desc: '普通上传 - 图片 - 小尺寸',
                source: 'regularSmallImage'
            },
            {
                title: '带列表',
                desc: '普通上传 - 带列表 - 非受控',
                source: 'defaultList'
            },
            {
                title: '带列表-受控',
                desc: '普通上传 - 带列表 - 受控',
                source: 'normal'
            },
            {
                title: '多选',
                desc: '普通上传 - 多选 - 带列表 - 受控',
                source: 'multiple'
            },
            {
                title: '图片-带列表',
                desc: '普通上传 - 单图上传 - 带列表 - 受控',
                source: 'image'
            },
            {
                title: '多图上传',
                desc: '普通上传 - 图片多选上传 - 带列表 - 受控',
                source: 'multipleImage'
            },
            {
                title: '禁用',
                desc: '普通上传 - 禁用',
                source: 'disabled'
            },
            {
                title: '加载中',
                desc: '普通上传 - 加载中',
                source: 'loading'
            },
            {
                title: '上传后回调',
                desc: `可以通过 {afterUpload} ，对上传后文件状态、文件名等信息进行更新。
                {afterUpload} 在上传完成后且没有发生错误的情况下触发，需返回一个 Object 对象（不支持异步返回）`,
                source: 'afterUpload'
            },
            {
                title: '限制个数',
                desc: '普通上传 - 限制文件上传个数 - 文件上传',
                source: 'maxLength'
            },
            {
                title: '图片-限制个数',
                desc: '普通上传 - 限制文件上传个数 - 图片上传',
                source: 'imageMaxLength'
            },
            {
                title: '自定义icon与描述',
                desc: '',
                source: 'pickerIcon'
            },
            {
                title: '自定义上传',
                desc: '普通上传 - 自定义上传的picker',
                source: 'customPicker'
            },
            {
                title: '自定义点击',
                desc: '不用默认上传，通过获取点击事件，自行对接其他上传形式',
                source: 'placeholder'
            },
            {
                title: '错误与帮助',
                desc: '展示错误与帮助不同形式组合',
                source: 'error'
            },
            {
                title: '自定义操作icon',
                desc: '普通上传 - 自定义操作icon',
                source: 'renderCustomIcon'
            },
            {
                title: '视频',
                desc: '普通上传(视频) - 带列表 - 受控',
                source: 'video'
            },
            {
                title: 'picker位置',
                desc: '',
                source: 'pickerPosition'
            },
            {
                title: '多路径',
                desc: '普通上传 - 多路径上传',
                source: 'entries'
            },
            {
                title: '自定义操作',
                desc: '普通上传 - 自定义操作',
                source: 'multipleOperation'
            },
            {
                title: '多图预览',
                desc: '普通上传 - 多图预览 - 上传后隐藏picker',
                source: 'view'
            },
            {
                title: '多picker',
                desc: '普通上传 - 多picker',
                source: 'multiplePicker'
            },
            {
                title: '自定义尺寸',
                desc: '通过CSS变量`--dls-uploader-media-item-height`与`--dls-uploader-media-item-width`进行尺寸自定义，'
                    + 'small尺寸区间为[60, 120]，medium尺寸区间为[100, 300]',
                source: 'customSize'
            }
        ],
        apis: [
            {
                apiKey: 'Uploader',
                title: 'Uploader'
            },
            {
                apiKey: 'UploadFile',
                title: 'UploadFile'
            },
            {
                apiKey: 'UploaderRequestOption',
                title: 'requestOption',
                desc: '自定义uploader方法入参：requestOption'
            },
            {
                apiKey: 'UploaderOnChange',
                title: 'onChange'
            },
            {
                apiKey: 'CSS',
                title: 'CSS变量',
                children: [
                    {
                        param: '--dls-uploader-media-item-width',
                        desc: '自定义宽度',
                        type: '<length>'
                    },
                    {
                        param: '--dls-uploader-media-item-height',
                        desc: '自定义宽度',
                        type: '<length>'
                    }
                ]
            }
        ]
    }
};
