import React, {PureComponent} from 'react';
import {Uploader, Button} from '@baidu/one-ui';
import {IconEye, IconUpload, IconCut, IconEllipsis} from 'dls-icons-react';

export default class Normal extends PureComponent {
    state = {
        fileList: []
    };

    onRemove = ({fileList}) => {
        this.setState({
            fileList: [...fileList]
        });
    }

    onChange = ({fileList}) => {
        this.setState({
            fileList: [...fileList]
        });
    }

    showUploadListIcon = (file, defaultControls) => {
        const customControls = [{
            key: 'IconCut',
            icon: <IconCut />
        }, {
            key: 'IconEllipsis',
            icon: <IconEllipsis />,
            children: [{
                key: '123',
                label: '操作1',
                onClick: () => {}
            }, {
                key: '1234',
                label: '操作2',
                onClick: () => {}
            }]
        }];
        return [...defaultControls, ...customControls];
    };

    render() {
        return (
            <div>
                <br />
                <br />
                多路径上传
                <br />
                <br />
                <Uploader
                    fileList={this.state.fileList}
                    onChange={this.onChange}
                    maxSize={5 * 1024 * 1024}
                    onRemove={this.onRemove}
                    uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                    listType="image"
                    controls={this.showUploadListIcon}
                    entries={[{
                        key: 'pic store disabled',
                        label: '禁止上传',
                        icon: <IconEye />,
                        disabled: true
                    }, {
                        key: 'upload local',
                        label: '本地上传',
                        icon: <IconUpload />,
                        isDefaultUpload: true
                    }]}
                />
            </div>
        );
    }
}
