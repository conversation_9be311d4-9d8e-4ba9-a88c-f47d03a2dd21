import React, {PureComponent} from 'react';
import {Uploader} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {
        fileList: [{
            status: 'uploading',
            name: '这是一个上传中的文件.png',
            progressStep: 30
        }, {
            status: 'success',
            name: '这是一个上传成功的文件.png'
        }, {
            status: 'error',
            name: '这是一个上传失败的文件.png',
            errorMessage: ['上传失败的原因是叭叭叭']
        }]
    };

    onRemove = ({fileList}) => {
        this.setState({
            fileList: [...fileList]
        });
    }

    onChange = ({fileList}) => {
        this.setState({
            fileList: [...fileList]
        });
    }

    render() {
        return (
            <Uploader
                fileList={this.state.fileList}
                pickerPosition="after"
                onChange={this.onChange}
                maxSize={5 * 1024 * 1024}
                onRemove={this.onRemove}
                multiple
                uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
            />
        );
    }
}
