import React, {PureComponent} from 'react';
import {Uploader, Button, Dialog} from '@baidu/one-ui';
import {IconEye, IconUpload, IconEllipsis} from 'dls-icons-react';

export default class Normal extends PureComponent {
    state = {
        fileList: []
    };

    onRemove = ({fileList}) => {
        this.setState({
            fileList: [...fileList]
        });
    }

    onChange = ({fileList}) => {
        this.setState({
            fileList: [...fileList]
        });
    }

    onClickEntry = () => {
        Dialog.alert({
            title: 'Do you Want to delete these items?',
            content: 'Some descriptions',
            iconType: 'success',
            buttonPosition: 'right',
            onOk() {
                console.log('OK');
            }
        });
    }

    onGoToLink = () => {
        window.open('https://www.baidu.com');
    }

    render() {
        return (
            <div>
                <br />
                <br />
                多路径上传
                <br />
                <br />
                <Uploader
                    fileList={this.state.fileList}
                    onChange={this.onChange}
                    maxSize={5 * 1024 * 1024}
                    onRemove={this.onRemove}
                    uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                    listType="image"
                    entries={[
                        {
                            key: 'pic store',
                            onClick: this.onClickEntry,
                            label: <Button type="text">图片库里</Button>,
                            icon: <IconEye />
                        },
                        {
                            key: 'upload local',
                            label: <Button type="text">本地上传</Button>,
                            icon: <IconUpload />,
                            isDefaultUpload: true
                        },
                        {
                            key: 'more operate',
                            label: <Button type="text">更多操作</Button>,
                            icon: <IconEllipsis />,
                            children: [
                                {
                                    key: 'operate1',
                                    label: '操作1',
                                    onClick: this.onGoToLink
                                },
                                {
                                    key: 'operate2',
                                    label: '操作2',
                                    onClick: this.onGoToLink
                                },
                                {
                                    key: 'operate3',
                                    label: '操作3',
                                    onClick: this.onGoToLink
                                }
                            ]
                        }]
                    }
                />
            </div>
        );
    }
}
