import React, {PureComponent} from 'react';
import {Uploader, Tooltip} from '@baidu/one-ui';
import {IconEye, IconUpload, IconCut} from 'dls-icons-react';
import {partial} from 'lodash';


export default class Normal extends PureComponent {
    state = {
        fileList: [{
            status: 'success',
            name: '这是一个上传成功的文件.png',
            thumbUrl: 'https://s3.amazonaws.com/static.neostack.com/img/react-slick/abstract04.jpg'
        }, {
            status: 'success',
            name: '这是一个上传成功的文件.png',
            thumbUrl: 'https://s3.amazonaws.com/static.neostack.com/img/react-slick/abstract04.jpg'
        }, {
            status: 'error',
            name: '这是一个上传失败的文件.png',
            errorMessage: ['上传失败的原因是叭叭叭']
        }]
    };

    onRemove = ({fileList}) => {
        this.setState({
            fileList: [...fileList]
        });
    }

    onChange = ({fileList}) => {
        this.setState({
            fileList: [...fileList]
        });
    }

    renderCustomIcon = props => {
        const {
            prefixCls, status, thumbUrl, onRemove, index, onPreview, onReUpload, showUploadListIcon
        } = props;
        const showPreview = status === 'success'
            && thumbUrl && showUploadListIcon
            && showUploadListIcon.showPreviewIcon;
        const showReUpload = (status === 'success' || status === 'error')
            && showUploadListIcon
            && showUploadListIcon.showReUploadIcon;
        return (
            <div className={`${prefixCls}-image-item-card-operation `}>
                <div className={`${prefixCls}-image-item-card-operation-mask`} />
                <div className={`${prefixCls}-image-item-card-operation-icons`}>
                    {
                        showPreview ? (
                            <IconEye onClick={partial(onPreview, index)} />
                        ) : null
                    }
                    {
                        showReUpload ? (
                            <IconUpload onClick={partial(onReUpload, index)} />
                        ) : null
                    }
                    {
                        index === 0 ? (
                            <Tooltip title="第一个不能删">
                                <IconCut />
                            </Tooltip>
                        ) : (
                            <IconCut onClick={partial(onRemove, index)} />
                        )
                    }
                </div>
            </div>
        );
    }

    render() {
        return (
            <div>
                <br />
                <br />
                自定义操作ICON
                <br />
                <br />
                <Uploader
                    fileList={this.state.fileList}
                    onChange={this.onChange}
                    maxSize={5 * 1024 * 1024}
                    onRemove={this.onRemove}
                    uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                    listType="image"
                    multiple
                    renderCustomIcon={this.renderCustomIcon}
                />
            </div>
        );
    }
}
