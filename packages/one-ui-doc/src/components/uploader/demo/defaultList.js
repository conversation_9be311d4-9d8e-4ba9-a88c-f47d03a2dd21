import React from 'react';
import {Toast, Uploader} from '@baidu/one-ui';
import {IconAnticlockwise} from 'dls-icons-react';
const renderControls = (file, actions) => {
    if (file.status === 'error') {
        return [
            {key: 'custom', icon: <IconAnticlockwise />, onClick: () => Toast.info({content: '自定义操作'})},
            ...actions
        ];
    }
    if (file.status === 'uploading') {
        return [];
    }
    return actions;
};
export default () => (
    <Uploader
        defaultFileList={[{
            status: 'uploading',
            name: '这是一个上传中的文件.png',
            progressStep: 30
        }, {
            status: 'success',
            name: '这是一个上传成功的文件.png'
        }, {
            status: 'error',
            name: '这是一个上传失败的文件.png',
            errorMessage: ['上传失败的原因是叭叭叭']
        }]}
        maxSize={5 * 1024 * 1024}
        uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
        controls={renderControls}
    />
);