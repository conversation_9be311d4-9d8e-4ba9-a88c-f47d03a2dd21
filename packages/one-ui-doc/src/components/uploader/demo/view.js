import React, {PureComponent} from 'react';
import {Uploader} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {
        fileList: [{
            status: 'success',
            name: '这是一个等待上传文件.png',
            thumbUrl: 'https://s3.amazonaws.com/static.neostack.com/img/react-slick/abstract04.jpg'
        }, {
            status: 'success',
            name: '这是一个上传中的文件.png',
            thumbUrl: 'https://s3.amazonaws.com/static.neostack.com/img/react-slick/abstract04.jpg'
        }, {
            status: 'success',
            name: '这是一个上传成功的文件.png',
            thumbUrl: 'https://s3.amazonaws.com/static.neostack.com/img/react-slick/abstract04.jpg'
        }],
        previewIndex: 0,
        hidePicker: false
    };

    onRemove = ({fileList}) => {
        this.setState({
            fileList: [...fileList]
        });
    }

    onChange = ({file, fileList}) => {
        const newFileList = [...fileList];
        const lastItem = newFileList[newFileList.length - 1];
        if (file.status === 'success') {
            lastItem.thumbUrl = 'https://images.pexels.com/videos/2156021/free-video-2156021.jpg?auto=compress&cs=tinysrgb&dpr=1&w=500';
        }
        newFileList.pop();
        newFileList.push(lastItem);
        this.setState({
            fileList: newFileList,
            hidePicker: true
        });
    }

    renderPreviewList = () => {
        const list = [
            {
                src:
                'https://cms-image.cdn.bcebos.com/1919965741%2C177922360.jpg',
                alt: 'A cute kitty looking at you with its greenish eyes.',
                name: '猫',
                type: 'image',
                desc: 'hahahahahhahaha'.repeat(10)
            },
            {
                src:
                'https://cms-image.cdn.bcebos.com/3349723427%2C2625224597.jpg',
                alt: 'A common kingfisher flying above river.',
                name: '翠鸟',
                type: 'image',
                desc: 'hahahahahhahaha1'
            },
            {
                src:
                'https://cms-image.cdn.bcebos.com/1214177510%2C515504630.jpg',
                alt: 'A white and gray dolphin in blue water.',
                name: '海豚',
                type: 'image',
                desc: 'hahahahahhahaha2'.repeat(10)
            },
            {
                src: 'https://cms-image.cdn.bcebos.com/2608100595%2C1414551417.jpg',
                alt: 'Baidu logo.',
                name: '百度',
                type: 'image',
                desc: 'hahahahahhahaha3'
            },
            {
                src: 'https://interactive-examples.mdn.mozilla.net/media/cc0-videos/flower.webm',
                alt: 'Tesla logo.',
                name: '特斯拉',
                type: 'video',
                desc: 'hahahahahhahaha4'.repeat(10)
            }
        ];
        return list.splice(this.state.previewIndex);
    };

    onPreview = file => {
        this.setState({
            previewIndex: file.index
        });
    }

    render() {
        return (
            <Uploader
                fileList={this.state.fileList}
                onChange={this.onChange}
                maxSize={5 * 1024 * 1024}
                onRemove={this.onRemove}
                onPreview={this.onPreview}
                uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                listType="image"
                previewList={this.renderPreviewList()}
                hidePicker={this.state.hidePicker}
            />
        );
    }
}