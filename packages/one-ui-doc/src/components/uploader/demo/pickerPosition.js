import React, {useState} from 'react';
import {Uploader, Radio} from '@baidu/one-ui';

const fileList = [{
    status: 'error',
    name: '文件名文件名文件名文件名文件名文件名文件名文件名.png',
    errorMessage: ['文件错误: 文件错误文件错误文件错误文件错误文件错误文件错误文件错误'],
    footer: '文件1'
}, {
    status: 'error',
    name: 'bar.png',
    thumbUrl: 'https://s3.amazonaws.com/static.neostack.com/img/react-slick/abstract04.jpg',
    errorMessage: ['尺寸错误'],
    footer: '文件2'
}];
export default () => {
    const [pickerPosition, setPickerPosition] = useState('after');
    const [list, setList] = useState(fileList);
    return (
        <>
            <div style={{display: 'flex', alignItems: 'center', gap: 8, fontSize: 12}}>
                <Radio.Group size="small" value={pickerPosition} onChange={e => setPickerPosition(e.target.value)}>
                    <Radio.Button value="before">前</Radio.Button>
                    <Radio.Button value="after">后</Radio.Button>
                    <Radio.Button value="top">上</Radio.Button>
                    <Radio.Button value="none">隐藏</Radio.Button>
                </Radio.Group>
            </div>
            <br />
            <Uploader
                fileList={list}
                maxSize={5 * 1024 * 1024}
                uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                listType="media"
                pickerPosition={pickerPosition}
                onChange={({fileList}) => setList(fileList)}
                onRemove={({fileList}) => setList(fileList)}
            />
        </>
    );
};