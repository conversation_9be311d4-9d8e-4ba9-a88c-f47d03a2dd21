import React, {PureComponent} from 'react';
import {Uploader} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {
        fileList: []
    };

    onRemove = ({fileList}) => {
        this.setState({
            fileList: [...fileList]
        });
    }

    onChange = ({fileList, file}) => {
        console.log('fileList', fileList);
        console.log('file', file);
        this.setState({
            fileList: [...fileList]
        });
    }

    render() {
        return (
            <div>
                <Uploader
                    fileList={this.state.fileList}
                    onChange={this.onChange}
                    maxSize={5 * 1024 * 1024}
                    onRemove={this.onRemove}
                    uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                    listType="media"
                    helperText={<>格式：MPEG<br />大小：不超过100M</>}
                />
                <br />
                <br />
                <Uploader
                    fileList={this.state.fileList}
                    onChange={this.onChange}
                    maxSize={5 * 1024 * 1024}
                    onRemove={this.onRemove}
                    uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                    listType="media"
                    helperText={<>格式：MPEG<br />大小：不超过100M</>}
                    helperTextPosition="bottom"
                />
            </div>
        );
    }
}
