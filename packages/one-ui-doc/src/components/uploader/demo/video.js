import React, {PureComponent} from 'react';
import {Uploader} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {
        fileList: [{
            status: 'uploading',
            name: '这是一个上传中的文件.png',
            progressStep: 30
        }, {
            status: 'success',
            name: '这是一个上传成功的文件.png',
            thumbUrl: 'https://s3.amazonaws.com/static.neostack.com/img/react-slick/abstract04.jpg'
        }, {
            status: 'error',
            name: '这是一个上传失败的文件.png',
            errorMessage: ['上传失败的原因是叭叭叭']
        }]
    };

    onRemove = ({fileList}) => {
        this.setState({
            fileList: [...fileList]
        });
    }

    onChange = ({file, fileList}) => {
        const newFileList = [...fileList];
        const lastItem = newFileList[newFileList.length - 1];
        if (file.status === 'success') {
            lastItem.thumbUrl = 'https://images.pexels.com/videos/2156021/free-video-2156021.jpg?auto=compress&cs=tinysrgb&dpr=1&w=500';
        }
        newFileList.pop();
        newFileList.push(lastItem);
        this.setState({
            fileList: newFileList
        });
    }

    render() {
        return (
            <Uploader
                fileList={this.state.fileList}
                onChange={this.onChange}
                maxSize={5 * 1024 * 1024}
                onRemove={this.onRemove}
                uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                listType="video"
            />
        );
    }
}