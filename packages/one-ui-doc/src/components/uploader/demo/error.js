import React, {PureComponent, useState} from 'react';
import {Uploader, Radio} from '@baidu/one-ui';

const fileList = [{
    status: 'error',
    name: '文件名文件名文件名文件名文件名文件名文件名文件名.png',
    errorMessage: ['文件错误: 文件错误文件错误文件错误文件错误文件错误文件错误文件错误'],
    footer: '文件1'
}, {
    status: 'error',
    name: 'bar.png',
    thumbUrl: 'https://s3.amazonaws.com/static.neostack.com/img/react-slick/abstract04.jpg',
    errorMessage: ['尺寸错误'],
    footer: '文件2'
}];
export default () => {
    const [helpPosition, setHelpPosition] = useState('bottom');
    const [errorDisplay, setErrorDisplay] = useState('normal');
    const [position, setPosition] = useState('after');
    const [count, setCount] = useState(100);
    const [size, setSize] = useState('medium');
    const [list, setList] = useState(fileList);
    return (
        <>
            <div className="demo-controls">
                <label>帮助位置:</label>
                <Radio.Group size="small" value={helpPosition} onChange={e => setHelpPosition(e.target.value)}>
                    <Radio.Button value="bottom">下</Radio.Button>
                    <Radio.Button value="right">右</Radio.Button>
                </Radio.Group>
                <label>错误位置:</label>
                <Radio.Group size="small" value={errorDisplay} onChange={e => setErrorDisplay(e.target.value)}>
                    <Radio.Button value="normal">底部</Radio.Button>
                    <Radio.Button value="popup">气泡</Radio.Button>
                </Radio.Group>
                <label>入口位置:</label>
                <Radio.Group size="small" value={position} onChange={e => setPosition(e.target.value)}>
                    <Radio.Button value="before">前</Radio.Button>
                    <Radio.Button value="after">后</Radio.Button>
                    <Radio.Button value="top">上</Radio.Button>
                </Radio.Group>
                <label>文件数:</label>
                <Radio.Group size="small" value={count} onChange={e => setCount(+e.target.value)}>
                    <Radio.Button value={1}>单个</Radio.Button>
                    <Radio.Button value={100}>多个</Radio.Button>
                </Radio.Group>
                <label>尺寸:</label>
                <Radio.Group size="small" value={size} onChange={e => setSize(e.target.value)}>
                    <Radio.Button value="small">小</Radio.Button>
                    <Radio.Button value="medium">中</Radio.Button>
                </Radio.Group>
            </div>
            <br />
            <Uploader
                fileList={list.slice(0, count)}
                maxSize={5 * 1024 * 1024}
                uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                listType="media"
                pickerPosition={position}
                errorDisplay={errorDisplay}
                maxFileLength={count}
                helperText={<>格式：MPEG<br />大小：不超过100M</>}
                helperTextPosition={helpPosition}
                size={size}
                onChange={({fileList}) => setList(fileList)}
                onRemove={({fileList}) => setList(fileList)}
            />
        </>
    );
};