import React, {PureComponent} from 'react';
import {Uploader, Button} from '@baidu/one-ui';
import classNames from 'classnames';

class Picker extends PureComponent {

    render() {
        const {onClick, loading, prefixCls, disabled, maxSize, helperTextPosition} = this.props;
        const buttonClassName = `${prefixCls}-anchor-file-button`;
        const buttonProps = {
            className: buttonClassName,
            onClick,
            loading,
            disabled,
            icon: 'upload'
        };
        const filePickerClassName = classNames(
            `${prefixCls}-anchor-file-container`,
            {
                [`${prefixCls}-anchor-file-container-${helperTextPosition}`]:
                    helperTextPosition === 'right' || helperTextPosition === 'bottom'
            }
        );
        return (
            <span className={filePickerClassName}>
                <Button {...buttonProps}>
                    自定义上传的icon
                </Button>
                {
                    maxSize ? (
                        <span className={`${prefixCls}-helper-text`}>
                            文件大小不超过
                            {maxSize / (1024 * 1024)}
                            MB
                        </span>
                    ) : null
                }
            </span>
        );
    }
}

// eslint-disable-next-line react/no-multi-comp
export default class Normal extends PureComponent {
    state = {
        fileList: []
    };

    onRemove = ({fileList}) => {
        this.setState({
            fileList: [...fileList]
        });
    }

    onChange = ({fileList}) => {
        this.setState({
            fileList: [...fileList]
        });
    }

    render() {
        return (
            <Uploader
                fileList={this.state.fileList}
                onChange={this.onChange}
                maxSize={5 * 1024 * 1024}
                onRemove={this.onRemove}
                CustomUploadPicker={Picker}
                uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
            />
        );
    }
}
