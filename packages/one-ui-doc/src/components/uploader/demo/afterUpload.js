import React, {useCallback, useState} from 'react';
import {Uploader} from '@baidu/one-ui';

export default () => {
    const [fileList, setFileList] = useState([]);

    const onRemove = useCallback(({fileList}) => {
        setFileList(fileList);
    }, []);

    const onChange = useCallback(({fileList}) => {
        setFileList(fileList);
    }, []);

    const afterUpload = useCallback((response, file, fileList) => {
        return {
            name: `文件名：${file.name}`
        };
    }, []);

    return (
        <Uploader
            multiple
            afterUpload={afterUpload}
            fileList={fileList}
            onChange={onChange}
            onRemove={onRemove}
            maxSize={5 * 1024 * 1024}
            uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
        />
    );
};
