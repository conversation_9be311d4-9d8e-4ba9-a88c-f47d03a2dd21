import React, {PureComponent} from 'react';
import {Uploader, Button} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {
        fileList: []
    };

    onRemove = ({fileList}) => {
        this.setState({
            fileList: [...fileList]
        });
    }

    onChange = ({fileList}) => {
        console.log('fileList', fileList);
        this.setState({
            fileList: [...fileList]
        });
    }

    render() {
        return (
            <>
                <Uploader
                    multiple
                    fileList={this.state.fileList}
                    onChange={this.onChange}
                    maxSize={5 * 1024 * 1024}
                    onRemove={this.onRemove}
                    uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                />
                <br />
                <Uploader
                    multiple
                    fileList={this.state.fileList}
                    onChange={this.onChange}
                    maxSize={5 * 1024 * 1024}
                    onRemove={this.onRemove}
                    uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                    helperTextPosition="bottom"
                />
            </>
        );
    }
}
