import React, {PureComponent} from 'react';
import {Uploader} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {
        fileList: []
    };

    onRemove = ({fileList}) => {
        this.setState({
            fileList: [...fileList]
        });
    }

    onChange = ({fileList}) => {
        this.setState({
            fileList: [...fileList]
        });
    }

    picker = picker => {
        return (
            <div key="format" style={{display: 'inline-block', marginBottom: '10px'}}>
                <span key="1" style={{marginRight: '10px', display: 'inline-block'}}>{picker}</span>
                <span key="2" style={{marginRight: '10px', display: 'inline-block'}}>{picker}</span>
                <span key="3" style={{marginRight: '10px', display: 'inline-block'}}>{picker}</span>
            </div>
        );
    }

    render() {
        return (
            <Uploader
                listType="image"
                fileList={this.state.fileList}
                onChange={this.onChange}
                maxSize={5 * 1024 * 1024}
                onRemove={this.onRemove}
                uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                picker={this.picker}
            />
        );
    }
}
