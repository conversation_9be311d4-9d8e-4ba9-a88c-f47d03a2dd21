import React, {PureComponent} from 'react';
import {Uploader, UploadStatus} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {
        fileList: [{
            /**
             * @type UploadStatus
             */
            status: 'success',
            name: '这是一个上传中的文件.png',
            thumbUrl: 'https://s3.amazonaws.com/static.neostack.com/img/react-slick/abstract02.jpg'
        }]
    };

    onRemove = ({fileList}) => {
        this.setState({
            fileList: [...fileList]
        });
    }

    onChange = ({fileList}) => {
        this.setState({
            fileList: [...fileList]
        });
    }

    render() {
        return (
            <div>
                <div
                    style={{
                        '--dls-uploader-media-item-height': '300px',
                        '--dls-uploader-media-item-width': '100px'
                    }}
                >
                    <Uploader
                        fileList={this.state.fileList}
                        onChange={this.onChange}
                        maxSize={5 * 1024 * 1024}
                        onRemove={this.onRemove}
                        uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                        listType="image"
                    />
                </div>
                <br />
                <br />
                <div
                    style={{
                        '--dls-uploader-media-item-height': '100px',
                        '--dls-uploader-media-item-width': '300px'
                    }}
                >
                    <Uploader
                        fileList={this.state.fileList}
                        onChange={this.onChange}
                        maxSize={5 * 1024 * 1024}
                        onRemove={this.onRemove}
                        uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                        listType="image"
                    />
                </div>
            </div>
        );
    }
}
