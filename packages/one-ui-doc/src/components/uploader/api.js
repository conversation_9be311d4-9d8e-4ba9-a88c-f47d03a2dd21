export default {
    Uploader: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义className',
            option: '',
            default: ''
        },
        {
            param: 'accept',
            type: 'array',
            desc: '传给input的accept，数组形式，表示可以接收的格式，如果默认不校验则不传',
            option: '',
            default: '[*]'
        },
        {
            param: 'validator',
            type: 'func(file): string | undefined',
            desc: '自定义校验，入参为上传后的单个file；正确不返回，错误返回错误信息',
            option: '',
            default: ''
        },
        {
            param: 'beforeUpload',
            type: 'func(file, fileList)',
            desc: '是一个promise， 入参file和fileList，传出一个processedFile，表示被自定义处理过的file，如果不传则用原始的file',
            option: '',
            default: ''
        },
        {
            param: 'uploader',
            type: 'func(requestOption)',
            desc: '自定义上传的方法，如果不自定义的话则使用默认的uploader，是一个promise，入参详情见下方requestOption',
            option: '',
            default: ''
        },
        {
            param: 'uploadResquestUrl',
            type: 'string',
            desc: '自定义的上传的接口url',
            option: '',
            default: ''
        },
        {
            param: 'disabled',
            type: 'bool',
            desc: '是否禁用',
            option: '',
            default: 'false'
        },
        {
            param: 'sortable',
            type: 'bool',
            desc: '是否可拖拽排序',
            option: '',
            default: 'false'
        },
        {
            param: 'headers',
            type: 'object',
            desc: '请求的headers',
            option: '',
            default: ''
        },
        {
            param: 'listType',
            type: 'string(file | image | video | media)',
            desc: '上传列表的类型，分为file和image类型',
            option: '',
            default: 'file'
        },
        {
            param: 'timeout',
            type: 'number',
            desc: '上传接口请求延时',
            option: '',
            default: '3000'
        },
        {
            param: 'multiple',
            type: 'bool',
            desc: 'input上multiple特性，是否支持多选',
            option: '',
            default: 'false'
        },
        {
            param: 'showUploadListIcon',
            type: 'object || func',
            desc: `
            listType为image的时候，浮层展示的操作icon，
            默认是对象，如果是函数时候，
            入参是(file: {}, defaultControls: [{}])，file则是fileList(index)，index为当前item的索引，
            defaultControls为[{
                icon: previewIcon,
                key: 'preview'
            }, {
                icon: reUploadIcon,
                key: 'reUpload'
            }, {
                icon: trashIcon,
                key: 'delete'
            }]
            `,
            option: '',
            default: '{showPreviewIcon: true, showReUploadIcon: true, showRemoveOnIcon: true}'
        },
        {
            param: 'onPreview',
            type: 'Func({file, fileList, index})',
            desc: '点击预览的时候的回调， return false的话将不会往下执行了',
            option: '',
            default: ''
        },
        {
            param: 'onRemove',
            type: 'Func({fileList, index})',
            desc: '点击删除时候的回调, index为未删除前的file索引',
            option: '',
            default: ''
        },
        {
            param: 'onReUpload',
            type: 'Func({fileList, index,  file})',
            desc: '点击重新上传的回调，return false将不会执行下去',
            option: '',
            default: ''
        },
        {
            param: 'maxSize',
            type: 'number',
            desc: '单文件可支持的最大尺寸，5M为 -> 5 * 1024 * 1024',
            option: '',
            default: ''
        },
        {
            param: 'maxParallelFileNumber',
            type: 'number',
            desc: 'multiple下最大可支持一次传多少文件，默认不限制，建议业务方限制',
            option: '',
            default: ''
        },
        {
            param: 'CustomUploadAnchor',
            type: 'ReactNode',
            desc: '可自定义上传的anchor锚点',
            option: '',
            default: ''
        },
        {
            param: 'inputControlName',
            type: 'string',
            desc: '上传input的name属性',
            option: '',
            default: ''
        },
        {
            param: 'loading',
            type: 'bool',
            desc: '上传锚点是否正在loading',
            option: '',
            default: ''
        },
        {
            param: 'helperText',
            type: 'ReactNode | function',
            desc: 'listType为file时，帮助文案（取消默认文案，可设置为null）。',
            option: '',
            default: '文件大小不超过<maxSize>MB'
        },
        {
            param: 'helperTextPosition',
            type: 'string',
            desc: 'listType为file时，帮助文案位置可为右侧 or 底部',
            option: 'right | bottom',
            default: ''
        },
        {
            param: 'onChange',
            type: 'func(obj)',
            desc: '状态改变的时候的回调，obj具体见下方详细解释',
            option: '',
            default: ''
        },
        {
            param: 'transformFile',
            type: 'func(file)',
            desc: 'promise对象，可将原始的file在上传之前进行转码',
            option: '',
            default: 'orginFile => orginFile'
        },
        {
            param: 'withCredentials',
            type: 'bool',
            desc: '上传请求时是否携带 cookie',
            option: '',
            default: 'false'
        },
        {
            param: 'method',
            type: 'string',
            desc: '请求的method，默认为post',
            option: '',
            default: 'post'
        },
        {
            param: 'reqData',
            type: 'object',
            desc: '上传请求额外的请求参数',
            option: '',
            default: ''
        },
        {
            param: 'customUploadListIcon',
            type: 'ReactNode',
            desc: 'listType为image的时候，可以自己定义浮层的操作',
            option: '',
            default: ''
        },
        {
            param: 'maxFileLength',
            type: 'number',
            desc: '当前允许可最大上传个数（报错的文件也算）3.0.14-beta-53支持',
            option: '',
            default: ''
        },
        {
            param: 'maxFileLengthErrorMessage',
            type: 'String | React Node',
            desc: '最大个数的报错话术 3.0.14-beta-53支持',
            option: '',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: '当listType为image、video、media时候，允许设置尺寸',
            option: 'small | medium',
            default: ''
        },
        {
            param: 'entries',
            type: 'array',
            desc: `多路径上传，只针对listType为image、video、media 生效
            {
                key: string,
                icon: node,
                label: node,
                chidren: array,
                disabled: bool,
                isDefaultUpload: bool
            }
            icon为需要展示的icon，label为展示的label，children针对存在children的下拉场景，
            disabled为禁用，isDefaultUpload为是否是默认是上传行为`,
            option: '',
            default: ''
        },
        {
            param: 'previewList',
            type: 'array',
            desc: '上传后预览的list，如果不传则使用thumbUrl进行兜底，具体值请参考lightbox',
            option: '',
            default: ''
        },
        {
            param: 'order',
            type: 'string',
            desc: 'anchor和list的顺序',
            option: '[normal, reverse]',
            default: 'normal'
        },
        {
            param: 'hideAnchor',
            type: 'boolean',
            desc: '是否隐藏anchor',
            option: '',
            default: 'false'
        },
        {
            param: 'formatUploadAnchor',
            type: 'func',
            desc: '格式化uploader的anchor',
            option: '',
            default: ''
        }
    ],
    UploaderRequestOption: [
        {
            param: 'action',
            type: 'string',
            desc: '请求的url',
            option: '',
            default: ''
        },
        {
            param: 'filename',
            type: 'string',
            desc: '上传文件的名称',
            option: '',
            default: ''
        },
        {
            param: 'reqData',
            type: 'object',
            desc: '请求中额外的参数',
            option: '',
            default: ''
        },
        {
            param: 'file',
            type: 'object',
            desc: '上传中放在formData中的file文件',
            option: '',
            default: ''
        },
        {
            param: 'headers',
            type: 'object',
            desc: '接口的headers',
            option: '',
            default: ''
        },
        {
            param: 'withCredentials',
            type: 'bool',
            desc: 'withCredentials',
            option: '',
            default: ''
        },
        {
            param: 'method',
            type: 'string',
            desc: 'method',
            option: '',
            default: ''
        },
        {
            param: 'onProgress',
            type: 'func(e)',
            desc: '上传中，e里面可以放percent表示当前的进度',
            option: '',
            default: ''
        },
        {
            param: 'onSuccess',
            type: 'func(ret, xhr)',
            desc: '上传成功后的回调',
            option: '',
            default: ''
        },
        {
            param: 'onError',
            type: 'func(err, ret)',
            desc: '上传失败后的回调',
            option: '',
            default: ''
        }
    ],
    UploaderOnChange: [{
        param: 'file',
        type: 'object',
        desc: '当前操作文件',
        option: '',
        default: ''
    }, {
        param: 'index',
        type: 'number',
        desc: '当前操作文件在fileList中的index',
        option: '',
        default: ''
    }, {
        param: 'fileList',
        type: 'array',
        desc: '文件列表',
        option: '',
        default: ''
    }, {
        param: 'response',
        type: 'object or array',
        desc: '不使用自定义uploader的话，请求onSuccess/onError会将上传请求的response传回',
        option: '',
        default: ''
    }, {
        param: 'err',
        type: 'object or array',
        desc: '不使用自定义uploader的话，请求onError会将上传请求的报错传回',
        option: '',
        default: ''
    }]
};
