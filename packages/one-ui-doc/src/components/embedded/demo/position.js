import React, {PureComponent} from 'react';
import {Embedded} from '@baidu/one-ui';

export default class Normal extends PureComponent {

    onClose = () => {
        console.log('关闭');
    };

    onOk = e => {
        console.log(e);
    }

    onCancel = e => {
        console.log(e);
    }

    render() {
        return (
            <div>
                <br />
                <br />
                按钮居中对齐
                <br />
                <br />
                <Embedded
                    title="标题位置"
                    onClose={this.onClose}
                    onOk={this.onOk}
                    onCancel={this.onCancel}
                    hideDefaultFooter={false}
                    position="center"
                >
                    <div style={{height: 150, textAglin: 'center', background: '#eee'}}>自定义内容区域</div>
                </Embedded>
                <br />
                <br />
                按钮居右对齐
                <br />
                <br />
                <Embedded
                    title="标题位置"
                    onClose={this.onClose}
                    onOk={this.onOk}
                    onCancel={this.onCancel}
                    hideDefaultFooter={false}
                    position="right"
                >
                    <div style={{height: 150, textAglin: 'center', background: '#eee'}}>自定义内容区域</div>
                </Embedded>
            </div>
        );
    }
}
