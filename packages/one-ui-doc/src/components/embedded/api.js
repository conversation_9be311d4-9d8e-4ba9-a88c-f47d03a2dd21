export default {
    Embedded: [
        {
            param: 'closable',
            type: 'boolean',
            desc: '嵌入式面板能否关闭',
            option: '',
            default: 'true'
        },
        {
            param: 'footer',
            type: 'array',
            desc: '自定义底部的footer按钮',
            option: '',
            default: 'null'
        },
        {
            param: 'onClose',
            type: 'Function(e)',
            desc: '面板关闭时触发',
            option: '',
            default: ''
        },
        {
            param: 'title',
            type: 'string|ReactNode',
            desc: '面板的标题',
            option: '',
            default: ''
        },
        {
            param: 'visible',
            type: 'boolean',
            desc: '面板是否可见',
            option: '',
            default: ''
        }, {
            param: 'onOk',
            type: 'function(e)',
            desc: '点击确认按钮触发',
            option: '',
            default: ''
        }, {
            param: 'onCancel',
            type: 'function(e)',
            desc: '点击取消按钮触发',
            option: '',
            default: ''
        }, {
            param: 'okProps',
            type: 'object',
            desc: '自定义确认按钮的props',
            option: '',
            default: ''
        }, {
            param: 'cancelProps',
            type: 'object',
            desc: '自定义取消按钮的props',
            option: '',
            default: ''
        }, {
            param: 'okOrder',
            type: 'number',
            desc: '确定按钮的排序，用于flex下，按钮的排序',
            option: '',
            default: 1
        }, {
            param: 'cancelOrder',
            type: 'number',
            desc: '取消按钮的排序，用于flex下，按钮的排序',
            option: '',
            default: 2
        }, {
            param: 'buttonSize',
            type: 'string | (参考button的size)',
            desc: '按钮的size',
            option: '',
            default: 'medium'
        }, {
            param: 'size',
            type: 'string (small | medium)',
            desc: '按钮的size',
            option: '',
            default: 'medium'
        },
        {
            param: 'okText',
            type: 'string | react Node',
            desc: '确认按钮的文案',
            option: '',
            default: '确认'
        }, {
            param: 'cancelText',
            type: 'string | react Node',
            desc: '取消按钮的文案',
            option: '',
            default: '取消'
        }, {
            param: 'className',
            type: 'string',
            desc: '自定义类名',
            option: '',
            default: ''
        },
        {
            param: 'position',
            type: 'string',
            desc: '按钮的对齐方式，默认左对齐',
            option: 'left | center | right',
            default: 'left'
        }
    ],
    EmbeddedHorizol: [
        {
            param: 'closable',
            type: 'boolean',
            desc: '嵌入式面板能否关闭',
            option: '',
            default: 'true'
        },
        {
            param: 'onClose',
            type: 'Function(e)',
            desc: '面板关闭时触发',
            option: '',
            default: ''
        },
        {
            param: 'visible',
            type: 'boolean',
            desc: '面板是否可见',
            option: '',
            default: ''
        }
    ]
};
