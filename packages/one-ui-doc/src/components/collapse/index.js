import BaseComponent from '../base';

export default {
    collapse: {
        value: BaseComponent,
        label: 'Collapse 折叠面板',
        demos: [
            {
                title: '普通',
                desc: '普通 - 折叠面板',
                source: 'normal'
            },
            {
                title: '尺寸',
                desc: '普通 - 折叠面板 - 尺寸',
                source: 'size'
            },
            {
                title: '非受控',
                desc: '普通 - 折叠面板 - defaultActiveKey 非受控',
                source: 'uncontrolled'
            },
            {
                title: '受控',
                desc: '普通 - 折叠面板 - activeKey 受控',
                source: 'controlled'
            },
            {
                title: '手风琴',
                desc: '普通 - 折叠面板 - 手风琴',
                source: 'accordion'
            },
            {
                title: '嵌套面板',
                desc: '普通 - 折叠面板 - 面板嵌套面板',
                source: 'collapse'
            }
        ],
        apis: [
            {
                apiKey: 'Collapse',
                title: 'Collapse'
            },
            {
                apiKey: 'CollapsePanel',
                title: 'Collapse.Panel'
            }
        ]
    }
};
