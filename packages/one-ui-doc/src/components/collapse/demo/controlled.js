import React, {PureComponent} from 'react';
import {Collapse} from '@baidu/one-ui';

const Panel = Collapse.Panel;

export default class NormalBadge extends PureComponent {
    state = {
        activeKey: ['1']
    }

    callback = key => {
        this.setState({
            activeKey: key
        });
    }

    render() {
        const text = `
            A dog is a type of domesticated animal.
            Known for its loyalty and faithfulness,
            it can be found as a welcome guest in many households across the world.
            `;
        return (
            <div>
                <Collapse activeKey={this.state.activeKey} onChange={this.callback}>
                    <Panel header="This is panel header 1" key="1">
                        <p>{text}</p>
                    </Panel>
                    <Panel header="This is panel header 2" key="2">
                        <p>{text}</p>
                    </Panel>
                    <Panel header="This is panel header 3" key="3" disabled>
                        <p>{text}</p>
                    </Panel>
                </Collapse>
            </div>
        );
    }
}
