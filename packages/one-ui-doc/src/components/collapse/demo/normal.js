import React, {PureComponent} from 'react';
import {Collapse, Checkbox, Radio, NumberInput} from '@baidu/one-ui';

const Panel = Collapse.Panel;

export default class NormalBadge extends PureComponent {
    callback = key => {
        console.log(key);
    }

    state = {
        bordered: 'default',
        dull: 'default',
        gutter: 12,
        type: 'normal',
        showExpandIcon: true,
        expandIconPosition: 'left',
        withGutter: false
    };

    render() {
        const text = `
            A dog is a type of domesticated animal.
            Known for its loyalty and faithfulness,
            it can be found as a welcome guest in many households across the world.
            `;
        const {
            bordered,
            gutter,
            type,
            dull,
            showExpandIcon,
            expandIconPosition,
            withGutter
        } = this.state;
        const groupStyle = {
            display: 'inline-block',
            verticalAlign: 'middle',
            marginLeft: 4,
            marginRight: 10
        };
        return (
            <div>
                type:
                <Radio.Group
                    size="small"
                    value={type}
                    style={groupStyle}
                    onChange={e => this.setState({type: e.target.value})}
                >
                    <Radio.Button value="normal" key="normal">normal</Radio.Button>
                    <Radio.Button value="simple" key="simple">simple</Radio.Button>
                    <Radio.Button value="basic" key="basic">basic</Radio.Button>
                    <Radio.Button value="strong" key="strong">strong</Radio.Button>
                </Radio.Group>
                bordered:
                <Radio.Group
                    size="small"
                    value={bordered}
                    style={groupStyle}
                    onChange={e => this.setState({bordered: e.target.value})}
                >
                    <Radio.Button value="default" key="null">null</Radio.Button>
                    <Radio.Button value key="true">true</Radio.Button>
                    <Radio.Button value={false} key="false">false</Radio.Button>
                </Radio.Group>
                <br />
                <br />
                dull:
                <Radio.Group
                    size="small"
                    value={dull}
                    style={groupStyle}
                    onChange={e => this.setState({dull: e.target.value})}
                >
                    <Radio.Button value="default" key="null">null</Radio.Button>
                    <Radio.Button value key="true">true</Radio.Button>
                    <Radio.Button value={false} key="false">false</Radio.Button>
                </Radio.Group>
                icon:
                <span>
                    <Checkbox
                        checked={showExpandIcon}
                        onChange={() => this.setState({showExpandIcon: !showExpandIcon})}
                    />
                </span>
                {showExpandIcon
                    && (
                        <Radio.Group
                            size="small"
                            value={expandIconPosition}
                            style={groupStyle}
                            onChange={e => this.setState({expandIconPosition: e.target.value})}
                        >
                            <Radio.Button value="left" key="left">left</Radio.Button>
                            <Radio.Button value="right" key="right">right</Radio.Button>
                        </Radio.Group>
                    )
                }
                gutter:
                <span>
                    <Checkbox
                        checked={withGutter}
                        onChange={() => this.setState({withGutter: !withGutter})}
                    />
                </span>
                {withGutter
                    && (
                        <NumberInput
                            size="small"
                            value={gutter}
                            min={0}
                            width={60}
                            onChange={e => this.setState({gutter: +e.target.value})}
                        />
                    )
                }
                <br />
                <br />
                <Collapse
                    bordered={bordered === 'default' ? null : bordered}
                    dull={dull === 'default' ? null : dull}
                    gutter={withGutter ? gutter : undefined}
                    type={type}
                    width={50}
                    showExpandIcon={showExpandIcon}
                    expandIconPosition={expandIconPosition}
                >
                    <Panel header="This is panel header 1" key="1">
                        <p>{text}</p>
                    </Panel>
                    <Panel header="This is panel header 2" key="2">
                        <p>{text}</p>
                    </Panel>
                    <Panel header="This is panel header 3" key="3" disabled>
                        <p>{text}</p>
                    </Panel>
                    <Panel header="This is panel header 4" key="4">
                        <p>{text}</p>
                    </Panel>
                </Collapse>
            </div>
        );
    }
}
