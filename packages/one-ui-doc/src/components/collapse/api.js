export default {
    Collapse: [{
        param: 'className',
        type: 'string',
        desc: '自定义className',
        option: '',
        default: ''
    }, {
        param: 'size',
        type: 'string',
        desc: '尺寸',
        option: 'small | medium',
        default: 'medium'
    }, {
        param: 'type',
        type: 'string',
        desc: '风格类型',
        option: 'normal | simple | basic | strong',
        default: 'normal'
    }, {
        param: 'bordered',
        type: 'bool',
        desc: '是否有边框',
        option: '',
        default: ''
    }, {
        param: 'dull',
        type: 'bool',
        desc: '标题是否不展示交互视觉反馈',
        option: '',
        default: ''
    }, {
        param: 'gutter',
        type: 'number',
        desc: '间距',
        option: '',
        default: ''
    }, {
        param: 'showExpandIcon',
        type: 'bool',
        desc: '是否显示展开图标',
        option: '',
        default: 'true'
    }, {
        param: 'expandIconPosition',
        type: 'string',
        desc: '展开图标位置',
        option: 'left | right',
        default: 'left'
    }, {
        param: 'disabled',
        type: 'bool',
        desc: '是否禁用，如果disabled为true，则折叠面板都禁用',
        option: '',
        default: 'false'
    }, {
        param: 'defaultActiveKey',
        type: 'string | [string]',
        desc: '非受控情况，默认展开的key',
        option: '',
        default: ''
    }, {
        param: 'activeKey',
        type: 'string | [string]',
        desc: '受控情况，展开的key',
        option: '',
        default: ''
    }, {
        param: 'onChange',
        type: 'onChange(key:string) | onChange(key:[string])',
        desc: '点击header的回调，返回key，如果accordion模式下，返回的key为string类型，其余情况下为[string]类型',
        option: '',
        default: ''
    }, {
        param: 'accordion',
        type: 'bool',
        desc: '是否手风琴模式，手风琴模式：展开的永远只有一个，如果受控模式下传入数组，取数组第一个',
        option: '',
        default: ''
    }, {
        param: 'destroyNotActivePanel',
        type: 'bool',
        desc: '收起的时候时候销毁panel',
        option: '',
        default: 'false'
    }, {
        param: 'style',
        type: 'object',
        desc: '自定义样式',
        option: '',
        default: ''
    }],
    CollapsePanel: [{
        param: 'header',
        type: 'string | node',
        desc: '标题dom',
        option: '',
        default: ''
    }, {
        param: 'headerClass',
        type: 'string',
        desc: '自定义标题dom的className',
        option: '',
        default: ''
    }, {
        param: 'style',
        type: 'object',
        desc: '自定义标题dom的style',
        option: '',
        default: ''
    }, {
        param: 'disabled',
        type: 'bool',
        desc: '该panel禁用',
        option: '',
        default: 'false'
    }, {
        param: 'renderDomWhenHide',
        type: 'bool',
        desc: '该dom隐藏的时候是否需要渲染dom',
        option: '',
        default: 'false'
    }]
};
