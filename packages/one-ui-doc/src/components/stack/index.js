import BaseComponent from '../base';

export default {
    stack: {
        value: BaseComponent,
        label: 'Stack 堆叠容器',
        demos: [
            {
                title: '基本用法',
                desc: '可以使用 {gap} 属性来设置堆叠项的间距。',
                source: 'normal'
            },
            {
                title: '纵向',
                desc: '设置 {direction} 为 {column} 可让堆叠项纵向排列。',
                source: 'direction'
            },
            {
                title: '对齐',
                desc: '设置 {align} 可指定堆叠项在垂直于 {direction} 对应方向上的对齐方式。',
                source: 'align'
            },
            {
                title: '分布',
                desc: '设置 {justify} 可指定堆叠项在 {direction} 对应方向上的分布方式。',
                source: 'justify'
            },
            {
                title: '换行',
                desc: '设置 {wrap} 可让堆叠项允许换行。',
                source: 'wrap'
            },
            {
                title: '子项分布',
                desc: '如果是数组指定的话，最多支持24个子项',
                source: 'distribution'
            }
        ],
        apis: [
            {
                apiKey: 'Stack',
                title: 'Stack'
            }
        ]
    }
};
