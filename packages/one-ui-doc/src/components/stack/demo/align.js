import React from 'react';
import {Stack} from '@baidu/one-ui';

const itemStyle = {
    display: 'flex',
    minWidth: '48px',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '12px',
    backgroundColor: 'rgb(242, 247, 255)',
    borderRadius: 4
};

const blockStyle = {
    padding: '2px',
    border: '1px dotted rgb(226, 230, 240)'
};

const Item = ({minHeight, align}) => <div style={{...itemStyle, minHeight}}>{align}</div>;

const Block = ({align}) => (
    <Stack
        style={blockStyle}
        gap="xsmall"
        align={align}
    >
        <Item minHeight={40} align={align} />
        <Item minHeight={80} align={align} />
        <Item minHeight={60} align={align} />
    </Stack>
);


export default () => (
    <Stack gap="large">
        <Block align="start" />
        <Block align="center" />
        <Block align="end" />
        <Block align="stretch" />
    </Stack>
);
