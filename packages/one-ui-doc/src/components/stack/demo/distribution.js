import React from 'react';
import {Stack} from '@baidu/one-ui';

const itemStyle = {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '12px',
    backgroundColor: 'rgb(242, 247, 255)',
    borderRadius: 4
};

const blockStyle = {
    padding: '2px',
    border: '1px dotted rgb(226, 230, 240)'
};

const Item = ({minHeight, tip}) => <div style={{...itemStyle, minHeight}}>{tip}</div>;

const Block = ({distribution}) => (
    <Stack
        style={blockStyle}
        gap="xsmall"
        distribution={distribution}
    >
        <Item minHeight={40} tip={distribution}/>
        <Item minHeight={80} tip={distribution}/>
        <Item minHeight={60} tip={distribution}/>
    </Stack>
);


export default () => (
    <div>
        <Block />
        <Block distribution="auto"/>
        <Block distribution="even"/>
        <Block distribution={[1, 2, 3]}/>
    </div>
);
