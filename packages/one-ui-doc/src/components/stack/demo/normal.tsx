import React, { useState } from 'react';
import {
    Stack,
    Radio,
    Button,
    StackGap
} from '@baidu/one-ui';

const spacings = [
    { label: '/', value: '' },
    { label: 'XXS', value: 'xxsmall' },
    { label: 'XS', value: 'xsmall' },
    { label: 'S', value: 'small' },
    { label: 'M', value: 'medium' },
    { label: 'L', value: 'large' },
    { label: 'XL', value: 'xlarge' },
    { label: 'XXL', value: 'xxlarge' }
];

export default () => {

    const [gap, setGap] = useState('');

    return (
        <Stack direction="column" gap="large">
            <Radio.Group
                value={gap}
                onChange={e => setGap(e.target.value)}
                size="small"
                type="strong"
                options={spacings}
            />
            <Stack gap={gap} direction="row">
                <Button type="primary">
                    Submit
                </Button>
                <Button type="normal">
                    Save
                </Button>
                <Button type="text">
                    Cancel
                </Button>
            </Stack>
        </Stack>
    );
};
