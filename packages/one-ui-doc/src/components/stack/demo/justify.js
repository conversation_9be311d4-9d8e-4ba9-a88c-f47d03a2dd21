import React from 'react';
import {Stack} from '@baidu/one-ui';

const itemStyle = {
    display: 'flex',
    minWidth: '48px',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '12px',
    backgroundColor: 'rgb(242, 247, 255)',
    borderRadius: 4
};

const blockStyle = {
    width: '35%',
    padding: '2px',
    border: '1px dotted rgb(226, 230, 240)'
};

const Item = ({minHeight, justify}) => <div style={{...itemStyle, minHeight}}>{justify}</div>;

const Block = ({justify}) => (
    <Stack
        style={blockStyle}
        gap="xsmall"
        justify={justify}
    >
        <Item minHeight={40} justify={justify} />
        <Item minHeight={80} justify={justify} />
        <Item minHeight={60} justify={justify} />
    </Stack>
);


export default () => (
    <Stack gap="large" wrap>
        <Block justify="start" />
        <Block justify="center" />
        <Block justify="end" />
        <Block justify="space-between" />
    </Stack>
);
