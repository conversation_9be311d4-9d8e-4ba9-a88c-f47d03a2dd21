import React, {useState} from 'react';
import {Transition, Radio} from '@baidu/one-ui';

const options = [
    {label: 'immediate', value: 100},
    {label: 'fast', value: 200},
    {label: 'normal', value: 300},
    {label: 'slow', value: 400}
];

export default () => {
    const [visible, setVisible] = useState(false);
    const [timeout, setTimeout] = useState(300);
    return (
        <>
            <div style={{display: 'flex', alignItems: 'center', marginBottom: 12}}>
                速度：<Radio.Group
                    type="strong"
                    size="small"
                    options={options}
                    value={timeout}
                    onChange={e => {
                        setTimeout(e.target.value);
                    }}
                />
            </div>
            <div
                style={{
                    height: 360,
                    backgroundColor: '#F2F7FF',
                    border: '1px solid #E2E6F0',
                    borderRadius: 4,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    overflow: 'hidden'
                }}
                onMouseEnter={() => setVisible(true)}
                onMouseLeave={() => setVisible(false)}
            >
                {visible
                    && (
                        <Transition
                            name="translate-gravity"
                            in
                            appear
                            style={{
                                '--dls-transition-from-x': '-400px',
                                '--dls-transition-from-y': '-150px'
                            }}
                            timeout={timeout}
                        >
                            <div
                                style={{
                                    width: 120,
                                    height: 120,
                                    borderRadius: 10,
                                    backgroundColor: '#fff',
                                    boxShadow: `0px 6px 28px 2px rgba(0, 0, 0, 0.04),
                                        0px 4px 26px 2px rgba(0, 0, 0, 0.03),
                                        0px 2px 24px 1px rgba(0, 0, 0, 0.02)`
                                }}
                            />
                        </Transition>
                    )
                }
            </div>
        </>
    );
};
