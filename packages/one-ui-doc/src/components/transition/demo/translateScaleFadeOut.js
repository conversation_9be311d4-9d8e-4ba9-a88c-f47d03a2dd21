import React, {useState} from 'react';
import {Transition, Radio} from '@baidu/one-ui';

const positions = {
    top: {'--dls-transition-to-y': '-300px'},
    right: {'--dls-transition-to-x': '300px'},
    bottom: {'--dls-transition-to-y': '300px'},
    left: {'--dls-transition-to-x': '-300px'}
};

const options = [
    {label: 'immediate', value: 100},
    {label: 'fast', value: 200},
    {label: 'normal', value: 300},
    {label: 'slow', value: 400}
];

export default () => {
    const [visible, setVisible] = useState(true);
    const [placement, setPlacement] = useState('top');
    const [timeout, setTimeoutVal] = useState(100);

    return (
        <>
            <div style={{display: 'flex', alignItems: 'center', marginBottom: 12}}>
                速度：
                <Radio.Group
                    type="strong"
                    size="small"
                    options={options}
                    value={timeout}
                    onChange={e => {
                        setTimeoutVal(e.target.value);
                    }}
                />
                <span style={{marginLeft: 12}}>方向：</span>
                <Radio.Group
                    type="strong"
                    size="small"
                    options={[
                        'top',
                        'right',
                        'bottom',
                        'left'
                    ]}
                    value={placement}
                    onChange={e => {
                        setVisible(true);
                        setPlacement(e.target.value);
                        setTimeout(() => {
                            setVisible(false);
                        }, 500);
                    }}
                    onMouseLeave={() => setVisible(true)}
                />
            </div>
            <div
                style={{
                    height: 360,
                    backgroundColor: '#F2F7FF',
                    border: '1px solid #E2E6F0',
                    borderRadius: 4,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    overflow: 'hidden'
                }}
                onMouseEnter={() => setVisible(false)}
                onMouseLeave={() => setVisible(true)}
            >
                <Transition
                    key={placement}
                    name="translate-scale-fade"
                    in={visible}
                    appear={false}
                    unmountOnExit
                    timeout={timeout}
                >
                    <div
                        style={{
                            ...(!visible ? positions[placement] : null),
                            width: 120,
                            height: 120,
                            borderRadius: 10,
                            backgroundColor: '#fff',
                            boxShadow: `0px 6px 28px 2px rgba(0, 0, 0, 0.04),
                                0px 4px 26px 2px rgba(0, 0, 0, 0.03),
                                0px 2px 24px 1px rgba(0, 0, 0, 0.02)`
                        }}
                    />
                </Transition>
            </div>
        </>
    );
};
