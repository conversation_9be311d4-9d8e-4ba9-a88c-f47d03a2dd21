import React, {useState} from 'react';
import {Transition, Radio} from '@baidu/one-ui';

const options = [
    {label: 'immediate', value: 100},
    {label: 'fast', value: 200},
    {label: 'normal', value: 300},
    {label: 'slow', value: 400}
];

export default () => {
    const [{x, y}, setPosition] = useState({x: '-200px', y: '0px'});
    const [timeout, setTimeout] = useState(100);

    return (
        <>
            <div style={{display: 'flex', alignItems: 'center', marginBottom: 12}}>
                速度：<Radio.Group
                    type="strong"
                    size="small"
                    options={options}
                    value={timeout}
                    onChange={e => {
                        setTimeout(e.target.value);
                    }}
                />
            </div>
            <div
                style={{
                    height: 360,
                    backgroundColor: '#F2F7FF',
                    border: '1px solid #E2E6F0',
                    borderRadius: 4,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    overflow: 'hidden'
                }}
                onMouseEnter={
                    () => setPosition({
                        x: '200px',
                        y: '0px'
                    })
                }
                onMouseLeave={
                    () => setPosition({
                        x: '-200px',
                        y: '0px'
                    })
                }
                onClick={e => {
                    if (e.currentTarget !== e.target) {
                        return;
                    }
                    const h = e.currentTarget.offsetHeight;
                    const w = e.currentTarget.offsetWidth;
                    setPosition({
                        x: `${e.nativeEvent.offsetX - w / 2}px`,
                        y: `${e.nativeEvent.offsetY - h / 2}px`
                    });
                }}
            >
                <Transition
                    name="move"
                    style={{
                        '--dls-transition-x': x,
                        '--dls-transition-y': y
                    }}
                    timeout={timeout}
                >
                    <div
                        style={{
                            width: 120,
                            height: 120,
                            borderRadius: 10,
                            backgroundColor: '#fff',
                            boxShadow: `0px 6px 28px 2px rgba(0, 0, 0, 0.04),
                                0px 4px 26px 2px rgba(0, 0, 0, 0.03),
                                0px 2px 24px 1px rgba(0, 0, 0, 0.02)`
                        }}
                    />
                </Transition>
            </div>
        </>
    );
};
