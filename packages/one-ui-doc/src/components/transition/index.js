import BaseComponent from '../base';

export default {
    transition: {
        value: BaseComponent,
        label: 'Transition 动效',
        demos: [
            {
                title: '渐现',
                desc: '',
                source: 'fadeIn'
            },
            {
                title: '放大渐现',
                desc: '',
                source: 'scaleFadeIn'
            },
            {
                title: '展开',
                desc: '',
                source: 'slideIn'
            },
            {
                title: '移入',
                desc: '',
                source: 'translateIn'
            },
            {
                title: '渐变移入',
                desc: '',
                source: 'translateScaleFadeIn'
            },
            {
                title: '渐变移入(曲线)',
                desc: '',
                source: 'translateGravityIn'
            },
            {
                title: '移动',
                desc: '通过调整`--dls-transition-x`，`--dls-transition-y`实现位置的变化',
                source: 'move'
            },
            {
                title: '渐隐',
                desc: '',
                source: 'fadeOut'
            },
            {
                title: '缩小渐隐',
                desc: '',
                source: 'scaleFadeOut'
            },
            {
                title: '收起',
                desc: '',
                source: 'slideOut'
            },
            {
                title: '移出',
                desc: '',
                source: 'translateOut'
            },
            {
                title: '渐变移出',
                desc: '',
                source: 'translateScaleFadeOut'
            },
            {
                title: '渐变移出(曲线)',
                desc: '',
                source: 'translateGravityOut'
            }
        ],
        apis: [
            {
                apiKey: 'Transition',
                title: 'Transition'
            },
            {
                apiKey: 'CSS',
                title: 'CSS变量',
                children: [
                    {
                        param: '--dls-transition-from-x',
                        desc: '进入X位置',
                        type: '<length>'
                    },
                    {
                        param: '--dls-transition-from-y',
                        desc: '进入Y位置',
                        type: '<length>'
                    },
                    {
                        param: '--dls-transition-to-x',
                        desc: '退出X位置',
                        type: '<length>'
                    },
                    {
                        param: '--dls-transition-to-y',
                        desc: '退出Y位置',
                        type: '<length>'
                    },
                    {
                        param: '--dls-transition-x',
                        desc: '当前X位置',
                        type: '<length>'
                    },
                    {
                        param: '--dls-transition-y',
                        desc: '当前Y位置',
                        type: '<length>'
                    },
                    {
                        param: '--dls-transition-duration-in',
                        desc: '进入时长',
                        type: '<time>'
                    },
                    {
                        param: '--dls-transition-duration-out',
                        desc: '退出时长',
                        type: '<time>'
                    }
                ]
            }
        ]
    }
};
