export default {
    Input: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义样式名',
            option: '',
            default: ''
        },
        {
            param: 'disabled',
            type: 'boolean',
            desc: '是否不可操作',
            option: '',
            default: 'false'
        },
        {
            param: 'width',
            type: 'number',
            desc: '指定宽度',
            option: '',
            default: 160
        },
        {
            param: 'value',
            type: 'string',
            desc: '输入框值-受控',
            option: '',
            default: ''
        },
        {
            param: 'defaultValue',
            type: 'string',
            desc: '输入框值-非受控',
            option: '',
            default: ''
        },
        {
            param: 'placeholder',
            type: 'string',
            desc: '在用户输入值之前显示的提示信息',
            option: '',
            default: ''
        },
        {
            param: 'isRequired',
            type: 'boolean',
            desc: '是否必填',
            option: '',
            default: 'true'
        },
        {
            param: 'requiredErrorMessage',
            type: 'string',
            desc: '当为空时的错误信息',
            option: '',
            default: ''
        },
        {
            param: 'minLen',
            type: 'number',
            desc: '最短字符长度',
            option: '',
            default: ''
        },
        {
            param: 'minLenErrorMessage',
            type: 'string',
            desc: '当比最短字符短时的错误信息',
            option: '',
            default: ''
        },
        {
            param: 'maxLen',
            type: 'number',
            desc: '最长字符长度',
            option: '',
            default: ''
        },
        {
            param: 'maxLenErrorMessage',
            type: 'string',
            desc: '当比最长字符长时的错误信息',
            option: '',
            default: ''
        },
        {
            param: 'showErrorMessage',
            type: 'boolean',
            desc: '是否显示错误信息，默认显示。可以设置组件不显示错误信息，在业务自定义错误信息位置。',
            option: '',
            default: 'true'
        },
        {
            param: 'errorMessage',
            type: 'string',
            desc: '错误信息；当不传此值时，会内部校验是否非空、最短字符、最长字符；当传此值时，错误信息以此值为准',
            option: '',
            default: ''
        },
        {
            param: 'errorLocation',
            type: 'string',
            desc: '错误信息位置',
            option: 'right、layer、bottom',
            default: 'right'
        },
        {
            param: 'countMode',
            type: 'string',
            desc: '计数方式<br />当为cn时，一个中文字符记为{1}个字符，当为en时一个中文字符记为{2}个字符<br />当计数时，前后空格会trim掉不计数，中间的空格会计数',
            option: 'cn、en',
            default: 'cn'
        },
        {
            param: 'filterArray',
            type: 'string[]',
            desc: '某些字符不计数',
            option: '',
            default: ''
        },
        {
            param: 'getLength',
            type: 'Function(value)',
            desc: '当countMode中两种校验方式都不满足时，自定义校验方式',
            option: '',
            default: ''
        },
        {
            param: 'onChange',
            type: 'Function({value: string, errorMessage: string})',
            desc: '变化时回调函数',
            option: '',
            default: ''
        },
        {
            param: 'readOnly',
            type: 'bool',
            desc: '是否只是可读',
            option: '',
            default: 'false'
        },
        {
            param: 'prefix',
            type: 'Icon Node',
            desc: '前缀-icon',
            option: '',
            default: ''
        },
        {
            param: 'suffix',
            type: 'Icon Node',
            desc: '后缀-icon',
            option: '',
            default: ''
        },
        {
            param: 'originInputProps',
            type: 'object',
            desc: '可直接挂载在input上的属性，比较粗暴，不建议使用',
            option: '',
            default: ''
        },
        {
            param: 'inputRef',
            type: 'Func(ref)',
            desc: 'input的ref',
            option: '',
            default: ''
        },
        {
            param: 'type',
            type: 'string',
            desc: 'normal or inline',
            option: '',
            default: 'normal'
        },
        {
            param: 'showClear',
            type: 'bool',
            desc: '是否支持能被清除',
            option: '',
            default: 'false'
        },
        {
            param: 'onClear',
            type: 'function',
            desc: '点击清除icon的时候的回调',
            option: '',
            default: '() => {}'
        }
    ],
    InputMethods: [
        {
            param: 'focus()',
            type: '',
            desc: '获取焦点',
            option: '',
            default: ''
        },
        {
            param: 'blur()',
            type: '',
            desc: '失去焦点',
            option: '',
            default: ''
        }
    ]
};
