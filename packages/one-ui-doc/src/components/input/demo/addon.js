import React from 'react';
import {Input, Select} from '@baidu/one-ui';

const {Option} = Select;

const selectBefore = (
    <Select size="medium" defaultValue="Http://" style={{width: 100}}>
        <Option value="Http://">Http://</Option>
        <Option value="Https://">Https://</Option>
    </Select>
);
const selectAfter = (
    <Select size="medium" defaultValue=".com" style={{width: 90}}>
        <Option value=".com">.com</Option>
        <Option value=".jp">.jp</Option>
        <Option value=".cn">.cn</Option>
        <Option value=".org">.org</Option>
    </Select>
);

export default () => (
    <>
        <Input addonBefore={selectBefore} addonAfter={selectAfter} />
        <br />
        <br />
        <Input addonBefore={selectBefore} />
        <br />
        <br />
        <Input addonAfter={selectAfter} />
    </>
);
