import React, {PureComponent} from 'react';
import {Input} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {
        value: '内容内容'
    };

    onChange = e => {
        this.setState({
            value: e.value
        });
    }

    render() {
        return (
            <div>
                <div>内联样式</div>
                <br />
                <br />
                <div>
                    <Input
                        onChange={this.onChange}
                        value={this.state.value}
                        type="inline"
                        maxLen={20}
                    />
                </div>
            </div>
        );
    }
}
