import React, {PureComponent} from 'react';
import {Input, IconSvg} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {
        value: 'jjj'
    };

    onChange = e => {
        console.log(e.value);
        this.setState({
            value: e.value
        });
    }

    render() {
        return (
            <div>
                <div>前缀</div>
                <br />
                <br />
                <br />
                <Input
                    onChange={this.onChange}
                    value={this.state.value}
                    maxLen={10}
                    prefix={(<IconSvg type="calendar" />)}
                />
                <br />
                <br />
                <div>后缀</div>
                <br />
                <br />
                <br />
                <Input
                    onChange={this.onChange}
                    value={this.state.value}
                    maxLen={10}
                    suffix={(<IconSvg type="calendar" />)}
                />
                <br />
                <br />
                <div>前缀</div>
                <br />
                <br />
                <br />
                <Input
                    type="inline"
                    onChange={this.onChange}
                    value={this.state.value}
                    maxLen={10}
                    prefix={(<IconSvg type="calendar" />)}
                />
                <br />
                <br />
                <div>后缀</div>
                <br />
                <br />
                <br />
                <Input
                    type="inline"
                    onChange={this.onChange}
                    value={this.state.value}
                    maxLen={10}
                    suffix={(<IconSvg type="calendar" />)}
                />
            </div>
        );
    }
}
