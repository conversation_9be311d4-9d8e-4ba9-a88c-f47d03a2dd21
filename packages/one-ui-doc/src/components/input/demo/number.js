import React from 'react';
import {Input} from '@baidu/one-ui';

const getLength = str => {
    if (/[0-9]/i.test(str)) {
        return str.match(/[0-9]/ig).length;
    };
    return 0;
};

export default () => {
    return (<div>
        <div>需要显示输入内容的计数信息，默认汉字作为2个字符计数</div>
        <br />
        <Input maxLen={10} minLen={2} placeholder="请输入" errorMessage="" />
        <br />
        <br />
        <div>汉字作为1个字符计数</div>
        <br />
        <Input maxLen={10} countMode="en" placeholder="请输入" />
        <br />
        <br />
        <div>自定义计数规则，比如只记输入数字的长度</div>
        <br />
        <Input maxLen={10} getLength={getLength} placeholder="请输入" />
        <br />
        <br />
        <div>当没有最大值时，不显示输入内容的计数信息</div>
        <br />
        <Input placeholder="请输入" />
    </div>);
};

