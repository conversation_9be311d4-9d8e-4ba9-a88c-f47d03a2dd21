import React from 'react';
import {
    Input,
    Select,
    Dropdown,
    Cascader,
    Button,
    SearchBox,
    NumberInput,
    TextArea,
    TimePicker,
    DatePicker,
    Uploader,
    Radio,
    Checkbox,
    Pagination
} from '@baidu/one-ui';

export default () => {
    return (
        <>
            <Input.Group>
                <Input />
                <Input />
                <Input />
            </Input.Group>
            <br />
            <Input.Group>
                <Select />
                <Select />
            </Input.Group>
            <br />
            <Input.Group>
                <Dropdown.Button options={[]} title="请选择" />
                <Input />
                <Dropdown.Button options={[]} title="请选择" />
            </Input.Group>
            <br />
            <Input.Group>
                <Cascader />
                <Input />
            </Input.Group>
            <br />
            <Input.Group>
                <NumberInput />
                <Input />
            </Input.Group>
            <br />
            <Input.Group>
                <Input />
                <Button type="primary">确定</Button>
            </Input.Group>
            <br />
            <Input.Group>
                <TimePicker />
                <Input />
            </Input.Group>
            <br />
            <Input.Group>
                <DatePicker />
                <Input />
            </Input.Group>
            <br />
            <Input.Group>
                <SearchBox />
                <Button type="primary">搜索</Button>
            </Input.Group>
        </>
    );
};

