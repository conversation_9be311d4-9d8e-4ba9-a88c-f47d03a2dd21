import React, {useRef} from 'react';
import {Input, Button, Stack} from '@baidu/one-ui';

export default function FocusOrBlur() {
    const inputRef = useRef(null);

    return (
        <>
            <Stack gap="small" style={{marginBottom: 16}}>
                <Button onClick={() => inputRef.current.focus()}>Focus</Button>
                <Button onClick={() => inputRef.current.blur()}>Blur</Button>
            </Stack>
            <Input
                ref={inputRef}
            />
        </>
    ); 
}
