import React, {PureComponent} from 'react';
import {Input} from '@baidu/one-ui';

export default class ErrorMessage extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            errorMessage: undefined,
            value: ''
        };
    }

    onChange = e => {
        const value = e.value;
        this.setState({
            value,
            errorMessage: (value.indexOf('%') > -1 ? '含有特殊字符' : undefined)
        });
    }

    onFocus = e => {
        console.info(e);
    }

    onBlur = e => {
        console.info(e);
    }

    render() {
        const inputProps = {
            maxLen: 20,
            onChange: this.onChange,
            value: this.state.value,
            errorMessage: this.state.errorMessage,
            onFocus: this.onFocus,
            onBlur: this.onBlur
        };
        return <Input {...inputProps} />;
    }
}
