import BaseComponent from '../base';

export default {
    input: {
        value: BaseComponent,
        label: 'Input 单行文本输入框',
        demos: [
            {
                title: '受控',
                desc: '受控',
                source: 'controlled'
            },
            {
                title: '非受控',
                desc: '非受控',
                source: 'uncontrolled'
            },
            {
                title: '计数',
                desc: '是否展示输入内容的计数信息，当有最大可输入值时才展示输入内容的计数信息',
                source: 'number'
            },
            {
                title: '尺寸',
                desc: 'Input目前支持四种尺寸xsmall, small, medium, large',
                source: 'size'
            },
            {
                title: '错误信息',
                desc: '',
                source: 'errorMessage'
            },
            {
                title: '错误位置',
                desc: '{右方} {下方} {浮层}',
                source: 'errorLocation'
            },
            {
                title: '禁用',
                desc: '',
                source: 'disabled'
            },
            {
                title: '宽度',
                desc: '',
                source: 'width'
            },
            {
                title: '分组',
                desc: '',
                source: 'group'
            },
            {
                title: '组合输入',
                desc: '建议使用{Input.Group}代替该功能',
                source: 'addon'
            },
            {
                title: '前后缀',
                desc: '前缀 + 后缀',
                source: 'prefix'
            },
            {
                title: '清空',
                desc: '支持清空',
                source: 'showClear'
            },
            {
                title: '内联样式',
                desc: 'input输入框内联样式',
                source: 'inline'
            },
            {
                title: '必填',
                desc: 'input required 时的展现',
                source: 'required'
            },
            {
                title: '带下拉选择',
                desc: '带下拉选择的input请参考select的用法',
                source: 'select'
            },
            {
                title: '获取/失去焦点',
                desc: '',
                source: 'FocusOrBlur'
            }
        ],
        apis: [
            {
                apiKey: 'Input',
                title: 'Input'
            },

            {
                apiKey: 'InputGroup',
                title: 'Input.Group'
            },
            {
                apiKey: 'InputMethods',
                title: 'Methods'
            }
        ]
    }
};
