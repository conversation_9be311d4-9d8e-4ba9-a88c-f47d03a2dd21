import BaseComponent from '../base';

export default {
    rating: {
        value: BaseComponent,
        label: 'Rating 评分',
        demos: [
            {
                title: '默认值',
                desc: '',
                source: 'defaultValue'
            },
            {
                title: '更新回调',
                desc: '',
                source: 'onChange'
            },
            {
                title: '个数',
                desc: '',
                source: 'max'
            },
            {
                title: '只读',
                desc: '',
                source: 'readOnly'
            },
            {
                title: '半选',
                desc: '',
                source: 'allowHalf'
            },
            {
                title: '清空',
                desc: '在此点击已选项可清空',
                source: 'allowClear'
            },
            {
                title: '标签展示',
                desc: '',
                source: 'labels'
            },
            {
                title: '气泡提示',
                desc: '',
                source: 'labelDisplay'
            },
            {
                title: '自定义',
                desc: '',
                source: 'symbol'
            },
            {
                title: '表情变体',
                desc: '',
                source: 'emoji'
            }
        ],
        apis: [
            {
                apiKey: 'Rating',
                title: 'Rating'
            },
            {
                apiKey: 'RatingEmoji',
                title: 'Rating.Emoji'
            }
        ]
    }
};
