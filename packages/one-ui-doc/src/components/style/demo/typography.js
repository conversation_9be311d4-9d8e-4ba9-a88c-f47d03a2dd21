import React, {useState} from 'react';
import {Checkbox, Slider} from '@baidu/one-ui';

export default () => {

    const [value, setValue] = useState(20170320);
    const [tabular, setTabular] = useState(false);

    return (
        <>
            <div style={{display: 'flex', gap: 24, alignItems: 'center'}}>
                <strong
                    style={{
                        fontFamily: '"Baidu Number", sans-serif',
                        fontVariantNumeric: tabular ? 'tabular-nums' : 'inherit',
                        fontSize: 32
                    }}
                >
                    {value}
                </strong>
                <Checkbox
                    checked={tabular}
                    onChange={e => setTabular(e.target.checked)}
                >
                    等宽
                </Checkbox>
            </div>
            <Slider
                value={value}
                onChange={value => setValue(value)}
                step={1}
                min={19000101}
                max={21001231}
                style={{maxWidth: 260}}
            />
        </>
    );
}