import React from 'react';
import Highlight from 'react-highlight';
import Title from '../../common/title';
import {Link} from '@baidu/one-ui';
import Demo from '../../common/demo';
import {menu} from '../../config';
import './style.less';

const importCSS = file =>
`// css
@import '~@baidu/one-ui/lib/${file}.css';

// js
import '@baidu/one-ui/lib/${file}.css';
`;

export default props => {
    // eslint-disable-next-line react/prop-types
    const id = props.id;
    const {label: title} = menu[id] || {};
    const config = `module.exports = {
    rules: [{
        test: /\\.less$/,
        use: [
            {
                loader: 'style-loader',
            }, {
                loader: 'css-loader',
            }, {
                loader: 'less-loader',
                options: {
                modifyVars: {
                    @dls-border-radius-s: '2px';
                    @dls-border-radius-m: '3px';
                    @dls-border-radius-l: '4px';
                    ...
                },
                javascriptEnabled: true,
            }
        }]
    }]
}`;
    const str1 = '@import "~@baidu/one-ui/src/index.less"';
    const str2 = '@import "~@baidu/one-ui/lib/index.css";';
    return (
        <div>
            <Title title={title} />
            <div className="doc">
                <h2>基础样式</h2>
                <p>需要先全局引入基础样式，主要包括样式的 normalize 及一些基本元素的样式。</p>
                <Highlight className="javascript">
                    {importCSS('common')}
                </Highlight>
                <h2>组件样式</h2>
                <Highlight className="javascript">
                    {importCSS('index')}
                </Highlight>
                <h2>滚动条样式</h2>
                <Highlight className="javascript">
                    {importCSS('scrollbar')}
                </Highlight>
                <h2>数字字体</h2>
                <Demo
                    title={title}
                    id="style"
                    source="typography"
                    inline
                />
                组件库自带一款展示型的数字字体“Baidu Number”，但由于启用后会自动加载 Web 字体，故默认未包含在全局样式中。
                需要使用时请在项目中手动引入：
                <Highlight className="javascript">
                    {importCSS('typography')}
                </Highlight>
                引入后可在 CSS 中使用名为 "Baidu Number" 的字体。
                同时在动态数字场景下，往往需要数字等宽以保证布局的相对稳定，
                此时可以设置 font-variant-numeric: tabular-nums 以开启字体中的对应 Open Type 功能。
                <Highlight className="css">
                    {`.heading-numbers {
    font-family: "Baidu Number", sans-serif;
    font-variant-numeric: tabular-nums; /* 等宽场景 */
}`}
                </Highlight>
                <h2>主题定制</h2>
                需要定制皮肤的话在顶层样式目录引用less
                <Highlight className="css">
                    {str1}
                </Highlight>
                在webpack中对less-loader 的 options 属性进行相应配置，以达到自定义皮肤的目的
                <Highlight className="javascript">
                    {config}
                </Highlight>
                 样式变量您可以先查阅：
                <Link
                    type="strong"
                    target="_blank"
                    toUrl="https://github.com/ecomfe/light-dls/tree/master/packages/less-plugin-dls/tokens"
                >
                    D20 皮肤token
                </Link>
            </div>
        </div>
    );
};
