import React from 'react';
import Highlight from 'react-highlight';
import Title from '../../common/title';
import {menu} from '../../config';

export default props => {
    // eslint-disable-next-line react/prop-types
    const id = props.id;
    const {label: title} = menu[id] || {};

    const config = `
import React, {PureComponent} from 'react';
import {ConfigProvider} from '@baidu/one-ui';
import Size from './dlsTest';


export default class Normal extends PureComponent {
    render() {
        return (
            <ConfigProvider
                size="small"
                prefixCls="one"
                normalized
                table={{loadingOption: {type: 'spinner'}}}
            >
                <div>
                    <Size />
                </div>
            </ConfigProvider>
        );
    }
}

// dlsTest.js
import React, {PureComponent} from 'react';
import {Nav} from '@baidu/one-ui';

const datasource = [
    {
        label: '导航1',
        key: '1'
    },
    {
        label: '导航2',
        key: '2'
    },
    {
        label: '导航3',
        key: '3'
    },
    {
        label: '导航4',
        key: '4'
    },
    {
        label: '导航5',
        key: '5',
        disabled: true
    },
    {
        label: '导航6',
        key: '6'
    },
    {
        label: '导航7',
        key: '7'
    },
    {
        label: '导航8',
        key: '8'
    },
    {
        label: '导航9',
        key: '9'
    }
];

export default class Normal extends PureComponent {

    const columns = [
        {
            title: '名称(提示)',
            dataIndex: 'name',
            key: 'name'
        }, {
            title: 'Age',
            dataIndex: 'age',
            key: 'age'
        }
    ];
    Array(10).fill(0).map((item, index) => ({
        key: index,
        name: 'John Brown' + index,
        age: 100 - index
    }));

    render() {
        return (
            <div>
                <Nav defaultValue="1" dataSource={datasource} />
                <div>
                    <Tag>Normalized Tag</Tag>
                </div>
                <Table
                    columns={columns}
                    dataSource={data}
                />
            </div>
        );
    }
}
`;
    return (
        <div>
            <Title title={title} />
            <div className="doc">
                <div>
                    支持可以通过配置顶层context，定制全局参数，目前支持prefixCls、size、normalized和表格的table.loadingOption.type
                </div>
                <div>
                    <Highlight className="javascript">
                        {config}
                    </Highlight>
                </div>
            </div>
        </div>
    );
};
