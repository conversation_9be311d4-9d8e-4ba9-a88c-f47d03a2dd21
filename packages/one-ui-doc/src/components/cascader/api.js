export default {
    Cascader: [
        {
            param: 'className',
            type: 'string',
            desc: '自定义样式名',
            option: '',
            default: ''
        },
        {
            param: 'allowClear',
            type: 'boolean',
            desc: '是否支持清除',
            option: '',
            default: 'true'
        },
        {
            param: 'changeOnSelect',
            type: 'boolean',
            desc: '当此选项为true时，点选每级菜单选项值都会发生变化',
            option: '',
            default: ''
        },
        {
            param: 'defaultValue',
            type: 'string []',
            desc: '默认的选中项',
            option: '',
            default: ''
        },
        {
            param: 'disabled',
            type: 'boolean',
            desc: '禁用',
            option: '',
            default: 'false'
        },
        {
            param: 'displayRender',
            type: '(label, selectedOptions) => ReactNode',
            desc: '选择后展示的渲染函数',
            option: '',
            default: "label => label.join(' / ')"
        },
        {
            param: 'getPopupContainer',
            type: 'Function(triggerNode)',
            desc: '弹层渲染父节点。默认渲染到 body 上',
            option: '',
            default: '() => document.body'
        },
        {
            param: 'loadData',
            type: '(selectedOptions) => void',
            desc: '用于动态加载选项，无法与 showSearch 一起使用',
            option: '',
            default: ''
        },
        {
            param: 'notFoundContent',
            type: 'string',
            desc: '当下拉列表为空时显示的内容',
            option: '',
            default: '未找到合适的选项'
        },
        {
            param: 'options',
            type: 'array [{value: , label: , disabled: , children: }]',
            desc: '可选项数据源',
            option: '',
            default: ''
        },
        {
            param: 'placeholder',
            type: 'string',
            desc: '输入框占位文本',
            option: '',
            default: '请选择'
        },
        {
            param: 'showSearch',
            type: 'boolean',
            desc: '在选择框中显示搜索框',
            option: '',
            default: 'false'
        },
        {
            param: 'size',
            type: 'string',
            desc: '尺寸',
            option: 'small|medium',
            default: 'medium'
        },
        {
            param: 'value',
            type: 'string []',
            desc: '指定选中项',
            option: '',
            default: ''
        },
        {
            param: 'onChange',
            type: '(value, selectedOptions) => void',
            desc: '选择完成后的回调',
            option: '',
            default: ''
        },
        {
            param: 'onPopupVisibleChange',
            type: '(visible) => void',
            desc: '显示/隐藏浮层的回调',
            option: '',
            default: ''
        },
        {
            param: 'header',
            type: 'ReactNode | () => ReactNode',
            desc: '头部自定义区',
            option: '',
            default: ''
        },
        {
            param: 'footer',
            type: 'ReactNode | () => ReactNode',
            desc: '尾部自定义区',
            option: '',
            default: ''
        },
        {
            param: 'columnHeader',
            type: 'ReactNode | (columnIndex, parentOption) => ReactNode',
            desc: '列头部自定义区',
            option: '',
            default: ''
        },
        {
            param: 'columnFooter',
            type: 'ReactNode | (columnIndex, parentOption) => ReactNode',
            desc: '列尾部自定义区',
            option: '',
            default: ''
        }
    ]
};
