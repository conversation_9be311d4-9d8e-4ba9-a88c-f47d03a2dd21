import React, { useState } from 'react';
import {Cascader} from '@baidu/one-ui';
import {IconLocation} from 'dls-icons-react';

const options = [
    {
        value: '北京',
        label: '北京',
        children: [
            {
                value: '海淀区',
                label: '海淀区',
                children: [
                    {
                        value: '后厂村',
                        label: '后厂村'
                    },
                    {
                        value: '上地',
                        label: '上地'
                    }
                ]
            }
        ]
    },
    {
        value: '湖南省',
        label: '湖南省',
        children: [
            {
                value: '长沙市',
                label: '长沙市',
                children: [
                    {
                        value: '岳麓山',
                        label: '岳麓山'
                    },
                    {
                        value: '橘子洲',
                        label: '橘子洲'
                    }
                ]
            }
        ]
    }
];

export default () => {
    const [val, setVal] = useState([]);
    const [vals, setVals] = useState([]);
    return (
        <>
            <Cascader
                options={options}
                before={<IconLocation active />}
                style={{width: 300}}
                showSearch
                placeholder="请选择地域"
                allowClear={false}
                displayRender={() => null}
                popupWidthStretch="min-width"
                value={val}
                onChange={setVal}
            />
            {' '}
            {val.join(',')}
            <br />
            <br />
            <Cascader
                options={options}
                before={<IconLocation active />}
                style={{width: 300}}
                showSearch
                placeholder="请选择地域"
                allowClear={false}
                multiple
                displayRender={() => null}
                popupWidthStretch="min-width"
                value={vals}
                onChange={setVals}
            />
            {' '}
            {vals.join(',')}
        </>
    );
};
