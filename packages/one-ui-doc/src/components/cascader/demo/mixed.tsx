import React, {PureComponent} from 'react';
import {Cascader, Checkbox} from '@baidu/one-ui';

const options = [
    {
        value: 'beijing',
        label: '线索收集（支持组合）',
        selectable: false,
        children: [
            {
                value: 'haidian',
                label: '电话类',
                children: [
                    {
                        value: 'houchangcun',
                        label: 'ct1'
                    }
                ]
            },
            {
                value: 'haidia1n',
                label: '表单类',
                children: [
                    {
                        value: 'houchang2cun',
                        label: 'ct2'
                    }
                ]
            }
        ]
    },
    {
        value: 'hunan',
        label: '线索收集（不支持组合）',
        selectable: false,
        children: [
            {
                value: 'changsha',
                label: '综合线索收集',
                exclusive: true
            },
            {
                value: 'changsha1',
                label: 'xxx线索收集',
                exclusive: true
            },
            {
                value: 'changsha2',
                label: '回访意向发现',
                exclusive: true,
                disabled: true
            }
        ]
    }
];

class DemoCascader extends PureComponent {

    onChange = value => {
        console.log(value);
    }
    render() {
        return (
            <div>
                <Cascader
                    style={{width: 300}}
                    options={options}
                    onChange={this.onChange}
                    placeholder="Please select"
                    showSearch
                    allowClear
                    multiple
                />
            </div>
        );
    }
}

export default function NoramlCascader() {
    return (
        <div>
            混选
            <br />
            <br />
            <DemoCascader />
        </div>
    );
}