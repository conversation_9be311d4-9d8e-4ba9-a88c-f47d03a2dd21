function columnHeader(columnIndex, parentOption) {
    return '自定义列头' + (parentOption ? parentOption.label : '');
}

function columnFooter(columnIndex, parentOption) {
    return '自定义列尾' + (parentOption ? parentOption.label : '');
}

import React, {PureComponent} from 'react';
import {Cascader} from '@baidu/one-ui';

const options = [
    {
        value: 'beijing',
        label: '北京',
        children: [
            {
                value: 'haidian',
                label: '海淀区',
                children: [
                    {
                        value: 'houchangcun',
                        label: '后厂村'
                    }
                ]
            }
        ]
    },
    {
        value: 'hunan',
        label: '湖南省',
        children: [
            {
                value: 'changsha',
                label: '长沙市',
                children: [
                    {
                        value: 'yuelushan',
                        label: '岳麓山'
                    },
                    {
                        value: 'AAA',
                        label: 'AAA'
                    },
                    {
                        value: 'BBB',
                        label: 'BBB'
                    },
                    {
                        value: 'CCC',
                        label: 'CCC'
                    },
                    {
                        value: 'DDD',
                        label: 'DDD'
                    },
                    {
                        value: 'EEE',
                        label: 'EEE'
                    },
                    {
                        value: 'FFF',
                        label: 'FFF'
                    }
                ]
            }
        ]
    }
];

export default class NoramlCascader extends PureComponent {
    onChange = value => {
        console.log(value);
    }

    render() {
        return (
            <div>
                <Cascader
                    defaultValue={['beijing', 'haidian', 'houchangcun']}
                    options={options}
                    onChange={this.onChange}
                    columnHeader={columnHeader}
                    columnFooter={columnFooter}
                    header="顶部自定义"
                    footer="底部自定义"
                />
            </div>
        );
    }
}
