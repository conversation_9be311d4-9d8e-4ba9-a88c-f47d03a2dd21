import React, {PureComponent} from 'react';
import {Cascader} from '@baidu/one-ui';

const options = [
    {
        value: 'beijing',
        label: '北京',
        isLeaf: false
    },
    {
        value: 'hunan',
        label: '湖南省',
        isLeaf: false
    }
];

export default class NoramlCascader extends PureComponent {
    state = {
        options
    };

    onChange = (value, selectedOptions) => {
        console.log(value, selectedOptions);
    };

    loadData = selectedOptions => {
        const targetOption = selectedOptions[selectedOptions.length - 1];
        const options = this.state.options;
        targetOption.loading = true;
        setTimeout(() => {
            targetOption.loading = false;
            targetOption.children = [
                {
                    label: `${targetOption.label} Dynamic 1`,
                    value: 'dynamic1'
                },
                {
                    label: `${targetOption.label} Dynamic 2`,
                    value: 'dynamic2'
                }
            ];
            this.setState({
                options: [...options]
            });
        }, 3000);
    };

    render() {
        return (
            <Cascader
                options={this.state.options}
                loadData={this.loadData}
                onChange={this.onChange}
                changeOnSelect
            />
        );
    }
}
