import React, {useState} from 'react';
import {Cascader} from '@baidu/one-ui';

const options = [
    {
        label: '浙江',
        value: '浙江',
        children: [
            {
                label: '杭州',
                value: '杭州'
            },
            {
                label: '温州',
                value: '温州'
            },
            {
                label: '嘉兴',
                value: '嘉兴'
            },
            {
                label: '绍兴',
                value: '绍兴'
            }
        ]
    },
    {
        label: '安徽',
        value: '安徽',
        children: [
            {
                label: '合肥',
                value: '合肥'
            },
            {
                label: '芜湖',
                value: '芜湖'
            }
        ]
    },
    {
        label: '上海',
        value: '上海',
        children: [
            {
                label: '浦东',
                value: '浦东'
            },
            {
                label: '徐汇',
                value: '徐汇'
            }
        ]
    },
    {
        label: '山东',
        value: '山东',
        children: [
            {
                label: '菏泽',
                value: '菏泽'
            },
            {
                label: '潍坊',
                value: '潍坊'
            },
            {
                label: '泰山',
                value: '泰山'
            },
            {
                label: '烟台',
                value: '烟台'
            }
        ]
    }
];

export default () => {
    const [value, setValue] = useState([]);
    return (
        <Cascader
            options={options}
            value={value}
            onChange={value => setValue(value)}
            showCheckAll
            displayOptions={items => {
                if (items.length === 16) { // 简单模拟一下全选
                    return [{
                        label: '全部',
                        value: Cascader.ALL_VALUE
                    }];
                }
                const rets = options
                    .map(option => {
                        if (items.find(item => option.value === item.value)) {
                            return {
                                value: option.value,
                                label: `${option.label}全部`,
                                count: option.children ? option.children.length : 1
                            };
                        }
                        const len = items.filter(item => option.children.find(o => o.value === item.value)).length;
                        if (len > 0) {
                            return {
                                value: option.value,
                                label: `${option.label}${len}个区县`,
                                count: len
                            };
                        }
                        return null;
                    })
                    .filter(Boolean);
                const max = 2;
                if (rets.length > max) {
                    return rets
                        .slice(0, max)
                        .concat({
                            label: `+${rets.slice(max - 1).reduce((c, o) => c + (o.count ? o.count : 1), 0) - max}个市`,
                            value: 'more',
                            deletable: false
                        });
                }
                return rets;
            }}
            multiple
            style={{width: 400}}
        />
    );
};
