import React, {PureComponent} from 'react';
import {Cascader} from '@baidu/one-ui';
const options = [
    {
        value: 'beijing',
        label: '北京',
        aggreative: [
            {
                value: 'haidian',
                label: '海淀区',
                aggreative: [
                    {
                        value: 'houchangcun',
                        label: '后厂村'
                    }
                ]
            }
        ]
    },
    {
        value: 'hunan',
        label: '湖南省',
        aggreative: [
            {
                value: 'changsha',
                label: '长沙市',
                aggreative: [
                    {
                        value: 'yuelushan',
                        label: '岳麓山'
                    }
                ]
            }
        ]
    }
];
export default class NoramlCascader extends PureComponent {
    onChange = value => {
        console.log(value);
    }
    render() {
        return (
            <div>
                <Cascader
                    options={options}
                    onChange={this.onChange}
                    placeholder="Please select"
                    fieldNames={{value: 'value', label: 'label', children: 'aggreative'}}
                />
            </div>
        );
    }
}