import React, {PureComponent} from 'react';
import {<PERSON>r, Button} from '@baidu/one-ui';

const options = [
    {
        value: 'beijing',
        label: '北京',
        children: [
            {
                value: 'haidian',
                label: '海淀区',
                disabled: true,
                children: [
                    {
                        value: 'houchangcun',
                        label: '后厂村'
                    }
                ]
            }
        ]
    },
    {
        value: 'hunan',
        label: '湖南省',
        children: [
            {
                value: 'changsha',
                label: '长沙市',
                children: [
                    {
                        value: 'yuelushan',
                        label: '岳麓山'
                    }
                ]
            }
        ]
    }
];

export default class NoramlCascader extends PureComponent {
    onChange = value => {
        console.log(value);
    }

    render() {
        return (
            <div>
                <Cascader
                    options={options}
                    onChange={this.onChange}
                    placeholder="Please select"
                    triggerTarget={<Button type="ghost">请选择</Button>}
                />
            </div>
        );
    }
}
