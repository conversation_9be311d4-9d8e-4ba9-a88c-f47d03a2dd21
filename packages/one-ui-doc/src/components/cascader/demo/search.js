import React, {PureComponent} from 'react';
import {Cascader} from '@baidu/one-ui';

const options = [
    {
        value: 'beijing',
        label: '北京',
        children: [
            {
                value: 'haidian',
                label: '海淀区',
                children: [
                    {
                        value: 'houchangcun',
                        label: '后厂村'
                    }
                ]
            }
        ]
    },
    {
        value: 'hunan',
        label: '湖南省',
        children: [
            {
                value: 'changsha',
                label: '长沙市',
                children: [
                    {
                        value: 'yuelushan',
                        label: '岳麓山'
                    }
                ]
            }
        ]
    }
];

export default class NoramlCascader extends PureComponent {
    onChange = value => {
        console.log(value);
    }

    filter = (inputValue, path) => {
        return path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
    }

    render() {
        return (
            <div>
                <Cascader
                    options={options}
                    onChange={this.onChange}
                    placeholder="Please select"
                    showSearch={{filter: this.filter}}
                />
            </div>
        );
    }
}
