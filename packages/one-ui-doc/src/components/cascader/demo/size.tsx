import React, {useState} from 'react';
import {Cascader, Radio} from '@baidu/one-ui';
import type {CascaderProps} from '@baidu/one-ui';

const options = [
    {
        value: 'beijing',
        label: '北京',
        children: [
            {
                value: 'haidian',
                label: '海淀区',
                children: [
                    {
                        value: 'houchangcun',
                        label: '后厂村'
                    }
                ]
            }
        ]
    },
    {
        value: 'hunan',
        label: '湖南省',
        children: [
            {
                value: 'changsha',
                label: '长沙市',
                children: [
                    {
                        value: 'yuelushan',
                        label: '岳麓山'
                    }
                ]
            }
        ]
    }
];

export default () => {
    type Size = CascaderProps['size'];
    const [size, setSize] = useState<Size>('medium');
    return (
        <>
            <Radio.Group
                type="strong"
                size="small"
                value={size}
                options={['xsmall', 'small', 'medium', 'large']}
                onChange={e => setSize(e.target.value as Size)}
            />
            <br />
            <Cascader
                size={size}
                options={options}
            />
        </>
    );
}
