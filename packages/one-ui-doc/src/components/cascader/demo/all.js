import React, {PureComponent} from 'react';
import {Cascader, Checkbox} from '@baidu/one-ui';

const options = [
    {
        label: '浙江',
        value: '浙江',
        children: [
            {
                label: '威海',
                value: '威海'
            },
            {
                label: '滨州',
                value: '滨州'
            },
            {
                label: '临沂',
                value: '临沂'
            },
            {
                label: '东营',
                value: '东营'
            },
            {
                label: '济南',
                value: '济南'
            }
        ]
    },
    {
        label: '山东',
        value: '山东',
        children: [
            {
                label: '菏泽',
                value: '菏泽',
                children: [
                    {
                        label: '菏泽1',
                        value: '菏泽1'
                    }
                ]
            },
            {
                label: '潍坊',
                value: '潍坊',
                children: [
                    {
                        label: '潍坊1',
                        value: '潍坊1'
                    }
                ]
            },
            {
                label: '泰山',
                value: '泰山'
            },
            {
                label: '烟台',
                value: '烟台'
            },
            {
                label: '华山',
                value: '华山'
            },
            {
                label: '衡山',
                value: '衡山'
            },
            {
                label: '嵩山',
                value: '嵩山'
            },
            {
                label: '恒山',
                value: '恒山'
            },
            {
                label: '大雪山',
                value: '大雪山'
            }
        ]
    },
    {
        label: '上海',
        value: '上海',
        disabled: true
    },
    {
        label: '北京',
        value: '北京'
    },
    {
        label: '海外',
        value: '海外',
        disabled: true,
        children: [
            {
                label: '日本',
                value: '日本'
            }
        ]
    }
];

function columnHeader(columnIndex, parentOption) {
    return '自定义列头' + (parentOption ? parentOption.label : '');
}

function columnFooter(columnIndex, parentOption) {
    return '自定义列尾' + (parentOption ? parentOption.label : '');
}

class DemoCascader extends PureComponent {
    state = {
        showSearch: false,
        allowClear: true,
        changeOnSelect: false,
        expandTrigger: 'click',
        firstColumnGroup: false,
        disabled: false,
        displayParent: true,
        showHeaderFooter: false
    };

    onChange = value => {
        console.log(value);
    }
    renderFlags() {
        const {
            showSearch,
            allowClear,
            changeOnSelect,
            expandTrigger,
            firstColumnGroup,
            disabled,
            showCheckAll,
            displayParent,
            showHeaderFooter,
            displayRender
        } = this.state;
        const multiple = this.props.multiple;
        return (
            <>
                <Checkbox
                    checked={showSearch}
                    onChange={() => this.setState({showSearch: !showSearch})}
                >可搜索</Checkbox>
                <Checkbox
                    checked={allowClear}
                    onChange={() => this.setState({allowClear: !allowClear})}
                >可清除</Checkbox>
                {!multiple
                    && (
                        <Checkbox
                            checked={changeOnSelect}
                            onChange={() => this.setState({changeOnSelect: !changeOnSelect})}
                        >可选任意层级</Checkbox>
                    )
                }
                {multiple
                    && (
                        <Checkbox
                            checked={firstColumnGroup}
                            onChange={() => this.setState({firstColumnGroup: !firstColumnGroup})}
                        >分组</Checkbox>
                    )
                }
                {multiple
                    && (
                        <Checkbox
                            checked={showCheckAll}
                            onChange={() => this.setState({showCheckAll: !showCheckAll})}
                        >全选</Checkbox>
                    )
                }
                <Checkbox
                    checked={expandTrigger === 'hover'}
                    onChange={() => this.setState({
                        expandTrigger: expandTrigger === 'click' ? 'hover' : 'click'
                    })}
                >hover展开</Checkbox>
                {!multiple
                    && (
                        <Checkbox
                            checked={displayParent}
                            onChange={() => this.setState({displayParent: !displayParent})}
                        >展示父级</Checkbox>
                    )
                }
                <Checkbox
                    checked={disabled}
                    onChange={() => this.setState({disabled: !disabled})}
                >禁用</Checkbox>
                <Checkbox
                    checked={showHeaderFooter}
                    onChange={() => this.setState({showHeaderFooter: !showHeaderFooter})}
                >展示头部/底部</Checkbox>
                <Checkbox
                    checked={displayRender}
                    onChange={() => this.setState({displayRender: !displayRender})}
                >自定义结果渲染</Checkbox>
            </>
        );
    }
    render() {
        const {
            showSearch,
            allowClear,
            changeOnSelect,
            expandTrigger,
            firstColumnGroup,
            disabled,
            showCheckAll,
            displayParent,
            showHeaderFooter,
            displayRender
        } = this.state;
        const multiple = this.props.multiple;
        return (
            <div>
                {this.renderFlags()}
                <br />
                <br />
                <Cascader
                    style={{width: 300}}
                    options={options}
                    onChange={this.onChange}
                    placeholder="Please select"
                    showSearch={showSearch}
                    allowClear={allowClear}
                    changeOnSelect={changeOnSelect}
                    expandTrigger={expandTrigger}
                    multiple={multiple}
                    firstColumnGroup={firstColumnGroup}
                    showCheckAll={showCheckAll}
                    disabled={disabled}
                    displayParent={displayParent}
                    columnHeader={showHeaderFooter ? columnHeader : undefined}
                    columnFooter={showHeaderFooter ? columnFooter : undefined}
                    header={showHeaderFooter ? '头部演示' : undefined}
                    footer={showHeaderFooter ? '底部演示' : undefined}
                    displayRender={displayRender ? labels => labels.join(' / ') : undefined}
                />
            </div>
        );
    }
}

export default function NoramlCascader() {
    return (
        <div>
            单选
            <br />
            <br />
            <DemoCascader />
            <br />
            <br />
            多选
            <br />
            <br />
            <DemoCascader multiple />
        </div>
    );
}