import BaseComponent from '../base';

export default {
    cascader: {
        value: BaseComponent,
        label: 'Cascader 级联器',
        demos: [
            {
                title: '综合示例',
                desc: '综合示例',
                source: 'all'
            },
            {
                title: '单/复选混合',
                desc: '可在多选{mutiple}模式下(不可开启全选)，通过选项增加{exclusive}属性来进行排他选择，从而实现单/复选混合。',
                source: 'mixed'
            },
            {
                title: '普通级联器',
                desc: '普通级联器',
                source: 'normal'
            },
            {
                title: '默认值',
                desc: '展示默认值',
                source: 'default'
            },
            {
                title: '禁用',
                desc: '禁用内部某些选项',
                source: 'disabledItem'
            },
            {
                title: '合并展示',
                desc: '通过`mergeChecked`设置合并展示方式，默认`downwards`，其他可选值为`keep-all` | `downwards` | `upwards`.<br />'
                    + '由于已选value与展示行为早期已不一致，所以value保持`keep-all`不变',
                source: 'mergeChecked'
            },
            {
                title: '自定义展示选项',
                desc: '通过`displayOptions`回调函数返回自定义展示选项',
                source: 'displayOptions'
            },
            {
                title: '展示最末一级',
                desc: 'hover时候即出现下一层，并且最终展示最末一级',
                source: 'showLastOne'
            },
            {
                title: '实时展现',
                desc: '选择下一层级的时候，实时展现',
                source: 'showCurrent'
            },
            {
                title: '尺寸',
                desc: '两种不同尺寸',
                source: 'size'
            },
            {
                title: '搜索',
                desc: '支持内部搜索',
                source: 'search'
            },
            {
                title: '自定义前置区',
                desc: '通过`before`自定义前置区',
                source: 'before'
            },
            {
                title: '动态加载数据',
                desc: '动态加载数据',
                source: 'dynamic'
            },
            {
                title: '自定义options',
                desc: '使用fieldNames自定义options的格式',
                source: 'fieldNames'
            },
            {
                title: '自定义区域',
                desc: '支持整体头/尾, 每个Menu头/尾定制',
                source: 'custom'
            },
            {
                title: '自定义触发区',
                desc: '支持自定义触发区customTrigger',
                source: 'customTrigger'
            }
        ],
        apis: [
            {
                apiKey: 'Cascader',
                title: 'Cascader'
            }
        ]
    }
};
