import React, {PureComponent} from 'react';
import {Pagination} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {
        pageNo: 1,
        pageSize: 10
    };

    onChange = e => {
        this.setState({
            pageNo: +e.target.value
        });
    }

    render() {
        const state = this.state;
        return (
            <>
                <br />
                <br />
                <Pagination
                    total={200}
                    showSizeChange={false}
                    showTotal
                    pageNo={state.pageNo}
                    onPageNoChange={this.onChange}
                />
                <br />
                <br />
                <Pagination
                    total={200}
                    showTotal
                    pageNo={state.pageNo}
                    onPageNoChange={this.onChange}
                />
                <br />
                <br />
                <Pagination
                    total={0}
                    showTotal
                    pageNo={state.pageNo}
                    onPageNoChange={this.onChange}
                />
            </>
        );
    }
}
