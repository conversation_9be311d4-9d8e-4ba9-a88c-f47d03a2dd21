import React, {PureComponent} from 'react';
import {Pagination} from '@baidu/one-ui';

export default class Normal extends PureComponent {

    onShowSizeChange = e => {
        console.log(e);
    }

    onChange = e => {
        console.log(e);
    }

    render() {
        return (
            <div>
                <div>
                    <div style={{marginBottom: '20px'}}>非完全受控分页</div>
                    <Pagination
                        onPageSizeChange={this.onShowSizeChange}
                        total={200}
                        onPageNoChange={this.onChange}
                    />
                </div>
            </div>
        );
    }
}
