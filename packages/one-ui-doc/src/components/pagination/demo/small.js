import React, {PureComponent} from 'react';
import {Pagination} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {
        pageNo: 1,
        pageSize: 10
    };

    onChange = e => {
        this.setState({
            pageNo: +e.target.value
        });
    }

    render() {
        const state = this.state;
        return (
            <div>
                <div>
                    <Pagination
                        total={200}
                        pageNo={state.pageNo}
                        onPageNoChange={this.onChange}
                        size="xsmall"
                    />
                    <br />
                    <br />
                    <br />
                    <Pagination
                        total={200}
                        pageNo={state.pageNo}
                        onPageNoChange={this.onChange}
                        size="small"
                    />
                    <br />
                    <br />
                    <br />
                    <Pagination
                        total={200}
                        pageNo={state.pageNo}
                        onPageNoChange={this.onChange}
                        size="medium"
                    />
                </div>
            </div>
        );
    }
}
