import React, {PureComponent} from 'react';
import {Pagination} from '@baidu/one-ui';

export default class Normal extends PureComponent {
    state = {
        pageNo: 1,
        pageSize: 10,
        total: 200
    };

    onShowSizeChange = e => {
        this.setState({
            pageSize: +e.target.value,
            pageNo: 1,
            total: 200
        });
    }

    onChange = e => {
        this.setState({
            pageNo: +e.target.value
        });
    }

    render() {
        const state = this.state;
        return (
            <div>
                <div>
                    <div style={{marginBottom: '20px'}}>完全受控分页</div>
                    <Pagination
                        onPageSizeChange={this.onShowSizeChange}
                        total={state.total}
                        pageNo={state.pageNo}
                        pageSize={state.pageSize}
                        pageSizeOptions={[10, 20, 30]}
                        onPageNoChange={this.onChange}
                    />
                </div>
            </div>
        );
    }
}
