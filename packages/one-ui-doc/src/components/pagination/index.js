import BaseComponent from '../base';

export default {
    pagination: {
        value: BaseComponent,
        label: 'Pagination 分页器',
        demos: [
            {
                title: '受控',
                desc: '普通分页器 - 受控分页',
                source: 'normal'
            },
            {
                title: '尺寸',
                desc: '分页器-尺寸',
                source: 'small'
            },
            {
                title: '总计',
                desc: '分页器-总计',
                source: 'total'
            },
            {
                title: '非受控',
                desc: '普通分页器 - 非受控分页',
                source: 'uncontrolled'
            }
        ],
        apis: [
            {
                apiKey: 'Pagination',
                title: 'Pagination'
            }
        ]
    }
};
