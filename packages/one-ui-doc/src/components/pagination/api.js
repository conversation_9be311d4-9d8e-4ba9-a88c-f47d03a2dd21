export default {
    Pagination: [
        {
            param: 'total',
            type: 'number',
            desc: '当前总数',
            option: '',
            default: '0'
        },
        {
            param: 'pageNo',
            type: 'number',
            desc: '当前页数pageNo，受控属性',
            option: '',
            default: '1'
        },
        {
            param: 'defaultPageNo',
            type: 'number',
            desc: '默认的页数pageNo，非受控属性',
            option: '',
            default: '1'
        },
        {
            param: 'pageSize',
            type: 'number',
            desc: '当前页码pageSize，受控属性',
            option: '',
            default: '20'
        },
        {
            param: 'defaultPageSize',
            type: 'number',
            desc: '默认页码pageSize，非受控属性',
            option: '',
            default: '20'
        },
        {
            param: 'hideOnSinglePage',
            type: 'boolean',
            desc: '是否隐藏分页器，如果只有一页的情况',
            option: '',
            default: 'false'
        },
        {
            param: 'showSizeChange',
            type: 'boolean',
            desc: '是否展示分页器，页码下拉选择',
            option: '',
            default: 'true'
        },
        {
            param: 'onPageNoChange',
            type: 'Function(e) 传出e.target.value 为选择的pageNo',
            desc: '页数change的时候触发的函数， 传出e',
            option: '',
            default: ''
        },
        {
            param: 'onPageSizeChange',
            type: 'Function(e) 传出e.target.value 为选择的pageSize',
            desc: '页码改变的时候触发的函数, 传出e',
            option: '',
            default: ''
        },
        {
            param: 'pageSizeOptions',
            type: 'string []',
            desc: '页码选项',
            option: '',
            default: "['20', '50', '100']"
        },
        {
            param: 'showPageJumper',
            type: 'boolean',
            desc: '是否展示跳页',
            option: '',
            default: 'true'
        },
        {
            param: 'showPageJumper',
            type: 'boolean',
            desc: '是否展示跳页',
            option: '',
            default: 'true'
        },
        {
            param: 'size',
            type: 'string',
            desc: '分页器尺寸 xsmall, small, medium',
            option: 'xsmall, small, medium',
            default: 'medium'
        },
        {
            param: 'selectProps',
            type: 'object',
            desc: '自定义分页器的页码下拉选择器的属性',
            option: '',
            default: ''
        },
        {
            param: 'selectWidth',
            type: 'object',
            desc: '下拉选择器（页码选择）的宽度',
            option: '',
            default: '64'
        }
    ]
};
