import React, {useState} from 'react';
import {Anchor, Radio} from '@baidu/one-ui';

const Link = Anchor.Link;
const links = [
    {
        title: '基础',
        href: '#demo-基础'
    },
    {
        title: '简洁',
        href: '#demo-简洁'
    },
    {
        title: '容器',
        href: '#demo-容器'
    },
    {
        title: 'API',
        href: '#apis',
        children: [
            {
                title: 'Anchor',
                href: '#api-Anchor'
            },
            {
                title: 'Anchor.Link',
                href: '#api-Anchor.Link'
            }
        ]
    }
];

export default () => {
    const [size, setSize] = useState('small');
    return (
        <>
            <Radio.Group
                options={['small', 'medium']}
                value={size}
                onChange={e => setSize(e.target.value)}
                type="strong"
                size="small"
            />
            <br />
            <Anchor size={size} style={{backgroundColor: '#fff', width: 150}}>
                {links.map(({title, href, children}) => (
                    <Link href={href} title={title} key={href}>
                        {children
                            ? children.map(
                                ({title, href}) => (<Link href={href} title={title} key={href} />)
                            )
                            : null
                        }
                    </Link>
                ))}
            </Anchor>
        </>
    );
};
