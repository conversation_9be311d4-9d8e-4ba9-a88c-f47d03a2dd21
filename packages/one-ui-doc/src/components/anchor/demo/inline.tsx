import React from 'react';
import {Anchor} from '@baidu/one-ui';

const Link = Anchor.Link;

const links = [
    {
        title: '基础',
        href: '#demo-基础'
    },
    {
        title: '简洁',
        href: '#demo-简洁'
    },
    {
        title: '容器',
        href: '#demo-容器'
    },
    {
        title: 'API',
        href: '#apis',
        children: [
            {
                title: 'Anchor',
                href: '#api-Anchor'
            },
            {
                title: 'Anchor.Link',
                href: '#api-Anchor.Link'
            }
        ]
    }
];

export default () => (
    <div style={{marginLeft: 300, width: 150}}>
        <Anchor type="inline" style={{backgroundColor: '#fff'}}>
            {links.map(({title, href, children}) => (
                <Link href={href} title={title} key={href}>
                    {children
                        ? children.map(
                            ({title, href}) => (<Link href={href} title={title} key={href} />)
                        )
                        : null
                    }
                </Link>
            ))}
        </Anchor>
    </div>
);
