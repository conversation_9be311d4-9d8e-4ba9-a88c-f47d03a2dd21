import React from 'react';
import {Anchor, Stack} from '@baidu/one-ui';

const Link = Anchor.Link;

const colors = ['green', 'blue', 'red'];

export default () => (
    <Stack align="start" gap={20}>
        <Anchor
            getContainer={() => document.getElementById('small-container')}
        >
            {
                colors.map(
                    color => (
                        <Link href={`#small-container-${color}`} title={color} key={color} />
                    )
                )
            }
        </Anchor>
        <div
            id="small-container"
            style={{
                width: 700,
                height: 300,
                overflow: 'scroll'
            }}
        >
            {
                colors.map(
                    backgroundColor => (
                        <div
                            key={backgroundColor}
                            id={`small-container-${backgroundColor}`}
                            style={{height: 300, backgroundColor, opacity: .5}}
                        />
                    )
                )
            }
        </div>
    </Stack>
);
