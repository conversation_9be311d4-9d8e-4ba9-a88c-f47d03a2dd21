import BaseComponent from '../base';

export default {
    anchor: {
        value: BaseComponent,
        label: 'Anchor 锚点',
        demos: [
            {
                title: '尺寸',
                desc: '可通过`size`设置，可选值`medium` | `small`',
                source: 'size'
            },
            {
                title: '简洁',
                desc: '可通过`inline`开启无左侧边框的简洁样式',
                source: 'inline'
            },
            {
                title: '容器',
                desc: '可通过`getContainer`指定滚动容器',
                source: 'getContainer'
            }
        ],
        apis: [
            {
                apiKey: 'Anchor',
                title: 'Anchor'
            },
            {
                apiKey: 'AnchorLink',
                title: 'Anchor.Link'
            }
        ]
    }
};
