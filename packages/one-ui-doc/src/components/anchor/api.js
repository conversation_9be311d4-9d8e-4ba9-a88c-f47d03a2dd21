export default {
    Anchor: [
        {
            param: 'className',
            type: 'string',
            desc: '可以自定义anchor的className',
            option: '',
            default: ''
        },
        {
            param: 'style',
            type: 'object',
            desc: '可以自定义anchor的style',
            option: '',
            default: ''
        },
        {
            param: 'affix',
            type: 'bool',
            desc: '固定模式，常用语吸顶',
            option: '',
            default: ''
        },
        {
            param: 'getContainer',
            type: 'func',
            desc: '指定滚动的容器',
            option: '',
            default: '() => window'
        },
        {
            param: 'offsetTop',
            type: 'number',
            desc: '距离窗口（容器）顶部达到指定偏移量后触发',
            option: '',
            default: ''
        },
        {
            param: 'onClick',
            type: 'Function(e: Event, link: Object)',
            desc: 'click 事件的 handler',
            option: '',
            default: ''
        },
        {
            param: 'size',
            type: 'string',
            desc: 'anchor的尺寸，提供{small} {medium}，默认是{small}',
            option: '',
            default: 'small'
        }
    ],
    AnchorLink: [
        {
            param: 'className',
            type: 'string',
            desc: '可以自定义anchor.Link的className',
            option: '',
            default: ''
        },
        {
            param: 'href',
            type: 'string',
            desc: '锚点对应的#id',
            option: '',
            default: ''
        },
        {
            param: 'title',
            type: 'string|ReactNode',
            desc: '文字内容',
            option: '',
            default: ''
        },
        {
            param: 'isALabel',
            type: 'bool',
            desc: 'link是否是A标签',
            option: '',
            default: 'false'
        }
    ]
};
