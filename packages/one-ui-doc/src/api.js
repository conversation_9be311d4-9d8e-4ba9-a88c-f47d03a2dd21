import checkboxApi from './components/checkbox/api';
import radioApi from './components/radio/api';
import switchApi from './components/switch/api';
import inputApi from './components/input/api';
import searchApi from './components/searchBox/api';
import numberBox<PERSON>pi from './components/numberInput/api';
import textAreaApi from './components/textArea/api';
import transferApi from './components/transfer/api';
import timePickerApi from './components/timePicker/api';
import treeApi from './components/tree/api';
import affixApi from './components/affix/api';
import BadgeApi from './components/badge/api';
import BreadcrumbApi from './components/breadcrumb/api';
import ButtonApi from './components/button/api';
import CarouselApi from './components/carousel/api';
import CascaderlApi from './components/cascader/api';
import DrawerlApi from './components/drawer/api';
import DropdownApi from './components/dropdown/api';
import GridApi from './components/grid/api';
import LoadingApi from './components/loading/api';
import TagApi from './components/tag/api';
import PaginationApi from './components/pagination/api';
import PopoverApi from './components/popover/api';
import StepsApi from './components/steps/api';
import TextLinkApi from './components/link/api';
import TooltipApi from './components/tooltip/api';
import MenuApi from './components/menu/api';
import ToastApi from './components/toast/api';
import ModalApi from './components/dialog/api';
import SelectApi from './components/select/api';
import SliderApi from './components/slider/api';
import ProgressApi from './components/progress/api';
import AlertApi from './components/alert/api';
import AnchorApi from './components/anchor/api';
import CollapseApi from './components/collapse/api';
import BackTopApi from './components/backTop/api';
import OverlayApi from './components/overlay/api';
import DatePickerApi from './components/datePicker/api';
import UploaderApi from './components/uploader/api';
import TableApi from './components/table/api';
import FormApi from './components/form/api';
import TabsApi from './components/tabs/api';
import CascaderPaneApi from './components/cascaderPane/api';
import NavApi from './components/nav/api';
import LightboxApi from './components/lightbox/api';
import SidenavApi from './components/sidenav/api';
import SortableApi from './components/sortable/api';
import LayoutApi from './components/layout/api';
import TransitionApi from './components/transition/api';
import typeApi from './type.api';

export default {
    ...checkboxApi,
    ...radioApi,
    ...switchApi,
    ...inputApi,
    ...searchApi,
    ...numberBoxApi,
    ...textAreaApi,
    ...transferApi,
    ...timePickerApi,
    ...treeApi,
    ...affixApi,
    ...BadgeApi,
    ...BreadcrumbApi,
    ...ButtonApi,
    ...CarouselApi,
    ...CascaderlApi,
    ...DrawerlApi,
    ...DropdownApi,
    ...GridApi,
    ...LoadingApi,
    ...TagApi,
    ...PaginationApi,
    ...PopoverApi,
    ...StepsApi,
    ...TextLinkApi,
    ...TooltipApi,
    ...MenuApi,
    ...ToastApi,
    ...ModalApi,
    ...SelectApi,
    ...SliderApi,
    ...TableApi,
    ...ProgressApi,
    ...AlertApi,
    ...AnchorApi,
    ...TabsApi,
    ...FormApi,
    ...CollapseApi,
    ...BackTopApi,
    ...OverlayApi,
    ...DatePickerApi,
    ...UploaderApi,
    ...CascaderPaneApi,
    ...NavApi,
    ...LightboxApi,
    ...SidenavApi,
    ...SortableApi,
    ...LayoutApi,
    ...TransitionApi,
    ...typeApi
};
