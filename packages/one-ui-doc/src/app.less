@import '../../one-ui/src/common.less';
@import '../../one-ui/src/scrollbar.less';
@import '../../one-ui/src/typography.less';
@import '../../one-ui/src/index.ai.less';
@import './components/carousel/demo/index.less';

.main-header {
    background: #050527;
    padding: 0 32px;
    height: 50px;
    display: flex;
    align-items: center;
    .main-header-logo {
        flex: 1;
        font-weight: 600;
        font-size: 16px;
    }
    a {
        color: #fff;
        text-decoration: none;
    }
    .main-header-nav a {
        margin-left: 30px;
    }
    .doc-search {
        width: 160px;
        pointer-events: none;
    }
}

body {
    font-weight: 400;
    font-size: 14px;
    background-color: #f6f7fa;
    color: #333;
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica Neue, Arial, PingFang SC, Hiragino Sans GB, Microsoft YaHei, sans-serif;
}

.demo-controls {
    & > * {
        margin-left: 0!important;
    }
    & > label {
        margin-right: 8px!important;
    }
    & > *:not(:last-child) {
        margin-right: 16px;
    }
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.demo-group {
    & > * + * {
        margin-left: 16px;
    }
    & + & {
        margin-top: 16px;
    }
}
