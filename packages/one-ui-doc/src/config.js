import indexConfig from './components/index';
import styleConfig from './components/style';
import dynamicConfig from './components/dynamic';
import devConfig from './components/dev';
import logConfig from './components/log';
import themeConfig from './components/theme';
import focusVisibleConfig from './components/focusVisible';
import checkboxConfig from './components/checkbox';
import radioConfig from './components/radio';
import switchConfig from './components/switch';
import inputConfig from './components/input';
import searchConfig from './components/searchBox';
import numberBoxConfig from './components/numberInput';
import textAreaConfig from './components/textArea';
import transferConfig from './components/transfer';
import timePicker from './components/timePicker';
import treeConfig from './components/tree';
import affixConfig from './components/affix';
import badgeConfig from './components/badge';
import breadcrumbConfig from './components/breadcrumb';
import buttonConfig from './components/button';
import carouselConfig from './components/carousel';
import cascaderConfig from './components/cascader';
import drawerConfig from './components/drawer';
import dropdownConfig from './components/dropdown';
import gridConfig from './components/grid';
import tagConfig from './components/tag';
import loadingConfig from './components/loading';
import paginationApi from './components/pagination';
import popoverApi from './components/popover';
import stepsApi from './components/steps';
import textLinkApi from './components/link';
import tooltipApi from './components/tooltip';
import menuApi from './components/menu';
import toastApi from './components/toast';
import modalApi from './components/dialog';
import selectApi from './components/select';
import tableApi from './components/table';
import sliderConfig from './components/slider';
import progressApi from './components/progress';
import alertApi from './components/alert';
import anchorApi from './components/anchor';
import collapseApi from './components/collapse';
import BackTopApi from './components/backTop';
import OverlayApi from './components/overlay';
import UploaderApi from './components/uploader';
import formApi from './components/form';
import tabsApi from './components/tabs';
import datePicker from './components/datePicker';
import cascaderPaneApi from './components/cascaderPane';
import navConfig from './components/nav';
import lightboxConfig from './components/lightbox';
import contextApi from './components/provider';
import sidenavApi from './components/sidenav';
import sortableApi from './components/sortable';
import layoutApi from './components/layout';
import transitionApi from './components/transition';
import messageApi from './components/message';
import emptyApi from './components/empty';
import stackApi from './components/stack';
import cardApi from './components/card';
import ratingApi from './components/rating';

export const menu = {
    ...indexConfig,
    ...styleConfig,
    ...dynamicConfig,
    ...devConfig,
    ...logConfig,
    ...checkboxConfig,
    ...radioConfig,
    ...switchConfig,
    ...inputConfig,
    ...searchConfig,
    ...numberBoxConfig,
    ...textAreaConfig,
    ...transferConfig,
    ...timePicker,
    ...treeConfig,
    ...affixConfig,
    ...badgeConfig,
    ...breadcrumbConfig,
    ...buttonConfig,
    ...carouselConfig,
    ...cascaderConfig,
    ...drawerConfig,
    ...dropdownConfig,
    ...gridConfig,
    ...loadingConfig,
    ...tagConfig,
    ...paginationApi,
    ...popoverApi,
    ...progressApi,
    ...stepsApi,
    ...textLinkApi,
    ...tooltipApi,
    ...menuApi,
    ...toastApi,
    ...modalApi,
    ...selectApi,
    ...sliderConfig,
    ...alertApi,
    ...anchorApi,
    ...collapseApi,
    ...BackTopApi,
    ...OverlayApi,
    ...datePicker,
    ...UploaderApi,
    ...tabsApi,
    ...tableApi,
    ...formApi,
    ...focusVisibleConfig,
    ...cascaderPaneApi,
    ...navConfig,
    ...contextApi,
    ...lightboxConfig,
    ...sidenavApi,
    ...sortableApi,
    ...layoutApi,
    ...transitionApi,
    ...messageApi,
    ...emptyApi,
    ...stackApi,
    ...cardApi,
    ...ratingApi,
    ...themeConfig
};

export const introMenu = {
    ...indexConfig,
    ...styleConfig,
    ...themeConfig,
    ...dynamicConfig,
    ...devConfig,
    ...logConfig,
    ...focusVisibleConfig,
    ...contextApi
};

export const basicMenu = {
    ...buttonConfig,
    ...textLinkApi,
    ...cascaderPaneApi
};

export const navMenu = {
    ...menuApi,
    ...treeConfig,
    ...stepsApi,
    ...anchorApi,
    ...BackTopApi,
    ...breadcrumbConfig,
    ...paginationApi,
    ...navConfig,
    ...sidenavApi
};

export const dataInputMenu = {
    ...inputConfig,
    ...textAreaConfig,
    ...numberBoxConfig,
    ...searchConfig,
    ...radioConfig,
    ...checkboxConfig,
    ...switchConfig,
    ...selectApi,
    ...cascaderConfig,
    ...timePicker,
    ...datePicker,
    ...sliderConfig,
    ...transferConfig
};

export const dataShowMenu = {
    ...tagConfig,
    ...carouselConfig
};

export const containerMenu = {
    ...modalApi,
    ...drawerConfig,
    ...dropdownConfig,
    ...gridConfig,
    ...lightboxConfig,
    ...layoutApi,
    ...stackApi,
    ...cardApi
};

export const feedBackMenu = {
    ...collapseApi,
    ...popoverApi,
    ...toastApi,
    ...tooltipApi,
    ...loadingConfig,
    ...progressApi,
    ...badgeConfig,
    ...alertApi,
    ...messageApi,
    ...emptyApi
};

export const otherMenu = {
    ...affixConfig,
    ...OverlayApi,
    ...UploaderApi,
    ...tabsApi,
    ...ratingApi,
    ...tableApi,
    ...formApi,
    ...sortableApi,
    ...transitionApi
};
