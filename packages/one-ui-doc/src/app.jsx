import React, {useState, useEffect} from 'react';
import {Browser<PERSON>outer, Routes, Route, Link, useParams} from 'react-router-dom';
import {Sidenav, Menu, Anchor, SearchBox, Radio, ConfigProvider} from '@baidu/one-ui';
import Demo from './common/demo';
import {map} from 'lodash';
import Search from './search';
import {SearchContext} from './search/context';
import {
    menu,
    introMenu,
    basicMenu,
    navMenu,
    dataInputMenu,
    dataShowMenu,
    containerMenu,
    feedBackMenu,
    otherMenu
} from './config';
import '../../one-ui/src/index.less';
import './app.less';

const menus = [
    ['介绍', introMenu, 'intro'],
    ['基础', basicMenu],
    ['导航', navMenu],
    ['数据输入', dataInputMenu],
    ['数据展示', dataShowMenu],
    ['容器', containerMenu],
    ['反馈', feedBackMenu],
    ['其他', otherMenu]
].map(([title, menu, dir]) => ({title, dir, children: map(menu, (item, id) => ({id, label: item.label}))}));

const Layout = ({mode, onModeChange, children}) => (
    <>
        <header className="main-header">
            <div className="main-header-logo">
                <Link to="/">ONE UI</Link>
            </div>
            <Radio.Group
                options={['D20', 'D22', 'AI']}
                value={mode}
                onChange={onModeChange}
                type="strong"
                theme="light-d22"
                style={{background: '#fff', marginRight: 20}}
            />
            <SearchContext.Consumer>
                {setVisible => (
                    <div onClick={() => setVisible(true)}>
                        <SearchBox placeholder="⌘ + K" className="doc-search" style={{background: '#fff'}} />
                    </div>
                )}
            </SearchContext.Consumer>
            <div className="main-header-nav">
                <a href="https://light-ai.now.baidu-int.com/" rel="noreferrer" target="_blank">AI</a>
                <a href="https://pages.baidu-int.com/fc-fe/one-ui-pro" rel="noreferrer" target="_blank">业务组件</a>
                <a href="https://pages.baidu-int.com/fc-fe/one-charts" rel="noreferrer" target="_blank">图表</a>
                <a href="https://github.com/ecomfe/dls-icons/tree/master/packages/dls-icons-react" rel="noreferrer" target="_blank">图标</a>
                <a href="https://d20.veui.dev/" rel="noreferrer" target="_blank">VEUI</a>
                <a href="http://light-design.baidu.com/" rel="noreferrer" target="_blank">设计</a>
            </div>
        </header>
        <div className="main">
            {children}
        </div>
    </>
);

const App = () => {
    const {id} = useParams();
    const selectedId = id || 'index';
    useEffect(() => {
        document.body.scrollTop = document.documentElement.scrollTop = 0;
    }, [selectedId]);
    const {
        value: Main,
        demos = [],
        apis = []
    } = menu[selectedId] || {};
    const anchors = demos.slice();
    if (apis.length > 0) {
        anchors.push({title: 'api', children: apis});
    }
    const [mode, setMode] = useState('D20');
    const onModeChange = mode => {
        setMode(mode.target.value);
    };
    const themeMap = {
        D20: 'light-d20',
        D22: 'light-d22',
        AI: 'light-ai'
    };
    return (
        <ConfigProvider theme={themeMap[mode]}>
            <Layout mode={mode} onModeChange={onModeChange}>
                <div className="left-menu">
                    <Sidenav selectedKeys={[selectedId]}>
                        {
                            menus.map(({title, children, dir}) => (
                                <Menu.ItemGroup
                                    title={<span className="nav-group-title">{title}</span>}
                                    key={title}
                                >
                                    {
                                        children.map(({id, label}) => {
                                            const [en, zh] = label.split(/\s+/);
                                            return (
                                                <Menu.Item key={id}>
                                                    <Link to={`/${dir || 'components'}/${id}`}>
                                                        {en}<span className="nav-item-desc">{zh}</span>
                                                    </Link>
                                                </Menu.Item>
                                            );
                                        })
                                    }
                                </Menu.ItemGroup>
                            ))
                        }
                    </Sidenav>
                </div>
                <div className="right-main">
                    {anchors.length > 0
                        ? (
                            <div className="anchor-menu">
                                <Anchor size="small" offsetTop={20} type="inline" className="anchor-menu-container">
                                    {anchors.map(({title, children}) => {
                                        if (children) {
                                            return (
                                                <Anchor.Link
                                                    isALabel
                                                    href="#apis"
                                                    className="anchor-link"
                                                    title="API"
                                                    key={title}
                                                >
                                                    {children.length > 1
                                                        ? children.map(({title}) => (
                                                            <Anchor.Link
                                                                isALabel
                                                                href={`#api-${title}`}
                                                                className="anchor-link"
                                                                title={title}
                                                                key={title}
                                                            />
                                                        ))
                                                        : null
                                                    }
                                                </Anchor.Link>
                                            );
                                        }
                                        return (
                                            <Anchor.Link
                                                isALabel
                                                className="anchor-link"
                                                href={`#demo-${title}`}
                                                title={title}
                                                key={title}
                                            />
                                        );
                                    })}
                                </Anchor>
                            </div>
                        )
                        : null
                    }
                    {
                        Main
                            ? <Main id={id} />
                            : null
                    }
                </div>
            </Layout>
        </ConfigProvider>
    );
};

const Playground = () => (
    <Layout>
        <Demo playground desc="Playground" />
    </Layout>
);

export default () => (
    <BrowserRouter>
        <Search>
            <Routes>
                <Route path="/components/:id" element={<App />} />
                <Route path="/intro/:id" element={<App />} />
                <Route path="/playground" element={<Playground />} />
                <Route path="*" element={<App />} />
            </Routes>
        </Search>
    </BrowserRouter>
);
