import {
    Nav,
    Button,
    Overlay,
    Checkbox,
    Input,
    CascaderPane,
    Tooltip,
    Menu,
    Sidenav,
    Radio,
    Dropdown,
    Select,
    Pagination,
    Table,
    SearchBox,
    Link,
    Tree,
    Transfer,
    Tag,
    Toast,
    Dialog,
    Steps,
    Popover,
    Switch,
    Cascader,
    Affix,
    DatePicker,
    Anchor,
    Badge,
    BackTop,
    Breadcrumb,
    TextArea,
    NumberInput,
    Loading,
    Grid,
    Drawer,
    Carousel,
    Collapse,
    Progress,
    Alert,
    Uploader,
    Transition,
    Form,
    Slider,
    Tabs,
    TimePicker,
    Lightbox,
    Sortable,
    Layout,
    Message,
    Empty,
    Stack,
    Card,
    Rating
} from '@baidu/one-ui';
import {map, forEach} from 'lodash';
import TableColumn from './type/table/TableColumn';
import TablerowSelection from './type/table/TablerowSelection';
import ToastDoc from './type/toast/ToastDoc';
import ToastConfig from './type/toast/ToastConfig';
import UploadFile from './type/uploader/UploadFile';
import DialogAlert from './type/dialog/DialogAlert';
import FormCreate from './type/form/FormCreate';

const componentDocs =
    [
        Nav,
        Button,
        Overlay,
        Checkbox,
        Input,
        CascaderPane,
        Tooltip,
        Menu,
        Sidenav,
        Radio,
        Dropdown,
        Select,
        Pagination,
        Table,
        SearchBox,
        Link,
        Tree,
        Transfer,
        Tag,
        Toast,
        Dialog,
        Steps,
        Popover,
        Switch,
        Cascader,
        Affix,
        DatePicker,
        Anchor,
        Badge,
        BackTop,
        Breadcrumb,
        TextArea,
        NumberInput,
        Loading,
        Grid,
        Drawer,
        Carousel,
        Collapse,
        Progress,
        Alert,
        Uploader,
        Transition,
        Form,
        Slider,
        Tabs,
        TimePicker,
        Lightbox,
        Sortable,
        Layout,
        Message,
        Empty,
        Stack,
        Card,
        Rating,
        TableColumn,
        TablerowSelection,
        ToastDoc,
        ToastConfig,
        UploadFile,
        DialogAlert,
        FormCreate
    ]
    .reduce((components, component) => {
        /* @ts-ignore */
        const doc = component.__docgenInfo;
        components.push(doc);
        // 子组件提取
        forEach(component, ({__docgenInfo: doc} = {__docgenInfo: null}) => {
            if (doc) {
                components.push(doc);
            }
        });
        return components;
    }, [])
    .reduce((docs, doc) => {
        let props = [];
        if (doc) {
            // API转换，适配当前文档数据结构
            props = map(doc.props, ({name, type, description, defaultValue}) => {
                const val = defaultValue?.value;
                const {name: typeName, value: typeValue} = type;
                return {
                    param: name,
                    type: typeName === 'enum' ? typeValue.map(o => o.value).join(' | ') : typeName,
                    desc: description,
                    option: '',
                    default: String(val == null ? '' : val)
                };
            });
            docs[doc.displayName] = props;
        }
        return docs;
    }, {});
export default componentDocs;
