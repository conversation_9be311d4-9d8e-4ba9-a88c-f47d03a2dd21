* {
    padding: 0;
    margin: 0;
}
body,
html {
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
}
.main {
    display: flex;
    display: -webkit-flex;
}
.left-menu {
    width: 220px;
    box-shadow: 0 4px 10px 0 rgba(173, 192, 255, 22%);
    padding-left: 6px;
    background-color: #fff;
    box-sizing: border-box;
    height: calc(100vh);
    overflow-y: auto;
    position: sticky;
    top: 0;
    .left-menu-title,
    .nav-group-title {
        padding-left: 24px;
        font-weight: bolder;
        color: #999;
    }
    .nav-item-desc {
        margin-left: 8px;
        color: #999;
        font-size: 12px;
    }
    &-title {
        position: relative;
        > span {
            font-size: 8px;
            color: #9D9D9D;
            top: -8px;
            margin-left: 5px;
            display: inline-block;
            position: relative;
        }
    }
    &-main {
        margin-top: 40px;
        &-sub-title {
            > span {
                font-size: 14px;
            }
        }
    }
    &-item-contrainer {
        margin: 15px 0 15px 24px;
        cursor: pointer;
        .left-menu-item {
            font-size: 12px;
            color: #333333;
            cursor: pointer;
            &:hover {
                border-bottom: 2px solid #9D9D9D;
            }
            &-selected {
                font-size: 12px;
                border-bottom: 2px solid #000;
            }
        }
    }
}
.right-main {
    flex: 1;
    padding: 30px 20px;
    width: calc(100% - 220px);
    box-sizing: border-box;
    .anchor-menu {
        position: absolute;
        right: 10px;
        top: 155px;
        &-container {
            background-color: #fff;
            box-shadow: 0 4px 10px 0 rgba(218, 226, 253, .22);
            padding: 10px 10px 10px 16px;
            border-radius: 5px;
            width: 130px;
            overflow: auto;
            box-sizing: border-box;
            max-height: 75vh!important;
        }
    }
}
