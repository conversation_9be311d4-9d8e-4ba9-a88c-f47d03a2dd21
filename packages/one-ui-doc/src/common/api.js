import React, {PureComponent} from 'react';
import Highlight from 'react-highlight';
import apis from '../api';
import './api.less';
import ICafe from './icafe';
import {Link} from '@baidu/one-ui';

export default class Api extends PureComponent {
    render() {
        const {title, list, desc, apiKey, ApiCustom, withoutOptional} = this.props;
        const apiList = list || apis[apiKey];
        return (
            <div className="api" id="api">
                <div className="api-title" id={`api-${title}`}>
                    {title}
                </div>
                {desc ? <div className="api-desc" id={`api-${desc}`}>{desc}</div> : null}
                {
                    ApiCustom ? <ApiCustom /> : null
                }
                <div className="api-main">
                    {
                        apiList && apiList.length ? (
                            <div className="api-header">
                                <span className="api-header-item">参数</span>
                                <span className="api-header-item">说明</span>
                                <span className="api-header-item">类型</span>
                                <span className="api-header-item">默认值</span>
                            </div>
                        ) : null
                    }
                    <div className="api-line-main">
                        {
                            apiList?.map(({param, type, option, default: defaultVal, desc}, index) => {
                                const id = `api-${apiKey}-${param}`;
                                return (
                                    <div className="api-line" key={index}>
                                        <span className="api-line-item api-line-item-param">
                                            <Link isAtag toUrl={`#${id}`} id={id}>{param}</Link>
                                            {<ICafe title={`组件[${apiKey}]属性[${param}]`} />}
                                        </span>
                                        <span className="api-line-item api-line-item-desc">
                                            {
                                                typeof desc === 'string'
                                                    ? (
                                                        <Highlight innerHTML>
                                                            {desc}
                                                        </Highlight>
                                                    )
                                                    : desc
                                            }
                                        </span>
                                        <span className="api-line-item api-line-item-type">{type}</span>
                                        <span className="api-line-item api-line-item-default">
                                            {defaultVal}
                                        </span>
                                    </div>
                                );
                            })
                        }
                    </div>
                </div>
            </div>
        );
    }
}
