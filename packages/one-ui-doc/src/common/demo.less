.hljs {
    background: #f9f9f9 !important;
    padding: 20px !important;
    border-radius: 3px;
    font-size: 12px;
    margin: 10px 0;
    font-family: Consolas, Monaco, monospace;
}
.doc {
    margin-top: 30px;
    background: #fff;
    box-shadow: 0 4px 10px 0 rgba(218, 226, 253, .22);
    border-radius: 10px;
    padding: 20px;
    h2 {
        margin: 24px 0;
    }
    h2:first-child {
        margin-top: 4px;
    }
}
.demo {
    margin-top: 30px;
    background: #fff;
    box-shadow: 0 4px 10px 0 rgba(218, 226, 253, .22);
    border-radius: 10px;
    padding: 20px;
    margin-right: 140px;
    &-inline {
        margin: 24px 0;
        padding: 0;
        .demo-instances {
            margin: 0;
        }
        .demo-head,
        .demo-desc {
            display: none;
        }
    }
    &-code-operator {
        .new-fc-one-icon-angle-down,
        .new-fc-one-icon-angle-up {
            margin-left: 10px;
        }
    }
    &-instances {
        border: 1px solid #F1F1F1;
        margin-top: 20px;
        border-radius: 5px 5px 0 0;
        >div {
            padding: 20px;
            position: relative;
        }
        &-code-copy {
            position: absolute;
            right: 30px;
            top: 30px;
            cursor: pointer;
            color: #999;
        }
    }
    &-head {
        font-size: 16px;
        font-weight: bold;
        display: flex;
        align-items: center;
    }
    &-title {
        flex: 1;
    }
    &-desc {
        color: #666666;
        margin-top: 10px;
        display: flex;
        align-items: center;
        line-height: 2;
        &-text {
            flex: 1;
        }
    }
    &-instances-code-container > div {
        background: #f9f9f9 !important;
    }
    textarea:focus-visible {
        outline: none;
    }
    &-code-operator {
        background: #fafafa;
        border: 1px solid #eee;
        border-top: 0;
        padding-left: 20px;
        cursor: pointer;
        text-align: left;
        line-height: 35px;
        border-radius: 0 0 5px 5px;
        .fenice-icon {
            margin: 0 5px;
        }
        span {
            color: #666;
        }
    }
    &-error {
        color: red;
    }
    &-full-screen-body {
        overflow: hidden;
        .api {
            margin: 0;
            box-shadow: none;
        }
    }
    &-full-screen {
        position: fixed;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        z-index: 100;
        margin: 0;
        border-radius: 0;
        overflow: auto;
        display: flex;
        flex-direction: column;
        &-icon {
            cursor: pointer;
        }
        & &-icon {
            transform: rotate(180deg);
        }
        .demo-instances {
            display: flex;
            flex: 1;
            overflow: auto;
        }
        .demo-instances-container {
            flex: 1;
            overflow: auto;
        }
        .demo-instances-code-container {
            overflow: auto;
            min-width: 45%;
            max-width: 60%;
            margin: 0;
            padding: 0;
            background: #f9f9f9 !important;
        }
        .hljs {
            margin: 0;
        }
        pre {
            height: 100%;
            code {
                height: 100%;
                box-sizing: border-box;
            }
        }
        .demo-code-operator {
            display: none;
        }
    }
    &-playground {
        position: static;
        width: 100%;
        height: calc(100vh - 50px);
        margin: 0;
    }
}
.gray-block {
    background: #f1f1f1;
    color: #666;
    font-size: 12px;
    padding: 2px 5px;
    margin: 0 5px;
}
