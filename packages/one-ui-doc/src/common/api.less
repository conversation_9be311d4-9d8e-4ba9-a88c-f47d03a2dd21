.api {
    background: #fff;
    box-shadow: 0 4px 10px 0 rgba(218, 226, 253, .22);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    margin-right: 140px;
    &-header-title {
        font-size: 30px;
        color: #333333;
        margin-top: 30px;
        margin-bottom: 10px;
    }
    &-title {
        font-size: 16px;
        color: #333333;
        margin-bottom: 20px;
    }
    &-main {
        border-bottom: 0;
        font-size: 14px;
    }
    &-header {
        display: flex;
        display: -webkit-flex;
        font-weight: bold;
        font-size: 14px;
        color: #797979;
        border-bottom: 1px solid #eee;
        &-item {
            flex: 1;
            padding: 10px;
            display: flex;
            display: -webkit-flex;
            align-items: center;
        }
    }
    &-line {
        display: flex;
        display: -webkit-flex;
        border-bottom: 1px solid #eee;
        &-main {
            color: #797979;
        }
        &-item {
            flex: 1;
            padding: 10px;
            display: flex;
            display: -webkit-flex;
            align-items: center;
            &-desc {
                line-height: 1.4;
                white-space: pre-line;
            }
        }
    }
}
.icafe {
    color: #0052cc;
    &:hover {
        color: #0066ff;
    }
    text-decoration: none;
    font-size: 10px;
    position: relative;
    left: 8px;
    display: none;
    font-weight: 400;
}
.api-line:hover,
.demo:hover {
    .icafe {
        display: inline;
    }
}