import React, {PureComponent} from 'react';
import ReactDOM, * as ReactDOMProps from 'react-dom';
import Highlight from 'react-highlight';
import * as one from '@baidu/one-ui';
import * as icons from 'dls-icons-react';
import * as illustrations from 'dls-illustrations-react';
import * as lodash from 'lodash';
import * as ReactRouterDOM from 'react-router-dom';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import IFrame from './iframe';
import {transToStyledText} from '../utils';
import {
    LiveProvider,
    LiveEditor,
    LivePreview,
    LiveError
} from 'react-live';
import {transformFromAst, parse} from '@babel/core';
import traverse from '@babel/traverse';
import theme from 'prism-react-renderer/themes/vsLight';
import lzstring from 'lz-string';
import clipboard from 'clipboard';

import './demo.less';
import ICafe from './icafe';

const {IconSvg, Button, Popover, Toast} = one;
const {
    IconExternalLink,
    IconQuestionCircle
} = icons;

const codeReq = require.context('../../rawLoader!../components', true, /\.(j|t)sx?$/);

const DemoInnerRender = ({children: C}) => (typeof C === 'function' ? <C /> : C);
const DemoInnerErrorRender = ({children}) => <pre className="demo-error">{children}</pre>;

export default class Demo extends PureComponent {
    constructor(props) {
        super(props);
        const {
            source,
            id,
            playground
        } = this.props;

        // 不允许在baidu.com下使用
        if (playground && /\.baidu\.com$/.test(location.host)) {
            location.href = location.href.replace('baidu.com', 'baidu-int.com');
        }

        const path = `./${id}/demo/${source}`;
        const urlParams = new URLSearchParams(window.location.search);
        const code = playground
            ? lzstring.decompressFromEncodedURIComponent(urlParams.get('code')) || '<>Hello World</>'
            : codeReq(codeReq.keys().find(key => key.startsWith(path)));

        this.state = {
            isExpendCode: false,
            code
        };
    }

    onCodeExpendChange = () => {
        const isExpendCode = this.state.isExpendCode;
        this.setState({
            isExpendCode: !isExpendCode
        });
    }

    toggleFullScreen = () => {
        const fullScreen = !this.state.fullScreen;
        if (fullScreen) {
            document.body.classList.add('demo-full-screen-body');
        }
        else {
            document.body.classList.remove('demo-full-screen-body');
        }
        this.setState({
            fullScreen
        });
        this.props.setFullscreen(fullScreen);
    }

    transformCode = code => {
        this.setState({
            code
        });
        try {
            const options = {
                sourceType: 'module',
                filename: 'demo.tsx',
                presets: [
                    require('@babel/preset-typescript')
                ],
                plugins: [
                    [
                        require('@babel/plugin-proposal-class-properties'),
                        {loose: true}
                    ]
                ]
            };
            code = code.replace(/^return \(|\)$/g, '');
            const ast = parse(code, options);
            traverse(ast, {
                enter(path) {
                    if (path.type === 'ImportDeclaration') {
                        path.remove();
                    }
                    if (path.type === 'ExportNamedDeclaration' || path.type === 'ExportDefaultDeclaration') {
                        path.replaceWith(path.node.declaration);
                    }
                    if (path.parent.type === 'Program'
                        && !path.getNextSibling().node
                        && path.type !== 'ClassDeclaration') {
                        path.replaceWith({
                            type: 'ReturnStatement',
                            argument: path.node
                        });
                        path.stop();
                    }
                }
            });

            const result = transformFromAst(ast, code, options);
            const body = ast.program.body;
            const last = body[body.length - 1];
            let newcode = result.code;
            if (last && last.type === 'ClassDeclaration') {
                newcode += `return <${last.id.name} />`;
            }
            return `<DemoInnerRender>{(() => {${newcode}})()}</DemoInnerRender>`;
        }
        catch (ex) {
            const error = JSON.stringify(ex.toString());
            return `<DemoInnerErrorRender>{${error}}</DemoInnerErrorRender>`;
        }
    };

    onClickShare = () => {
        const encodeCode = lzstring.compressToEncodedURIComponent(this.state.code);
        const sharedCodeURL = window.location.origin + '/playground?code=' + encodeCode;
        if (clipboard.isSupported) {
            clipboard.copy(sharedCodeURL);
            Toast.success({
                content: 'URL已经复制到剪切板',
                duration: 3
            });
        }
        else {
            prompt('请手动复制以下内容', sharedCodeURL);
        }
    };

    render() {
        const {
            title,
            desc,
            source,
            id,
            descFlag,
            showApi,
            inline,
            playground
        } = this.props;
        const {
            fullScreen,
            isExpendCode,
            code
        } = this.state;
        const operatorText = isExpendCode ? '隐藏代码' : '展开代码';
        const operatorIcon = isExpendCode ? 'chevron-up' : 'chevron-down';

        return (
            <div
                className={classNames('demo', {
                    'demo-full-screen': playground || fullScreen,
                    'demo-inline': inline,
                    'demo-playground': playground
                })}
                data-demo={source}
            >
                <div className="demo-head" id={`demo-${title}`}>
                    <div className="demo-title">
                        {title}
                        {!playground && <ICafe title={`组件[${lodash.upperFirst(id)}]示例[${title}]`} />}
                    </div>
                    {!playground
                        && (
                            <Button
                                type="text-aux"
                                icon={IconExternalLink}
                                size="medium"
                                onClick={this.toggleFullScreen}
                                className="demo-full-screen-icon"
                            />
                        )
                    }
                </div>
                <div className="demo-desc">
                    <Highlight innerHTML className="demo-desc-text">
                        {transToStyledText(desc || '', descFlag)}
                    </Highlight>
                    {playground || fullScreen
                        ? (
                            <div>
                                <Popover
                                    size="small"
                                    placement="bottomRight"
                                    content={(
                                        <div>
                                            依赖包支持范围如下(无需import)：
                                            <br />
                                            one-ui<br />
                                            dls-icons-react<br />
                                            dls-illustrations-react<br />
                                            lodash<br />
                                            React<br />
                                            ReactDOM<br />
                                            classNames<br />
                                            PropTypes<br />
                                            <br />
                                            可直接使用以上导出，如：
                                            <br />
                                            Select、IconExternalLink、map...
                                        </div>
                                    )}
                                >
                                    <Button
                                        type="text-aux"
                                        size="xsmall"
                                        icon={IconQuestionCircle}
                                        style={{marginRight: 8}}
                                    />
                                </Popover>
                                <Button
                                    type="primary"
                                    icon={<icons.IconShareAlt />}
                                    size="xsmall"
                                    onClick={this.onClickShare}
                                >
                                    分享
                                </Button>
                                {!playground
                                    && (
                                        <Button
                                            type="primary"
                                            size="xsmall"
                                            onClick={() => showApi(true)}
                                            style={{marginLeft: 8}}
                                        >API</Button>
                                    )
                                }
                            </div>
                        )
                        : null
                    }
                </div>
                <LiveProvider
                    code={code}
                    theme={theme}
                    scope={{
                        ...one,
                        React,
                        ...React,
                        ReactDOM,
                        ...ReactDOMProps,
                        ...icons,
                        ...illustrations,
                        ...lodash.omit(lodash, ['default']),
                        PropTypes,
                        ReactRouterDOM,
                        classNames,
                        DemoInnerRender,
                        DemoInnerErrorRender,
                        IFrame
                    }}
                    transformCode={this.transformCode}
                >
                    <div className="demo-instances" id="demo-instances">
                        <div className="demo-instances-container">
                            <LivePreview />
                            <LiveError className="demo-error" />
                        </div>
                        {
                            isExpendCode || fullScreen || playground
                                ? (
                                    <div className="demo-instances-code-container">
                                        <LiveEditor />
                                    </div>
                                )
                                : null
                        }
                    </div>
                </LiveProvider>
                <div className="demo-code-operator" id="demo-code-operator" onClick={this.onCodeExpendChange}>
                    <span>
                        {operatorText}
                        <IconSvg type={operatorIcon} />
                    </span>
                </div>
            </div>
        );
    }
}
