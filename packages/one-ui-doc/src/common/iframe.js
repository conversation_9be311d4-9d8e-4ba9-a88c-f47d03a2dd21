import React from 'react';
import {createPortal} from 'react-dom';

export default class <PERSON>rame extends React.PureComponent {
    state = {
        mountNode: null
    };

    setContentRef = contentRef => {
        if (contentRef && contentRef.contentDocument) {
            this.setState({
                mountNode: contentRef.contentDocument.body
            });
        }
    };

    render() {
        const {children, ...props} = this.props;
        const {mountNode} = this.state;
        const links = document.querySelectorAll('link[rel=stylesheet]');
        const styles = document.querySelectorAll('style');
        const content = (
            <>
                {Array.from(links).map((o, key) => <link key={key} rel="stylesheet" href={o.href} />)}
                {Array.from(styles).map((o, key) => <style key={key}>{o.innerText}</style>)}
                {children}
            </>
        );
        return (
            <iframe
                {...props}
                ref={this.setContentRef}
                style={{
                    border: '10px solid #666',
                    overflow: 'auto',
                    height: 400,
                    width: '100%',
                    boxSizing: 'border-box'
                }}
                // 兼容firefox
                onLoad={e => this.setContentRef(e.target)}
            >
                {mountNode && createPortal(content, mountNode)}
            </iframe>
        );
    }
}
