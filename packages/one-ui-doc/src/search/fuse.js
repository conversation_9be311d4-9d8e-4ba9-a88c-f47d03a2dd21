import Fuse from 'fuse.js';
import {forIn} from 'lodash';
import apiList from '../api';
import {menu} from '../config';

const list = [];
const labelMap = {};

forIn(menu, (component, componentName) => {
    const {demos, label, apis} = component;
    if (demos) {
        const demoDoc = demos.map(demo =>
            Object.assign(
                {},
                demo,
                {
                    label,
                    componentName
                }
            )
        );
        labelMap[componentName] = label;
        list.splice(0, 0, ...demoDoc);
    }
    if (apis) {
        apis.map(api => {
            const {apiKey} = api;
            const apiDoc = (apiList[apiKey] || []).map(apiItem => Object.assign({}, apiItem, {
                componentName,
                label: labelMap[componentName],
                apiKey
            }));
            list.splice(0, 0, ...apiDoc);
        });
    }
});

export default class FuseTool {
    fuseInstance = null;

    initFuse = list => {
        this.fuseInstance = new Fuse(list, {
            shouldSort: true,
            threshold: 0.4,
            location: 0,
            distance: 100,
            minMatchCharLength: 1,
            keys: [
                {
                    name: 'label',
                    weight: .8
                },
                {
                    name: 'title',
                    weight: 0.5
                },
                {
                    name: 'desc',
                    weight: 0.5
                },
                {
                    name: 'componentName',
                    weight: 1
                }
            ]
        });
    };

    querySearch = query => {
        if (query !== '') {
            return this.fuseInstance.search(query);
        }
        return null;
    }
}

const searchEngine = new FuseTool();

searchEngine.initFuse(list);

export const querySearch = searchEngine.querySearch;