// dialog hack
.whj-content-dialog {
    padding: 0 0 0 0 !important;
    .one-anchor {
        width: 100%;
    }
}

.container {
    border-radius: 4px;
    background: #f5f6f7;
    justify-content: space-around;
    &-header, &-body {
        padding-left: 12px;
        padding-right: 12px;
    }
    &-header {
        padding-top: 12px;
        border-radius: 4px 4px 0 0;
        .header-search {
            display: flex;
            width: 100%;
            height: 56px;
            box-sizing: border-box;
            margin-bottom: 4px;
            .one-search-box-detail {
                flex: 1;
                height: 100%;
            }
        }
    }
    &-body {
        min-height: 200px;
        max-height: 500px;
        overflow: overlay;
        scroll-behavior: smooth;
    }
    &-footer {
        box-sizing: border-box;
        width: 100%;
        height: 44px;
        padding: 12px;
        border-radius: 0 0 4px 4px;
        display: flex;
        align-items: center;
        font-size: 0.8rem;
        color: #888;
        background-color: #fff;
        box-shadow: 0 -1px 3px #d4d9e1;
        &-tips {
            flex: 1;
        }
        &-logo {
            display: flex;
            align-items: center;
            img {
                width: 20px;
                height: 20px;
            }

        }
    }
}
.search-result-empty {
    margin: 40px auto;
    width: max-content;
}
.search-result-container {
    .result-group-title {
        position: sticky;
        top: 0;
        display: block;
        width: 100%;
        padding: 0 4px;
        transform: translateX(-4px);
        line-height: 3;
        vertical-align: bottom;
        color: #5468ff;
        background-color: rgb(245, 246, 247);
        bottom: 0;
    }
    ul li {
        list-style: none;
        list-style-type: none;
    }
}
.result-item {
    display: flex;
    align-items: center;
    width: 100%;
    height: 60px;
    margin-bottom: 6px;
    background: #fff;
    box-shadow: 0 1px 3px 0 #d4d9e1;
    border-radius: 4px;
    font-size: .9em;
    color: #444950;
    text-decoration: none;
    cursor: pointer;
    & > * {
        margin-left: 8px;
    }
    & > *:last-child {
        margin-right: 8px;
    }
    &:hover {
        background: #0052CC;
        color: #fff;
        * {
            color: #fff;
        }
    }
    &-icon {
        height: 20px;
        width: 20px;
        color: #969faf;
    }
    &-svg {
        color: #969faf;
        stroke-width: 1.4;
    }
    &-content {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 1.3;
        &-desc {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        &-title {
            color: #969faf;
        }
    }
    &-search-text-highlight {
        color: #0052CC;
    }
}