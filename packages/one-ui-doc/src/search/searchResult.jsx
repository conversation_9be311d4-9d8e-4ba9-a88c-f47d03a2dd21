import React, {PureComponent, forwardRef} from 'react';
import {Link} from 'react-router-dom';
import {map, groupBy} from 'lodash';
import {Select, Empty} from '@baidu/one-ui';
import {IconAppsSettings, IconPlayCircle, IconTreasureChest} from 'dls-icons-react';
import {InView} from 'react-intersection-observer';
import IconEnter from './icons/iconEnter';
import IconContinue from './icons/iconContinue';
import IconTerminal from './icons/iconTerminal';


const SearchResultItem = React.memo(forwardRef((props, ref) => {
    const {dataItem: {item}, onSelect, searchValue} = props;
    const {componentName, param, type, required, desc, label, source, title, endFlog, apiKey} = item;
    let navIcon = null;
    let icon;
    let resultTitle;
    let resultContent;
    let target;

    navIcon = endFlog
        ? <IconTerminal className="result-item-svg" />
        : <IconContinue className="result-item-svg" />;
    if (param) {
        // API
        icon = <IconAppsSettings style={{width: '100%', height: '100%'}} />;
        resultTitle = `${componentName}-${param}`;
        resultContent = desc;
        target = `api-${apiKey}-${param}`;
    }
    else if (title || desc) {
        // DEMO
        icon = <IconPlayCircle style={{width: '100%', height: '100%'}} />;
        resultTitle = `${componentName}-${title}`;
        resultContent = desc;
        target = `demo-${title}`;
    }
    else {
        // Component
        icon = <IconTreasureChest style={{width: '100%', height: '100%'}} />;
        resultTitle = title;
        resultContent = `${label}`;
        target = '';
        navIcon = null;
    }

    return (
        <li ref={ref} onClick={() => onSelect?.(componentName, target)}>
            <Link
                key={`components-${componentName}-${target}`}
                to={`/components/${componentName}#${target}`}
                className='result-item'
            >
                {navIcon}
                <div className='result-item-icon'>{icon}</div>
                <div className='result-item-content'>
                    <p className='result-item-content-desc'>
                        <Select.SearchText
                            prefixCls='result-item'
                            searchValue={searchValue}
                            text={resultContent || ''}
                            showSearch
                        />
                    </p>
                    <p className='result-item-content-title'>
                        <Select.SearchText
                            prefixCls='result-item'
                            searchValue={searchValue}
                            text={resultTitle || ''}
                            showSearch
                        />
                    </p>
                </div>
                <IconEnter className="result-item-svg" />
            </Link>
        </li>
    );
}));

export default class SearchResult extends PureComponent {
    render() {
        const {dataSource} = this.props;
        if (!dataSource || !dataSource.length) {
            return (
                <div className="search-result-empty">
                    <Empty size="small" />
                </div>
            );
        }
        const groupDatas = groupBy(dataSource, 'item.componentName');
        return (
            <div className="search-result-container">
                {
                    map(groupDatas, (groupData, componentName) => {
                        groupData.unshift({
                            item: {
                                componentName,
                                label: groupData[0].item.label
                            }
                        });
                        groupData.at(-1).item.endFlog = true;
                        return (
                            <ul key={componentName} title={componentName}>
                                <label className="result-group-title">{componentName}</label>
                                {groupData.map((dataItem, index) => (
                                    <InView key={index}>
                                        {({inView, ref}) => (
                                            <div ref={ref}>
                                                {!inView
                                                    ? <div className='result-item' />
                                                    : (
                                                        <SearchResultItem
                                                            key={componentName + index}
                                                            dataItem={dataItem}
                                                            {...this.props}
                                                        />
                                                    )
                                                }
                                            </div>
                                        )}
                                    </InView>
                                ))}
                            </ul>
                        );
                    })
                }
            </div>
        );
    }
}
