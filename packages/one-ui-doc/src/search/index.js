import React, {useCallback, useEffect, useRef, useState} from 'react';
import {Dialog} from '@baidu/one-ui';
import HotKeys from 'react-hot-keys';
import {throttle} from 'lodash';
import Search from './search';
import {SearchContext} from './context';

const animationTimeout = 500;

export default props => {
    const {children} = props;
    const [visible, setVisible] = useState(false);
    const searchRef = useRef();
    const onSwitch = useCallback(
        () => {
            setVisible(!visible);
        }
        , []);
    useEffect(throttle(() => {
        const focusTimeout = setTimeout(() => {
            searchRef.current?.forceGetSearchFocus();
        }, animationTimeout);
        return () => {
            clearTimeout(focusTimeout);
        };
    }), [visible]);
    const onSelect = useCallback((componentName, target) => {
        setVisible(false);
        setTimeout(() => {
            const el = document.getElementById(target);
            el?.scrollIntoView({
                behavior: 'smooth'
            });
        }, animationTimeout);
    }, []);
    const hideModal = useCallback(() => setVisible(false), []);
    return (
        <HotKeys
            keyName="⌘+k"
            // eslint-disable-next-line react/jsx-no-bind
            onKeyUp={onSwitch}
        >
            <SearchContext.Provider value={setVisible}>
                {children}
                <Dialog
                    visible={visible}
                    onCancel={hideModal}
                    needCloseIcon={false}
                    maskClosable
                    footer={[]}
                    contentClassName="whj-content-dialog"
                    maskStyle={{
                        backgroundColor: 'rgba(101,108,133,0.8)'
                    }}
                >
                    <Search onSelect={onSelect} ref={searchRef} />
                </Dialog>
            </SearchContext.Provider>
        </HotKeys>
    );
};