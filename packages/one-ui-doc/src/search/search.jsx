import {SearchBox} from '@baidu/one-ui';
import {throttle} from 'lodash';
import React, {PureComponent} from 'react';

import {querySearch} from './fuse';
import SearchResult from './searchResult';
import './style.less';

const throttleWait = 90;
export default class Search extends PureComponent {
    state = {
        value: '',
        dataSource: []
    }
    inputRef = React.createRef();

    onChange = e => {
        this.setState({
            value: e.target.value
        });
        this.search(e.target.value);
    }

    forceGetSearchFocus() {
        this.inputRef.current.querySelector('input').focus();
    }

    componentDidMount() {
        this.forceGetSearchFocus();
    }

    search = throttle(query => {
        const res = querySearch(query);
        this.setState({
            dataSource: res
        });
    }, throttleWait);

    render() {
        const {value, dataSource} = this.state;
        return (
            <div className='container'>
                <header className='container-header' ref={this.inputRef}>
                    <SearchBox
                        size='large'
                        value={value}
                        placeholder="Search docs"
                        className='header-search'
                        onChange={this.onChange}
                        isShowDropDown={false}
                        onClearClick={this.onChange}
                    />
                </header>
                <div className='container-body'>
                    <SearchResult dataSource={dataSource} onSelect={this.props.onSelect} searchValue={value} />
                </div>
                <footer className='container-footer'>
                </footer>
            </div>
        );
    }
}