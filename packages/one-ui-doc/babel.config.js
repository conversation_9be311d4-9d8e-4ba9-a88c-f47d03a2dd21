const version = require('../one-ui/package.json').version;

module.exports = {
    presets: [
        ['@babel/env', {loose: true, modules: false}],
        '@babel/preset-typescript',
        '@babel/react'
    ],
    plugins: [
        ['@babel/plugin-proposal-decorators', {legacy: true}],
        ['@babel/proposal-class-properties', {loose: true }],
        'add-module-exports',
        ['transform-define', {
            'process.env.__VERSION__': version
        }],
        ['inline-react-svg', {svgo: false}]
    ]
};
