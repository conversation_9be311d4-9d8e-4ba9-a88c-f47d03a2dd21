/**
 * @file doc本地启动文件配置
 * <AUTHOR>
 * @email <EMAIL>
 */
const {merge} = require('webpack-merge');
const baseConfig = require('./webpack.base');
const paths = require('./paths');
const {DevUploadWebpackPlugin} = require('@baidu/med-upload');

module.exports = merge(baseConfig, {
    mode: 'development',
    devServer: {
        contentBase: paths.build,
        publicPath: '/',
        watchOptions: {
            ignored: /node_modules/,
            aggregateTimeout: 300,
            poll: 1000
        },
        port: 8082,
        host: '0.0.0.0',
        disableHostCheck: true,
        hot: true,
        historyApiFallback: {
            index: '/index.html'
        },
        stats: {
            colors: true,
            hash: true,
            timings: true,
            chunks: false
        }
    },
    plugins: [
        new DevUploadWebpackPlugin({
            environmentToken: '32137bab32'
        })
    ]
});
