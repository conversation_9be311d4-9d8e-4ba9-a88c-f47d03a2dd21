/**
 * @file    webpack base config
 * <AUTHOR> (<EMAIL>)
 * @date    2020-09-14 18:27:49
 */
const webpack = require('webpack');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const path = require('path');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const paths = require('./paths');
const {forEach} = require('lodash');

module.exports = {
    devtool: 'source-map',
    entry: {
        main: [
            '@babel/polyfill',
            paths.docIndex
        ]
    },
    node: {
        fs: 'empty'
    },
    output: {
        path: paths.build,
        pathinfo: true,
        filename: 'static/js/[name].bundle.js',
        publicPath: '/'
    },
    module: {
        rules: [
            {
                test: [/\.bmp$/, /\.gif$/, /\.jpe?g$/, /\.png$/],
                loader: 'url-loader',
                options: {
                    limit: 10000,
                    name: 'static/media/[name].[hash:8].[ext]'
                }
            },
            {
                test: /\.md$/,
                loader: 'raw-loader'
            },
            {
                test: /\.jsx?$/,
                exclude: /node_modules/,
                loader: 'babel-loader'
            },
            {
                test: /\.tsx?$/,
                exclude: /node_modules/,
                use: [
                    'babel-loader',
                    {
                        loader: 'webpack-react-docgen-typescript',
                        options: {
                            shouldExtractLiteralValuesFromEnum: true,
                            transformProps: tables => {
                                const table = tables[0];
                                forEach(table.props, (prop, key) => {
                                    const {description, parent} = prop;
                                    // 过滤掉无注释、注释带`@internal`以及React相关属性
                                    let ignore = false;
                                    if (!description || description.includes('@internal')) {
                                        ignore = true;
                                    }

                                    if (parent && parent.fileName.includes('@types/react')) {
                                        ignore = true;
                                    }
                                    if (ignore) {
                                        delete table.props[key];
                                    }
                                    else {
                                        delete prop.parent;
                                        delete prop.declarations;
                                    }
                                });
                                return table;
                            }
                        }
                    }
                ]
            },
            {
                test: /\.css$/,
                use: [
                    {
                        loader: MiniCssExtractPlugin.loader
                    },
                    'css-loader'
                ]
            },
            {
                test: /\.less$/,
                use: [
                    'css-hot-loader',
                    MiniCssExtractPlugin.loader,
                    'css-loader',
                    {
                        loader: 'less-loader',
                        options: {
                            lessOptions: {
                                javascriptEnabled: true
                            }
                        }
                    }
                ]
            },
            {
                test: [/\.woff$/, /\.ttf$/, /\.svg$/, /\.eot$/],
                loader: 'file-loader',
                options: {
                    name: 'static/media/[name].[hash:8].[ext]'
                }
            },
            {
                exclude: [
                    /\.html$/,
                    /\.(j|t)sx?$/,
                    /\.less$/,
                    /\.json$/,
                    /\.bmp$/,
                    /\.gif$/,
                    /\.jpe?g$/,
                    /\.png$/,
                    /\.woff$/,
                    /\.ttf$/,
                    /\.svg$/,
                    /\.eot$/,
                    /\.md$/
                ],
                loader: 'file-loader',
                options: {
                    name: 'static/media/[name].[hash:8].[ext]'
                }
            }
        ]
    },
    resolve: {
        extensions: ['.js', '.jsx', '.ts', '.tsx', '.json'],
        alias: {
            '@baidu/one-ui': path.resolve(__dirname, '../../one-ui/src'),
            'react': path.resolve(__dirname, '../node_modules/react'),
            'react-dom': path.resolve(__dirname, '../node_modules/react-dom')
        }
    },
    optimization: {
        splitChunks: {
            minChunks: 2,
            cacheGroups: {
                vendor: {
                    test: /@babel|react|react-dom|react-highlight|copy-to-clipboard/,
                    name: 'vendor',
                    chunks: 'all'
                }
            }
        }
    },
    plugins: [
        new webpack.DefinePlugin({
            'process.env': {
                // process.env.NODE_ENV is defined by webpack4's mode feature
                STAGE: JSON.stringify(process.env.STAGE || 'development')
            },
            'baseDir': JSON.stringify(__dirname)
        }),
        new webpack.HotModuleReplacementPlugin(),
        new MiniCssExtractPlugin({
            filename: 'static/css/styles.css',
            allChunks: true
        }),
        new HtmlWebpackPlugin({
            filename: 'index.html',
            template: path.resolve(paths.doc, 'index.html'),
            chunks: ['vendor', 'main'],
            minify: {
                removeComments: true,
                collapseWhitespace: false
            },
            chunksSortMode: 'manual'
        })
    ]
};
