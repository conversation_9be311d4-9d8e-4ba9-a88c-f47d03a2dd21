{"name": "@baidu/one-ui-doc", "version": "1.0.0", "description": "doc for @baidu/one-ui", "private": true, "main": "index.html", "scripts": {"test": "exit 0", "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider STAGE=online NODE_ENV=production BABEL_ENV=production webpack --config config/webpack.config.js --progress --profile --colors", "start": "cross-env NODE_OPTIONS=--openssl-legacy-provider STAGE=development NODE_ENV=development BABEL_ENV=development webpack-dev-server --debug --config  config/webpack.dev.js --progress --open"}, "keywords": ["one-ui", "doc"], "author": "", "license": "ISC", "dependencies": {"classlist-polyfill": "^1.2.0", "clipboard": "^2.0.11", "dls-icons-react": "^3.42.0", "dls-illustrations-react": "^1.3.0", "focus-visible": "^5.1.0", "fuse.js": "^6.6.2", "lz-string": "^1.4.4", "react": "^16.14.0", "react-dom": "^16.14.0", "react-highlight": "^0.12.0", "react-hot-keys": "^2.7.2", "react-intersection-observer": "^9.4.0", "react-live": "^2.3.0", "react-router-dom": "^6.4.1", "react-sortablejs": "^6.0.0", "sortablejs": "^1.13.0"}, "devDependencies": {"@babel/core": "^7.11.6", "@babel/plugin-proposal-class-properties": "^7.10.4", "@babel/plugin-proposal-decorators": "^7.16.0", "@babel/polyfill": "^7.11.5", "@babel/preset-env": "^7.11.5", "@babel/preset-react": "^7.10.4", "@babel/preset-typescript": "^7.16.0", "@baidu/med-upload": "^0.0.6", "@types/react": "^17.0.34", "@types/react-router-dom": "^5.3.3", "babel-loader": "^8.2.3", "babel-plugin-add-module-exports": "^1.0.4", "babel-plugin-inline-react-svg": "^2.0.2", "babel-plugin-transform-define": "^2.1.0", "cross-env": "^7.0.2", "css-hot-loader": "^1.4.4", "css-loader": "^4.3.0", "file-loader": "^6.1.0", "html-webpack-plugin": "^4.4.1", "less": "^4.1.2", "less-loader": "^7.3.0", "mini-css-extract-plugin": "^0.11.2", "raw-loader": "^4.0.2", "typescript": "4.2.2", "url-loader": "^4.1.0", "webpack": "^4.44.1", "webpack-cli": "^3.3.12", "webpack-dev-server": "^3.11.0", "webpack-merge": "^5.1.4", "webpack-react-docgen-typescript": "^0.9.5"}}