const coverFromList = require('./coverFromList');
const esModules = ['dls-graphics'].join('|');

module.exports = {
    verbose: true,
    // 执行每一个测试前的环境初始化
    setupFiles: ['./tests/setup.js'],
    // jest 会查找的扩展名
    moduleFileExtensions: ['js', 'jsx', 'ts', 'tsx', 'json', 'css', 'less'],
    // 忽略的测试路径
    testPathIgnorePatterns: ['/node_modules/'],
    transform: {
        '^.+\\.jsx?$': 'babel-jest',
        '^.+\\.tsx?$': 'ts-jest',
        '.+\\.(css|styl|less|sass|scss)$': '<rootDir>/node_modules/jest-css-modules-transform'
    },
    testEnvironment: 'jsdom',
    testRegex: '.*\\.spec\\.(j|t)sx?$',
    collectCoverageFrom: coverFromList,
    transformIgnorePatterns: [
        `/node_modules/(?!${esModules})`
    ],
    snapshotSerializers: ['enzyme-to-json/serializer'],
    globals: {
        'ts-jest': {
            tsconfig: 'tsconfig.json'
        }
    },
    testURL: 'http://localhost'
};