# Changelog
All notable changes to this project will be documented in this file.

### [4.41.14] 2025-07-04
- Nav
[feat] 新增变体`ghost`, 交互颜色、大小、间距等有升级
- Button
[feat] 新增变体`text-reverse`, 是用于有色背景的文字按钮
- <PERSON>u
[feat] 新增变体`ghost`, 仅inline模式支持，交互颜色、大小、间距等有升级
- Badge
[feat] 新增变体`ghost`, 支持透明背景，防止影响内容显示

### [4.41.13] 2025-05-09
- Tabs
[feat] 调整选中态样式
- Toast
[feat] 支持AI主题，并调整AI主题圆角、编剧等样式

### [4.41.12] 2025-04-18
- DatePicker
[feat] 导出`Calendar`组件，可直接使用日期面板，并支持面板slot：CustomHeader, CustomFooter, CustomDayRender
- LightBox
[fix] 修复灯箱组件显示视频报错问题

### [4.41.11] 2025-04-08
- Cascader
[feat] `mutiple` 模式下，新增支持 `onPressEnter` 方法
- Tag
[feat] 调整AI主题样式
- Radio
[feat] 调整AI主题样式
- Checkbox
[feat] 调整AI主题样式

### [4.41.10] 2025-02-10
- ConfigProvider
[feat] 支持自定义列 `SortableSelectorV2` AI主题样式
- Button
[fix] `onlyIcon` 样式，children判断条件扩展falsy数组（如 `[null, false]` ）

### [4.41.8] 2024-07-23
- Lightbox
[feat] 新增控件旋转、下载，默认不开启
- Tag
[feat] 新增属性`closeIcon`, 支持自定义关闭按钮
- Input
[fix] 修复空内容时粘贴长文本，导致粘贴内容与清除按钮重叠的问题

### [4.41.7] 2024-07-05
- Form
[fix] 新增`enableDynamicRules`，控制是否检测rules变化

### [4.41.6] 2024-07-03
- Cascader
[fix] 修复`selectable: false`选项的子选项被选中时，其自身无active样式的问题
- Tag
[fix] 修复无法同时启用`fillStyle: outline` 和 `bordered: false`的问题
- DatePicker
[fix] 将`null | undefined` 视为空值，修复value为null时, RangePicker弹出面板报错问题

### [4.41.5] 2024-06-24
- Cascader
[feat] 开放`menuWidth`，支持配置菜单宽度
[fix] 修复`disabled`选项与`exclusive`选项冲突的问题

### [4.41.4] 2024-06-14
- Tag
[feat] 增加`xsmall`尺寸
[feat] 支持配置图标
[feat] 支持无填充背景样式
- Cascader
[feat] 支持自定义触发区
[feat] 支持单复选混合模式
- Select
[fix] 修复`disabled`选项无法被搜索到的问题
[fix] 多选回填为placeholder时阻止输入框删除

### [4.41.3] 2024-05-15
- Popover
[feat] 增加`showCloseIcon`支持手动点击右上角关闭按钮关闭浮层
[feat] 标题过长会截断并显示省略号
- Radio
[feat] 按钮类型宽度支持均分所处空间
- Checkbox
[feat] 按钮类型宽度支持均分所处空间

### [4.41.2] 2024-05-07
- Tabs
[feat] 增加`renderTip`支持自定义tab tip的渲染，默认在tab的截断文字上显示Tooltip，其他情况(比如禁用，或者其他的补充说明等)都用Popover

### [4.41.1] 2024-04-29
- Form
[feat] 实例上增加`submit`和`reset`方法，支持手动触发提交和重置
[feat] 支持rules动态校验
- Select
[fix] 修复多选时回车可以任意输入
[fix] 修复AI主题输入框背景色token
- deps
[feat] `less-plugin-dls`版本从`11.11.0`升级到`11.12.0`

### [4.41.0] 2024-04-18
- Lightbox
[feat] 增加`toolbar`、`minScale`、`maxScale`、`scaleStep`、`onScaleToFit`、`onZoomIn`、`onZoomOut`、`onScaleToOriginal`、`onTransform`支持图片缩放和回调
[feat] 展示视频时自动静音播放

### [4.40.14] 2024-04-16
- deps
[feat] `less-plugin-dls`版本从`11.9.0`升级到`11.11.0`

### [4.40.13] 2024-04-12
- Input
[feat] 增加`focus`、`blur`方法支持组件获取、失去焦点
- TextArea
[feat] 增加`focus`、`blur`方法支持组件获取、失去焦点

### [4.40.12] 2024-04-10
- Loading
[fix] 修复`children`的主题无法全局切换的问题

### [4.40.11] 2024-04-01
- Dropdown.Button
[fix] 带搜索的下拉框在`light-ai`主题下样式不符合预期的问题
- Stack
[fix] 修复多层`Stack`嵌套下`distribution`不符合预期的问题

### [4.40.10] 2024-03-29
- Stack
[feat] 新增`distribution`支持组件内部元素不同比例宽度的空间分布方式
- Dropdown.Button
[feat] 新增`icon`支持自定义前置图标

### [4.40.9] 2024-03-20
- Select
[feat] 废弃`dropdownMatchSelectWidth`，调整为`dropdownIndependentWidth`，默认行为改为下拉框的最小宽度宽度等于触发元素宽度，如果是存在自定义触发区，则默认下拉框宽度保持独立并自适应包裹内容
[feat] 下拉框增加最大宽度限制，如果超过则截断加上省略号
[feat] 增加`dropdownStyle`属性，用于自定义下拉弹窗样式
- Dropdown
[feat] 废弃`dropdownMatchSelectWidth`，调整为`dropdownIndependentWidth`，默认行为改为下拉框的最小宽度宽度等于触发元素宽度，如果是存在自定义触发区，则默认下拉框宽度保持独立并自适应包裹内容
[feat] 下拉框增加最大宽度限制，如果超过则截断加上省略号

### [4.40.8] 2024-03-15
- Select
[feat] 增加`triggerTarget`支持自定义触发区
- DatePicker
[feat] 增加`hideInput`支持隐藏日期面板顶部输入框
[fix] 修复月份选择器面板高度过高的问题
- Input
[fix] 修复在组件挂载后再次rerender时，`width`为100%不生效的问题

### [4.40.7] 2024-03-07
- Form
[feat] 增加`fillWidth` 支持表单项撑满宽度，并增加css变量`--dls-form-field-min-width`、`--dls-form-field-max-width`、`--dls-form-grid-column-gap`精细化控制表单项宽度大小
- ConfigProvider
[feat] 支持全局增加`normalized`属性，目前只有Tag组件有此属性，用于消除默认margin

### [4.40.6] 2024-03-05
- Table
[fix] 修复表头筛选器下拉菜单在`light-ai`主题下样式不符合预期的问题
- Tag
[fix] 修复在`checkable`属性为默认值或者false的时候`border`属性失效的问题

### [4.40.5] 2024-02-29
- Table
[fix] 修复Table组件分页器无法根据全局ConfigProvider和table组件自身props切换主题theme和尺寸size的问题

### [4.40.4] 2024-02-24
- Drawer
[fix] 修复滚动条锁定不符合预期问题

### [4.40.3] 2024-02-23
- Cascader
[feat] 增加`xsmall`尺寸
- DatePicker
[feat] 增加`xsmall`和`large`尺寸
- TimePicker
[feat] 增加`xsmall`和`large`尺寸

### [4.40.1] 2024-02-04
- Carousel
[fix] 修复图片内容底部有多余间距的问题
- Select/CascaderPane/TimePicker
[fix] 修复下拉菜单浮层上下方内间距

### [4.40.0] 2024-02-04
- Form
[feat] 更新Label必填标志字符为Icon（⚠️影响Label整体宽度）

### [4.39.21] 2024-02-01
- Form
[fix] 修复Label高度适配问题
- theme
[fix] 修复Dialog主题不透传问题

### [4.39.20] 2024-02-01
- theme
[fix] 修复d22 hover颜色token

### [4.39.19] 2024-01-31
- TextArea
[fix] 修复自带报错，在d22主题下样式问题

### [4.39.18] 2024-01-30
- Select
[feat] `defaultActiveFirstOption`关闭的情况下，支持Enter输入标签
- Input
[fix] 修复自带报错，在d22主题下样式问题

### [4.39.17] 2024-01-25
- Pagination
[fix] 样式对齐

### [4.39.16] 2024-01-08
- NumberInput
[fix] 增加按钮disabled行为不符合预期问题

### [4.39.15] 2024-01-02
- Transfer
[fix] 修复元素title非string类型搜索报错问题

### [4.39.14] 2023-12-26
- Tabs
[fix] 修复默认类型禁用样式不符合预期问题
- deps
[feat] 调整文字等全局色，`less-plugin-dls`从`11.8.4`升级到`11.9.0`

### [4.39.13] 2023-12-26
- theme
[feat] 更新`light-ai`功能色token

### [4.39.12] 2023-12-22
- Toast
[fix] 修复firefox下异常问题(⚠️v4.39.11引入)

### [4.39.11] 2023-12-21
- Toast
[feat] 支持根据container隔离实例，防止不同模块设置干扰

### [4.39.10] 2023-12-21
- Toast
[feat] 调用参数增加`getContainer`属性

### [4.39.9] 2023-12-20
- Carousel
[feat] 增加`autoplaySpeed`属性

### [4.39.8] 2023-12-20
- DatePicker
[feat] 增加`children`支持自定义触发区元素

### [4.39.7] 2023-12-14
- Drawer
[fix] 修复滚动条锁定不符合预期问题 (⚠️v4.32.0升级`rc-util`版本引入)

### [4.39.6] 2023-12-13
- Select
[fix] 修复下拉内容右侧贴边遮挡位置未自动调整问题

### [4.39.5] 2023-12-13
- Cascader
[fix] 修复`value`值不包含子项时，回填区删除不符合预期问题

### [4.39.4] 2023-12-07
- Uploader
[fix] 修复开发期间的Warning

### [4.39.3] 2023-11-28
- Button
[feat] 调整按钮纵向对齐方式
- NumberInput
[bugfix] 修正宽度不符合预期问题

### [4.39.2] 2023-11-28
- Rating.Emoji
[feat] 去掉hover缩放效果

### [4.39.1] 2023-11-28
- Rating.Emoji
[feat] 标签文字支持配置
- Dialog
[bugfix] 对齐标题字重规范

### [4.39.0] 2023-11-27
- Rating
[feat] 新增表情评分变体`Rating.Emoji`，用于对话反馈场景
- Tag
[feat] 调整`Tag`基础样式，`less-plugin-dls`从`11.8.2`升级到`11.8.4`
- Loading
[bugfix] 修复无法遮罩有`z-index`的元素
- theme
[feat] 增加透明色板CSS变量导出

### [4.38.3] 2023-11-21
- types
[bugfix] 修复Stack等组件的类型

### [4.38.2] 2023-11-21
- theme
[bugfix] 修复主题色板重复输出问题

### [4.38.1] 2023-11-20
- deps
[feat] `less-plugin-dls`从`11.6.3`升级到`11.8.2`，调整`Pagination`已选页码样式

### [4.38.0] 2023-11-20
- theme
[feat] 增加主题，灰度，功能色板CSS变量导出
- NumberInput
[bugfix] 修复d22主题下背景色

### [4.37.2] 2023-11-16
- Select
[feat] 多选自定义回填回调函数`customRenderTarget`补充`options`参数

### [4.37.1] 2023-11-16
- Button
[feat] 调整`ghost-light`按钮背景色

### [4.37.0] 2023-11-16
- Button
[feat] 新增`ghost-light`按钮，用于浅色底场景
- theme
[feat] `light-ai`的`info`功能色与主题色同步

### [4.36.4] 2023-11-14
- Uploader
[bugfix] 修复`style`不生效问题

### [4.36.3] 2023-11-10
- Select
[bugfix] 修复多选自定义选项内容换行不可见问题

### [4.36.2] 2023-11-07
- Select
[bugfix] 修复选择框右侧间距过大问题
- Form.Field
[feat] 增加`validatingMessage`用于校验中信息提示(暂不区分rule)
- TextArea
[feat] 支持`minRows`与`maxRows`受控

### [4.36.1] 2023-10-25
- Table
[bugfix] 修复表头鼠标滚轮事件异常 (⚠️v4.32.0升级`rc-util`版本引入)

### [4.36.0] 2023-10-24
- Rating
[feat] 新增评分组件

### [4.35.5] 2023-10-19
- theme
[bugfix] `light-ai`主题调整`s`与`xs`高度token

### [4.35.4] 2023-10-19
- Cascader
[feat] 增加`ALL_VALUE`常量导出，用于全部选项
- ConfigProvider
[feat] `ProviderConfig` => `ConfigProvider`，并导出`withConfigConsumer`

### [4.35.3] 2023-10-16
- Cascader
[feat] `displayOptions`回填选项增加是否可删除，用于自定义缩略选项

### [4.35.2] 2023-10-13
- Form
[feat] 校验规则类型增加`string-cn`，用于保持与Input与TextArea默认计算规则一致

### [4.35.1] 2023-10-11
- Cascader
[feat] 增加`displayOptions`支持定制回填区展示选项

### [4.35.0] 2023-10-11
- Cascader
[feat] 增加`mergeChecked`支持合并展示方式，默认`downwards`，其他可选值为`keep-all` | `downwards` | `upwards`

### [4.34.1] 2023-09-25
- lightPrefix
[fix] 修复开发期间的Warning

### [4.34.0] 2023-09-21
- Card
[feat] 新增
- ConfigProvider
[feat] `theme`支持`light-ai`, `light-d22`, `light-d20`
- Dropdown
[feat] 增加`buttonType`与`split`属性替代`type`, `primaryType`, `textLink`
- style
[feat] 增加`index.ai`样式文件，删除`common.d22`与`index.d22`
- Pagination
[feat] 跳转输入框调整为数字输入框，防止非数字输入
- NumberInput
[feat] 增加`onPressEnter`回调

### [4.33.0] 2023-09-19
- Menu
[bugfix] 增加padding部分背景色
- Nav
[bugfix] 修复宽度计算不精确导致的导航缩略问题(原计算用offsetWidth默认取整)
- NumberInput
[bugfix] 修复加强型高度不规范问题
- Tag
[feat] 增加`normalized`属性用于消除默认margin
- Transfer
[feat] 选择面板增加背景色
- Select
[feat] `defaultActiveFirstOption`默认值调整为`false`
- theme
[feat] 支持组件粒度`theme`属性

### [4.32.1] 2023-09-01
- NumberInput
[bugfix] 修复当输入框清空后，再输入`0`没有触发`onChange`回调问题

### [4.32.0] 2023-08-23
- Drawer
[bugfix] `rc-util`从`4.21.0`升级到`5.37.0`，修复Drawer无法在React 18 Concurrent & React.StrictMode下展示问题
- Dropdown
[bugfix] 修复React 18下拉抖动问题

### [4.31.6] 2023-08-21
- TextArea
[bugfix] 修复type的ref问题

### [4.31.5] 2023-08-18
- Lightbox
[feat] 非当前视频，自动暂停播放

### [4.31.4] 2023-08-16
- TextArea
[bugfix] 修复输入时提前增高问题(原计算方式过于粗暴)
[bugfix] 修复宽度无法自适应问题(支持无侧边信息提示情况下)

### [4.31.3] 2023-08-14
- Table
[bugfix] column render函数返回类型支持TableCell属性
[bugfix] clearfix删除空格，防止`white-space: pre`引起空行问题
[bugfix] 修复某些场景下resize抖动问题

### [4.31.2] 2023-08-11
- Cascader
[feat] 增加`before`属性，支持自定义前置区
[feat] 增加`popupWidthStretch`属性，可选`width` | `min-width`，支持下拉面板不同宽度拉伸策略
[feat] 当`displayRender`返回值为空时，展示`placeholder`

### [4.31.1] 2023-08-10
- Table
[feat] 增加`variant`属性，支持`basic`变体，用于单元格自定义样式

### [4.31.0] 2023-08-07
- Table
[feat] `updateWidthChange`默认值调整为`true`，尺寸变化触发更新
[bugfix] 修复`ResizeObserver`在某些极端场景下不触发(如`display`从`block`=>`none`=>`block`快速切换)导致的尺寸为`0`问题

### [4.30.9] 2023-08-07
- Transfer
[feat] 增加`display`属性，可配置仅展示`candidate`(备选区)或`selected`(已选区)
- Button
[bugfix] `icon`类型检测，`dls-icons-react 3.16.0`，从`function`升级为`forwardRef`

### [4.30.8] 2023-08-03
- NumberInput
[bugfix] 修复当输入值不在范围内，且非首次blur时，展示的值未做修正
- deps
[feat] `dls-icons-react`从`^3.4.0`升级到`^3.15.0`

### [4.30.7] 2023-07-21
- Dropdown.Button
[feat] 适配`overlay`属性，支持自定义下拉面板

### [4.30.6] 2023-07-14
- Transfer
[feat] 新增`cadidateEmpty`与`seletedEmpty`属性，并补充默认无数据展示
- Cascader
[bugfix] 修复placeholder溢出问题

### [4.30.5] 2023-07-04
- Transfer
[feat] `CandidateItem`增加`itemKey`属性表示选项的`key`

### [4.30.4] 2023-06-30
- Cascader
[bugfix] 修复无边框样式下搜索框样式问题

### [4.30.3] 2023-06-19
- Dropdown.Button
[feat] 增加`loading`与`loadingText`属性，支持加载状态
[feat] 调整空状态内容高度限制

### [4.30.2] 2023-06-07
- Toast
[feat] 默认顶部距离调整为10%
- Loading
[feat] 遮罩类Loading增加渐显效果
- Stack
[bugfix] 修复Stack前元素的交互遮挡问题
- SearchBox
[bugfix] 修复`searchIconType`为`button`且`disabled`时边框样式问题

### [4.30.1] 2023-05-10
- Menu
[bugfix] 修复`basic`变体的`disabled`样式不生效问题

### [4.30.0] 2023-05-09
- NumberInput
[feat] 增加`valueAsNumber`属性，值为`true`时，`onChange`的`value`调整为`number`类型或者空字符串
[bugfix] 非预期范围的值不触发`onChange`回调
- Loading
[bugfix] 移除垂直布局文本的marginLeft
- DatePicker
[bugfix] 修复`validateMinDate`与`validateMaxDate`从有值变为`undefined`后，禁用日期范围不符合预期问题
- Dropdown.Button
[feat] 增加`showArrow`用于控制下拉剪头Icon展示
- Input
[bugfix] 修复包含suffix/affix且错误信息为bottom时位置不正确问题
- Drawer
[feat] 增加`headerClassName`属性
- style
[feat] 增加`scrollbar`样式文件，使用方式参考样式帮助
- deps
[feat] `less-plugin-dls`从`11.3.0`升级到`11.6.3`(⚠️主要更新到D22风格)

### [4.29.8] 2023-03-29
- Radio.Group
[bugfix] 修复已选值不可清空问题
- Uploader
[feat] 增加`afterUpload`回调，可用于对返回数据进行相关处理
- style
[feat] 增加`common.d22`与`index.d22`样式文件，使用方式参考样式帮助

### [4.29.7] 2023-03-29
- Tabs
[feat] 新增`onBeforeDelete`接口，支持删除前回调

### [4.29.6] 2023-03-24
- Tooltip
[bugfix] 修复`defaultVisible`功能失效问题

### [4.29.5] 2023-03-22
- Dropdown
[bugfix] 修复当`type`为`primary`且宽度受挤压时的折行问题
- deps
[feat] `dls-icons-react`从`^2.9.0`升级到`^3.4.0`

### [4.29.4] 2023-03-21
- Select
[bugfix] 修复当`value`为`null`时，搜索框输入文字时placeholder遮挡问题

### [4.29.3] 2023-03-16
- Slider
[bugfix] 修复className未生效问题
- Form
[bugfix] 修复Form.Field的required标记控制不正确问题

### [4.29.2] 2023-03-15
- Anchor
[feat] 对齐规范：选中样式、缩进、点击区域

### [4.29.1] 2023-03-15
- Uploader
[feat] 新增`lightboxProps`属性，用于自定义预览

### [4.29.0] 2023-03-08
- Stack
[feat] 新增堆叠容器组件，支持容器内元素多种排列方式
- Button
[feat] 当`loading`为`true`时，阻止默认行为(如`submit`)

### [4.28.0] 2023-03-02
- Dropdown
[feat] 增加`header`与`footer`自定义区
- Dropdown.Button
[bugfix] 修复`visible`属性不完全受控问题
- NumberInput
[feat] 增加`prefix`与`suffix`前后自定义
- Select
[feat] 增加`visible`属性控制下拉展示

### [4.27.1] 2023-02-06
- Form
[bugfix] 修复`onFinish`回调函数状态更新问题
- Popover
[feat] 标题对齐样式规范(移除底边线，调整内边距)

### [4.27.0] 2023-01-12
- Button
[feat] 增加`ghost`系列变体，包括`ghost`、`ghost-aux`、`ghost-strong`、`ghost-reverse`
- Dialog
[bugfix] 修复高度`height`设置不生效问题
- Form
[feat] 属性`layout`增加`grid`网格布局
[feat] 增加`density`属性，支持紧凑布局
[feat] 调整`inline`布局下的间距
[feat] 支持在`grid`或`inline`情况下，操作独占行
[feat] 增加`FieldGroup`支持局部布局
- Anchor
[bugfix] 修复二级选项选择抖动问题
[feat] 移除二级选项的下划线

### [4.26.0] 2023-01-10
- Select
[feat] 选项新增`exclusive`属性，支持单复选混合
- Checkbox
[feat] 新增`exclusive`属性，支持单复选混合。同时`Group`增加`emptyValue`可在取消选择时，填充默认选择
- Table
[bugfix] 修复容器宽度为存在小数时出现滚动条问题
[bugfix] 修复展开列无法固定(fixed)问题
[bugfix] 修复展开列宽度无法保持问题(总列宽小于容器宽时)

### [4.25.0] 2022-12-12
- DatePicker
[feat] 新增`mode`属性(值: 'date' | 'week')，支持单个自然周选择
- Transfer
[feat] 新增`mergeChecked`属性(值：'keep-all' | 'downwards' | 'upwards')，支持已选值不同合并策略
- Input, TextArea, SearchBox
[bugfix] 修复`value`在IME composition时不完全受控问题

### [4.24.3] 2022-12-02
- Table
[bugfix] 修复非预期横向滚动条问题

### [4.24.2] 2022-11-30
- Tabs
[bugfix] 修复tabs翻页样式不符合预期问题(v4.24.1引入)

### [4.24.1] 2022-11-23
- Menu
[feat] `MenuItem`新增`value`属性，用于`key`(string类型)无法满足的场景
- Dialog
[bugfix] 修复标题(`title`)与关闭图标(`needCloseIcon`)都不展示时header仍然占位问题
- Table
[bugfix] 筛选的选中态支持非数组类型值(主要用于自定义筛选场景)
- Tabs
[bugfix] 修复不同类型tabs嵌套样式问题

### [4.24.0] 2022-11-22
- CascaderPane
[feat] `option`增加`selectable`设置是否可选
[feat] `option`增加`expandType`设置不同展开方式: `inline`内联展开不可收起; `toggle`内联可收起; `column`列展开

### [4.23.0] 2022-11-16
- Tabs
[feat] 增加`sortable` `onSort`属性，支持拖拽排序
[feat] 增加删除元素动效
[feat] 支持鼠标滚动前后翻页
[feat] 调整新增Icon，调整删除Icon间距等规范
- NumberInput
[feat] 增加`showSpinButton`属性控制调节按钮展示隐藏
- Table
[bugfix] 修复子列宽度调整更新不正确问题

### [4.22.7] 2022-11-04
- Uploader
[feat] `file`类型支持`pickerIcon`与`pickerText`自定义
- Cascader
[bugfix] 修复`value`受控不符合预期问题
- Select
[bugfix] 修复回填为`enum`且值为`0`时，回填标签不正确问题
- Table
[bugfix] 修复列调整时，固定列位置不符合预期问题(列变更，相同位置fixed不变情况下)
- Message
[bugfix] 修复不同类型嵌套时，状态样式不符合预期问题
- Form
[bugfix] 修复校验函数`validator`返回空字符串异常
- deps
[feat] `less-plugin-dls`版本从`10.0.0`升级到`11.3.0`

### [4.22.6] 2022-10-28
- Pagination
[bugfix] 修复`total`与`pageSize`同时更新结果不正确问题

### [4.22.5] 2022-10-18
- theme
[bugfix] 修复d22主题下`strong`单复选组禁用背景样式

### [4.22.4] 2022-10-14
- Table
[bugfix] 修复子表无法展开问题(v4.22.0引入)

### [4.22.3] 2022-10-13
- Table
[bugfix] 支持`rowSelection.type`属性受控

### [4.22.2] 2022-10-13
- Select
[feat] 新增`footer`属性，支持底部自定义

### [4.22.1] 2022-10-12
- theme
[bugfix] 修复d22主题下button禁用样式问题

### [4.22.0] 2022-09-13
- DatePicker.RangePicker
[feat] 新增`mode`属性(值: date | week)，支持自然周选择
- Table
[feat] 支持表头区(固定表头)横向滚动整个表格
[feat] 条状Loading调整为默认, 尺寸调整为xs, 并默认增加遮罩
- theme
[bugfix] 修复d22主题下simple类型单复选样式问题

### [4.21.0] 2022-08-29
- Input/TextArea
[feat] `value`长度校验时，调整为不进行`trim`处理
- Steps
[feat] 新增`type`属性，增加`dot`类型
[feat] 新增`onChange`属性，废弃`onClickStep`(1-based string)
[feat] 调整默认类型的步骤色彩规范
[bugfix] 修复`error`状态，连接线颜色不正确问题
- Switch
[feat] 禁用时支持Popover
- Select
[bugfix] 修复Option内容不兼容Select.CheckboxText问题(v4.16.0引入)
- Uploader
[feat] 调整卡片边框与背景色(light-d22主题)
- Progress
[feat] 调整尺寸规范，增加`xsmall`尺寸
- deps
[feat] `less-plugin-dls`从`9.1.1`升级到`10.0.0`

### [4.20.1] 2022-08-19
- Loading
[bugfix] 修复Loading嵌套时样式问题
[feat] 优化Loading.Bar初始进度效果
- Cascader
[bugfix] 修复多选框勾选影响节点后第三级列展开问题
[bugfix] 修复`expandTrigger`触发方式为`hover`时无法自定义加载
- Badge
[feat] 内联方式支持`color`配置
- deps
[bugfix] `async-validator`升级到`4.2.5`修复某些URL校验卡死问题
- Progress
[feat] 中号线宽从8调整为6
- Table
[bugfix] 修复`loadingOption`为空时loading不展示问题(4.20.0)
[feat] 完善条形Loading对固定表头和列支持
- Dialog
[bugfix] 修复无遮罩时`box-shadow`不展示问题

### [4.20.0] 2022-08-15
- Table
[feat] 新增条形无遮罩加载进度条支持
- Loading
[feat] 新增Loading.Bar组件
- Embedded
[feat] 废弃该组件
- Dialog
[bugfix] 修复关闭按钮遮挡标题问题
- Button
[feat] `normal`类型按钮还原半透明背景色调整(v4.19.0时增加)

### [4.19.0] 2022-08-08
- Input
[feat] 新增Input.Group，完善分组能力
[fix] 修复配置`addonBefore`，计数器、清空按钮位置错乱问题
[refactor] 非弹出方式报错(`errorLocation`为`layer`)的输入框移除Popover
- NumberInput
[bugfix] 修复max/min变更，步进器状态不同步问题
- Select
[fix] 修复多选搜索到匹配项，Blur时自动选中问题
[feat] 放开搜索不到时，空面板高度限制，方便自定义内容
- theme
[feat] 新增`light`主题，具体功能参考线上文档的【主题】页面
- deps
[feat] `dls-icons-react`从`2.6.0`升级到`2.9.0`
[feat] `less-plugin-dls`从`9.1.0`升级到`9.1.1`

### [4.18.1] 2022-07-25
- Table
[bugfix] 去除`overflow: hidden`，修复Popover挂载到单元格，展示不全问题(表格高度不够时)。

### [4.18.0] 2022-07-12
- Empty 新增
[feat] 支持图片/标题/描述/底栏与尺寸定义，满足不同场景下空状态需求
- Uploader
[feat] 属性`accept`值调整为大小写不敏感
[bugfix] 从inline-block调整为flex，修复负边距占位问题
- Badge
[bugfix] 修复状态圆点挤压问题
- less-plugin-dls 从5.1.0升级到9.1.0
[feat] ⚠️移除Loading自带的padding。其他参考: https://github.com/ecomfe/light-dls/blob/master/packages/less-plugin-dls/CHANGELOG.md

### [4.17.1] 2022-07-12
- Form
[bugfix] 值失效比较从eq调整为isEqual深比较，修复当使用`mapPropsToFields`且值为`Object`类型时值标记为`expired`需要二次校验问题

### [4.17.0] 2022-06-28
- Uploader
[bugfix] 修复文件超过最大限制时，替换文件操作不可用问题
- Cascader / CascaderPane
[feat] 新增`renderOption`，支持自定义选项渲染
- CascaderPane
[feat] 新增`loadData`，支持异步数据加载

### [4.16.0] 2022-06-16
- Transfer
[feat] 新增`onChange`，用于简化`handleSelect`, `handleSelectAll`, `handleDelete`, `handleDeleteAll`四个接口
[feat] 新增`dataSource`，用于简化`allDataMap`与`candidateList`
[feat] 废弃`selectedList`，调整为`value`
[feat] 新增`loadData`，数据项增加`isLeaf`，支持数据异步加载与Loading图标
- Tree
[feat] 支持选择与勾选时触发`loadData`异步加载数据
- Input
[feat] `onChange`接口对齐，参数增加`target`属性
- TextArea
[feat] `onChange`接口对齐，参数增加`target`属性
- Select
[bugfix] 修复多选不展示勾选框问题(当`mode`为`tags`或`multiple`，`children`为ReactElement类型时)
[bugfix] 修复单选搜索无关键词高亮问题
[bugfix] 修复`value`为`null`时，`placeholder`展示逻辑问题
[feat] 属性`placeholder`增加默认值`请选择`
[feat] 增加`autoClearSearchValue`属性
- Toast
[feat] 调整为面形图标
[bugfix] 调整间距与对齐
- Overlay
[feat] 增加圆角
- Cascader/CascaderPane/TimePicker
[bugfix] 优化圆角

### [4.15.1] 2022-05-30
- rc-trigger
[bugfix] 版本升级到`5.3.1`，修复`hover`展开面板非预期收起问题

### [4.15.0] 2022-05-27
- Uploader
[feat] `file`类型上传错误信息展示从气泡调整为常显
[feat] `file`类型上传去除状态Icon, 操作Icon展示从hover调整为常显
[feat] `file`类型上传增加`controls`自定义操作
[feat] `file`类型上传调整整体样式包括：图标/宽度/间距等
[feat] `image`类型上传增加`pickerIcon`与`pickerDesc`支持自定义图标与描述
[feat] `image`类型上传增加`onPickerClick`支持取消默认上传与自定义点击操作
[feat] `image`类型上传支持`helperText`与`helperTextPosition`支持配置帮助信息
[feat] `image`类型上传增加`errorDisplay`支持配置错误信息展示方式`popup` | `normal`
[feat] `image`类型上传文件增加`bottom`支持底部自定义
[feat] `image`类型上传增加`pickerPosition`支持`top` | `before` | `after` | `none`
[feat] 废弃`order` 合并到 `pickerPosition`
[feat] 废弃`hideAnchor` 调整为 `hidePicker`
[feat] 废弃`helperTextPostion` 调整为 `helperTextPosition`
[feat] 废弃`formatUploadAnchor` 调整为 `picker`
[feat] 废弃`customUploadListIcon` 与 `renderCustomIcon` 调整为 `controls`
[feat] 优化上传数达到上限，picker隐藏/禁用逻辑
[bugfix] 修复上传最大文件数限制逻辑

### [4.14.5] 2022-05-30
- rc-trigger
[bugfix] 版本升级到`5.3.1`，修复`hover`展开面板非预期收起问题

### [4.14.4] 2022-05-23
- 样式属性
[bugfix] Cascader`style`属性不支持`width`
[feat] CascaderPane支持`style`属性
[feat] DatePicker/DateRangePicker/MonthPicker支持`style`属性
[feat] Lightbox支持`style`属性
[feat] Pagination支持`style`属性
[feat] Progress支持`style`属性
[feat] SearchBox支持`style`属性
[feat] Slider支持`className`属性
[feat] Transfer支持`style`属性
[feat] Tree支持`style`属性
[feat] Nav支持`style`属性
[feat] Uploader支持`style`属性
- Badge
[bugfix] 修复状态与文字对齐
- Form
[feat] Form横向布局Field增加间距
- rc-trigger
[bugfix] 减少下拉过程中的reflow
- Drawer
[bugfix] `width`兼容字符串型数字

### [4.14.3] 2022-05-19
- 样式
[bugfix] 支持LESS 4
[feat] 新增`typography.css`，包含数字字体`Baidu Number`
- Cascader
[bugfix] 修复React 17下label为ReactElement时性能问题

### [4.14.2] 2022-05-18
- Form
[bugfix] 修复create表单的ref
- Alert
[bugfix] 修复`style`属性不生效问题

### [4.14.1] 2022-05-16
- Cascader
[bugfix] 修复选项右侧icon展示

### [4.14.0] 2022-05-13
- Message 新增
[feat] `size`支持`medium` | `small`
[feat] `type`支持`info` | `error` | `success` | `warning` | `aux` 对应：信息 | 错误 | 成功 | 警告 | 辅助信息
[feat] `icon`支持自定义
[feat] `display`支持`normal` | `popup` | `simple` | `standalone`展示方式
- Form
[feat] 支持不采用`Form.create`，简化表单创建接口
[feat] 新增`Form.Field`合并`Form.getFieldDecorator`与`Form.Item`，简化表单项创建接口
[feat] `Form.Field`异步校验，自动展示loading
[feat] `Form.Field`通过`abstract`属性支持校验状态展示，校验信息由上级非`abstract`统一展示
[feat] `Form.Field`增加`tip`支持标签后问号帮助提示
[feat] `Form.Field`增加`helpPosition`支持多个位置(`top` | `side` | `bottom`)辅助信息展示
[feat] `Form.Field`校验信息支持`warning` | `success`类型
[feat] `Form`新增`onFinish`, `onFinishFailed`, `scrollToFirstError`简化表单提交
[feat] `Form`内置重置处理
[feat] `Form`新增`labelPosition`支持不同位置(`side` | `top`)展示
[feat] 新增CSS变量`--dls-form-label-width`，支持标签宽度设置
[feat] `Form`实例增加`setFieldsError`与`scrollToField`
[bugfix] 修复validator内部报错未捕获问题
- Select
[feat] 支持focus态样式

### [4.13.9] 2022-05-13
- Tabs
[bugfix] 移除子组件`TabPane`类型检查
- Steps
[bugfix] 移除子组件`Step`类型检查

### [4.13.8] 2022-05-13
- Tree
[bugfix] 修复`treeData`属性失效问题

### [4.13.7] 2022-05-10
- Dialog
[bugfix] 修复`maskClosable`功能失效问题
- Checkbox
[bugfix] 修复纵向组文字过长间距问题
- Table
[bugfix] 修复有子表头的表格筛选异常问题

### [4.13.6] 2022-04-24
- Table
[feat] 支持合并表头固定
[bugfix] 修复表头非首行边框线样式不正确问题
- Steps
[bugfix] 修复步骤连接线间距不正确问题

### [4.13.5] 2022-04-15
- Table
[bugfix] 修复子母表选择列对齐
[bugfix] 修复子表非预期横向滚动条
[bugfix] 修复右固定列右侧偶现漏出文字
[bugfix] 修复`rowSelection`属性`hideDefaultSelections`不受控问题
- Cascader
[bugfix] 修复当`inputIsControlled`为`true`且`options`为空异常问题
[bugfix] 修复当叶子节点存在`children`空数组，搜索不到该选项问题
- DatePicker
[bugfix] 修复下拉展开抖动问题

### [4.13.4] 2022-04-07
- Lightbox
[bugfix] 修复`dataSource`为空数组时异常问题
- DatePicker
[bugfix] 修复在输入框编辑年份时出现错误年份的问题（识别为`dd-mm-yy`格式）
- Cascader
[bugfix] 允许叶子节点存在重复`value`

### [4.13.3] 2022-04-01
- Form
[bugfix] 修复表单`子`项受表单`labelCol`影响布局问题

### [4.13.2] 2022-03-31
- Cascader
[bugfix] 修复`value`为`null | undefined`是异常问题
- Select
[bugfix] 没有选项时展示`无匹配结果`(原`showSearch`为`false`才展示)
[feat] 自定义回填为`string`类型时，可支持搜索

### [4.13.1] 2022-03-30
- Select
[bugfix] 修复向上展开位置非预期
- Transfer
[bugfix] 修复值判断错误(if直接判断)，导致某些选项无法选中问题
- Tooltip
[bugfix] `onVisibleChange`移除是否有内容判断，始终生效
- Cascader
[bugfix] 修复`children`为空数组非预期展示问题

### [4.13.0] 2022-03-29
- Cascader
[feat] 新增多选功能
- Alert.Page
[feat] 单条数据隐藏翻页
- DatePicker
[bugfix] 回填区修正为左右对齐
- Input
[bugfix] 修正前后缀禁用态背景色，调整垂直居中对齐
- TextArea
[bugfix] 修正非预期滚动条
- dls-icons-react
[feat] 版本调整为 `>= 2.6.0`
[bugfix] 调整组件Icon颜色实现方式，适配新版
- Tooltip
[bugfix] 修复非预期自动关闭问题(Popover基于Tooltip同时生效，4.12.0版升级`rc-trigger`引入，请升级到最新版本)
- Form
[bugfix] 修复清理无用field异常问题

### [4.12.0] 2022-03-21
- Cascader
[refactor] 类型化
[refactor] 移除`findDOMNode`
[refactor] 移除`react-lifecycles-compat`
- CascaderPane
[refactor] 类型化
[refactor] 移除`findDOMNode`
[refactor] 移除`react-lifecycles-compat`
- Dialog/Dialog.confirm/Dialog.alert
[refactor] 类型化
[refactor] 移除兼容React 15代码
- Dropdown/Dropdown.Button
[refactor] 类型化
[refactor] 移除`react-lifecycles-compat`
- Form/Form.Item/Form.create/Form.createFormField
[refactor] 类型化
[refactor] 移除`createReactClass`
[refactor] 移除`componentWillReceiveProps`
- Grid/Grid.Col/Grid.Row
[refactor] 类型化
- Input
[refactor] 类型化
[refactor] 移除`react-lifecycles-compat`
- Lightbox
[refactor] 类型化
[refactor] 移除`react-lifecycles-compat`
[feat] 属性`datasource`变更为`dataSource`
- Link/Link.NavLink
[refactor] 类型化
[refactor] 移除兼容React 15代码
- Loading
[refactor] 类型化
- Menu/Menu.Item/Menu.SubMenu/Menu.Divider/Menu.ItemGroup
[refactor] 类型化
[refactor] 移除`rc-menu`依赖
[refactor] 移除`componentWillReceiveProps`
[refactor] 移除legacy Context
- Nav
[refactor] 类型化
[refactor] 移除`react-lifecycles-compat`
[feat] 属性`datasource`变更为`dataSource`
- NumberInput
[refactor] 类型化
- Overlay
[refactor] 类型化
- Progress
[refactor] 类型化
- Radio/Radio.Button/Radio.Group
[refactor] 类型化
- SearchBox
[refactor] 类型化
[refactor] 移除`react-lifecycles-compat`
- Select
[refactor] 移除`react-lifecycles-compat`
[refactor] `rc-menu`从`7.4.32`升级到`8.0.3`
[refactor] 移除`componentWillReceiveProps`
[bugfix] 修复`options`内`label`为非`string`类型时搜索异常
- Sidenav
[refactor] 类型化
- Slider
[refactor] 类型化
- Sortable
[refactor] 类型化
- Steps/Steps.Step
[refactor] 类型化
- Switch
[refactor] 类型化
[refactor] 移除`react-lifecycles-compat`
- Table
[refactor] 类型化
[refactor] 移除`findDOMNode`
[refactor] 移除`react-lifecycles-compat`
[refactor] 移除`componentWillReceiveProps`
[refactor] 移除legacy Context
- Tabs
[refactor] 类型化
[refactor] 移除`react-lifecycles-compat`
- Tag/Tag.Group/Tag.EditableGroup
[refactor] 类型化
[refactor] 移除`react-lifecycles-compat`
- TextArea
[refactor] 类型化
[refactor] 移除`react-lifecycles-compat`
- TimePicker
[refactor] 类型化
[refactor] 移除`react-lifecycles-compat`
[refactor] 移除`findDOMNode`
[bugfix] 修复组件卸载时未清理状态更新问题
- Toast
[refactor] 类型化
- Transfer
[refactor] 类型化
[refactor] 移除`react-lifecycles-compat`
- Tree/Tree.TreeNode/Tree.VirtualTreeNode
[refactor] 类型化
[refactor] 移除`react-lifecycles-compat`
[refactor] 移除legacy Context
[refactor] 清理drag相关逻辑
- React/ReactDOM依赖调整为`>= 16.12.0`，对齐EE规范要求

### [4.11.1] 2022-03-18
- Tabs
[bugfix] 修复从非翻页切换到翻页边界时报错问题(4.10.0版引入，请升级到最新版本)
- Table
[bugfix] 修复全选右侧自定义选择项下拉Icon位置问题(4.11.0版引入，请升级到最新版本)

### [4.11.0] 2022-03-17
- Checkbox/Radio
[feat] 文字换行时选择框相对首行文字居中对齐
- CSS
[feat] common文件`line-height`调整为`@dls-line-height-1`

### [4.10.0] 2022-03-15
- Tabs
[feat] 去除关闭按钮动效，尺寸统一调整为`12px`
[feat] 提示状态图标改用空心样式；规范图标尺寸为字体大小+2px
[feat] `line`型Tab已选底线长度与文字对齐(大于最小宽时)
[bugfix] 修复`hideSpace`时，`line`型Tab已选底线展示不全问题
[feat] `line`型Tab项增加最大最小宽限制
[feat] 增加CSS变量`--dls-tab-menu-padding`，支持左右边距定制
[feat] 调整`simple`与`line`型Tab不同尺寸的项目间距
[feat] 调整添加标签button尺寸，跟组件size保持一致
- Table
[feat] `rowSelection.renderCheckbox`增加已渲染节点参数
[bugfix] 修复表头全选与半选态错乱问题
- DatePicker
[feat] 新增`placeholder`配置属性

### [4.9.0] 2022-03-08
- Transfer
[feat] 调整下拉固定宽`70px`为最大`128px`最小宽`88px`自适应
[feat] 调整搜索placeholder为`请搜索`
[bugfix] 修复搜索非受控情况下，搜索词匹配highlight不正确问题
[bugfix] 已选禁用项，删除按钮样式调整为禁用态
- Affix
[bugfix] 修复mount时位置不正确问题
- Checkbox
[feat] 支持选项文字超长自动换行(同时从inline-block调整为inline-flex)
- Radio
[feat] 支持选项文字超长自动换行(同时从inline-block调整为inline-flex)
- Transition
[feat] 新增动效组件，支持渐隐渐现/展开收起/位移等多种基础动效
[feat] Select/Cascader/Dropdown/Overlay/TimePicker增加下拉动效

### [4.8.1] 2022-02-22
- Pagination
[bugfix] 属性`pageSizeOptions`选项兼容`string`类型数组

### [4.8.0] 2022-02-18
- Lightbox
[feat] 新增`maskClosable`属性，支持点击蒙层关闭
- Popover
[bugfix] 当内容为空时应不展示
- Table
[bugfix] 修复表格横向滚动1px偏差问题
- Dialog
[bugfix] scroll lock移除scrollbar-gutter方式，修复scrollbar-gutter遮挡元素问题

### [4.7.0] 2022-01-24
- Tabs
[feat] 增加`bordered`属性，当`type`为`line`时，支持去除底边
- Dialog
[bugfix] 修复通过api调起的对话框关闭异常问题(v4.5.0引入，不影响功能，但建议升级)
- Affix
[bugfix] 修复resize时，尺寸不同步问题
- Tooltip
[bugfix] 支持文字溢出换行
[refactor] 类型化/老代码清理/单测
- Popover
[refactor] 类型化/老代码清理/单测
- Badge
[bugfix] 规范状态小圆点与文字间距
- Select
[feat] 新增`showCheckAll`属性，支持`mode`为`multiple`时全选功能
[feat] 样式: 调整选项不同size下的左右间距
[bugfix] 修复多选框选择态只匹配`key`不匹配`value`问题，而`key`只能为`string`类型
[refactor] 类型化/老代码清理/单测
- Menu
[feat] 链接元素点击区扩展到整个选项
- Drawer
[bugfix] 关闭按钮尺寸对齐规范
[refactor] 类型化/老代码清理/单测

### [4.6.0] 2022-01-12
- Nav
[feat] 移除底部边框
[feat] 调整动效
[feat] 调整选项下拉列表样式
- Uploader
[feat] 图片类上传增加自定义尺寸配置(--dls-uploader-media-item-height, --dls-uploader-media-item-width)
- Popover
[bugfix] 调整样式位置，修复无圆角问题

### [4.5.1] 2022-01-07
- Input/TextArea
[bugfix] 修复字符长度计算错误（按字节计算小于\x20被误计算为2）

### [4.5.0] 2022-01-04
- Layout
[bugfix] 内容区宽度被子元素撑大问题
- Uploader
[bugfix] 移除图片上传底部多余margin
[bugfix] 调整图片上传布局，适配inline-flex容器，避免非预期折行
- Sortable
[bugfix] 修复有`before`节点时，拖拽项判断错误问题
- Input
[bugfix] 修复组合输入框圆角问题
- Table
[bugfix] 宽度计算从offsetWidth调整为clientWidth, 解决非预期横向滚动条问题
- Dialog
[bugfix] 修复模态对话框scroll lock时页面宽度抖动以及状态问题
- less-plugin-dls 升级到5.1.0
[feat] 对齐Dropdown与Layout tokens
- CSS
[feat] 增加common样式文件(抹平浏览器差异)，入口处直接引入即可，方式同index

### [4.4.0] 2021-12-15
- Table
[bugfix] 统一内部column的key(dataIndex与key优先级不一致)，修复类似拖拽后宽度丢失等问题
[bugfix] 修复列拖拽没有捕获dragging过程时，onDragEnd数据不正确问题
- Uploader
[bugfix] 提升`hideAnchor`属性优先级（高于`maxFileLength`时的自动隐藏）
- Select
[bugfix] `mode`为`multiple`时，去除底部多余空间
- Cascader
[feat] 新增`header`, `footer`, `columnHeader`, `columnFooter`自定义区
[feat] 样式调整: 选项padding, 面板高度与圆角, 清除Icon
[bugfix] 去除移除最后一列右边框
- Layout 新增
[feat] 包括Layout, Header, Content, Footer, Sidebar几个部分
[feat] Header, Footer, Sidebar支持sticky
[feat] Sidebar支持部分/完全收起，自动收起，动效等

### [4.3.0] 2021-12-02
- Table
[bugfix] 当属性`rowSelection`包含`getCheckboxProps`时(无法感知变化)，选择列强制render
- Collapse
[bugfix] 修正tabindex
- Uploader
[bugfix] 去除文件上传组件底部margin
- Tooltip
[bugfix] 修复对禁用Button类型识别
- 升级`dls-graphics`到`1.0.0`
[bugfix] 解决loading图标闪烁问题
- 升级到TS与老代码清理 (13/48)
  Checkbox(.Button, .Group)/Collapse(.Panel)/DatePicker(.RangePicker, .MonthPicker)
  以上组件完成API一致性对齐
  以上组件移除`react-lifecycles-compat`

### [4.2.1] 2021-11-29
- Badge
[bugfix] 属性`type`中`process`值变更为`processing`

### [4.2.0] 2021-11-29
- DatePicker.RangePicker
[bugfix] 在表单项中且无值时，反复展开收起会报错
- Menu
[bugfix] 清理require，解决rollup编译问题[(混合require与import)](https://github.com/rollup/rollup-plugin-commonjs/issues/273)
- Uploader
[bugfix] 文件数大于等于上传限制`maxFileLength`时，隐藏上传入口
[bugfix] 默认上传逻辑文件识别错误问题
- Button
[bugfix] icon尺寸对齐规范(增加2px)
- Pagination
[feat] 新增`showTotal`展示总计
[feat] `...`页码增加tip
[feat] 删除`selectWidth`属性，分页下拉调整为自适应宽度
[feat] 分页下拉前`每页显示`调整为`条/页`，并挪到下拉选项中
- Select
[feat] 属性`width`类型从`number`调整为`number | string`
- Alert.Page
[feat] 属性`slider`变更为`activeIndex`, `initialSlide`变更为`defaultActiveIndex`
- less-plugin-dls 升级到4.0.0
[feat] 对齐Collapse与Pagination tokens
- Steps
[bugfix] 修复当前状态指示错位问题(当Step的`status`为`error`可复现)
- React/ReactDOM依赖调整为`>= 16.3.0`
- 升级到TS与老代码清理 (10/48)
  Affix/Alert(.Page)/Anchor(.Link)/BackTop/Badge/Breadcrumb(.Item)/Button/Carousel/Uploader/Pagination/ProviderConfig
  以上组件完成API一致性对齐
  以上组件移除`react-lifecycles-compat`
  `Affix`清理`findDOMNode`
  `Anchor`清理legacy context

### [4.1.1] 2021-11-09
- Table
[bugfix] 树表展开/收起Icon与内容产生折行问题
[bugfix] 树表缩进对齐问题
- DatePicker/RangePicker
[bugfix] 面板宽度不够样式错乱
[bugfix] 快捷方式样式不正确
- NumberInput
[bugfix] 支持接受更多属性，解决Tooltip等功能不生效问题
[feat] 属性`tipText`与`errorMessage`类型从`string`调整为`node`

### [4.1.0] 2021-10-28
- Select
[bugfix] 多选支持`showSearch`开启/关闭搜索功能
- DatePicker/MonthPicker/RangePicker
[feat] 调整为`inline-block`
[bugfix] 修复右下/向上展开定位不正确问题
- Tabs
[feat] 增加`extra`属性，对右侧区域自定义
[feat] `addButtonText`属性类型从`string`调整为`node`
- Collapse
[feat] `type`新增`simple`透明背景变体
[feat] 新增`dull`属性，用来控制标题是否不展示交互视觉反馈
[feat] 变更不同`type`的默认视觉样式，并对`bordered`与`dull`进行约束如下：
    normal: bordered(true, 固定) dull(false, 固定)
    simple: bordered(true, 可配) dull(true, 固定)
    basic: bordered(false, 可配) dull(false, 可配)
    normal: bordered(false, 可配) dull(false, 可配)
- Overlay
[refactor] 样式容器从`one-overlay`变为`one-overlay-inner`元素，将定位与样式容器分离

### [4.0.14] 2021-10-15
- Uploader
[bugfix] 修复属性`helperTextPostion`值为`bottom`时功能失效问题
- Table
[bugfix] 修复列`title`为`undefined`时异常问题

### [4.0.13] 2021-10-09
- Table
[feat] column属性`desc`，增加`show`与`hide`属性方法，用于控制是否展示`desc`的`Popover`
- Tabs
[bugfix] 修复仅包含一个子元素时，提示filter异常

### [4.0.12] 2021-09-22
- Dialog
[bugfix] 修复当显式设置`zIndex`属性时，遮罩保留了`zIndex`问题

### [4.0.11] 2021-09-16 (请升级到4.0.12)
- Dialog
[bugfix] 修复打开多个模态对话框时，遮罩乱序问题
- Transfer
[bugfix] 修复已选禁用节点可被父级取消问题
- less-plugin-dls
升级到`1.5.0`

### [4.0.10] 2021-09-13
- Dropdown.Button
[bugfix] 去除transparent属性warning
- SearchBox
[bugfix] onChange输入法合成(composition)时不触发
- Uploader
[bugfix] 图片列表增加背景色; 上传区增加圆角等
[feat] 文件上传异常信息提示调整为`hover`展示; 删除图标调整为按钮行为
[bugfix] 调整`entries`方式hover颜色; 更多下拉调整为圆角
[bugfix] 调整文件上传进度条占位
- Anchor
[bugfix] 二级缩进不正确
- Dialog
[bugfix] 关闭按钮样式错误，替换为`Button`组件
- Checkbox/Radio
[feat] 增加无边框简单样式
[feat] 支持折行
[feat] `strong`类型增加最小宽度配置`--one-checkbox-strong-min-width`
[feat] `disabled`时，标签也使用禁用色
[refactor] 升级Context
- less-plugin-dls
升级到`1.3.0`

### [4.0.9] 2021-08-16
- Uploader
[feat] 新增`sortable`属性，支持可拖拽排序
- DatePicker.RangePicker
[feat] `validateDisabled`参数新增`selectedValue`属性，表示当前所选时间
- Collapse
[feat] 新增`expandIconPosition`属性，支持Icon位置调整
[feat] 新增`showExpandIcon`属性，支持Icon隐藏
[feat] 新增`type`属性，支持多种内置风格(`basic`, `strong`, `normal`)样式
[feat] 新增`gutter`属性，支持间距配置
[feat] 新增`bordered`属性，支持边框配置
- Table
[feat] 新增`cellLines`与`headCellLines`用来固定内容与表头内容展示行数

### [4.0.8] 2021-08-11
- Tag
[feat] 背景/边框颜色调整为实色填充
- Dropdown.Button
[feat] 触发区内容居中调整为左对齐
- MonthPicker
[bugfix] 修复`YYYY/MM`格式Firefox/Safari兼容性问题
- Table
[bugfix] 当首列单元格`rowSpan`合并行，次行首列单元格边框重叠

### [4.0.7] 2021-08-06
- Form
[bugfix] 修复当`validateFieldsAndScroll`成功时，原`callback`未执行（仅错误时执行）

### [4.0.6] 2021-07-26
- Form
[feat] `validateFieldsAndScroll`支持Promise
- Table
[feat] normalize `column`的`minWidth`(应小于`width`)与`children`(去除空数组)
[bugfix] 修复被遮挡列拖拽线hover可见问题
- Pagination
[bugfix] 修复输入框与按钮对齐问题
- Drawer
[feat] 支持内容区无边距模式 (增加`type`属性，值为`basic`)
[bugfix] 修复关闭按钮遮挡标题问题

### [4.0.5] 2021-07-21
- Tree
[bugfix] 删除`TreeNode/VirtualTreeNode`的`onSelect`方法中`preventDefault`（让默认行为生效）
- Dialog
[bugfix] 修复多个弹窗同时可见时，body清除滚动锁定问题，造成多滚动条
- Tabs
[bugfix] 对非TabPane元素进行过滤
- Transfer
[bugfix] 修复`checkedKeys`数据类型不一致问题
- package.json
[bugfix] sideEffects增加`*.css`

### [4.0.4] 2021-07-07
- Nav
[feat] `datasource`数据项增加`target`属性，支持链接目标设置
[bugfix] 修复更多菜单下拉为垂直排版
- Sortable
[feat] 新增可拖拽排序组件

### [4.0.3] 2021-06-28
- Alert
[bugfix] 状态icon尺寸规范
- Menu
[refactor] 当非inlineCollapsed，MenuItem去除Tooltip包装
[feat] 增加`type`属性，值为`basic|strong`，增加basic样式(原有实现默认为strong)
- Select
[bugfix] 下拉箭头旋转方向改为顺时针
- TextArea
[bugfix] 字体样式reset为inherit

### [4.0.2] 2021-06-21
- Table
[refactor] 优化TableRow, TableHeader, Filter, 列拖拽，行选择框等渲染与交互性能
[feat] resize时保持已拖拽调整的列宽
- NumberInput
[bugfix] placeholder颜色不正确
- Drawer
[bugfix] 组件卸载click事件未释放
- DatePicker
[bugfix] Safari下dateFormat为YYYY-MM-DD不工作问题
- SearchBox
[refactor] 去除定时100ms读取icon容器offsetWidth

### [4.0.1-alpha.65] 2021-05-31
- Table
[feat] `getCheckboxProps`方法增加`visible`返回值，支持表格行checkbox/radio隐藏
[feat] 增加`autoHideOperation`属性(all|filter|sort)，支持统一配置filter与sort图标hover时才展示；当filter展开时，sort与filter不隐藏
[feat] tip触发区改为title
[refactor] 优化TableCell渲染性能
- Transfer
[bugfix] 修复当candidateList为number类型时，多次点全选，顶层节点会多一次选择
- Select
[bugfix] 修复多选回填输入框高度撑大产生抖动问题
- Checkbox
[bugfix] 修复勾选未居中，改为dls-icons-react图标(IconCheck, IconMinus)


### [4.0.1-alpha.64] 2021-05-24
- Tooltip
[feat] 自动识别内容尺寸变化，进行重新定位，防止位置偏差
- Table
[bugfix] body scrollY占位时，head最后一列对不齐问题

### [4.0.1-alpha.63] 2021-05-19
- Dropdown
[feat] 新增`title`支持标题
[feat] 新增`showConfirm`支持 应用/取消。配合属性`onOk`, `onCancel`, `okProps`, `cancelProps`, `okText`, `cancelText`
[feat] 新增`transparent`属性，默认为`true`（兼容默认下拉背景透明的实现方式），`false`则携带背景/圆角/阴影

### [4.0.1-alpha.62] 2021-05-18
- Input
[bugfix] 不支持onKeyDown事件

### [4.0.1-alpha.61] 2021-05-14
- Table
[feat] 新增`headerBottom`，支持表头附加扩展内容
[feat] 列新增`filterDropdownTitle`，可配置筛选标题
[feat] 列新增`filterDropdownProps`，支持自定义dropdown属性
[feat] 列新增`filterIconVisible`，可配置筛选的显隐
[feat] 列新增`desc`，作为tip展示
[feat] 列`rowSelection`新增`renderCheckbox`，支持checkbox自定义render
[feat] 点击列头触发排序
[feat] 表格滚动时，自动收起筛选下拉框
[refactor] 替换排序icon
[refactor] 空数据移入到表格td中
[refactor] 表格没有内容时，滚动条也在最下面
[refactor] 支持filter hover展示标题无抖动
[refactor] 优化表头过滤Icon展示与下拉间隔
[refactor] 移除单选再次选择已选中时，取消当前筛选逻辑
[bugfix] 拖拽线显示到其他列问题
[bugfix] 表头icon与内容右对齐偏差问题

### [4.0.1-alpha.60] 2021-05-13
- Table
[refactor] 性能优化

### [4.0.1-alpha.59] 2021-05-11
- Transfer & Tree
[refactor] 性能优化

### [4.0.1-alpha.58] 2021-05-11
- Toast
[bugfix] 点击关闭按钮失效

### [4.0.1-alpha.57] 2021-05-08
- DatePicker
[bugfix] 快捷方式在滚动条情况下出现折行(MAC Firefox)

### [4.0.1-alpha.56] 2021-05-07
- Dropdown
[bugfix] 分组子选项名称错误展示为分组名称
- Tabs
[feat] 移除带底线tabs两边间距

### [4.0.1-alpha.55] 2021-04-30
- Toast
[bugfix] 点击关闭按钮没有触发onClose回调

### [4.0.1-alpha.54] 2021-04-28
- Popover
[feat] 支持设置最大宽度

### [4.0.1-alpha.53] 2021-04-28
- Tree
[bugfix] 虚拟树节点勾选状态未立即渲染生效

### [4.0.1-alpha.52] 2021-04-27
- Tabs
[feat] 关闭icon调整为hover时展示
[feat] 增加status属性，展示Tab状态

### [4.0.1-alpha.51] 2021-04-26
- Lightbox
[feat] defaultSlideIndex/slideIndex变更为defaultActiveIndex/activeIndex

### [4.0.1-alpha.50] 2021-04-25
- Table
[feat] 增加body update更新通知

### [4.0.1-alpha.49] 2021-04-25
- DatePicker
[feat] 日历面板增加高度限制

### [4.0.1-alpha.48] 2021-04-19
- Lightbox
[feat] 增加zIndex

### [4.0.1-alpha.47] 2021-04-14
- Transfer
[feat] 增加selectedPaneFooter，支持自定义结果区

### [4.0.1-alpha.46] 2021-04-09
- Lightbox
[bugfix] Safari下无法预览

### [4.0.1-alpha.45] 2021-04-07
- NavLink
[bugfix] forwardRef is not defined

### [4.0.1-alpha.44] 2021-04-02
- Lightbox
[bugfix] 解决onChange无效问题
[feat] 控制展示某个slide，增加defaultSlideIndex与slideIndex属性

### [4.0.1-alpha.42] 2021-04-02
- Uploader
[feat] onChange增加index
[feat] helperText可自定义配置
[bugfix] 文件列表文案对齐问题

### [4.0.1-alpha.41] 2021-03-25
- NumberInput
[bugfix] 连续点击增加按钮，周边文字会被选中
- CascaderPane
支持Menu列定宽设置

### [4.0.1-alpha.40] 2021-03-15
- Sidenav
新增侧导航组件。
- Menu
MenuItem支持icon属性。

### [4.0.1-alpha.39] 2021-02-24
- Uploader
[bugfix] accept增加文件扩展名校验，解决部分mime-type拿不到情况。

### [4.0.1-alpha.38] 2021-02-24
- Transfer
[bugfix] 在文字超长情况下，已选框的文字会和删除Icon重叠

### [4.0.1-alpha.37] 2021-02-20
- CascaderPane
[bugfix] 使用firstColumnGroup时点击搜索结果，滚动条没有准确定位到结果元素

### [4.0.1-alpha.36] 2021-02-20
- Table
[bugfix] 无法仅对选择列进行固定

### [4.0.1-alpha.35] 2021-02-18
- Input
layer错误提示改为Popover方式呈现
- Tree
[bugfix] 虚拟节点下输入框、下拉框等无法进行交互

### [4.0.1-alpha.34] 2021-01-22
- SearchBox
[bugfix] Warning: React does not recognize the `customRender` prop on a DOM element
- Dropdown
[bugfix] 可展开菜单展开Icon和文字之间没有间距
- Transfer
[bugfix] 穿梭框多层级重复选择回调参数id重复
[bugfix] expandedSelectedKeys值类型与selectedList不一致
- Cascader
[bugfix] Cascader有选中值的时候设置了disabled，但是文字没有disable态
- CascaderPane
[bugfix] loading 图标没有居中
[refactor] 数据量大卡顿问题

### [4.0.1-alpha.33] 2021-01-21
- Transfer
[bugfix] 修复transfer onScroll无响应问题

### [4.0.1-alpha.32] 2021-01-20
- Dialog
[bugfix] Dialog.confirm的okProps和cancelProps中传入style会被覆盖
Dialog.confirm/alert支持自定义footer

### [4.0.1-alpha.31] 2021-01-18
- select
[bugfix] 下拉多选checkbox支持disabled样式

### [4.0.1-alpha.30] 2020-12-15
- uploader
[bugfix] hideAnchor 的时候依旧保留input

### [4.0.1-alpha.29] 2020-12-15
- tag
[bugfix] tag关闭的时候不应该重新计算宽度

### [4.0.1-alpha.28] 2020-12-14
- tag
[bugfix]输入型tag超长的时候，input报错

### [4.0.1-alpha.27] 2020-12-11
- monthPicker
dateFormat 从 YYYY/MM -> YYYY-MM

- tag
[bugfix]修复editableGroup删除tag的时候会多删除一个的bugfix


### [4.0.1-alpha.26] 2020-12-02
- textarea
支持通过ref获取textarea实例

### [4.0.1-alpha.24] 2020-12-01
- iconSvg
升级dls-icons-react，替换IconMenu -> IconApps

### [4.0.1-alpha.23] 2020-11-26
- uploader
[bugfix]修复hover后多余的剪头样式
- less-plugin-dls
升级less-plugin-dls最新版本1.0.0-alpha.34
- toast、embedded
变量样式相加使用 dls-sum
- link
feature: 支持新组件 Link.NavLink，可以被react-router-dom的link使用


### [4.0.1-alpha.22] 2020-11-25
- alert
feature: 支持alert被popover/tooltip的使用

### [4.0.1-alpha.21] 2020-11-24
- timePicker
时间赋值兼容

### [4.0.1-alpha.20] 2020-11-19
- searchbox
[feature](支持自定义渲染搜索button，支持文字按钮button; searchIconType属性新增'custom'选项；新增属性customRender)
[issue](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-117/show?source=drawer-header)

### [4.0.1-alpha.19] 2020-11-18
- link
feature: 支持link被popover/tooltip的使用

### [4.0.1-alpha.18] 2020-11-16
- upload
升级样式，替换预览的icon

- form
form item的下边距只在form中，单独使用form item没有边距

- input
value有值的时候，才会校验max length error与min length error

- tabs
增加属性hideSpace，隐藏tabs的下边距

### [4.0.1-alpha.17] 2020-11-13
- uploader
feature 新增order、hideAnchor、formatUploadAnchor三个参数，具体看文档

- button
loading 优化转速

- table
优化loading遮罩效果

- transfer
优化loading遮罩效果

### [4.0.1-alpha.16] 2020-11-11
- transfer
  支持onScroll事件

### [4.0.1-alpha.15] 2020-11-11
- cascaderPane
refactor 获取半选的方法，减少数据量大的时候造成的卡顿
- checkbox
去掉无用的animation effect
- form
最后一个item不展示下边距
- loading
增加blur效果
- table
集成 loading blur效果
- transfer
集成 loading blur效果


### [4.0.1-alpha.12] 2020-11-10
- cascaderPane
refactor 获取半选的方法，减少数据量大的时候造成的卡顿
- light
refactor remove carousel only use div
- button
文字按钮去掉了内边距
- tag
disabled状态不能关闭
- tabs
优化样式

### [4.0.1-alpha.11] 2020-10-26
- lightbox
Add new component lightbox

- feature Upload
新增上传组件 上传视频、媒体、图片功能，支持多入口上传，预览修改为使用lightbox

### [4.0.1-alpha.10] 2020-10-26
- CascaderPane
  升级cascaderPane，二级只有一个item，点击三级的时候，一级不会被选中的问题
- Cascader
  输入字符的时候，底部placeholder没有隐藏的问题
- Table
  排序/筛选 icon 缺失 hover、active状态

### [4.0.1-alpha.9] 2020-10-20
- Loading
  [feature](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-94/show?source=drawer-header)
  引入dls-graphics包，升级loading交互规范
- Switch
  引入最新的loading
- CascaderPane
  引入最新的loading
- Cascader
  引入最新的loading
- Form
  报错焦点状态不依赖 .focus-visible
- Menu
  collapse下样式fix
- Select
  支持加载状态，loading、loadingText
- Table
  升级加载状态，locale增加loadingText字段，为loading文案
- Toast
  升级加载样式
- Transfer
  支持加载状态，loading、loadingText
- Tree
  升级加载状态样式


### [4.0.1-alpha.8] 2020-10-19
- Dropdown
  兼容label传dom时的搜索报错问题
  [bug](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-92/show?source=drawer-header)
  增加底部自定义slot
  [feature](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-93/show?source=drawer-header)

[released]
### [4.0.1-alpha.7] 2020-10-15
- Table
[bug](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-91/show?source=drawer-header)
componentDidUpdate后，如果列宽/列数有变化，使用forceUpdate重新渲染，scroll.x如果不传默认则是true。

- Radio、Checkbox
[bug](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-90/show?source=drawer-header)
加强多选样式下，one-checkbox-button、one-radio-button样式由block变成inline-flex，解决块状元素占高度的情况。

### [4.0.1-alpha.6] 2020-10-15
- CascaderPane
[feature](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-89/show?source=drawer-header) 高级级联cascaderPane支持有关闭icon操作的场景

### [4.0.1-alpha.5] 2020-10-13
- Uploader
[bug](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-88/show?source=drawer-header)fix firefox浏览器中，图片上传锚点样式问题

### [4.0.1-alpha.4] 2020-09-27
- Dialog
  增加contentClassName属性，用于挂在content的classname

### [4.0.1-alpha.3] 2020-09-25
- Anchor
Link的className被覆盖
- Uploader
预览的confirm没有【x】

### [4.0.1-alpha.2] 2020-09-24
- NumberInput
【bugfix】fix 输入的时候没有焦点态
- Uploader
新增属性：renderCustomIcon，Uploader组件listType为image的时候，支持对操作栏icon自定义并访问内部props

### [4.0.1-alpha.1] 2020-09-24
- Anchor
新增内联的样式，type为inline
- Breadcrumb.Item
【bugfix】传入新的className后，面包屑item自身的className被覆盖
- Button
【bugfix】支持传入icon为函数的形式，即直接传 dls-icons-react中的icon
- Dialog
【bugfix】fix footer为空数组的情况下，渲染了footer的div
【bugfix】confirm 执行 确定的时候会触发onCancel
- NumberInput
【bugfix】修复点击 + or - 的icon时候，造成input闪动，以及旁边的文字被选中
- Overlay
组件内部替换iconSvg为dls-icons-react的icon
- Select
suffixIcon 支持传入函数，即直接传 dls-icons-react中的icon
- Uploader
修复图片上传时，超过两行的时候图片错位的bugfix

### [4.0.1] 2020-09-18
- Cascader
支持fieldNames的情况，3.0已支持，4.0遗漏相关代码
- Dialog.confirm
支持点击x也会回调onCancel
- Drawer
支持关闭的的时候，body上的宽度没有被清空的问题，修复没有title的时候，body上边距样式的问题
- Dropdown
修复 overlay样式被覆盖的问题
- Dropdown.Button
修复option.onClick点击覆盖面不是整个option的item，主要是option的样式调整，padding赋给了下一层
搜索条增加className -> `${prefixCls}-search-item`
- Link
修改isAtag情况下，disabled下还可以被点击的问题
- Menu
menuItem,subMenu引错了第三方包 classnames
- Progress
IconRefresh -> IconAnticlockwise
- Table
修复排序icon没有对齐的问题
- iconSvgTools
remove IconStorage、IconShopping、IconYuanCircle
rename IconPlusSquare -> IconPlusSquareCircle

- dls-icons-react
为了防止业务端多打dls-icons-react这个包，dls-icons-react包正式作为one-ui的peer dependencies里加载了


### [4.0.0-alpha-99] 2020-09-18
- Dialog
fix visibleControlledWhenClose参数在底层的common下dialog组件中，点击x的方法中未传入该参数
导致控制不关闭弹窗未生效的问题

### [4.0.0-alpha-98] 2020-09-17
- Dialog
新增visibleControlledWhenClose参数，可定义点击X是否关闭弹窗

### [4.0.0-alpha-97] 2020-09-16
- Transfer
fix disabled状态和选中状态冲突的问题

### [4.0.0-alpha-96] 2020-09-16
- Transfer
fix 全选button的disabled状态

### [4.0.0-alpha-95] 2020-09-16
- Carousel
去掉无效属性 effect
- Cascader
input的宽度根据容器的宽度自适应
当children为[]数组的时候不进行渲染
cascader的高度从10 item的高度个改成5个item的高度和
- CascaderPane
CascaderPane的高度从10 item的高度个改成5个item的高度和
- Drawer
renderBody 函数 删除永远不执行的一段代码
- Dropdown
删除无用属性transitionName
onVisibleChange、onHandleMenuClick、handleMenuClick 增加defaultProps默认值

### [4.0.0-alpha-94] 2020-09-09
- Tabs
[bug](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-67/show?source=drawer-header)
修改strong类型，圆角的实现方式
- Cascader
fix cascader 箭头样式
- Dialog
去掉dialog的componentWillUnMount，移至componentDidMount
- Form
formItemWrapper font-size从0至inherit
- Table
fix 排序icon没有对齐的问题

### [4.0.0-alpha-93] 2020-09-09
- Table
[bug](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-63/show?source=drawer-header)
columns为空数据的时候 fix报错逻辑，fix筛选icon对齐

### [4.0.0-alpha-92] 2020-09-08
- Menu
选中状态由border-right改成after伪类，支持A标签形式

### [4.0.0-alpha-91] 2020-09-08
- Table
[issue](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-64/show?source=drawer-header)
无数据的时候高度由height改成min-height

### [4.0.0-alpha-90] 2020-09-07
- Collapse
增加contentClassName字段，给一些只需要collapse header的业务使用
- DatePicker.RangePicker
fix value为空的时候的样式
- formItem
新增字段extralPlacemenet，表示表单项报错时，错误提示的对齐方式，共有三种：left、center、right三种，默认是左对齐
fix form中最后一项只有为button并且类型是submit才特殊处理间距
- Steps
垂直步骤条，由默认最小高度，且由父容器高度决定（改为flex布局）


### [4.0.0-alpha-89] 2020-09-03
- DatePicker
[bugfix](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-60/show?source=drawer-header)
- Table
[bugfix](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-59/show?source=drawer-header)
排序icon升级替换

### [4.0.0-alpha-88] 2020-08-30
- Tree
fix 对齐问题

### [4.0.0-alpha-86] 2020-08-30
- Tree
fix 对齐问题

### [4.0.0-alpha-85] 2020-08-30
- Drawer
增加抽屉弹出的动画
- Dropdown、Transfer
fix 搜索框样式问题

### [4.0.0-alpha-84] 2020-08-28
- Menu
出现问题，先由a标签先改回li，后续再升级

### [4.0.0-alpha-83] 2020-08-27
- Uploader
[bug](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-55/show?source=drawer-header)
fileList没有完全受控

- SearchBox
[issue](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-50/show?source=drawer-header)
优化SearchBox的样式

- Menu、Nav
[issue](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-53/show?source=drawer-header)
Menu和Nav的item标签有li或者div全部改成a标签，同时支持传href进行直接跳转


### [4.0.0-alpha-82] 2020-08-25
- Uploader
增加一下fileList为[]的时候的健壮性

### [4.0.0-alpha-81] 2020-08-25
- Nav
onChange 暴露item，结构为onChange({item, target})
- Uploader
[issue](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-49/show?source=drawer-header)
上传组件，当点击删除正在上传过程中的组件时候，将当前请求abort掉

### [4.0.0-alpha-80] 2020-08-24
- Table
bottomScroll的bottom没有兼容数字0的情况
[issue](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-46/show?source=drawer-header)

### [4.0.0-alpha-79] 2020-08-17
- Tree
Tree的utils没有React导致报错

### [4.0.0-alpha-78] 2020-08-17
-Table
合入sticky吸顶部分的代码，重新发包

### [4.0.0-alpha-77] 2020-08-13
- Table
[feature] 吸顶使用sticky方式让整个表头吸顶，而不再渲染多个表头了
[issue](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-34/show?source=drawer-header)
[bugfix] 修复带确认和取消按钮的筛选，点击取消或空白处还是能选中的问题
[issue](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-42/show?source=drawer-header)

### [4.0.0-alpha-76] 2020-08-11
- IconSvg
丰富图标库内容
- Transfer
所以自定义的内容都支持reactNode
### [4.0.0-alpha-75] 2020-08-10
- table
优化了吸顶的sticky样式，增加了-webkit-sticky

### [4.0.0-alpha-74] 2020-08-10
- table
优化了一些table的代码

### [4.0.0-alpha-73] 2020-08-06
- transfer
处理了文字超出未截断的问题
[issue](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-38/show?source=drawer-header)
增加逻辑 (maxSelectedNum && maxSelectedNum < selectedListLength + unSelectedNum) 时全选按钮不能选择
[issue](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-39/show?source=drawer-header)

- numberInput
修复了int类型，增加减少会有报错提示
增加了上下键，disabled的样式
[issue](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-36/show?source=drawer-header)

### [4.0.0-alpha-72] 2020-08-03
- drawer
新增closeDrawerByClickBody参数，支持drawer在无遮罩场景下点击body可以关闭
[feature](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-37/show?source=drawer-header)

### [4.0.0-alpha-71] 2020-08-03
- table
优化表格吸顶，useStickyFixTop默认为true，即默认使用sticky进行吸顶

### [4.0.0-alpha-70] 2020-08-02
- transfer
fix 修复transfer自定义selectedItem，将key传入selectedProps

### [4.0.0-alpha-69] 2020-07-27
- table
[feature](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-34/show?source=drawer-header)
表格增加useStickyFixTop支持用sticky的方式吸顶表头
- transfer
[bugfix](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-34/show?source=drawer-header)
修复onSelect只能选中不能取消选中的bugfix

### [4.0.0-alpha-68] 2020-07-21
- table
fix table updateWidthChange在scroll变化时的参数错误

### [4.0.0-alpha-67] 2020-07-20
- input
[feature](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-32/show?source=drawer-header)inline类型的input支持 suffix and prefix
- transfer
candaidateList变化的时候，会重新计算selectedList的引用

### [4.0.0-alpha-66] 2020-07-09
- dropdown
fix dropdown.button在有二级菜单时选中，e.key不是value而是index的问题

### [4.0.0-alpha-65] 2020-07-13
- form
fix explain和extra重叠的问题，去掉了这两块绝对定位，改成relative定位
- transfer
[feature](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-28/show?source=drawer-header)
支持 CustomCandidateOperation 和 CustomSelectedOperation 两个新特性
- toast
fix 样式
- table
[feature](http://newicafe.baidu.com/v5/issue/baidu-fc-fe-one-ui-30/show?source=drawer-header)
新增updateWidthChange参数，当顶层宽度变化的时候，会forceUpdate整个组件，强制进行渲染，常用于表头吸顶时候的时候改变表格宽度，表头不会重绘的场景
- datePicker
fix 多选场景下，选中开始和结束为同一天的时候，单元格样式问题
- link
toUrl 参数不再是isRequired

### [4.0.0-alpha-64] 2020-07-09
由于16.3版本才引进 unsafe_componentWillRecieveProps，一些平台没有升级16.3所以这个暂时不能加

### [4.0.0-alpha-63] 2020-07-08
- numberInput
bugfix

### [4.0.0-alpha-62] 2020-07-07
- table
优化表格性能，拖拽的时候只渲染拖拽线

### [4.0.0-alpha-61] 2020-07-07
- iconSvg
修复 IconUnSorted被移除，增加
IconCheckCircleSolid,IconInfoCircleSolid,
IconExclamationCircleSolid, IconTimesCircleSolid

### [4.0.0-alpha-60] 2020-07-06
- alert
[bug]修复了alert page 点击最后一个关闭时候出现异常
- searchBox
[feature]引入showCloseIcon，是否展示关闭icon
- table
[bug]修复tableloading的时候的样式
- transfer
[bug]修复safari下，搜索框样式问题

### [4.0.0-alpha-59] 2020-07-02
- menu
重构

### [4.0.0-alpha-58] 2020-07-01
- cascader
  add loadOnSelect

### [4.0.0-alpha-57] 2020-07-01
- 删除v3的代码

### [4.0.0-alpha-56] 2020-06-25
- 修复了一些样式， checkbox的name为undefined

### [4.0.0-alpha-55] 2020-06-23
- 修复一波样式
- table
    吸右的单元格的right计算不依赖dom结构，而是根据初始计算的宽度

### [4.0.0-alpha-54] 2020-06-17
- 修改了一波样式

### [4.0.0-alpha-53] 2020-06-17
- table
    无数据时候，sticky的列left置为0
    修复列宽不足时，均列宽至每一列，均匀后如果还不足则把多余的加至后面每一列

### [4.0.0-alpha-52] 2020-06-15
- table
    无数据的时候允许表头滚动
- slider
    样式修复
- menu
    样式修复
### [4.0.0-alpha-51] 2020-06-15
- datePicker
  fix import wrong icons

### [4.0.0-alpha-50] 2020-06-10
- fix nav的样式问题
- cascaderPane
    搜索时定位到激活区域位置
- menu
    fix 间距问题
- transfer
    children为空数组的时候，依旧能被选中


### [4.0.0-alpha-49] 2020-06-10
- fix some style bugs
- rename textLink => link

### [4.0.0-alpha-48] 2020-06-08
- icons
change icons from one-ui-svg => dls-icons-react
- menu
bugfix style
- tabs
remove transform transition

### [4.0.0-alpha-47] 2020-06-04
- cascader
- add func onInputChange
- add inputIsControlled 搜索受控
### [4.0.0-alpha-46] 2020-06-02
- transfer
- add feature BeforeSelectedPane and BeforeCandidatePane
- switch openVirtualList => useVirtualScroll

### [4.0.0-alpha-44] 2020-06-02
- add new component timePicker

### [4.0.0-alpha-43] 2020-05-31
- transfer
全选按钮支持隐藏
- nav
bugfix
- add new component tabs

### [4.0.0-alpha-42] 2020-05-31
- table
fix header多余的border bugfix
hover到拖拽部分展示拖拽线

### [4.0.0-alpha-41] 2020-05-31
- add some new components
alert、breadcrumb、carousel、collapse、drawer、embedded、form、grid
loading、numberInput、progress、slider、textArea、uploader

### [4.0.0-alpha-40] 2020-05-25
- transfer
bugfix
- input
增加行内样式

### [4.0.0-alpha-39] 2020-05-25
- add new components
anchor、backTop、badge、datePicker
- dropdown
修复bug

### [4.0.0-alpha-38] 2020-05-25
-table
bugfix

### [4.0.0-alpha-37] 2020-05-25
- table
bugfix
- add new Component cascader

### [4.0.0-alpha-35] 2020-05-25
- tranfer
candidateList > maxSelectNum 全选置灰

### [4.0.0-alpha-33] 2020-05-25
- table
宽度不够时候均匀分配给其他列

### [4.0.0-alpha-32] 2020-05-19
- table
重置宽度bugfix

### [4.0.0-alpha-31] 2020-05-18
- transfer
item变成itemRender

### [4.0.0-alpha-30] 2020-05-18
- transfer
fix bug
- table
fix bug
- add toast、dialog、steps、popover

### [4.0.0-alpha-28] 2020-05-18
- transfer
bugfix
- table
update column width支持传入column

### [4.0.0-alpha-27] 2020-05-18
- nav
下划线bugfix

### [4.0.0-alpha-26] 2020-05-18
- nav
下划线bugfix
- table
bugfix
- tag
新增
- transfer
bugfix

### [4.0.0-alpha-25] 2020-05-18
- nav,dropdown
bugfix
### [4.0.0-alpha-24] 2020-05-15
- transfer
bugfix

### [4.0.0-alpha-20] 2020-05-15
- table
columns 变化的时候bugfix
- transfer
重写 - 扩展功能（集成tree的虚拟滚动）
- tree
扩展功能 - tree升级虚拟滚动

### [4.0.0-alpha-19] 2020-05-15
- table
可拖拽表头列表，修改为松开后宽度变化

### [4.0.0-alpha-17] 2020-05-15
- nav
修改导航样式影响icon

### [4.0.0-alpha-17] 2020-05-14
- nav
visible渲染多次问题

### [4.0.0-alpha-16] 2020-05-14
- table
树形表
- nav
overlay的visibleChange可受控

### [4.0.0-alpha-15] 2020-05-14
- table
暴露 onDragEnd onDragStart onDraging
- nav
样式fix

### [4.0.0-alpha-14] 2020-05-13
- nav
带箭头
- overlay
给根类上绑定className
- table
优化

### [4.0.0-alpha-13] 2020-05-12
columns变化时候，重新计算columns

### [4.0.0-alpha-12] 2020-05-07
table bugfix

### [4.0.0-alpha-11] 2020-05-07
fix nav hover的时候的样式

### [4.0.0-alpha-09] 2020-05-07
发布Table、textLink

### [4.0.0-alpha-06] 2020-05-07
更新4.0组件

### [4.0.0-alpha-03] 2020-05-07
fix providerConfig

### [4.0.0-alpha-02] 2020-05-07
打包改为babel打包，fix providerConfig

### [4.0.0-alpha-01] 2020-05-06
更新4.0组件，button，cascaderPane，checkbox，iconSvg，input，nav，overlay，providerConfig
