# 顶层
- default prefixCls: 'one'
- all Components's default size: small => medium

# 组件的样式
- @baidu/one-ui/lib/components/组件/style.css -> @baidu/one-ui/lib/components/组件/style/index.css
如果你按需引入了组件样式需要额外修改一下

# Button
- 3.0 type normal 样式 -> type basic
- 新增一种按钮类型 type: basic
- 2.0 type 转化 直接去掉了

# CascaderPane
- add size (small, medium)

# SearchBox
- rename Input.Search -> SearchBox

# Menu
- 提供专门的横向导航组件 - Nav

# Overlay
- rename: popLayer -> Overlay

# Link
- rename TextLink => Link

# Dialog
- rename Modal -> Dialog

# Table
- 布局变化，表格一定会撑满容器

# Toast
- rename message -> toast

# Icon
- remove Icon 
- use IconSvg

# Region
- remove Region
- use Region in @baidu/one-ui-pro

# TextLine
- remove TextLine
- use TextLine in @baidu/one-ui-pro

# Calendar
- remove Calendar
- use DatePicker instead of Calendar

# Menu
- remove horizonal Menu
- use Nav instead of horizonal Menu

# NumberBox
- rename NumberBox -> NumberInput



