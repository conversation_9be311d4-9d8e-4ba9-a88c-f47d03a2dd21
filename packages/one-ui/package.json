{"name": "@baidu/one-ui", "version": "4.41.14", "description": "React implemention of Baidu Light Design D20", "main": "lib/index.js", "module": "es/index.js", "types": "es/index.d.ts", "scripts": {"build": "npm run clean && npm run build:cjs && npm run build:es && npm run build:less", "build:cjs": "cross-env BABEL_ENV=production NODE_ENV=production BUILD_TARGET=cjs babel src --out-dir lib --extensions .ts,.tsx,.js,.jsx", "build:es": "cross-env BABEL_ENV=production NODE_ENV=production BUILD_TARGET=es babel src --out-dir es --extensions .ts,.tsx,.js,.jsx && npm run build:types", "build:types": "tsc", "lint": "eslint src --ext '.js,.jsx,.ts,.tsx'", "build:less": "node bin/css", "clean": "<PERSON><PERSON>f lib dist es", "cjs": "cross-env BUILD_TARGET=lib STAGE=online NODE_ENV=production BABEL_ENV=production webpack --config build/webpack.component.js --progress --profile --colors", "es": "cross-env BUILD_TARGET=es STAGE=online NODE_ENV=production BABEL_ENV=production webpack --config build/webpack.component.js --progress --profile --colors", "analyzer": "cross-env BUILD_TARGET=es STAGE=production NODE_ENV=production BABEL_ENV=production webpack --config build/webpack.analyze.js --progress --profile --colors", "test": "jest --config jest.config.js --no-cache --coverage --max_old_space_size=4096", "test:update": "jest --config jest.config.js --update-snapshot", "test:coverage": "jest --coverage", "publish:alpha": "npm publish --tag alpha"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["eslint"]}, "sideEffects": ["*.less", "*.css"], "files": ["lib", "es", "src"], "license": "ISC", "peerDependencies": {"dls-icons-react": "^3.42.0", "prop-types": ">=15.0.0", "react": ">=16.12.0", "react-dom": ">=16.12.0"}, "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.4", "@babel/plugin-proposal-class-properties": "^7.4.4", "@babel/plugin-proposal-decorators": "^7.16.0", "@babel/plugin-transform-runtime": "^7.11.5", "@babel/plugin-transform-typescript": "^7.16.1", "@babel/polyfill": "^7.4.4", "@babel/preset-env": "^7.4.4", "@babel/preset-react": "^7.0.0", "@babel/preset-typescript": "^7.16.0", "@types/lodash": "^4.14.176", "@types/react": "^17.0.33", "@types/react-dom": "^17.0.10", "@types/shallowequal": "^1.1.1", "babel-jest": "^27.5.1", "babel-loader": "^8.0.6", "babel-plugin-add-module-exports": "^1.0.2", "babel-plugin-import": "^1.13.0", "babel-plugin-inline-react-svg": "^2.0.2", "babel-plugin-lodash": "^3.3.4", "babel-plugin-transform-define": "^2.1.0", "case-sensitive-paths-webpack-plugin": "^2.3.0", "classlist-polyfill": "^1.2.0", "cross-env": "^7.0.2", "css-loader": "^2.1.1", "dls-icons-react": "^3.42.0", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.2", "enzyme-to-json": "^3.4.4", "jest": "^27.3.1", "jest-css-modules-transform": "^4.0.0", "less": "^4.1.2", "less-plugin-npm-import": "^2.1.0", "mini-css-extract-plugin": "^0.6.0", "nyc": "^15.1.0", "postcss": "^8.3.11", "react": "^16.8.6", "react-dom": "^16.13.1", "regenerator-runtime": "^0.13.7", "rimraf": "^2.6.3", "ts-jest": "^27.0.7", "typescript": "^4.4.4", "webpack": "^4.31.0"}, "dependencies": {"@babel/runtime": "^7.11.2", "array-tree-filter": "^2.1.0", "async-validator": "^4.2.5", "classnames": "^2.3.1", "clone-element": "^0.1.0", "component-classes": "^1.2.6", "create-react-class": "^15.6.3", "create-react-context": "^0.3.0", "css-animation": "^2.0.4", "dayjs": "^1.8.27", "dls-graphics": "^1.0.0", "dls-illustrations-react": "^1.3.0", "dom-scroll-into-view": "^2.0.1", "hoist-non-react-statics": "^3.3.2", "less-plugin-dls": "^11.13.0", "lodash": "^4.17.15", "mini-store": "^3.0.6", "moment": "^2.26.0", "mutationobserver-shim": "^0.3.7", "omit.js": "^1.0.2", "raf": "^3.4.1", "rc-menu": "^8.0.3", "rc-notification": "^3.3.0", "rc-progress": "^2.3.0", "rc-slider": "^8.6.5", "rc-trigger": "^5.3.4", "rc-util": "^5.37.0", "react-is": "^16.13.1", "react-lifecycles-compat": "^3.0.4", "react-slick": "^0.26.1", "react-sortablejs": "6.0.0", "react-transition-group": "^4.4.2", "react-window": "^1.8.5", "resize-observer-polyfill": "^1.5.1", "shallowequal": "^1.1.0", "sortablejs": "1.14.0", "warning": "^4.0.3"}}