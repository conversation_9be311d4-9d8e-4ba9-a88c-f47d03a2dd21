/**
 * @file eslint配置文件
 * <AUTHOR>
 * @date 2020-04-22
 */
module.exports = {
    'root': true,
    'env': {
        'browser': true,
        'es6': true,
        'node': true,
        'jest': true
    },
    'extends': [
        '@ecomfe/eslint-config',
        '@ecomfe/eslint-config/react'
    ],
    'parser': '@typescript-eslint/parser',
    'parserOptions': {
        'parser': 'babel-eslint',
        'ecmaVersion': 6,
        'sourceType': 'module'
    },
    'rules': {
        'no-console': ['error', {allow: ['warn', 'error']}],
        'linebreak-style': 0,
        'comma-dangle': [2, 'never'],
        'react/require-default-props': 0,
        'react/no-array-index-key': 0,
        'react/prefer-stateless-function': 0,
        'react/jsx-uses-react': 2,
        'no-use-before-define': 'off',
        '@typescript-eslint/no-use-before-define': ['error', {'functions': false}]
    },
    'plugins': [
        'react',
        '@typescript-eslint/eslint-plugin'
    ]
};
