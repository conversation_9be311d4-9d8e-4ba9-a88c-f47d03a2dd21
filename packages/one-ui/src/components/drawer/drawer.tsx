import React, {Component, CSSProperties} from 'react';
import createReactContext from 'create-react-context';
import {IconTimes} from 'dls-icons-react';
import Button from '../button';
import DrawerWrapper from './common/drawerWrapper';
import {classnames} from '../../core/commonTools';
import {DrawerProps} from './interface';
import {withConfigConsumer} from '../providerConfig/context';

const DrawerContext = createReactContext(null);

interface DrawerState {
    push: boolean;
};
目前bug：配置了destoryOnClose: true，当点击右上角关闭的时候s
@withConfigConsumer('drawer')
class Drawer extends Component<DrawerProps, DrawerState> {

    static defaultProps = {
        prefixCls: 'one-drawer',
        width: 400,
        height: 256,
        closable: true,
        placement: 'right',
        maskClosable: true,
        mask: true,
        footer: null,
        hideDefaultFooter: true,
        onOk() {},
        onCancel() {},
        okText: '确定',
        cancelText: '取消',
        okProps: {},
        cancelProps: {},
        size: 'medium',
        type: 'default',
        closeDrawerByClickBody: false
    };

    constructor(props) {
        super(props);
        this.state = {
            push: false
        };
    }

    parentDrawer;

    componentDidUpdate = prevProps => {
        if (
            prevProps.visible !== this.props.visible
            && this.parentDrawer
            && this.parentDrawer.props?.placement === this.props.placement
        ) {
            if (this.props.visible) {
                this.parentDrawer.push();
            }
            else {
                this.parentDrawer.pull();
            }
        }
    }

    onMaskClick = e => {
        if (!this.props.maskClosable) {
            return;
        }
        this.close(e);
    }

    getDestoryOnClose = () => (this.props.destroyOnClose && !this.props.visible);

    // get drawar push width or height
    getPushTransform = placement => {
        if (placement === 'left' || placement === 'right') {
            return `translateX(${placement === 'left' ? 180 : -180}px)`;
        }
        if (placement === 'top' || placement === 'bottom') {
            return `translateY(${placement === 'top' ? 180 : -180}px)`;
        }
    }

    getDrawerWrapperStyle = () => {
        const {zIndex, placement, maskStyle} = this.props;
        return this.state.push
            ? {
                ...maskStyle,
                zIndex,
                transform: this.getPushTransform(placement)
            }
            : {
                ...maskStyle,
                zIndex
            };
    }

    close = e => {
        if (this.props.visible !== undefined) {
            if (this.props.onClose) {
                this.props.onClose(e);
            }
        }
    }

    push = () => {
        this.setState({
            push: true
        });
    }

    pull = () => {
        this.setState({
            push: false
        });
    }

    // render drawer body dom
    renderBody = () => {
        const isDestroyOnClose = this.getDestoryOnClose();
        if (isDestroyOnClose) {
            return null;
        }
        const {
            prefixCls, title, closable,
            footer, placement, onOk, onCancel,
            okText, cancelText, okProps,
            cancelProps, hideDefaultFooter, size, type,
            headerClassName
        } = this.props;
        const containerStyle = (placement === 'left' || placement === 'right')
            ? {
                overflow: 'auto',
                height: '100%'
            } : {};

        // is have closer button
        let closer;
        if (closable) {
            closer = (
                <Button
                    type="text-aux"
                    size={size}
                    className={`${prefixCls}-close`}
                    onClick={this.close}
                >
                    <IconTimes className={`${prefixCls}-close-icon`} />
                </Button>
            );
        }

        let header;
        if (title) {
            header = (
                <div className={classnames(`${prefixCls}-header`, headerClassName)}>
                    <div className={`${prefixCls}-header-container`}>
                        <div className={`${prefixCls}-title`}>{title}</div>
                    </div>
                    {closer}
                </div>
            );
        }

        let drawerFooter = !hideDefaultFooter ? [
            <Button key="confirm" type="primary" size={size} onClick={onOk} {...okProps}>{okText}</Button>,
            <Button key="cancel" onClick={onCancel} size={size} {...cancelProps}>{cancelText}</Button>
        ] : null;
        if (footer && footer.length) {
            drawerFooter = footer;
        }
        let footerDom = null;
        if (drawerFooter) {
            footerDom = (
                <div className={`${prefixCls}-footer`}>
                    {drawerFooter}
                </div>
            );
        }
        const bodyClass = classnames(`${prefixCls}-body`, {
            [`${prefixCls}-body-${placement}`]: true,
            [`${prefixCls}-body-has-footer`]: footer && footer.length
        });

        const drawerBodyClass = classnames(`${prefixCls}-wrapper-body`, `${prefixCls}-wrapper-body-${size}`, {
            [`${prefixCls}-wrapper-with-header`]: header,
            [`${prefixCls}-wrapper-with-close`]: closer,
            [`${prefixCls}-wrapper-type-basic`]: type === 'basic'
        });
        return (
            <div
                className={drawerBodyClass}
                style={containerStyle}
            >
                <div className={`${prefixCls}-wrapper-body-container`}>
                    {header ? header : closer}
                    <div className={bodyClass} style={this.props.style}>
                        {this.props.children}
                    </div>
                    {footerDom}
                </div>
            </div>
        );
    }

    // render Provider for Multi-level drawe
    renderProvider = value => {
        const {
            zIndex,
            style,
            placement,
            className,
            width,
            height,
            visible,
            mask,
            destroyOnClose,
            closable,
            title,
            cancelProps,
            okProps,
            okText,
            cancelText,
            onOk,
            onCancel,
            hideDefaultFooter,
            footer,
            type,
            ...rest
        } = this.props;
        const haveMask = mask ? '' : 'no-mask';
        this.parentDrawer = value;
        const offsetStyle: CSSProperties = {};
        if (placement === 'left' || placement === 'right') {
            offsetStyle.width = width;
        }
        else {
            offsetStyle.height = height;
        }
        return (
            <DrawerContext.Provider value={this}>
                <DrawerWrapper
                    level={null}
                    handler={false}
                    {...rest}
                    {...offsetStyle}
                    open={visible}
                    onClose={this.onMaskClick}
                    showMask={mask}
                    placement={placement}
                    style={this.getDrawerWrapperStyle()}
                    className={classnames(className, haveMask)}
                >
                    {this.renderBody()}
                </DrawerWrapper>
            </DrawerContext.Provider>
        );
    }

    render() {
        return (
            <DrawerContext.Consumer>
                {this.renderProvider}
            </DrawerContext.Consumer>
        );
    }
}

export default Drawer;
