import Portal from 'rc-util/lib/PortalWrapper';
import * as React from 'react';
import Child from './drawerChild';
import {DrawerProps} from '../interface';

export interface DrawerWrapperProps
    extends Omit<DrawerProps,
        'zIndex'
        | 'visible'
        | 'mask'
        | 'destroyOnClose'
        | 'closable'
        | 'title'
        | 'cancelProps'
        | 'okProps'
        | 'okText'
        | 'cancelText'
        | 'onOk'
        | 'onCancel'
        | 'hideDefaultFooter'
        | 'footer'
        | 'type'
    > {
    open: boolean;
    defaultOpen: boolean;
    onHandleClick?(event);
    wrapperClassName;
    forceRender;
    handler;
    level;
    afterVisibleChange;
    onClose;
    showMask: boolean;
    ease;
    duration;
};

interface DrawerWrapperState {
    open?: boolean;
    prevProps?: DrawerWrapperProps;
};

class DrawerWrapper extends React.Component<DrawerWrapperProps, DrawerWrapperState> {

    static defaultProps = {
        prefixCls: 'one-drawer',
        placement: 'left',
        getContainer: () => {
            return document.body;
        },
        defaultOpen: false,
        level: 'all',
        duration: '.3s',
        ease: 'cubic-bezier(0.78, 0.14, 0.15, 0.86)',
        onChange: () => { },
        afterVisibleChange: () => { },
        handler: (
            <div className="drawer-handle">
                <i className="drawer-handle-icon" />
            </div>
        ),
        showMask: true,
        maskClosable: true,
        maskStyle: {},
        wrapperClassName: '',
        className: '',
        keyboard: true,
        forceRender: false
    }

    constructor(props) {
        super(props);
        const open = typeof props.open !== 'undefined' ? props.open : !!props.defaultOpen;
        this.state = {
            open
        };
    }

    dom;

    static getDerivedStateFromProps = (props, {prevProps}) => {
        const newState: DrawerWrapperState = {
            prevProps: props
        };
        if (typeof prevProps !== 'undefined' && props.open !== prevProps.open) {
            newState.open = props.open;
        }
        return newState;
    }

    onHandleClick = e => {
        const {onHandleClick, open: $open} = this.props;
        if (onHandleClick) {
            onHandleClick(e);
        }
        if (typeof $open === 'undefined') {
            const open = this.state.open;
            this.setState({
                open: !open
            });
        }
    }

    onClose = e => {
        const {onClose, open} = this.props;
        if (onClose) {
            onClose(e);
        }
        if (typeof open === 'undefined') {
            this.setState({
                open: false
            });
        }
    }

    render() {
        const {
            defaultOpen,
            getContainer,
            wrapperClassName,
            forceRender,
            handler,
            closeDrawerByClickBody,
            ...props
        } = this.props;
        const open = this.state.open;
        // 渲染在当前 dom 里；
        if (!getContainer) {
            return (
                <div
                    className={wrapperClassName}
                    ref={c => {
                        this.dom = c;
                    }}
                >
                    <Child
                        {...props}
                        open={open}
                        handler={handler}
                        getContainer={() => this.dom}
                        onClose={this.onClose}
                        onHandleClick={this.onHandleClick}
                        closeDrawerByClickBody={closeDrawerByClickBody}
                    />
                </div>
            );
        }
        // 如果有 handler 为内置强制渲染；
        const $forceRender = !!handler || forceRender;
        return (
            <Portal
                visible={open}
                forceRender={$forceRender}
                getContainer={getContainer}
                wrapperClassName={wrapperClassName}
            >
                {({...childProps}) => (
                    <Child
                        {...props}
                        {...childProps}
                        afterVisibleChange={props.afterVisibleChange}
                        handler={handler}
                        onClose={this.onClose}
                        onHandleClick={this.onHandleClick}
                        closeDrawerByClickBody={closeDrawerByClickBody}
                    />
                )}
            </Portal>
        );
    }
}

export default DrawerWrapper;
