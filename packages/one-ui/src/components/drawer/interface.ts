import {<PERSON>actNode, MouseEventHandler, ReactElement, CSSProperties} from 'react';
import {BasePropsWithChildren} from '../interface';
import {ButtonProps} from '../button';

export interface DrawerProps extends BasePropsWithChildren {

    /**
     * 是否允许关闭
     */
    closable?: boolean;

    /**
     * 关闭时候是否销毁
     */
    destroyOnClose?: boolean;

    /**
     * 挂载的dom
     */
    getContainer?: () => HTMLElement;

    /**
     * 是否有遮罩
     */
    mask?: boolean;

    /**
     * 外层遮罩是否可点击关闭
     */
    maskClosable?: boolean;

    /**
     * 遮罩的style
     */
    maskStyle?: CSSProperties;

    /**
     * 抽屉样式
     */
    drawerStyle?: CSSProperties;

    /**
     * 标题
     */
    title?: ReactNode;

    /**
     * 头部样式
     */
    headerClassName?: string;

    /**
     * basic为无边距基础样式;
     */
    type?: 'basic' | 'default';

    /**
     * 是否可见
     */
    visible?: boolean;

    /**
     * 变化回调
     */
    onChange?(visible: boolean): void;

    /**
     * draw的宽度
     */
    width?: string | number;

    /**
     * drawer的高度
     */
    height?: string | number;

    /**
     * z-index层级
     */
    zIndex?: number;

    /**
     *  弹出的位置
     */
    placement?: 'left' | 'top' | 'right' | 'bottom';

    /** 关闭时触发 */
    onClose?: MouseEventHandler;

    /**
     * 传入底部按钮
     */
    footer?: ReactElement[];

    /**
     * 是否隐藏默认底部
     */
    hideDefaultFooter?: boolean;

    /**
     * 点击确定回调，仅仅对默认footer生效
     */
    onOk?: MouseEventHandler;

    /**
     * 点击取消回调，仅仅对默认footer生效
     */
    onCancel?: MouseEventHandler;

    /**
     * 确定按钮文字
     */
    okText?: ReactNode;

    /**
     * 取消按钮文字
     */
    cancelText?: ReactNode;

    /**
     * 确定按钮属性
     */
    okProps?: ButtonProps;

    /**
     * 取消按钮属性
     */
    cancelProps?: ButtonProps;

    /**
     * 尺寸
     */
    size?: 'small' | 'medium';

    /**
     * 点击body关闭抽屉
     */
    closeDrawerByClickBody?: boolean;

    /**
     * esc退出
     */
    keyboard?: boolean;
};
