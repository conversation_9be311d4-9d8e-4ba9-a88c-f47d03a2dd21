/**
 * @file 顶层context
 * <AUTHOR>
 * @date 2020-04-28
 */
import React, {forwardRef} from 'react';
import classnames from 'classnames';
import {THEME_LIGHT_D22, PREFIX, THEME_LIGHT_AI, THEME_LIGHT_AI_PREFIX} from '../config';
import {TableProps} from '../table/interface';
import {ConfigProviderProps} from './interface';
import {BasePropsWithChildren} from '../interface';

type NormalizeConfig = Omit<ConfigProviderProps, 'table'> & {
    lightPrefix?: string,
    className?: string,
    // table
    loadingOption?: {
        type: TableProps['loadingOption']['type']
    }
};

const context = React.createContext<ConfigProviderProps>(undefined);

const lightThemeComponents = [
    // components
    'select',
    'dropdown',
    'cascader',
    'uploader',
    'pagination',
    'radio-group',
    'checkbox-group',
    'input',
    'input-group',
    'number-input',
    'textarea',
    'search-box',
    'time-picker',
    'date-picker',
    'button',
    // containers
    'popover',
    'loading',
    'tooltip',
    'overlay',
    'drawer',
    'dialog',
    'row',
    'col',
    'card',
    'tabs',
    'form',
    'stack',
    'layout',
    'layout-header',
    'layout-footer',
    'layout-sidebar',
    'layout-content',
    'ui-pro-sortable-selector-v2'
];

export const Consumer = context.Consumer;

export default context;

/**
 * 组件decorator，用于处理全局config
 *
 * @param suffixCls 组件className后缀
 */
export const withConfigConsumer = (suffixCls: string) => Component => {
    const ComponentWithConsumer = forwardRef((props: BasePropsWithChildren, ref) => (
        <Consumer>
            {
                config => {
                    const {
                        size,
                        prefixCls = PREFIX,
                        theme,
                        normalized,
                        table
                    } = config || {};
                    const normalizedConfig: NormalizeConfig = suffixCls === 'table' && table && table.loadingOption
                        ? {
                            loadingOption: table.loadingOption
                        }
                        : {};
                    if (normalized) {
                        normalizedConfig.normalized = normalized;
                    }
                    if (size) {
                        normalizedConfig.size = size;
                    }
                    let {className, theme: compnentTheme, ...restProps} = props;
                    let normalizedTheme = compnentTheme || theme;
                    let normalizedPrefixCls = prefixCls;

                    if (normalizedTheme === THEME_LIGHT_AI) {
                        normalizedPrefixCls = THEME_LIGHT_AI_PREFIX;
                        normalizedTheme = THEME_LIGHT_D22;
                    }

                    if (prefixCls) {
                        normalizedConfig.lightPrefix = normalizedPrefixCls;
                        normalizedConfig.prefixCls = `${normalizedPrefixCls}-${suffixCls}`;
                    }
                    if (normalizedTheme) {
                        normalizedConfig.theme = normalizedTheme;
                        if (normalizedTheme !== THEME_LIGHT_D22 || lightThemeComponents.includes(suffixCls)) {
                            const themeClassName = `${normalizedPrefixCls}-theme-${normalizedTheme}`;
                            if (!className || className.indexOf(themeClassName) === -1) {
                                className = classnames(className, themeClassName);
                            }
                        }
                    }

                    const themeName = normalizedConfig.lightPrefix === THEME_LIGHT_AI_PREFIX
                        ? THEME_LIGHT_AI
                        : compnentTheme || theme;

                    normalizedConfig.className = !className
                        ? themeName
                        : (RegExp(`(^|\s)${themeName}`).test(className)
                            ? className
                            : classnames(className, themeName)
                        );

                    const component = (
                        <Component ref={ref} {...normalizedConfig} {...restProps} />
                    );
                    if (normalizedTheme === THEME_LIGHT_D22 && !lightThemeComponents.includes(suffixCls)) {
                        return (
                            <context.Provider
                                value={{
                                    ...config,
                                    prefixCls: normalizedPrefixCls,
                                    theme: ''
                                }}
                            >
                                {component}
                            </context.Provider>
                        );
                    }
                    return (
                        <context.Provider
                            value={{
                                ...config,
                                theme: normalizedTheme,
                                prefixCls: normalizedPrefixCls
                            }}
                        >
                            {component}
                        </context.Provider>
                    );
                }
            }
        </Consumer>
    ));
    ComponentWithConsumer.displayName = Component.displayName || Component.name;
    return ComponentWithConsumer as typeof Component;
};
