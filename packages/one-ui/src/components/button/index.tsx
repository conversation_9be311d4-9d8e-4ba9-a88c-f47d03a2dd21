/**
 * @file 按钮
 * <AUTHOR>
 * @date 2020-05-04
 */
import React, {HTMLAttributes, PureComponent, ReactNode} from 'react';
import {isArray, noop} from 'lodash';
import {withConfigConsumer} from '../providerConfig/context';
import {classnames} from '../../core/commonTools';
import Loading from '../loading';
import IconSvg from '../iconSvg';
import {ButtonProps} from './interface';
import {COMPONENT_MAIN} from '../config';
import {isValidElementType} from 'react-is';

@withConfigConsumer('button')
class Button extends PureComponent<ButtonProps> {

    static defaultProps = {
        prefixCls: 'one-button',
        type: 'normal',
        size: 'medium',
        name: '',
        icon: '',
        htmlType: 'button',
        disabled: false,
        readonly: false,
        readOnly: false,
        onClick: noop,
        loading: false
    }

    static displayName = 'Button';

    handleClick = e => {
        const {disabled, readonly, onClick, readOnly, loading} = this.props;
        if (disabled || readonly || readOnly || loading) {
            e.preventDefault();
            return;
        }
        if (onClick) {
            onClick(e);
        }
    }

    render() {
        const {
            type,
            className,
            icon,
            htmlType,
            disabled,
            readonly,
            readOnly,
            loading,
            children,
            prefixCls,
            size,
            lightPrefix,
            ...rest
        } = this.props;
        // 有时children可能是多个null, 会渲染出一个空的span，导致onlyIcon样式出不来
        const hasChildren = children && (!isArray(children) || children.some(item => !!item));
        const onlyIcon = !hasChildren && icon;
        const newType = type === 'link' ? 'text' : type;
        const classes = classnames(prefixCls, className, COMPONENT_MAIN(lightPrefix), {
            [`${prefixCls}-${newType}`]: newType,
            [`${prefixCls}-${size}`]: size,
            [`${prefixCls}-${newType}-disabled`]: disabled,
            [`${prefixCls}-readOnly`]: readonly || readOnly,
            [`${prefixCls}-icon-only`]: onlyIcon,
            [`${prefixCls}-${newType}-loading`]: loading,
            [`${prefixCls}-has-icon`]: icon
        });
        const buttonProps = {
            type: htmlType || 'button',
            disabled,
            ...rest,
            onClick: this.handleClick
        };
        let iconNode: ReactNode = null;
        if (icon) {
            if (typeof icon === 'string') {
                iconNode = <IconSvg type={icon} />;
            }
            else if (isValidElementType(icon)) {
                const RealIcon = icon as React.ForwardRefExoticComponent<HTMLAttributes<HTMLElement>>;
                iconNode = <RealIcon />;
            }
            else if (typeof icon === 'object') {
                iconNode = icon;
            }
            if (React.isValidElement<HTMLElement>(iconNode)) {
                iconNode = React.cloneElement(iconNode, {
                    className: classnames(iconNode.props.className, `${prefixCls}-icon`)
                });
            }
        }
        if (onlyIcon && loading) {
            // 只有图标，并且只在loading状态的时候，只展现loading状态
            return (
                // eslint-disable-next-line react/button-has-type
                <button
                    {...buttonProps}
                    className={classes}
                >
                    <Loading size="small" className={`${prefixCls}-loading-icon`} />
                </button>
            );
        }
        return (
            <button
                {...buttonProps}
                className={classes}
            >
                {
                    loading ? (
                        <Loading size="small" className={`${prefixCls}-loading-icon`} />
                    ) : iconNode
                }
                {
                    hasChildren && <span>{children}</span>
                }
            </button>
        );
    }
}

export default Button;
export * from './interface';
