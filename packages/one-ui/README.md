## Install
npm install @baidu/one-ui --registry=http://registry.npm.baidu-int.com

## Usage
```js
import {Button} from '@baidu/one-ui';
<Button>这是一个按钮</Button>
```

## use css or less
需要定制皮肤的话在顶层样式目录引用less
```less
@import "~@baidu/one-ui/src/index.less";

使用webpack的less loader的modifyVars进行自定义配置，详情见官网
```
不要定制皮肤的话，在顶层样式引用css
```css
@import "~@baidu/one-ui/lib/index.css";
```

## Links

- [文档 (4.x)](https://one-ui.now.baidu-int.com/)
- [文档 (3.x)](https://pages.baidu-int.com/fc-fe/one-ui@v3)

## QuickStart（研发）

```
npm run install
```

* 修改组件代码，发CR，给组件库负责同学和自己业务方
* 更新 changelog，package.json（建议用 alpha 版本），发CR
* CR 通过后，本地构建，本地发包(见下）

```
npm run build
npm run publish:alpha
```

