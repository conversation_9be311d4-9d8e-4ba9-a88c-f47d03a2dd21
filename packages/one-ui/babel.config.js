const version = require('./package.json').version;

module.exports = {
    presets: [
        [
            '@babel/preset-env',
            {
                loose: true,
                modules: process.env.BUILD_TARGET === 'es' ? false : 'commonjs',
                useBuiltIns: false
            }
        ],
        '@babel/preset-typescript',
        '@babel/react'
    ],
    plugins: [
        ['@babel/plugin-proposal-decorators', {legacy: true}],
        ['@babel/proposal-class-properties', {loose: true }],
        'add-module-exports',
        'lodash',
        [
            '@babel/plugin-transform-runtime'
        ],
        ['transform-define', {
            'process.env.__VERSION__': version
        }],
        ['inline-react-svg', {svgo: false}]
    ],
    env: {
        test: {
            presets: [
                '@babel/preset-env',
                '@babel/preset-react'
            ],
            plugins: [
                '@babel/proposal-class-properties',
                'add-module-exports'
            ]
        }
    }
};
