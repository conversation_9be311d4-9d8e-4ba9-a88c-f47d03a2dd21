const React = require('react');
const Enzyme = require('enzyme');
const Adapter = require('enzyme-adapter-react-16');

require('regenerator-runtime');

// eslint-disable-next-line no-console
console.log('Current React Version:', React.version);

global.matchMedia = global.matchMedia || function () {
    return {
        matches: false, addListener: function () {}, removeListener: function () {}
    };
};

/* eslint-disable global-require */
if (typeof window !== 'undefined') {
    global.window.resizeTo = (width, height) => {
        global.window.innerWidth = width || global.window.innerWidth;
        global.window.innerHeight = height || global.window.innerHeight;
        global.window.dispatchEvent(new Event('resize'));
    };
    global.window.scrollTo = () => {};

    global.window.open = () => {
        // eslint-disable-next-line no-console
        console.log('window open success');
    };
}

// The built-in requestAnimationFrame and cancelAnimationFrame not working with jest.runFakeTimes()
// https://github.com/facebook/jest/issues/5147
global.requestAnimationFrame = cb => setTimeout(cb, 0);
global.cancelAnimationFrame = cb => clearTimeout(cb, 0);

Enzyme.configure({adapter: new Adapter()});
