import {treeMap} from '../../../../src/core/commonTools';
const tree = [
    {
        key: 1,
        children: [
            {
                key: 11,
                children: [
                    {key: 111},
                    {key: 112}
                ]
            }
        ]
    },
    {key: 2}
];

const tree2 = [
    {
        key: '1',
        children: [
            {
                key: '11',
                children: [
                    {key: '111'},
                    {key: '112'}
                ]
            }
        ]
    },
    {key: '2'}
];

describe('common tools', () => {
    test('treeMap', () => {
        const newTree = treeMap(tree, (node, index, parentNode) => {
            node.key = String(node.key);
            return node;
        });
        expect(newTree).toEqual(tree2);
        treeMap(tree, (node, index, parentNode) => {
            node.key = String(node.key);
        });
        expect(tree).toEqual(tree2);
    });
});
