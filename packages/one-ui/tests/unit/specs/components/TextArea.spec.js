/**
 * @file TextArea.spec.js
 * <AUTHOR> <EMAIL>
 * @date 2020-09-03
 */
import {mount} from 'enzyme';
import React from 'react';
import RTextArea from '../../../../src/components/textArea/index';
import TextArea from '../../../../src/components/textArea/textArea';
import Form from '../../../../src/components/form/index';
import mountTest from '../../../../tests/shared/mountTest';
import focusTest from '../../../../tests/shared/focusTest';
import calculateNodeHeight, {calculateNodeStyling} from '../../../../src/core/textAreaTools';

describe('TextArea Component', () => {
    mountTest(RTextArea);
    focusTest(TextArea);

    test('should create a TextArea component with small size', () => {
        const wrapper = mount(<TextArea size="small" />);
        expect(wrapper.props().size).toBe('small');
    });

    test('should create a TextArea component with custom className', () => {
        const wrapper = mount(<TextArea className="two-ui" />);
        expect(wrapper.find('.two-ui').length).toBe(3);
    });

    test('should create a TextArea component with custom style', () => {
        const wrapper = mount(<TextArea style={{marginTop: '20px'}} />);
        expect(wrapper.props().style).toMatchObject({marginTop: '20px'});
    });

    test('should support maxLength', () => {
        const wrapper = mount(<TextArea maxLen={3} />);
        expect(wrapper).toMatchSnapshot();
    });

    test('should support disabled state', () => {
        const wrapper = mount(<TextArea disabled />);
        expect(wrapper.props().disabled).toBe(true);
        expect(wrapper).toMatchSnapshot();
    });

    describe('TextArea As Form control', () => {
        test('should be reset when wrapped in form.getFieldDecorator without initialValue', () => {
            class Demo extends React.Component {
                reset = () => {
                    const {form} = this.props;
                    form.resetFields();
                };

                render() {
                    const {
                        form: {getFieldDecorator}
                    } = this.props;
                    return (
                        <Form>
                            <Form.Item>{getFieldDecorator('textarea', {
                                getValueFromEvent: e => {
                                    return e.value;
                                }
                            })(<TextArea />)}</Form.Item>
                            <button type="button" onClick={this.reset}>
                                reset
                            </button>
                        </Form>
                    );
                }
            }
            const DemoForm = Form.create()(Demo);
            const wrapper = mount(<DemoForm />);
            wrapper.find('textarea').simulate('change', {target: {value: '111'}});
            expect(wrapper.find('textarea').prop('value')).toBe('111');
            wrapper.find('button').simulate('click');
            expect(wrapper.find('textarea').prop('value')).toBe('');
        });
    });

    test('calculateNodeStyling works correctly', () => {
        const wrapper = document.createElement('textarea');
        wrapper.id = 'test';
        wrapper.wrap = 'wrap';
        calculateNodeStyling(wrapper, true);
        const value = calculateNodeStyling(wrapper, true);
        expect(value).toEqual({
            borderSize: 2,
            boxSizing: '',
            paddingSize: 4,
            sizingStyle:
            // eslint-disable-next-line max-len
            'letter-spacing:normal;line-height:normal;padding-top:2px;padding-bottom:2px;font-family:-webkit-small-control;font-weight:;font-size:;text-rendering:auto;text-transform:none;width:;text-indent:0;padding-left:2px;padding-right:2px;border-width:1px;box-sizing:'
        });
    });

    test('boxSizing === "border-box"', () => {
        const wrapper = document.createElement('textarea');
        wrapper.style.boxSizing = 'border-box';
        const {height} = calculateNodeHeight(wrapper);
        expect(height).toBe(2);
    });

    test('boxSizing === "content-box"', () => {
        const wrapper = document.createElement('textarea');
        wrapper.style.boxSizing = 'content-box';
        const {height} = calculateNodeHeight(wrapper);
        expect(height).toBe(-4);
    });

    test('minRows or maxRows is not null', () => {
        const wrapper = document.createElement('textarea');
        expect(calculateNodeHeight(wrapper, 1, 1)).toEqual({
            height: 0,
            maxHeight: Infinity,
            minHeight: -4,
            overflowY: 'hidden'
        });
        wrapper.style.boxSizing = 'content-box';
        expect(calculateNodeHeight(wrapper, 1, 1)).toEqual({
            height: -4,
            maxHeight: Infinity,
            minHeight: -4,
            overflowY: 'hidden'
        });
    });
});
