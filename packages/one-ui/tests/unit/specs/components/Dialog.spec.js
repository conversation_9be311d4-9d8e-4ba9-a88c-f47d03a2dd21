/**
 * @file dialog UT test
 * <AUTHOR>
 * @date 2020-09-10
 */
import {mount} from 'enzyme';
import React from 'react';
import Dialog from '../../../../src/components/dialog/index';
import mountTest from '../../../shared/mountTest';
import ActionButton from '../../../../src/components/dialog/actionButton';

const {confirm} = Dialog;

class ModalTester extends React.Component {
    constructor(props) {
        super(props);
        this.state = {visible: false};
    }

    componentDidMount() {
        this.setState({visible: true});
    }

    saveContainer = container => {
        this.container = container;
    };

    getContainer = () => this.container;

    render() {
        const {visible} = this.state;
        return (
            <div>
                <div ref={this.saveContainer} />
                <Dialog {...this.props} visible={visible} getContainer={this.getContainer}>
                    Here is content of Modal
                </Dialog>
            </div>
        );
    }
}

describe('Dialog Component', () => {
    mountTest(Dialog);
    it('render correctly', () => {
        const wrapper = mount(<ModalTester />);
        expect(wrapper.render()).toMatchSnapshot();
    });
    it('render without footer', () => {
        const wrapper = mount(<ModalTester footer={null} />);
        expect(wrapper.render()).toMatchSnapshot();
    });
    it('render with custom width', () => {
        const wrapper = mount(<ModalTester width={1000} />);
        expect(wrapper.render()).toMatchSnapshot();
    });
    it('render with custom width with size', () => {
        const wrapper = mount(<ModalTester width='small' />);
        expect(wrapper.render()).toMatchSnapshot();
    });
    it('onCancel should be called', () => {
        const onCancel = jest.fn();
        const wrapper = mount(<Dialog visible onCancel={onCancel} />);
        wrapper.find('.one-dialog-footer .one-button').last().simulate('click');
        expect(onCancel).toHaveBeenCalled();
    });
    it('onOk should be called', () => {
        const onOk = jest.fn();
        const wrapper = mount(<Dialog visible onOk={onOk} />);
        wrapper.find('.one-dialog-footer .one-button').first().simulate('click');
        expect(onOk).toHaveBeenCalled();
    });

    it('support closeIcon', () => {
        const wrapper = mount(<Dialog closeIcon={<a>closeIcon</a>} visible />);
        expect(wrapper.render()).toMatchSnapshot();
    });
    it('destroyOnClose and none visible', () => {
        const onCancel = jest.fn();
        const wrapper = mount(<Dialog visible={false} onCancel={onCancel} destroyOnClose />);
        expect(wrapper.find('.one-dialog').exists()).toBe(false);
    });
    it('onCancel should be called with order', () => {
        const onCancel = jest.fn();
        const wrapper = mount(<Dialog visible onCancel={onCancel} okOrder={2} cancelOrder={1} />);
        wrapper.find('.one-button').last().simulate('click');
        expect(onCancel).toHaveBeenCalled();
    });
    it('onOk should be called with order', () => {
        const onOk = jest.fn();
        const wrapper = mount(<Dialog visible onOk={onOk} okOrder={2} cancelOrder={1} />);
        wrapper.find('.one-dialog-footer .one-button').first().simulate('click');
        expect(onOk).toHaveBeenCalled();
    });
    it('onOk should be called with buttonPosition', () => {
        const onOk = jest.fn();
        const wrapper = mount(<Dialog visible onOk={onOk} buttonPosition="right" />);
        wrapper.find('.one-dialog-footer .one-button').last().simulate('click');
        expect(onOk).toHaveBeenCalled();
    });
});

function $$(className) {
    return document.body.querySelectorAll(className);
}
function open(args) {
    jest.useFakeTimers();
    confirm({
        title: 'Want to delete these items?',
        content: 'some descriptions',
        ...args
    });
    jest.runAllTimers();
    jest.useRealTimers();
}

describe('Dialog.confirm triggers callbacks correctly', () => {
    const errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    afterEach(() => {
        errorSpy.mockReset();
        document.body.innerHTML = '';
    });
    afterAll(() => {
        errorSpy.mockRestore();
    });
    it('should not render title when title not defined', () => {
        jest.useFakeTimers();
        confirm({
            content: 'some descriptions'
        });
        jest.runAllTimers();
        expect(document.querySelector('.one-dialog-confirm-title') === null).toBe(true);
        jest.useRealTimers();
    });
});

describe('trigger onCancel once when click on cancel button', () => {
    const errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    afterEach(() => {
        errorSpy.mockReset();
        document.body.innerHTML = '';
    });
    afterAll(() => {
        errorSpy.mockRestore();
    });
    it('trigger onCancel once when click on cancel button', () => {
        const onCancel = jest.fn();
        const onOk = jest.fn();
        open({
            onCancel,
            onOk
        });
        // first Modal
        $$('.one-button')[1].click();
        expect(onCancel.mock.calls.length).toBe(1);
        expect(onOk.mock.calls.length).toBe(0);
    });
});

describe('trigger onOk once when click on ok button', () => {
    const errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    afterEach(() => {
        errorSpy.mockReset();
        document.body.innerHTML = '';
    });
    afterAll(() => {
        errorSpy.mockRestore();
    });
    it('trigger onOk once when click on ok button', () => {
        const onCancel = jest.fn();
        const onOk = jest.fn();
        open({
            onCancel,
            onOk,
            buttonPosition: 'right'
        });
        // second Modal
        $$('.one-button')[1].click();
        expect(onCancel.mock.calls.length).toBe(0);
        expect(onOk.mock.calls.length).toBe(1);
    });
});

describe('trigger onOk once when click on ok button with okCancel is false', () => {
    const errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    afterEach(() => {
        errorSpy.mockReset();
        document.body.innerHTML = '';
    });
    afterAll(() => {
        errorSpy.mockRestore();
    });
    it('trigger onOk once when click on ok button with okCancel is false', () => {
        const onCancel = jest.fn();
        const onOk = jest.fn();
        open({
            onCancel,
            onOk,
            okCancel: false
        });
        // second Modal
        $$('.one-button')[0].click();
        expect(onCancel.mock.calls.length).toBe(0);
        expect(onOk.mock.calls.length).toBe(1);
    });
});

describe('render icon is Ok', () => {
    const errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    afterEach(() => {
        errorSpy.mockReset();
        document.body.innerHTML = '';
    });
    afterAll(() => {
        errorSpy.mockRestore();
    });
    it('render icon is Ok', () => {
        const onCancel = jest.fn();
        const onOk = jest.fn();
        open({
            onCancel,
            onOk,
            icon: <a className="demo-confirm-icon">x</a>
        });
        expect(typeof document.querySelector('.one-dialog-confirm-title-icon')).toBe('object');
        expect(document.querySelector('.one-dialog-confirm-title-icon') === null).toBe(false);
    });
});

describe('render iconType success is Ok', () => {
    const errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    afterEach(() => {
        errorSpy.mockReset();
        document.body.innerHTML = '';
    });
    afterAll(() => {
        errorSpy.mockRestore();
    });
    it('render iconType success is Ok', () => {
        const onCancel = jest.fn();
        const onOk = jest.fn();
        open({
            onCancel,
            onOk,
            iconType: 'success'
        });
        expect(typeof document.querySelector('.one-dialog-confirm-title-icon')).toBe('object');
        expect(document.querySelector('.one-dialog-confirm-title-icon') === null).toBe(false);
    });
});

describe('render iconType warning is Ok', () => {
    const errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    afterEach(() => {
        errorSpy.mockReset();
        document.body.innerHTML = '';
    });
    afterAll(() => {
        errorSpy.mockRestore();
    });
    it('render iconType warning is Ok', () => {
        const onCancel = jest.fn();
        const onOk = jest.fn();
        open({
            onCancel,
            onOk,
            iconType: 'warning'
        });
        expect(typeof document.querySelector('.one-dialog-confirm-title-icon')).toBe('object');
        expect(document.querySelector('.one-dialog-confirm-title-icon') === null).toBe(false);
    });
});

describe('render iconType fail is Ok', () => {
    const errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    afterEach(() => {
        errorSpy.mockReset();
        document.body.innerHTML = '';
    });
    afterAll(() => {
        errorSpy.mockRestore();
    });
    it('render iconType fail is Ok', () => {
        const onCancel = jest.fn();
        const onOk = jest.fn();
        open({
            onCancel,
            onOk,
            iconType: 'fail'
        });
        expect(typeof document.querySelector('.one-dialog-confirm-title-icon')).toBe('object');
        expect(document.querySelector('.one-dialog-confirm-title-icon') === null).toBe(false);
    });
});

describe('render iconType info is Ok', () => {
    const errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    afterEach(() => {
        errorSpy.mockReset();
        document.body.innerHTML = '';
    });
    afterAll(() => {
        errorSpy.mockRestore();
    });
    it('render iconType info is Ok', () => {
        const onCancel = jest.fn();
        const onOk = jest.fn();
        open({
            onCancel,
            onOk,
            iconType: 'info'
        });
        expect(typeof document.querySelector('.one-dialog-confirm-title-icon')).toBe('object');
        expect(document.querySelector('.one-dialog-confirm-title-icon') === null).toBe(false);
    });
});

describe('render iconType xxx is Ok', () => {
    const errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    afterEach(() => {
        errorSpy.mockReset();
        document.body.innerHTML = '';
    });
    afterAll(() => {
        errorSpy.mockRestore();
    });
    it('render iconType xxx is Ok', () => {
        const onCancel = jest.fn();
        const onOk = jest.fn();
        open({
            onCancel,
            onOk,
            iconType: 'xxx'
        });
        expect(document.querySelector('.one-dialog-confirm-title-icon') === null).toBe(true);
    });
});
describe('render loading icon', () => {
    const errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
    afterEach(() => {
        errorSpy.mockReset();
        document.body.innerHTML = '';
    });
    afterAll(() => {
        errorSpy.mockRestore();
    });
    it('render iconType xxx is Ok', () => {
        open({
            onOk() {
                return new Promise((resolve, reject) => {
                    setTimeout(Math.random() > 0.5 ? resolve : reject, 1000);
                }).catch(() => console.log('Oops errors!'));
            },
            onCancel() {
                console.log('Cancel');
            }
        });
        expect(document.querySelector('.one-dialog-confirm-title-icon') === null).toBe(true);
    });
});

describe('action button render correctly 1', () => {
    const wrapper = mount(<ActionButton order={1} />);
    expect(wrapper.render()).toMatchSnapshot();
});

describe('action button render correctly 2', () => {
    const wrapper = mount(<ActionButton
        order={1}
        otherProps={{
            className: 'demo-action-button'
        }}
        actionFn={() => {
            return new Promise((resolve, reject) => {
                resolve(123);
            });
        }}
    />);
    expect(wrapper.render()).toMatchSnapshot();
});

describe('action button render correctly 4', () => {
    const wrapper = mount(<ActionButton
        order={1}
        otherProps={{
            className: 'demo-action-button'
        }}
        actionFn={() => {
            return {
                then: () => {
                    console.log(123);
                }
            };
        }}
    />);
    expect(wrapper.render()).toMatchSnapshot();
});
describe('action button render correctly 3', () => {
    const closeModal = jest.fn();
    const wrapper = mount(<ActionButton
        order={1}
        otherProps={{
            className: 'demo-action-button'
        }}
        closeModal={closeModal}
    />);
    wrapper.find('.demo-action-button').at(0).simulate('click');
    expect(closeModal).toHaveBeenCalled();
    expect(wrapper.render()).toMatchSnapshot();
});