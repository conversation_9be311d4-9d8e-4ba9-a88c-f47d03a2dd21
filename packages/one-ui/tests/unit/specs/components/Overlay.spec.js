/**
 * @file overlay的单测
 * <AUTHOR>
 * @date 2020-09-02
 */

import {mount} from 'enzyme';
import React from 'react';
import Overlay from '../../../../src/components/overlay/index';
import Button from '../../../../src/components/button/index';
import mountTest from '../../../../tests/shared/mountTest';

describe('Overlay Component', () => {
    mountTest(Overlay);

    let wrapper;
    let onVisibleChange;

    beforeAll(() => {
        onVisibleChange = jest.fn();
        wrapper = mount(
            <Overlay
                onVisibleChange={onVisibleChange}
                trigger="click"
                overlay={(
                    <div>弹层内容</div>
                )}
                header="这是一个下拉弹层"
            />
        );
    });

    // has console error
    test('should render a Overlay component', () => {
        expect(wrapper).toMatchSnapshot();
    });

    test('should create a Overlay component with `trigger` props', () => {
        expect(wrapper.props().trigger).toBe('click');
    });

    test('should create a Overlay support click', () => {
        wrapper.simulate('click');
        expect(onVisibleChange.mock.calls.length).toBe(1);
    });

    test('should render overlay ok when click button', () => {
        const list = [];
        for (let i = 1; i <= Math.ceil(Math.random() * 20); i++) {
            list.push({
                label: `操作的点点滴滴多多多的点点滴滴多多多多多多多多多多多多多多多命令${i}`,
                value: i
            });
        }
        const wrapper = (
            <Overlay
                trigger="click"
                dropdownMatchSelectWidth
                overlay={(
                    <div style={{
                        padding: '20px'
                    }}
                    >
                        {
                            list.map(item => {
                                return (
                                    <div
                                        key={item.value}
                                    >
                                        <div style={{
                                            marginBottom: '10px'
                                        }}
                                        >
                                            {item.label}
                                        </div>
                                    </div>
                                );
                            })
                        }
                    </div>
                )}
                header="这是一个下拉弹层"
            >
                <Button className="demo-wrapper">这是一个案例</Button>
            </Overlay>
        );
    });
});
