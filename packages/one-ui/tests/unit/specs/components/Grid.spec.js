/**
 * @file Grid.spec.js
 * @<NAME_EMAIL>
 * @date 2020-09-03
 */
import {render} from 'enzyme';
import React from 'react';
import Grid from '../../../../src/components/grid';
import mountTest from '../../../shared/mountTest';
const {Row, Col} = Grid;

describe('Grid Component', () => {
    mountTest(Row);
    mountTest(Col);

    test('should render Col', () => {
        const wrapper = render(<Col span={2} />);
        expect(wrapper).toMatchSnapshot();
    });

    test('should render Row', () => {
        const wrapper = render(<Row type="flex" justify="center" align="center" />);
        expect(wrapper).toMatchSnapshot();
    });

    test('renders wrapped Col correctly', () => {
        const wrapper = render(
            <Row gutter={20}>
                <div>
                    <Col span={12} />
                </div>
                <Col span={12} />
            </Row>,
        );
        expect(wrapper).toMatchSnapshot();
    });

    test('renders xs lg correctly', () => {
        const wrapper = render(
            <Row>
                <Col xs={{span: 5, offset: 1}} lg={{span: 6, offset: 2}}>
                    Col1
                </Col>
                <Col xs={6} lg={{span: 6, offset: 2}}>
                    Col2
                </Col>
                <Col xs={5} lg={8}>
                    Col3
                </Col>
            </Row>
        );
        expect(wrapper).toMatchSnapshot();
    });

});