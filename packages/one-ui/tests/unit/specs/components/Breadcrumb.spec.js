/**
 * @file 面包屑的UT测试
 * <AUTHOR>
 * @date 2020-08-24
 */
import {mount, render} from 'enzyme';
import React from 'react';
import Breadcrumb from '../../../../src/components/breadcrumb/index';
import RawBreadcrumb from '../../../../src/components/breadcrumb/breadcrumb';
import BreadcrumbItem from '../../../../src/components/breadcrumb/breadcrumbItem';
import mountTest from '../../../shared/mountTest';
import ProviderConfig from '../../../../src/components/providerConfig';

describe('Breadcrumb', () => {
    mountTest(Breadcrumb);
    it('should be rendered correctly', () => {
        const wrapper = mount(
            <ProviderConfig size="small">
                <Breadcrumb>
                    <Breadcrumb.Item>首页</Breadcrumb.Item>
                    <Breadcrumb.Item>
                        这是一个计划
                    </Breadcrumb.Item>
                    <Breadcrumb.Item>
                        这是一个x单元
                    </Breadcrumb.Item>
                    <Breadcrumb.Item>这是一个这是一个关键词</Breadcrumb.Item>
                </Breadcrumb>
            </ProviderConfig>,
        );
        expect(render(wrapper)).toMatchSnapshot();
    });
    it('should be rendered correctly 2', () => {
        const wrapper = mount(
            <ProviderConfig size="small">
                <Breadcrumb>
                    <BreadcrumbItem>首页</BreadcrumbItem>
                    <BreadcrumbItem>
                        这是一个计划
                    </BreadcrumbItem>
                    <BreadcrumbItem>
                        这是一个x单元
                    </BreadcrumbItem>
                    <BreadcrumbItem>这是一个这是一个关键词</BreadcrumbItem>
                </Breadcrumb>
            </ProviderConfig>,
        );
        expect(render(wrapper)).toMatchSnapshot();
    });
    it('should be rendered correctly 3', () => {
        const wrapper = mount(
            <ProviderConfig size="small">
                <Breadcrumb>
                    {null}
                    {null}
                </Breadcrumb>
            </ProviderConfig>,
        );
        expect(render(wrapper)).toMatchSnapshot();
    });
    it('should be rendered correctly 4', () => {
        const wrapper = mount(
            <RawBreadcrumb size="small">
                <BreadcrumbItem>首页</BreadcrumbItem>
                <BreadcrumbItem>
                    这是一个计划
                </BreadcrumbItem>
                <BreadcrumbItem disabled>
                    这是一个x单元
                </BreadcrumbItem>
                <BreadcrumbItem>这是一个这是一个关键词</BreadcrumbItem>
            </RawBreadcrumb>
        );
        expect(render(wrapper)).toMatchSnapshot();
        expect(wrapper.find('.one-breadcrumb').at(0).hasClass('one-breadcrumb-small')).toBe(true);
        expect(wrapper.find('.one-breadcrumb').at(0).hasClass('one-breadcrumb-normal')).toBe(true);
    });
    it('should be rendered correctly 5', () => {
        const wrapper = mount(
            <ProviderConfig>
                <Breadcrumb type="strong">
                    <BreadcrumbItem>首页</BreadcrumbItem>
                    <BreadcrumbItem>
                        这是一个计划
                    </BreadcrumbItem>
                    <BreadcrumbItem disabled>
                        这是一个x单元
                    </BreadcrumbItem>
                    <BreadcrumbItem>这是一个这是一个关键词</BreadcrumbItem>
                </Breadcrumb>
            </ProviderConfig>,
        );
        expect(render(wrapper)).toMatchSnapshot();
    });
    it('should be rendered correctly 6', () => {
        const wrapper = mount(
            <RawBreadcrumb type="strong">
                <Breadcrumb.Item href="http://www.baidu.com">
                    首页
                </Breadcrumb.Item>
                <Breadcrumb.Item href="http://www.baidu.com">
                    这是一个计划
                </Breadcrumb.Item>
                <Breadcrumb.Item disabled href="http://www.baidu.com">
                    这是一个x单元
                </Breadcrumb.Item>
                <Breadcrumb.Item>这是一个这是一个关键词</Breadcrumb.Item>
            </RawBreadcrumb>
        );
        expect(render(wrapper)).toMatchSnapshot();
        expect(wrapper.props().type).toBe('strong');
    });
    it('should be rendered correctly 7', () => {
        const logSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
        const wrapper = mount(
            <ProviderConfig>
                <Breadcrumb type="strong">
                    <Breadcrumb.Item href="http://www.baidu.com" />
                    <Breadcrumb.Item href="http://www.baidu.com" _target="blank">
                        这是一个计划
                    </Breadcrumb.Item>
                    <Breadcrumb.Item disabled href="http://www.baidu.com">
                        这是一个x单元
                    </Breadcrumb.Item>
                    <Breadcrumb.Item>这是一个这是一个关键词</Breadcrumb.Item>
                </Breadcrumb>
            </ProviderConfig>,
        );
        expect(render(wrapper)).toMatchSnapshot();
        wrapper.find('.one-breadcrumb-link').at(0).simulate('click');
    });
});
