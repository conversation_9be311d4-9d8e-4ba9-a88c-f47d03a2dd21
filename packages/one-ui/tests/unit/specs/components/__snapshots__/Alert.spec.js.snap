// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Alert Component should create a Alert component with base props 1`] = `
<Alert
  content="success content"
  title="success title"
  type="success"
>
  <Alert
    content="success content"
    onClose={[Function]}
    prefixCls="one-alert"
    size="medium"
    title="success title"
    type="success"
  >
    <div
      className="one-alert one-alert-success one-alert-medium one-alert-with-title"
    >
      <div>
        <div
          className="one-alert-title"
        >
          success title
        </div>
        <div
          className="one-alert-content"
        >
          success content
        </div>
      </div>
    </div>
  </Alert>
</Alert>
`;

exports[`Alert Component should has error when type is custom 1`] = `
<Alert
  closable={true}
  type="xxx"
>
  <Alert
    closable={true}
    onClose={[Function]}
    prefixCls="one-alert"
    size="medium"
    type="xxx"
  >
    <div
      className="one-alert one-alert-xxx one-alert-medium one-alert-no-title one-alert-has-close-icon"
    >
      <div>
        <div
          className="one-alert-content"
        />
      </div>
      <span
        className="one-alert-close-icon"
        onClick={[Function]}
      >
        <IconTimes>
          <svg
            className="dls-icon "
            dangerouslySetInnerHTML={
              Object {
                "__html": "<path fill=\\"currentColor\\" d=\\"m7.444 11.998-7 7 1.555 1.556 7-7 7 7L17.556 19l-7-7 7-7.001L16 3.442l-7 7-7.001-7L.443 4.998l7 7z\\"/>",
              }
            }
            fill="none"
            focusable={false}
            height={24}
            viewBox="0 0 18 24"
            width={18}
            xmlns="http://www.w3.org/2000/svg"
          />
        </IconTimes>
      </span>
    </div>
  </Alert>
</Alert>
`;

exports[`Alert Component should rende ok icon is react element 1`] = `
<Alert
  closable={true}
  icon={
    <div>
      啦啦啦
    </div>
  }
  type="success"
>
  <Alert
    closable={true}
    icon={
      <div>
        啦啦啦
      </div>
    }
    onClose={[Function]}
    prefixCls="one-alert"
    size="medium"
    type="success"
  >
    <div
      className="one-alert one-alert-success one-alert-medium one-alert-no-title one-alert-has-close-icon"
    >
      <div>
        <div
          className="one-alert-content"
        />
      </div>
      <span
        className="one-alert-close-icon"
        onClick={[Function]}
      >
        <IconTimes>
          <svg
            className="dls-icon "
            dangerouslySetInnerHTML={
              Object {
                "__html": "<path fill=\\"currentColor\\" d=\\"m7.444 11.998-7 7 1.555 1.556 7-7 7 7L17.556 19l-7-7 7-7.001L16 3.442l-7 7-7.001-7L.443 4.998l7 7z\\"/>",
              }
            }
            fill="none"
            focusable={false}
            height={24}
            viewBox="0 0 18 24"
            width={18}
            xmlns="http://www.w3.org/2000/svg"
          />
        </IconTimes>
      </span>
    </div>
  </Alert>
</Alert>
`;

exports[`Alert Component should rende ok icon is string 1`] = `
<Alert
  closable={true}
  icon="xxx"
  type="success"
>
  <Alert
    closable={true}
    icon="xxx"
    onClose={[Function]}
    prefixCls="one-alert"
    size="medium"
    type="success"
  >
    <div
      className="one-alert one-alert-success one-alert-medium one-alert-no-title one-alert-has-close-icon"
    >
      <div>
        <div
          className="one-alert-content"
        />
      </div>
      <span
        className="one-alert-close-icon"
        onClick={[Function]}
      >
        <IconTimes>
          <svg
            className="dls-icon "
            dangerouslySetInnerHTML={
              Object {
                "__html": "<path fill=\\"currentColor\\" d=\\"m7.444 11.998-7 7 1.555 1.556 7-7 7 7L17.556 19l-7-7 7-7.001L16 3.442l-7 7-7.001-7L.443 4.998l7 7z\\"/>",
              }
            }
            fill="none"
            focusable={false}
            height={24}
            viewBox="0 0 18 24"
            width={18}
            xmlns="http://www.w3.org/2000/svg"
          />
        </IconTimes>
      </span>
    </div>
  </Alert>
</Alert>
`;

exports[`Alert Component should show showIcon when has showIcon props 1`] = `
<Alert
  showIcon={true}
  size="small"
  type="info"
>
  <Alert
    onClose={[Function]}
    prefixCls="one-alert"
    showIcon={true}
    size="small"
    type="info"
  >
    <div
      className="one-alert one-alert-info one-alert-small one-alert-no-title one-alert-show-icon"
    >
      <span
        className="one-alert-icon"
      >
        <IconInfoCircleSolid
          className="one-alert-title-icon"
        >
          <svg
            className="dls-icon one-alert-title-icon"
            dangerouslySetInnerHTML={
              Object {
                "__html": "<path fill=\\"currentColor\\" fill-rule=\\"evenodd\\" d=\\"M1 12C1 5.925 5.925 1 12 1s11 4.925 11 11-4.925 11-11 11S1 18.075 1 12zm12.25-4.15a1.25 1.25 0 1 0-2.5 0 1.25 1.25 0 0 0 2.5 0zM11 17.4v-7h2v7h-2z\\" clip-rule=\\"evenodd\\"/>",
              }
            }
            fill="none"
            focusable={false}
            height={24}
            viewBox="0 0 24 24"
            width={24}
            xmlns="http://www.w3.org/2000/svg"
          />
        </IconInfoCircleSolid>
      </span>
      <div>
        <div
          className="one-alert-content"
        />
      </div>
    </div>
  </Alert>
</Alert>
`;

exports[`Alert Page Component should create a Alert Page component 1`] = `
<AlertPage
  dataSource={
    Array [
      <Alert
        closable={true}
        content="带有通知性质的通知条"
        type="success"
      />,
      <Alert
        closable={true}
        content="带有通知性质的通知条"
        type="info"
      />,
      <Alert
        closable={true}
        content="带有通知性质的通知条"
        type="error"
      />,
      <Alert
        closable={true}
        content="带有通知性质的通知条"
        type="warning"
      />,
    ]
  }
>
  <AlertPage
    dataSource={
      Array [
        <Alert
          closable={true}
          content="带有通知性质的通知条"
          type="success"
        />,
        <Alert
          closable={true}
          content="带有通知性质的通知条"
          type="info"
        />,
        <Alert
          closable={true}
          content="带有通知性质的通知条"
          type="error"
        />,
        <Alert
          closable={true}
          content="带有通知性质的通知条"
          type="warning"
        />,
      ]
    }
    defaultActiveIndex={0}
    onClose={[Function]}
    onNextChange={[Function]}
    onPrevChange={[Function]}
    prefixCls="one-alert-page"
    size="medium"
  >
    <div
      className="one-alert-page one-alert-page-medium one-alert-page-first-page"
    >
      <span
        className="one-alert-page-slick-item"
        key="1"
      >
        <Alert
          closable={true}
          content="带有通知性质的通知条"
          key="1"
          onClose={[Function]}
          type="success"
          visible={true}
        >
          <Alert
            closable={true}
            content="带有通知性质的通知条"
            onClose={[Function]}
            prefixCls="one-alert"
            size="medium"
            type="success"
            visible={true}
          >
            <div
              className="one-alert one-alert-success one-alert-medium one-alert-no-title one-alert-has-close-icon"
            >
              <div>
                <div
                  className="one-alert-content"
                >
                  带有通知性质的通知条
                </div>
              </div>
              <span
                className="one-alert-close-icon"
                onClick={[Function]}
              >
                <IconTimes>
                  <svg
                    className="dls-icon "
                    dangerouslySetInnerHTML={
                      Object {
                        "__html": "<path fill=\\"currentColor\\" d=\\"m7.444 11.998-7 7 1.555 1.556 7-7 7 7L17.556 19l-7-7 7-7.001L16 3.442l-7 7-7.001-7L.443 4.998l7 7z\\"/>",
                      }
                    }
                    fill="none"
                    focusable={false}
                    height={24}
                    viewBox="0 0 18 24"
                    width={18}
                    xmlns="http://www.w3.org/2000/svg"
                  />
                </IconTimes>
              </span>
            </div>
          </Alert>
        </Alert>
      </span>
      <div
        className="one-alert-page-count"
      >
        <IconChevronLeft
          className="one-alert-page-count-prev"
          onClick={[Function]}
        >
          <svg
            className="dls-icon one-alert-page-count-prev"
            dangerouslySetInnerHTML={
              Object {
                "__html": "<path fill=\\"currentColor\\" fill-rule=\\"evenodd\\" d=\\"m2.462 12 7.222-6.77-1.368-1.46-8 7.5a1 1 0 0 0 0 1.46l8 7.5 1.368-1.46L2.462 12z\\" clip-rule=\\"evenodd\\"/>",
              }
            }
            fill="none"
            focusable={false}
            height={24}
            onClick={[Function]}
            viewBox="0 0 10 24"
            width={10}
            xmlns="http://www.w3.org/2000/svg"
          />
        </IconChevronLeft>
        <span>
          1
          /
          4
        </span>
        <IconChevronRight
          className="one-alert-page-count-next"
          onClick={[Function]}
        >
          <svg
            className="dls-icon one-alert-page-count-next"
            dangerouslySetInnerHTML={
              Object {
                "__html": "<path fill=\\"currentColor\\" fill-rule=\\"evenodd\\" d=\\"M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z\\" clip-rule=\\"evenodd\\"/>",
              }
            }
            fill="none"
            focusable={false}
            height={24}
            onClick={[Function]}
            viewBox="0 0 10 24"
            width={10}
            xmlns="http://www.w3.org/2000/svg"
          />
        </IconChevronRight>
      </div>
    </div>
  </AlertPage>
</AlertPage>
`;
