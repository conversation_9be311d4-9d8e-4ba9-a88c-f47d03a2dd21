// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Carousel Component should works for customSuffix bottom 1`] = `
<div
  class="one-carousel one-carousel-slider-line"
  style="width: 300px;"
>
  <div
    class="slick-slider slick-initialized"
  >
    <div
      class="slick-list"
    >
      <div
        class="slick-track"
        style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
      >
        <div
          aria-hidden="false"
          class="slick-slide slick-active slick-current"
          data-index="0"
          style="outline: none; width: 0px;"
          tabindex="-1"
        >
          <div>
            <div
              style="width: 100%; display: inline-block;"
              tabindex="-1"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div>
    custom
  </div>
</div>
`;

exports[`Carousel Component should works for customSuffix left 1`] = `
<div
  class="one-carousel one-carousel-slider-line"
  style="width: 300px;"
>
  <div
    class="slick-slider slick-initialized"
  >
    <div
      class="slick-list"
    >
      <div
        class="slick-track"
        style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
      >
        <div
          aria-hidden="false"
          class="slick-slide slick-active slick-current"
          data-index="0"
          style="outline: none; width: 0px;"
          tabindex="-1"
        >
          <div>
            <div
              style="width: 100%; display: inline-block;"
              tabindex="-1"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div>
    custom
  </div>
</div>
`;

exports[`Carousel Component should works for customSuffix right 1`] = `
<div
  class="one-carousel one-carousel-slider-line"
  style="width: 300px;"
>
  <div
    class="slick-slider slick-initialized"
  >
    <div
      class="slick-list"
    >
      <div
        class="slick-track"
        style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
      >
        <div
          aria-hidden="false"
          class="slick-slide slick-active slick-current"
          data-index="0"
          style="outline: none; width: 0px;"
          tabindex="-1"
        >
          <div>
            <div
              style="width: 100%; display: inline-block;"
              tabindex="-1"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div>
    custom
  </div>
</div>
`;

exports[`Carousel Component should works for customSuffix top 1`] = `
<div
  class="one-carousel one-carousel-slider-line"
  style="width: 300px;"
>
  <div
    class="slick-slider slick-initialized"
  >
    <div
      class="slick-list"
    >
      <div
        class="slick-track"
        style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
      >
        <div
          aria-hidden="false"
          class="slick-slide slick-active slick-current"
          data-index="0"
          style="outline: none; width: 0px;"
          tabindex="-1"
        >
          <div>
            <div
              style="width: 100%; display: inline-block;"
              tabindex="-1"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div>
    custom
  </div>
</div>
`;

exports[`Carousel Component should works for dotPosition bottom 1`] = `
<div
  class="one-carousel one-carousel-slider-line"
  style="width: 300px;"
>
  <div
    class="slick-slider slick-initialized"
  >
    <div
      class="slick-list"
    >
      <div
        class="slick-track"
        style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
      >
        <div
          aria-hidden="false"
          class="slick-slide slick-active slick-current"
          data-index="0"
          style="outline: none; width: 0px;"
          tabindex="-1"
        >
          <div>
            <div
              style="width: 100%; display: inline-block;"
              tabindex="-1"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Carousel Component should works for dotPosition left 1`] = `
<div
  class="one-carousel one-carousel-vertical one-carousel-slider-line"
  style="width: 300px;"
>
  <div
    class="slick-slider slick-vertical slick-initialized"
  >
    <div
      class="slick-list"
    >
      <div
        class="slick-track"
        style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
      >
        <div
          aria-hidden="false"
          class="slick-slide slick-active slick-current"
          data-index="0"
          style="outline: none; width: 0px;"
          tabindex="-1"
        >
          <div>
            <div
              style="width: 100%; display: inline-block;"
              tabindex="-1"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Carousel Component should works for dotPosition right 1`] = `
<div
  class="one-carousel one-carousel-vertical one-carousel-slider-line"
  style="width: 300px;"
>
  <div
    class="slick-slider slick-vertical slick-initialized"
  >
    <div
      class="slick-list"
    >
      <div
        class="slick-track"
        style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
      >
        <div
          aria-hidden="false"
          class="slick-slide slick-active slick-current"
          data-index="0"
          style="outline: none; width: 0px;"
          tabindex="-1"
        >
          <div>
            <div
              style="width: 100%; display: inline-block;"
              tabindex="-1"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Carousel Component should works for dotPosition top 1`] = `
<div
  class="one-carousel one-carousel-slider-line"
  style="width: 300px;"
>
  <div
    class="slick-slider slick-initialized"
  >
    <div
      class="slick-list"
    >
      <div
        class="slick-track"
        style="opacity: 1; transform: translate3d(0px, 0px, 0px);"
      >
        <div
          aria-hidden="false"
          class="slick-slide slick-active slick-current"
          data-index="0"
          style="outline: none; width: 0px;"
          tabindex="-1"
        >
          <div>
            <div
              style="width: 100%; display: inline-block;"
              tabindex="-1"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
