// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Button Component only Icon button is loading 1`] = `
<Button
  icon="calendar"
  loading={true}
  type="primary"
>
  <Button
    disabled={false}
    htmlType="button"
    icon="calendar"
    loading={true}
    name=""
    onClick={[Function]}
    prefixCls="one-button"
    readOnly={false}
    readonly={false}
    size="medium"
    type="primary"
  >
    <button
      className="one-button one-main one-button-primary one-button-medium one-button-icon-only one-button-primary-loading one-button-has-icon"
      disabled={false}
      name=""
      onClick={[Function]}
      type="button"
    >
      <Loading
        className="one-button-loading-icon"
        size="small"
      >
        <Loading
          CustomIconNode={null}
          className="one-button-loading-icon"
          loading={true}
          prefixCls="one-loading"
          size="small"
          style={Object {}}
          textDirection="horizontal"
          tip=""
          type="normal"
        >
          <div
            className="one-loading one-loading-small one-loading-visible one-loading-type-normal one-loading-horizontal one-button-loading-icon"
            style={Object {}}
          >
            <div
              className="one-loading-icon-element"
            >
              <svg
                className="dls-loading one-loading-loading-icon"
                dangerouslySetInnerHTML={
                  Object {
                    "__html": "<circle cx=\\"50%\\" cy=\\"50%\\" r=\\"29\\" fill=\\"none\\" stroke=\\"currentColor\\" stroke-linecap=\\"round\\" stroke-width=\\"6\\"/><style>.dls-loading circle{stroke-dasharray:182.21;animation:chase-495fa 2s linear infinite;transform-origin:50% 50%}@keyframes chase-495fa{0%{stroke-dashoffset:163.99;transform:rotate(-90deg)}60%{stroke-dashoffset:27.33;transform:rotate(180deg)}to{stroke-dashoffset:163.99;transform:rotate(630deg)}}@keyframes spin-495fa{0%{transform:rotate(0)}to{transform:rotate(1turn)}}.dls-loading,_:-ms-lang(x){animation:spin-495fa 1s linear infinite}.dls-loading circle,_:-ms-lang(x){stroke-dashoffset:136.66}</style>",
                  }
                }
                height="40"
                viewBox="0 0 64 64"
                width="40"
              />
            </div>
          </div>
        </Loading>
      </Loading>
    </button>
  </Button>
</Button>
`;

exports[`Button Component should be only Icon button 1`] = `
<Button
  icon="calendar"
  size="large"
  style={
    Object {
      "marginRight": "20px",
    }
  }
  type="primary"
>
  <Button
    disabled={false}
    htmlType="button"
    icon="calendar"
    loading={false}
    name=""
    onClick={[Function]}
    prefixCls="one-button"
    readOnly={false}
    readonly={false}
    size="large"
    style={
      Object {
        "marginRight": "20px",
      }
    }
    type="primary"
  >
    <button
      className="one-button one-main one-button-primary one-button-large one-button-icon-only one-button-has-icon"
      disabled={false}
      name=""
      onClick={[Function]}
      style={
        Object {
          "marginRight": "20px",
        }
      }
      type="button"
    >
      <IconSvg
        className="one-button-icon"
        suffixCls="iconSvg"
        type="calendar"
      >
        <IconCalendar
          className="one-iconSvg one-iconSvg-calendar one-button-icon"
        >
          <svg
            className="dls-icon one-iconSvg one-iconSvg-calendar one-button-icon"
            dangerouslySetInnerHTML={
              Object {
                "__html": "<path fill=\\"currentColor\\" fill-rule=\\"evenodd\\" d=\\"M9 1.5H7V3h-.5a5 5 0 0 0-5 5v9.4a5 5 0 0 0 5 5h11a5 5 0 0 0 5-5V8a5 5 0 0 0-5-5H17V1.5h-2V3H9V1.5zM15 5H9v1.5H7V5h-.5a3 3 0 0 0-3 3v.5h17V8a3 3 0 0 0-3-3H17v1.5h-2V5zm5.5 5.5h-17v6.9a3 3 0 0 0 3 3h11a3 3 0 0 0 3-3v-6.9z\\" clip-rule=\\"evenodd\\"/>",
              }
            }
            fill="none"
            focusable={false}
            height={24}
            viewBox="0 0 24 24"
            width={24}
            xmlns="http://www.w3.org/2000/svg"
          />
        </IconCalendar>
      </IconSvg>
    </button>
  </Button>
</Button>
`;

exports[`Button Component should create a button component with base props 1`] = `
<Button
  size="large"
  type="primary"
>
  <Button
    disabled={false}
    htmlType="button"
    icon=""
    loading={false}
    name=""
    onClick={[Function]}
    prefixCls="one-button"
    readOnly={false}
    readonly={false}
    size="large"
    type="primary"
  >
    <button
      className="one-button one-main one-button-primary one-button-large"
      disabled={false}
      name=""
      onClick={[Function]}
      type="button"
    />
  </Button>
</Button>
`;

exports[`Button Component should create a button component with custom className 1`] = `
<Button
  className="two-ui"
>
  <Button
    className="two-ui"
    disabled={false}
    htmlType="button"
    icon=""
    loading={false}
    name=""
    onClick={[Function]}
    prefixCls="one-button"
    readOnly={false}
    readonly={false}
    size="medium"
    type="normal"
  >
    <button
      className="one-button two-ui one-main one-button-normal one-button-medium"
      disabled={false}
      name=""
      onClick={[Function]}
      type="button"
    />
  </Button>
</Button>
`;

exports[`Button Component should create a button component with custom style 1`] = `
<Button
  style={
    Object {
      "marginTop": "20px",
    }
  }
>
  <Button
    disabled={false}
    htmlType="button"
    icon=""
    loading={false}
    name=""
    onClick={[Function]}
    prefixCls="one-button"
    readOnly={false}
    readonly={false}
    size="medium"
    style={
      Object {
        "marginTop": "20px",
      }
    }
    type="normal"
  >
    <button
      className="one-button one-main one-button-normal one-button-medium"
      disabled={false}
      name=""
      onClick={[Function]}
      style={
        Object {
          "marginTop": "20px",
        }
      }
      type="button"
    />
  </Button>
</Button>
`;

exports[`Button Component should custom Icon button 1`] = `
<Button
  icon={
    <div>
      icon
    </div>
  }
  type="primary"
>
  <Button
    disabled={false}
    htmlType="button"
    icon={
      <div>
        icon
      </div>
    }
    loading={false}
    name=""
    onClick={[Function]}
    prefixCls="one-button"
    readOnly={false}
    readonly={false}
    size="medium"
    type="primary"
  >
    <button
      className="one-button one-main one-button-primary one-button-medium one-button-icon-only one-button-has-icon"
      disabled={false}
      name=""
      onClick={[Function]}
      type="button"
    >
      <div
        className="one-button-icon"
      >
        icon
      </div>
    </button>
  </Button>
</Button>
`;

exports[`Button Component should support disabled state 1`] = `
<Button
  disabled={true}
>
  <Button
    disabled={true}
    htmlType="button"
    icon=""
    loading={false}
    name=""
    onClick={[Function]}
    prefixCls="one-button"
    readOnly={false}
    readonly={false}
    size="medium"
    type="normal"
  >
    <button
      className="one-button one-main one-button-normal one-button-medium one-button-normal-disabled"
      disabled={true}
      name=""
      onClick={[Function]}
      type="button"
    />
  </Button>
</Button>
`;

exports[`Button Component should support icon type 1`] = `
<Button
  icon="calendar"
>
  <Button
    disabled={false}
    htmlType="button"
    icon="calendar"
    loading={false}
    name=""
    onClick={[Function]}
    prefixCls="one-button"
    readOnly={false}
    readonly={false}
    size="medium"
    type="normal"
  >
    <button
      className="one-button one-main one-button-normal one-button-medium one-button-icon-only one-button-has-icon"
      disabled={false}
      name=""
      onClick={[Function]}
      type="button"
    >
      <IconSvg
        className="one-button-icon"
        suffixCls="iconSvg"
        type="calendar"
      >
        <IconCalendar
          className="one-iconSvg one-iconSvg-calendar one-button-icon"
        >
          <svg
            className="dls-icon one-iconSvg one-iconSvg-calendar one-button-icon"
            dangerouslySetInnerHTML={
              Object {
                "__html": "<path fill=\\"currentColor\\" fill-rule=\\"evenodd\\" d=\\"M9 1.5H7V3h-.5a5 5 0 0 0-5 5v9.4a5 5 0 0 0 5 5h11a5 5 0 0 0 5-5V8a5 5 0 0 0-5-5H17V1.5h-2V3H9V1.5zM15 5H9v1.5H7V5h-.5a3 3 0 0 0-3 3v.5h17V8a3 3 0 0 0-3-3H17v1.5h-2V5zm5.5 5.5h-17v6.9a3 3 0 0 0 3 3h11a3 3 0 0 0 3-3v-6.9z\\" clip-rule=\\"evenodd\\"/>",
              }
            }
            fill="none"
            focusable={false}
            height={24}
            viewBox="0 0 24 24"
            width={24}
            xmlns="http://www.w3.org/2000/svg"
          />
        </IconCalendar>
      </IconSvg>
    </button>
  </Button>
</Button>
`;

exports[`Button Component should support submit type 1`] = `
<Button
  htmlType="submit"
>
  <Button
    disabled={false}
    htmlType="submit"
    icon=""
    loading={false}
    name=""
    onClick={[Function]}
    prefixCls="one-button"
    readOnly={false}
    readonly={false}
    size="medium"
    type="normal"
  >
    <button
      className="one-button one-main one-button-normal one-button-medium"
      disabled={false}
      name=""
      onClick={[Function]}
      type="submit"
    />
  </Button>
</Button>
`;
