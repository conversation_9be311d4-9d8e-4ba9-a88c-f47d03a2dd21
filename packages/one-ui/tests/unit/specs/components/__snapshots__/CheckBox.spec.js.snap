// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CheckBox Group Component passes prefixCls down to checkbox 1`] = `
<div
  class="my-checkbox-group my-checkbox-group-medium my-checkbox-group-row"
>
  <div
    class="my-checkbox-group-items"
  >
    <label
      class="my-checkbox-wrapper my-checkbox-wrapper-row my-checkbox-wrapper-medium"
    >
      <span
        class="my-checkbox"
      >
        <input
          class="my-checkbox-input"
          type="checkbox"
        />
        <span
          class="my-checkbox-inner"
        />
      </span>
      <span
        class="my-checkbox-item"
      >
        Apple
      </span>
    </label>
    <label
      class="my-checkbox-wrapper my-checkbox-wrapper-row my-checkbox-wrapper-medium"
    >
      <span
        class="my-checkbox"
      >
        <input
          class="my-checkbox-input"
          type="checkbox"
        />
        <span
          class="my-checkbox-inner"
        />
      </span>
      <span
        class="my-checkbox-item"
      >
        Orange
      </span>
    </label>
  </div>
</div>
`;
