// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Breadcrumb should be rendered correctly 1`] = `
<div
  class="one-breadcrumb one-breadcrumb-small one-breadcrumb-normal"
>
  <span>
    <span
      class="one-breadcrumb-link"
    >
      首页
    </span>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
  <span>
    <span
      class="one-breadcrumb-link"
    >
      这是一个计划
    </span>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
  <span>
    <span
      class="one-breadcrumb-link"
    >
      这是一个x单元
    </span>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
  <span>
    <span
      class="one-breadcrumb-link"
    >
      这是一个这是一个关键词
    </span>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
</div>
`;

exports[`Breadcrumb should be rendered correctly 2 1`] = `
<div
  class="one-breadcrumb one-breadcrumb-small one-breadcrumb-normal"
>
  <span>
    <span
      class="one-breadcrumb-link"
    >
      首页
    </span>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
  <span>
    <span
      class="one-breadcrumb-link"
    >
      这是一个计划
    </span>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
  <span>
    <span
      class="one-breadcrumb-link"
    >
      这是一个x单元
    </span>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
  <span>
    <span
      class="one-breadcrumb-link"
    >
      这是一个这是一个关键词
    </span>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
</div>
`;

exports[`Breadcrumb should be rendered correctly 3 1`] = `
<div
  class="one-breadcrumb one-breadcrumb-small one-breadcrumb-normal"
/>
`;

exports[`Breadcrumb should be rendered correctly 4 1`] = `
<div
  class="one-breadcrumb one-breadcrumb-small one-breadcrumb-normal"
>
  <span>
    <span
      class="one-breadcrumb-link"
    >
      首页
    </span>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
  <span>
    <span
      class="one-breadcrumb-link"
    >
      这是一个计划
    </span>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
  <span>
    <span
      class="one-breadcrumb-link-disabled"
    >
      这是一个x单元
    </span>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
  <span>
    <span
      class="one-breadcrumb-link"
    >
      这是一个这是一个关键词
    </span>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
</div>
`;

exports[`Breadcrumb should be rendered correctly 5 1`] = `
<div
  class="one-breadcrumb one-breadcrumb-medium one-breadcrumb-strong"
>
  <span>
    <span
      class="one-breadcrumb-link"
    >
      首页
    </span>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
  <span>
    <span
      class="one-breadcrumb-link"
    >
      这是一个计划
    </span>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
  <span>
    <span
      class="one-breadcrumb-link-disabled"
    >
      这是一个x单元
    </span>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
  <span>
    <span
      class="one-breadcrumb-link"
    >
      这是一个这是一个关键词
    </span>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
</div>
`;

exports[`Breadcrumb should be rendered correctly 6 1`] = `
<div
  class="one-breadcrumb one-breadcrumb-medium one-breadcrumb-strong"
>
  <span>
    <a
      class="one-breadcrumb-link"
      href="http://www.baidu.com"
    >
      首页
    </a>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
  <span>
    <a
      class="one-breadcrumb-link"
      href="http://www.baidu.com"
    >
      这是一个计划
    </a>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
  <span>
    <a
      class="one-breadcrumb-link-disabled"
    >
      这是一个x单元
    </a>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
  <span>
    <span
      class="one-breadcrumb-link"
    >
      这是一个这是一个关键词
    </span>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
</div>
`;

exports[`Breadcrumb should be rendered correctly 7 1`] = `
<div
  class="one-breadcrumb one-breadcrumb-medium one-breadcrumb-strong"
>
  <span>
    <a
      _target="blank"
      class="one-breadcrumb-link"
      href="http://www.baidu.com"
    >
      这是一个计划
    </a>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
  <span>
    <a
      class="one-breadcrumb-link-disabled"
    >
      这是一个x单元
    </a>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
  <span>
    <span
      class="one-breadcrumb-link"
    >
      这是一个这是一个关键词
    </span>
    <span
      class="one-breadcrumb-separator"
    >
      <svg
        class="dls-icon "
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
    </span>
  </span>
</div>
`;
