// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Badge Component badge should hide when visible is false 1`] = `
<span
  class="one-badge one-badge-not-a-wrapper"
>
  <span
    class="one-badge-count one-badge-hidden"
  />
</span>
`;

exports[`Badge Component badge should support custom badge content 1`] = `
<span
  class="one-badge one-badge-not-a-wrapper"
>
  <span
    class="one-badge-count"
  >
    hot
  </span>
</span>
`;

exports[`Badge Component badge should support float number 1`] = `
<span
  class="one-badge one-badge-not-a-wrapper"
  count="3.5"
>
  <span
    class="one-badge-count"
  >
    3.5
  </span>
</span>
`;

exports[`Badge Component badge should support int number 1`] = `
<span
  class="one-badge one-badge-not-a-wrapper"
  count="5"
>
  <span
    class="one-badge-count"
  >
    5
  </span>
</span>
`;

exports[`Badge Component should render when count is changed 1`] = `
<Badge
  count={10}
>
  <Badge
    count={10}
    isDot={false}
    overflowCount={99}
    prefixCls="one-badge"
    showZero={false}
    textContent=""
    visible={true}
  >
    <span
      className="one-badge one-badge-not-a-wrapper"
      count={10}
    >
      <span
        className="one-badge-count"
      >
        10
      </span>
    </span>
  </Badge>
</Badge>
`;

exports[`Badge Component should render when count is changed 2`] = `
<Badge
  count={11}
>
  <Badge
    count={11}
    isDot={false}
    overflowCount={99}
    prefixCls="one-badge"
    showZero={false}
    textContent=""
    visible={true}
  >
    <span
      className="one-badge one-badge-not-a-wrapper"
      count={11}
    >
      <span
        className="one-badge-count"
      >
        11
      </span>
    </span>
  </Badge>
</Badge>
`;

exports[`Badge Component should render when count is changed 3`] = `
<Badge
  count={11}
>
  <Badge
    count={11}
    isDot={false}
    overflowCount={99}
    prefixCls="one-badge"
    showZero={false}
    textContent=""
    visible={true}
  >
    <span
      className="one-badge one-badge-not-a-wrapper"
      count={11}
    >
      <span
        className="one-badge-count"
      >
        11
      </span>
    </span>
  </Badge>
</Badge>
`;

exports[`Badge Component should render when count is changed 4`] = `
<Badge
  count={10}
>
  <Badge
    count={10}
    isDot={false}
    overflowCount={99}
    prefixCls="one-badge"
    showZero={false}
    textContent=""
    visible={true}
  >
    <span
      className="one-badge one-badge-not-a-wrapper"
      count={10}
    >
      <span
        className="one-badge-count"
      >
        10
      </span>
    </span>
  </Badge>
</Badge>
`;

exports[`Badge Component should render when count is changed 5`] = `
<Badge
  count={9}
>
  <Badge
    count={9}
    isDot={false}
    overflowCount={99}
    prefixCls="one-badge"
    showZero={false}
    textContent=""
    visible={true}
  >
    <span
      className="one-badge one-badge-not-a-wrapper"
      count={9}
    >
      <span
        className="one-badge-count"
      >
        9
      </span>
    </span>
  </Badge>
</Badge>
`;
