// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Cascader Pane Component can use in input mode 1`] = `
<div
  class="one-cascader-pane one-cascader-pane-medium one-cascader-pane-show-search one-cascader-pane-multiple"
>
  <div>
    <div
      class="one-input-all-container one-input-all-container-medium one-input-all-container-inline"
    >
      <div
        class="one-input-detail"
        style="width: 360px;"
      >
        <div
          class="one-input-content"
        >
          <input
            class="one-input one-main one-input-medium"
            data-type="input"
            type="inline"
            value="湖南"
            width="360"
          />
        </div>
      </div>
    </div>
  </div>
  <div
    class="one-cascader-pane-menus one-cascader-pane-menus-search-box"
    style="min-width: 360px;"
  >
    <div
      class="one-cascader-pane-menus-search-box-container"
    >
      <ul>
        <li
          class="one-cascader-pane-menu-item one-cascader-pane-menu-item-expand one-cascader-pane-menu-item-disabled"
          title="湖南省"
        >
          <label
            class="one-checkbox-wrapper one-checkbox-wrapper-disabled one-checkbox-wrapper-row one-checkbox-wrapper-medium one-cascader-pane-menu-item-checkbox"
          >
            <span
              class="one-checkbox one-checkbox-disabled"
            >
              <input
                class="one-checkbox-input"
                disabled=""
                type="checkbox"
              />
              <span
                class="one-checkbox-inner"
              />
            </span>
          </label>
          <span
            class="one-cascader-pane-menu-item-label"
          >
            <span>
              <span>
                <span />
                <span
                  class="one-cascader-pane-menu-item-highlight"
                >
                  湖南
                </span>
                <span>
                  省
                </span>
              </span>
            </span>
          </span>
          <span
            class="one-cascader-pane-menu-item-expand-icon"
          />
        </li>
        <li
          class="one-cascader-pane-menu-item one-cascader-pane-menu-item-expand one-cascader-pane-menu-item-disabled"
          title="长沙市"
        >
          <label
            class="one-checkbox-wrapper one-checkbox-wrapper-disabled one-checkbox-wrapper-row one-checkbox-wrapper-medium one-cascader-pane-menu-item-checkbox"
          >
            <span
              class="one-checkbox one-checkbox-disabled"
            >
              <input
                class="one-checkbox-input"
                disabled=""
                type="checkbox"
              />
              <span
                class="one-checkbox-inner"
              />
            </span>
          </label>
          <span
            class="one-cascader-pane-menu-item-label"
          >
            <span>
              <span>
                <span />
                <span
                  class="one-cascader-pane-menu-item-highlight"
                >
                  湖南
                </span>
                <span>
                  省
                </span>
              </span>
              <svg
                class="dls-icon one-cascader-pane-menu-item-separator"
                fill="none"
                focusable="false"
                height="24"
                viewBox="0 0 10 24"
                width="10"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  clip-rule="evenodd"
                  d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                  fill="currentColor"
                  fill-rule="evenodd"
                />
              </svg>
            </span>
            <span>
              长沙市
            </span>
          </span>
          <span
            class="one-cascader-pane-menu-item-expand-icon"
          />
        </li>
        <li
          class="one-cascader-pane-menu-item one-cascader-pane-menu-item-disabled"
          title="岳麓山"
        >
          <label
            class="one-checkbox-wrapper one-checkbox-wrapper-disabled one-checkbox-wrapper-row one-checkbox-wrapper-medium one-cascader-pane-menu-item-checkbox"
          >
            <span
              class="one-checkbox one-checkbox-disabled"
            >
              <input
                class="one-checkbox-input"
                disabled=""
                type="checkbox"
              />
              <span
                class="one-checkbox-inner"
              />
            </span>
          </label>
          <span
            class="one-cascader-pane-menu-item-label"
          >
            <span>
              <span>
                <span />
                <span
                  class="one-cascader-pane-menu-item-highlight"
                >
                  湖南
                </span>
                <span>
                  省
                </span>
              </span>
              <svg
                class="dls-icon one-cascader-pane-menu-item-separator"
                fill="none"
                focusable="false"
                height="24"
                viewBox="0 0 10 24"
                width="10"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  clip-rule="evenodd"
                  d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                  fill="currentColor"
                  fill-rule="evenodd"
                />
              </svg>
            </span>
            <span>
              长沙市
              <svg
                class="dls-icon one-cascader-pane-menu-item-separator"
                fill="none"
                focusable="false"
                height="24"
                viewBox="0 0 10 24"
                width="10"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  clip-rule="evenodd"
                  d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                  fill="currentColor"
                  fill-rule="evenodd"
                />
              </svg>
            </span>
            <span>
              岳麓山
            </span>
          </span>
        </li>
        <li
          class="one-cascader-pane-menu-item one-cascader-pane-menu-item-disabled"
          title="橘子洲"
        >
          <label
            class="one-checkbox-wrapper one-checkbox-wrapper-disabled one-checkbox-wrapper-row one-checkbox-wrapper-medium one-cascader-pane-menu-item-checkbox"
          >
            <span
              class="one-checkbox one-checkbox-disabled"
            >
              <input
                class="one-checkbox-input"
                disabled=""
                type="checkbox"
              />
              <span
                class="one-checkbox-inner"
              />
            </span>
          </label>
          <span
            class="one-cascader-pane-menu-item-label"
          >
            <span>
              <span>
                <span />
                <span
                  class="one-cascader-pane-menu-item-highlight"
                >
                  湖南
                </span>
                <span>
                  省
                </span>
              </span>
              <svg
                class="dls-icon one-cascader-pane-menu-item-separator"
                fill="none"
                focusable="false"
                height="24"
                viewBox="0 0 10 24"
                width="10"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  clip-rule="evenodd"
                  d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                  fill="currentColor"
                  fill-rule="evenodd"
                />
              </svg>
            </span>
            <span>
              长沙市
              <svg
                class="dls-icon one-cascader-pane-menu-item-separator"
                fill="none"
                focusable="false"
                height="24"
                viewBox="0 0 10 24"
                width="10"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  clip-rule="evenodd"
                  d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                  fill="currentColor"
                  fill-rule="evenodd"
                />
              </svg>
            </span>
            <span>
              橘子洲
            </span>
          </span>
        </li>
        <li
          class="one-cascader-pane-menu-item one-cascader-pane-menu-item-disabled"
          title="湖南卫视"
        >
          <label
            class="one-checkbox-wrapper one-checkbox-wrapper-disabled one-checkbox-wrapper-row one-checkbox-wrapper-medium one-cascader-pane-menu-item-checkbox"
          >
            <span
              class="one-checkbox one-checkbox-disabled"
            >
              <input
                class="one-checkbox-input"
                disabled=""
                type="checkbox"
              />
              <span
                class="one-checkbox-inner"
              />
            </span>
          </label>
          <span
            class="one-cascader-pane-menu-item-label"
          >
            <span>
              <span>
                <span />
                <span
                  class="one-cascader-pane-menu-item-highlight"
                >
                  湖南
                </span>
                <span>
                  省
                </span>
              </span>
              <svg
                class="dls-icon one-cascader-pane-menu-item-separator"
                fill="none"
                focusable="false"
                height="24"
                viewBox="0 0 10 24"
                width="10"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  clip-rule="evenodd"
                  d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                  fill="currentColor"
                  fill-rule="evenodd"
                />
              </svg>
            </span>
            <span>
              长沙市
              <svg
                class="dls-icon one-cascader-pane-menu-item-separator"
                fill="none"
                focusable="false"
                height="24"
                viewBox="0 0 10 24"
                width="10"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  clip-rule="evenodd"
                  d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                  fill="currentColor"
                  fill-rule="evenodd"
                />
              </svg>
            </span>
            <span>
              <span>
                <span />
                <span
                  class="one-cascader-pane-menu-item-highlight"
                >
                  湖南
                </span>
                <span>
                  卫视
                </span>
              </span>
            </span>
          </span>
        </li>
      </ul>
    </div>
  </div>
</div>
`;

exports[`Cascader Pane Component can use in input with uncontrolled value 1`] = `
<div>
  <div
    class="one-input-all-container one-input-all-container-medium one-input-all-container-inline"
  >
    <div
      class="one-input-detail"
      style="width: 360px;"
    >
      <div
        class="one-input-content"
      >
        <input
          class="one-input one-main one-input-medium"
          data-type="input"
          placeholder="请输入..."
          type="inline"
          value="北京"
          width="360"
        />
      </div>
    </div>
  </div>
</div>
`;

exports[`Cascader Pane Component render ok in checkbox mode 1`] = `
<div
  class="one-cascader-pane one-cascader-pane-medium one-cascader-pane-multiple"
>
  <div
    class="one-cascader-pane-menus"
  >
    <div
      class="one-cascader-pane-menus-container"
    >
      <ul
        class="one-cascader-pane-menu"
      >
        <li
          class="one-cascader-pane-menu-item one-cascader-pane-menu-item-expand"
          title="北京"
        >
          <label
            class="one-checkbox-wrapper one-checkbox-wrapper-row one-checkbox-wrapper-medium one-cascader-pane-menu-item-checkbox"
          >
            <span
              class="one-checkbox"
            >
              <input
                class="one-checkbox-input"
                type="checkbox"
              />
              <span
                class="one-checkbox-inner"
              />
            </span>
          </label>
          <span
            class="one-cascader-pane-menu-item-label"
          >
            北京
          </span>
          <span
            class="one-cascader-pane-menu-item-expand-icon"
          >
            <svg
              class="dls-icon "
              fill="none"
              focusable="false"
              height="24"
              viewBox="0 0 10 24"
              width="10"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                fill="currentColor"
                fill-rule="evenodd"
              />
            </svg>
          </span>
        </li>
        <li
          class="one-cascader-pane-menu-item one-cascader-pane-menu-item-expand one-cascader-pane-menu-item-disabled"
          title="湖南省"
        >
          <label
            class="one-checkbox-wrapper one-checkbox-wrapper-disabled one-checkbox-wrapper-row one-checkbox-wrapper-medium one-cascader-pane-menu-item-checkbox"
          >
            <span
              class="one-checkbox one-checkbox-disabled"
            >
              <input
                class="one-checkbox-input"
                disabled=""
                type="checkbox"
              />
              <span
                class="one-checkbox-inner"
              />
            </span>
          </label>
          <span
            class="one-cascader-pane-menu-item-label"
          >
            湖南省
          </span>
          <span
            class="one-cascader-pane-menu-item-expand-icon"
          >
            <svg
              class="dls-icon "
              fill="none"
              focusable="false"
              height="24"
              viewBox="0 0 10 24"
              width="10"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                fill="currentColor"
                fill-rule="evenodd"
              />
            </svg>
          </span>
        </li>
        <li
          class="one-cascader-pane-menu-item one-cascader-pane-menu-item-loading"
          title="北京2"
        >
          <label
            class="one-checkbox-wrapper one-checkbox-wrapper-row one-checkbox-wrapper-medium one-cascader-pane-menu-item-checkbox"
          >
            <span
              class="one-checkbox"
            >
              <input
                class="one-checkbox-input"
                type="checkbox"
              />
              <span
                class="one-checkbox-inner"
              />
            </span>
          </label>
          <span
            class="one-cascader-pane-menu-item-label"
          >
            北京2
          </span>
        </li>
      </ul>
    </div>
  </div>
</div>
`;

exports[`Cascader Pane Component render ok in first column group mode 1`] = `
<div
  class="one-cascader-pane one-cascader-pane-medium one-cascader-pane-show-search one-cascader-pane-multiple"
>
  <div>
    <div
      class="one-input-all-container one-input-all-container-medium one-input-all-container-inline"
    >
      <div
        class="one-input-detail"
        style="width: 400px;"
      >
        <div
          class="one-input-content"
        >
          <input
            class="one-input one-main one-input-medium"
            data-type="input"
            placeholder="请输入..."
            type="inline"
            value=""
            width="400"
          />
        </div>
      </div>
    </div>
  </div>
  <div
    class="one-cascader-pane-menus"
    style="width: 400px;"
  >
    <div
      class="one-cascader-pane-menus-container"
    >
      <ul
        class="one-cascader-pane-menu"
      >
        <ul
          class="one-cascader-pane-menu-item-group"
        >
          <div
            class="one-cascader-pane-menu-item-group-title"
          >
            华北
          </div>
          <li
            class="one-cascader-pane-menu-item one-cascader-pane-menu-item-expand one-cascader-pane-menu-item-active one-cascader-pane-menu-item-selected"
            style=""
            title="北京"
          >
            <label
              class="one-checkbox-wrapper one-checkbox-wrapper-row one-checkbox-wrapper-medium one-cascader-pane-menu-item-checkbox"
            >
              <span
                class="one-checkbox"
              >
                <input
                  class="one-checkbox-input"
                  type="checkbox"
                />
                <span
                  class="one-checkbox-inner"
                />
              </span>
            </label>
            <span
              class="one-cascader-pane-menu-item-label"
            >
              北京
            </span>
            <span
              class="one-cascader-pane-menu-item-expand-icon"
            >
              <svg
                class="dls-icon "
                fill="none"
                focusable="false"
                height="24"
                viewBox="0 0 10 24"
                width="10"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  clip-rule="evenodd"
                  d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                  fill="currentColor"
                  fill-rule="evenodd"
                />
              </svg>
            </span>
          </li>
        </ul>
        <ul
          class="one-cascader-pane-menu-item-group"
        >
          <div
            class="one-cascader-pane-menu-item-group-title"
          >
            华南
          </div>
          <li
            class="one-cascader-pane-menu-item one-cascader-pane-menu-item-expand one-cascader-pane-menu-item-disabled"
            title="湖南省"
          >
            <label
              class="one-checkbox-wrapper one-checkbox-wrapper-disabled one-checkbox-wrapper-row one-checkbox-wrapper-medium one-cascader-pane-menu-item-checkbox"
            >
              <span
                class="one-checkbox one-checkbox-disabled"
              >
                <input
                  class="one-checkbox-input"
                  disabled=""
                  type="checkbox"
                />
                <span
                  class="one-checkbox-inner"
                />
              </span>
            </label>
            <span
              class="one-cascader-pane-menu-item-label"
            >
              湖南省
            </span>
            <span
              class="one-cascader-pane-menu-item-expand-icon"
            >
              <svg
                class="dls-icon "
                fill="none"
                focusable="false"
                height="24"
                viewBox="0 0 10 24"
                width="10"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  clip-rule="evenodd"
                  d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                  fill="currentColor"
                  fill-rule="evenodd"
                />
              </svg>
            </span>
          </li>
        </ul>
      </ul>
      <ul
        class="one-cascader-pane-menu"
      >
        <li
          class="one-cascader-pane-menu-item one-cascader-pane-menu-item-expand"
          title="海淀区"
        >
          <label
            class="one-checkbox-wrapper one-checkbox-wrapper-row one-checkbox-wrapper-medium one-cascader-pane-menu-item-checkbox"
          >
            <span
              class="one-checkbox"
            >
              <input
                class="one-checkbox-input"
                type="checkbox"
              />
              <span
                class="one-checkbox-inner"
              />
            </span>
          </label>
          <span
            class="one-cascader-pane-menu-item-label"
          >
            海淀区
          </span>
          <span
            class="one-cascader-pane-menu-item-expand-icon"
          >
            <svg
              class="dls-icon "
              fill="none"
              focusable="false"
              height="24"
              viewBox="0 0 10 24"
              width="10"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                fill="currentColor"
                fill-rule="evenodd"
              />
            </svg>
          </span>
        </li>
      </ul>
    </div>
  </div>
</div>
`;

exports[`Cascader Pane Component support controlled mode 1`] = `
<div
  class="one-cascader-pane one-cascader-pane-medium"
>
  <div
    class="one-cascader-pane-menus"
  >
    <div
      class="one-cascader-pane-menus-container"
    >
      <ul
        class="one-cascader-pane-menu"
      >
        <li
          class="one-cascader-pane-menu-item one-cascader-pane-menu-item-expand"
          title="北京"
        >
          <span
            class="one-cascader-pane-menu-item-label"
          >
            北京
          </span>
          <span
            class="one-cascader-pane-menu-item-expand-icon"
          >
            <svg
              class="dls-icon "
              fill="none"
              focusable="false"
              height="24"
              viewBox="0 0 10 24"
              width="10"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                fill="currentColor"
                fill-rule="evenodd"
              />
            </svg>
          </span>
        </li>
        <li
          class="one-cascader-pane-menu-item one-cascader-pane-menu-item-expand one-cascader-pane-menu-item-disabled"
          title="湖南省"
        >
          <span
            class="one-cascader-pane-menu-item-label"
          >
            湖南省
          </span>
          <span
            class="one-cascader-pane-menu-item-expand-icon"
          >
            <svg
              class="dls-icon "
              fill="none"
              focusable="false"
              height="24"
              viewBox="0 0 10 24"
              width="10"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                clip-rule="evenodd"
                d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                fill="currentColor"
                fill-rule="evenodd"
              />
            </svg>
          </span>
        </li>
        <li
          class="one-cascader-pane-menu-item one-cascader-pane-menu-item-loading"
          title="北京2"
        >
          <span
            class="one-cascader-pane-menu-item-label"
          >
            北京2
          </span>
        </li>
      </ul>
    </div>
  </div>
</div>
`;
