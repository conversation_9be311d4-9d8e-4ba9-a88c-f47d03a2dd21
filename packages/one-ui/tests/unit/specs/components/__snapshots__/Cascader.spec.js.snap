// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Cascader Component can be selected 1`] = `
<div>
  <div
    class="one-cascader-menus one-cascader-menus-medium"
    style="opacity:0"
  >
    <div
      class="one-cascader-pane one-cascader-pane-medium"
    >
      <div
        class="one-cascader-pane-menus"
      >
        <div
          class="one-cascader-pane-menus-container"
        >
          <ul
            class="one-cascader-pane-menu"
          >
            <li
              class="one-cascader-pane-menu-item one-cascader-pane-menu-item-expand"
              title="Zhejiang"
            >
              <span
                class="one-cascader-pane-menu-item-label"
              >
                Zhejiang
              </span>
              <span
                class="one-cascader-pane-menu-item-expand-icon"
              >
                <svg
                  class="dls-icon "
                  fill="none"
                  focusable="false"
                  height="24"
                  viewBox="0 0 10 24"
                  width="10"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                    fill="currentColor"
                    fill-rule="evenodd"
                  />
                </svg>
              </span>
            </li>
            <li
              class="one-cascader-pane-menu-item one-cascader-pane-menu-item-expand"
              title="Jiangsu"
            >
              <span
                class="one-cascader-pane-menu-item-label"
              >
                Jiangsu
              </span>
              <span
                class="one-cascader-pane-menu-item-expand-icon"
              >
                <svg
                  class="dls-icon "
                  fill="none"
                  focusable="false"
                  height="24"
                  viewBox="0 0 10 24"
                  width="10"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                    fill="currentColor"
                    fill-rule="evenodd"
                  />
                </svg>
              </span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Cascader Component can be selected 2`] = `
<div>
  <div
    class="one-cascader-menus one-cascader-menus-medium"
    style="opacity:0"
  >
    <div
      class="one-cascader-pane one-cascader-pane-medium"
    >
      <div
        class="one-cascader-pane-menus"
      >
        <div
          class="one-cascader-pane-menus-container"
        >
          <ul
            class="one-cascader-pane-menu"
          >
            <li
              class="one-cascader-pane-menu-item one-cascader-pane-menu-item-expand"
              title="Zhejiang"
            >
              <span
                class="one-cascader-pane-menu-item-label"
              >
                Zhejiang
              </span>
              <span
                class="one-cascader-pane-menu-item-expand-icon"
              >
                <svg
                  class="dls-icon "
                  fill="none"
                  focusable="false"
                  height="24"
                  viewBox="0 0 10 24"
                  width="10"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                    fill="currentColor"
                    fill-rule="evenodd"
                  />
                </svg>
              </span>
            </li>
            <li
              class="one-cascader-pane-menu-item one-cascader-pane-menu-item-expand"
              title="Jiangsu"
            >
              <span
                class="one-cascader-pane-menu-item-label"
              >
                Jiangsu
              </span>
              <span
                class="one-cascader-pane-menu-item-expand-icon"
              >
                <svg
                  class="dls-icon "
                  fill="none"
                  focusable="false"
                  height="24"
                  viewBox="0 0 10 24"
                  width="10"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                    fill="currentColor"
                    fill-rule="evenodd"
                  />
                </svg>
              </span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Cascader Component can be selected 3`] = `<div />`;

exports[`Cascader Component popup correctly when panel is hidden 1`] = `<div />`;

exports[`Cascader Component popup correctly when panel is open 1`] = `
<div>
  <div
    class="one-cascader-menus one-cascader-menus-medium"
    style="opacity:0"
  >
    <div
      class="one-cascader-pane one-cascader-pane-medium"
    >
      <div
        class="one-cascader-pane-menus"
      >
        <div
          class="one-cascader-pane-menus-container"
        >
          <ul
            class="one-cascader-pane-menu"
          >
            <li
              class="one-cascader-pane-menu-item one-cascader-pane-menu-item-expand"
              title="Zhejiang"
            >
              <span
                class="one-cascader-pane-menu-item-label"
              >
                Zhejiang
              </span>
              <span
                class="one-cascader-pane-menu-item-expand-icon"
              >
                <svg
                  class="dls-icon "
                  fill="none"
                  focusable="false"
                  height="24"
                  viewBox="0 0 10 24"
                  width="10"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                    fill="currentColor"
                    fill-rule="evenodd"
                  />
                </svg>
              </span>
            </li>
            <li
              class="one-cascader-pane-menu-item one-cascader-pane-menu-item-expand"
              title="Jiangsu"
            >
              <span
                class="one-cascader-pane-menu-item-label"
              >
                Jiangsu
              </span>
              <span
                class="one-cascader-pane-menu-item-expand-icon"
              >
                <svg
                  class="dls-icon "
                  fill="none"
                  focusable="false"
                  height="24"
                  viewBox="0 0 10 24"
                  width="10"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                    fill="currentColor"
                    fill-rule="evenodd"
                  />
                </svg>
              </span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Cascader Component popup correctly with defaultValue 1`] = `
<div>
  <div
    class="one-cascader-menus one-cascader-menus-medium"
    style="opacity:0"
  >
    <div
      class="one-cascader-pane one-cascader-pane-medium"
    >
      <div
        class="one-cascader-pane-menus"
      >
        <div
          class="one-cascader-pane-menus-container"
        >
          <ul
            class="one-cascader-pane-menu"
          >
            <li
              class="one-cascader-pane-menu-item one-cascader-pane-menu-item-expand one-cascader-pane-menu-item-active"
              title="Zhejiang"
            >
              <span
                class="one-cascader-pane-menu-item-label"
              >
                Zhejiang
              </span>
              <span
                class="one-cascader-pane-menu-item-expand-icon"
              >
                <svg
                  class="dls-icon "
                  fill="none"
                  focusable="false"
                  height="24"
                  viewBox="0 0 10 24"
                  width="10"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                    fill="currentColor"
                    fill-rule="evenodd"
                  />
                </svg>
              </span>
            </li>
            <li
              class="one-cascader-pane-menu-item one-cascader-pane-menu-item-expand"
              title="Jiangsu"
            >
              <span
                class="one-cascader-pane-menu-item-label"
              >
                Jiangsu
              </span>
              <span
                class="one-cascader-pane-menu-item-expand-icon"
              >
                <svg
                  class="dls-icon "
                  fill="none"
                  focusable="false"
                  height="24"
                  viewBox="0 0 10 24"
                  width="10"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                    fill="currentColor"
                    fill-rule="evenodd"
                  />
                </svg>
              </span>
            </li>
          </ul>
          <ul
            class="one-cascader-pane-menu"
          >
            <li
              class="one-cascader-pane-menu-item one-cascader-pane-menu-item-expand one-cascader-pane-menu-item-active one-cascader-pane-menu-item-selected"
              title="Hangzhou"
            >
              <span
                class="one-cascader-pane-menu-item-label"
              >
                Hangzhou
              </span>
              <span
                class="one-cascader-pane-menu-item-expand-icon"
              >
                <svg
                  class="dls-icon "
                  fill="none"
                  focusable="false"
                  height="24"
                  viewBox="0 0 10 24"
                  width="10"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    clip-rule="evenodd"
                    d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                    fill="currentColor"
                    fill-rule="evenodd"
                  />
                </svg>
              </span>
            </li>
          </ul>
          <ul
            class="one-cascader-pane-menu"
          >
            <li
              class="one-cascader-pane-menu-item"
              title="West Lake"
            >
              <span
                class="one-cascader-pane-menu-item-label"
              >
                West Lake
              </span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Cascader Component should highlight keyword and filter when search in Cascader 1`] = `
<div>
  <div
    class="one-cascader-menus one-cascader-menus-medium"
    style="opacity: 0;"
  >
    <div
      class="one-cascader-pane one-cascader-pane-medium"
    >
      <div
        class="one-cascader-pane-menus one-cascader-pane-menus-search-box"
        style="min-width: 360px;"
      >
        <div
          class="one-cascader-pane-menus-search-box-container"
        >
          <ul>
            <li
              class="one-cascader-pane-menu-item"
              title="Zhong Hua Men"
            >
              <span
                class="one-cascader-pane-menu-item-label"
              >
                <span>
                  Jiangsu
                  <svg
                    class="dls-icon one-cascader-pane-menu-item-separator"
                    fill="none"
                    focusable="false"
                    height="24"
                    viewBox="0 0 10 24"
                    width="10"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                      fill="currentColor"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
                <span>
                  Nanjing
                  <svg
                    class="dls-icon one-cascader-pane-menu-item-separator"
                    fill="none"
                    focusable="false"
                    height="24"
                    viewBox="0 0 10 24"
                    width="10"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                      fill="currentColor"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
                <span>
                  Zhong Hua Men
                </span>
              </span>
            </li>
            <li
              class="one-cascader-pane-menu-item"
              title="West Lake"
            >
              <span
                class="one-cascader-pane-menu-item-label"
              >
                <span>
                  Zhejiang
                  <svg
                    class="dls-icon one-cascader-pane-menu-item-separator"
                    fill="none"
                    focusable="false"
                    height="24"
                    viewBox="0 0 10 24"
                    width="10"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                      fill="currentColor"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
                <span>
                  <span>
                    <span>
                      Hang
                    </span>
                    <span
                      class="one-cascader-pane-menu-item-highlight"
                    >
                      z
                    </span>
                    <span>
                      hou
                    </span>
                  </span>
                  <svg
                    class="dls-icon one-cascader-pane-menu-item-separator"
                    fill="none"
                    focusable="false"
                    height="24"
                    viewBox="0 0 10 24"
                    width="10"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                      fill="currentColor"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
                <span>
                  West Lake
                </span>
              </span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Cascader Component should highlight keyword and filter when search in Cascader with same field name of label and value 1`] = `
<div>
  <div
    class="one-cascader-menus one-cascader-menus-medium"
    style="opacity: 0;"
  >
    <div
      class="one-cascader-pane one-cascader-pane-medium"
    >
      <div
        class="one-cascader-pane-menus one-cascader-pane-menus-search-box"
        style="min-width: 360px;"
      >
        <div
          class="one-cascader-pane-menus-search-box-container"
        >
          <ul>
            <li
              class="one-cascader-pane-menu-item"
              title="West Lake"
            >
              <span
                class="one-cascader-pane-menu-item-label"
              >
                <span>
                  Zhejiang
                  <svg
                    class="dls-icon one-cascader-pane-menu-item-separator"
                    fill="none"
                    focusable="false"
                    height="24"
                    viewBox="0 0 10 24"
                    width="10"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                      fill="currentColor"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
                <span>
                  <span>
                    <span>
                      Hang
                    </span>
                    <span
                      class="one-cascader-pane-menu-item-highlight"
                    >
                      z
                    </span>
                    <span>
                      hou
                    </span>
                  </span>
                  <svg
                    class="dls-icon one-cascader-pane-menu-item-separator"
                    fill="none"
                    focusable="false"
                    height="24"
                    viewBox="0 0 10 24"
                    width="10"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                      fill="currentColor"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
                <span>
                  West Lake
                </span>
              </span>
            </li>
            <li
              class="one-cascader-pane-menu-item one-cascader-pane-menu-item-disabled"
              title="Xia Sha"
            >
              <span
                class="one-cascader-pane-menu-item-label"
              >
                <span>
                  Zhejiang
                  <svg
                    class="dls-icon one-cascader-pane-menu-item-separator"
                    fill="none"
                    focusable="false"
                    height="24"
                    viewBox="0 0 10 24"
                    width="10"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                      fill="currentColor"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
                <span>
                  <span>
                    <span>
                      Hang
                    </span>
                    <span
                      class="one-cascader-pane-menu-item-highlight"
                    >
                      z
                    </span>
                    <span>
                      hou
                    </span>
                  </span>
                  <svg
                    class="dls-icon one-cascader-pane-menu-item-separator"
                    fill="none"
                    focusable="false"
                    height="24"
                    viewBox="0 0 10 24"
                    width="10"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      clip-rule="evenodd"
                      d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
                      fill="currentColor"
                      fill-rule="evenodd"
                    />
                  </svg>
                </span>
                <span>
                  Xia Sha
                </span>
              </span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Cascader Component should render not found content 1`] = `
<div>
  <div
    class="one-cascader-menus one-cascader-menus-medium"
    style="opacity: 0;"
  >
    <div
      class="one-cascader-pane one-cascader-pane-medium"
    >
      <div
        class="one-cascader-pane-menus one-cascader-pane-menus-search-box"
        style="min-width: 360px;"
      >
        <ul>
          <li
            class="one-cascader-pane-menu-item one-cascader-pane-menu-item-disabled"
          >
            <span
              class="one-cascader-pane-menu-item-label"
            >
              未找到合适的选项
            </span>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
`;

exports[`Cascader Component support controlled mode 1`] = `
<div
  class="one-main one-cascader-picker one-cascader-medium"
>
  <span
    class="one-cascader-picker-label"
  >
    <span
      class="one-cascader-picker-label-container"
    >
      <span>
        Zhejiang
        <svg
          class="dls-icon one-cascader-search-split-icon"
          fill="none"
          focusable="false"
          height="24"
          viewBox="0 0 10 24"
          width="10"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            clip-rule="evenodd"
            d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
            fill="currentColor"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span>
        Hangzhou
        <svg
          class="dls-icon one-cascader-search-split-icon"
          fill="none"
          focusable="false"
          height="24"
          viewBox="0 0 10 24"
          width="10"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            clip-rule="evenodd"
            d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
            fill="currentColor"
            fill-rule="evenodd"
          />
        </svg>
      </span>
      <span>
        West Lake
      </span>
    </span>
  </span>
  <span
    class="one-cascader-picker-icon"
  >
    <svg
      class="dls-icon one-cascader-picker-clear"
      fill="none"
      focusable="false"
      height="24"
      viewBox="0 0 24 24"
      width="24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="m8.5 7.086 3.5 3.5 3.5-3.5L16.914 8.5l-3.5 3.5 3.5 3.5-1.414 1.414-3.5-3.5-3.5 3.5L7.086 15.5l3.5-3.5-3.5-3.5L8.5 7.086z"
        fill="currentColor"
      />
      <path
        clip-rule="evenodd"
        d="M1 12C1 5.925 5.925 1 12 1s11 4.925 11 11-4.925 11-11 11S1 18.075 1 12zm11-9a9 9 0 1 0 0 18 9 9 0 0 0 0-18z"
        fill="currentColor"
        fill-rule="evenodd"
      />
    </svg>
    <svg
      class="dls-icon one-cascader-picker-arrow"
      fill="none"
      focusable="false"
      height="24"
      type="angle-down"
      viewBox="0 0 24 24"
      width="24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        clip-rule="evenodd"
        d="M13.709 16.406a2 2 0 0 1-2.918 0L4.02 9.184l1.458-1.368 6.771 7.222 6.77-7.222 1.46 1.368-6.771 7.222z"
        fill="currentColor"
        fill-rule="evenodd"
      />
    </svg>
  </span>
</div>
`;
