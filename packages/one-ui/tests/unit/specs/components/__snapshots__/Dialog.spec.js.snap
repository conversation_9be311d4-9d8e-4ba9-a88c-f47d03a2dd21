// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[` 1`] = `
<button
  class="one-button one-main one-button-normal one-button-medium"
  name=""
  style="order: 1;"
  type="button"
/>
`;

exports[` 2`] = `
<button
  class="one-button demo-action-button one-main one-button-normal one-button-medium"
  name=""
  style="order: 1;"
  type="button"
/>
`;

exports[` 3`] = `
<button
  class="one-button demo-action-button one-main one-button-normal one-button-medium"
  name=""
  style="order: 1;"
  type="button"
/>
`;

exports[` 4`] = `
<button
  class="one-button demo-action-button one-main one-button-normal one-button-medium"
  name=""
  style="order: 1;"
  type="button"
/>
`;

exports[`Dialog Component render correctly 1`] = `
<div>
  <div>
    <div>
      <div
        class="one-dialog-wrap one-dialog-left one-dialog-medium one-dialog-centered"
        role="dialog"
        style="display: block;"
        tabindex="-1"
      >
        <div
          class="one-dialog-mask"
        />
        <div
          class="one-dialog"
          role="document"
          style="width: 600px;"
        >
          <div
            aria-hidden="true"
            style="width: 0px; height: 0px; overflow: hidden;"
            tabindex="0"
          />
          <div
            class="one-dialog-content"
          >
            <div
              class="one-dialog-header"
            >
              <div
                class="one-dialog-title"
              />
              <button
                class="one-button one-dialog-close one-main one-button-text-aux one-button-medium"
                name=""
                type="button"
              >
                <span>
                  <svg
                    class="dls-icon "
                    fill="none"
                    focusable="false"
                    height="24"
                    viewBox="0 0 18 24"
                    width="18"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="m7.444 11.998-7 7 1.555 1.556 7-7 7 7L17.556 19l-7-7 7-7.001L16 3.442l-7 7-7.001-7L.443 4.998l7 7z"
                      fill="currentColor"
                    />
                  </svg>
                </span>
              </button>
            </div>
            <div
              class="one-dialog-body"
            >
              Here is content of Modal
            </div>
            <div
              class="one-dialog-footer"
            >
              <button
                class="one-button one-main one-button-primary one-button-medium"
                name=""
                type="button"
              >
                <span>
                  确定
                </span>
              </button>
              <button
                class="one-button one-main one-button-normal one-button-medium"
                name=""
                type="button"
              >
                <span>
                  取消
                </span>
              </button>
            </div>
          </div>
          <div
            aria-hidden="true"
            style="width: 0px; height: 0px; overflow: hidden;"
            tabindex="0"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Dialog Component render with custom width 1`] = `
<div>
  <div>
    <div>
      <div
        class="one-dialog-wrap one-dialog-left one-dialog-medium one-dialog-centered"
        role="dialog"
        style="display: block;"
        tabindex="-1"
      >
        <div
          class="one-dialog-mask"
        />
        <div
          class="one-dialog"
          role="document"
          style="width: 1000px;"
        >
          <div
            aria-hidden="true"
            style="width: 0px; height: 0px; overflow: hidden;"
            tabindex="0"
          />
          <div
            class="one-dialog-content"
          >
            <div
              class="one-dialog-header"
            >
              <div
                class="one-dialog-title"
              />
              <button
                class="one-button one-dialog-close one-main one-button-text-aux one-button-medium"
                name=""
                type="button"
              >
                <span>
                  <svg
                    class="dls-icon "
                    fill="none"
                    focusable="false"
                    height="24"
                    viewBox="0 0 18 24"
                    width="18"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="m7.444 11.998-7 7 1.555 1.556 7-7 7 7L17.556 19l-7-7 7-7.001L16 3.442l-7 7-7.001-7L.443 4.998l7 7z"
                      fill="currentColor"
                    />
                  </svg>
                </span>
              </button>
            </div>
            <div
              class="one-dialog-body"
            >
              Here is content of Modal
            </div>
            <div
              class="one-dialog-footer"
            >
              <button
                class="one-button one-main one-button-primary one-button-medium"
                name=""
                type="button"
              >
                <span>
                  确定
                </span>
              </button>
              <button
                class="one-button one-main one-button-normal one-button-medium"
                name=""
                type="button"
              >
                <span>
                  取消
                </span>
              </button>
            </div>
          </div>
          <div
            aria-hidden="true"
            style="width: 0px; height: 0px; overflow: hidden;"
            tabindex="0"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Dialog Component render with custom width with size 1`] = `
<div>
  <div>
    <div>
      <div
        class="one-dialog-wrap one-dialog-left one-dialog-medium one-dialog-centered"
        role="dialog"
        style="display: block;"
        tabindex="-1"
      >
        <div
          class="one-dialog-mask"
        />
        <div
          class="one-dialog"
          role="document"
          style="width: 400px;"
        >
          <div
            aria-hidden="true"
            style="width: 0px; height: 0px; overflow: hidden;"
            tabindex="0"
          />
          <div
            class="one-dialog-content"
          >
            <div
              class="one-dialog-header"
            >
              <div
                class="one-dialog-title"
              />
              <button
                class="one-button one-dialog-close one-main one-button-text-aux one-button-medium"
                name=""
                type="button"
              >
                <span>
                  <svg
                    class="dls-icon "
                    fill="none"
                    focusable="false"
                    height="24"
                    viewBox="0 0 18 24"
                    width="18"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="m7.444 11.998-7 7 1.555 1.556 7-7 7 7L17.556 19l-7-7 7-7.001L16 3.442l-7 7-7.001-7L.443 4.998l7 7z"
                      fill="currentColor"
                    />
                  </svg>
                </span>
              </button>
            </div>
            <div
              class="one-dialog-body"
            >
              Here is content of Modal
            </div>
            <div
              class="one-dialog-footer"
            >
              <button
                class="one-button one-main one-button-primary one-button-medium"
                name=""
                type="button"
              >
                <span>
                  确定
                </span>
              </button>
              <button
                class="one-button one-main one-button-normal one-button-medium"
                name=""
                type="button"
              >
                <span>
                  取消
                </span>
              </button>
            </div>
          </div>
          <div
            aria-hidden="true"
            style="width: 0px; height: 0px; overflow: hidden;"
            tabindex="0"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Dialog Component render without footer 1`] = `
<div>
  <div>
    <div>
      <div
        class="one-dialog-wrap one-dialog-left one-dialog-medium one-dialog-centered"
        role="dialog"
        style="display: block;"
        tabindex="-1"
      >
        <div
          class="one-dialog-mask"
        />
        <div
          class="one-dialog"
          role="document"
          style="width: 600px;"
        >
          <div
            aria-hidden="true"
            style="width: 0px; height: 0px; overflow: hidden;"
            tabindex="0"
          />
          <div
            class="one-dialog-content"
          >
            <div
              class="one-dialog-header"
            >
              <div
                class="one-dialog-title"
              />
              <button
                class="one-button one-dialog-close one-main one-button-text-aux one-button-medium"
                name=""
                type="button"
              >
                <span>
                  <svg
                    class="dls-icon "
                    fill="none"
                    focusable="false"
                    height="24"
                    viewBox="0 0 18 24"
                    width="18"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="m7.444 11.998-7 7 1.555 1.556 7-7 7 7L17.556 19l-7-7 7-7.001L16 3.442l-7 7-7.001-7L.443 4.998l7 7z"
                      fill="currentColor"
                    />
                  </svg>
                </span>
              </button>
            </div>
            <div
              class="one-dialog-body"
            >
              Here is content of Modal
            </div>
          </div>
          <div
            aria-hidden="true"
            style="width: 0px; height: 0px; overflow: hidden;"
            tabindex="0"
          />
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Dialog Component support closeIcon 1`] = `
<div
  class="one-dialog-wrap one-dialog-left one-dialog-medium one-dialog-centered"
  role="dialog"
  style="display: block;"
  tabindex="-1"
>
  <div
    class="one-dialog-mask"
  />
  <div
    class="one-dialog"
    role="document"
    style="width: 600px;"
  >
    <div
      aria-hidden="true"
      style="width: 0px; height: 0px; overflow: hidden;"
      tabindex="0"
    />
    <div
      class="one-dialog-content"
    >
      <div
        class="one-dialog-header"
      >
        <div
          class="one-dialog-title"
        />
        <button
          class="one-button one-dialog-close one-main one-button-text-aux one-button-medium"
          name=""
          type="button"
        >
          <span>
            <svg
              class="dls-icon "
              fill="none"
              focusable="false"
              height="24"
              viewBox="0 0 18 24"
              width="18"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="m7.444 11.998-7 7 1.555 1.556 7-7 7 7L17.556 19l-7-7 7-7.001L16 3.442l-7 7-7.001-7L.443 4.998l7 7z"
                fill="currentColor"
              />
            </svg>
          </span>
        </button>
      </div>
      <div
        class="one-dialog-body"
      />
      <div
        class="one-dialog-footer"
      >
        <button
          class="one-button one-main one-button-primary one-button-medium"
          name=""
          type="button"
        >
          <span>
            确定
          </span>
        </button>
        <button
          class="one-button one-main one-button-normal one-button-medium"
          name=""
          type="button"
        >
          <span>
            取消
          </span>
        </button>
      </div>
    </div>
    <div
      aria-hidden="true"
      style="width: 0px; height: 0px; overflow: hidden;"
      tabindex="0"
    />
  </div>
</div>
`;
