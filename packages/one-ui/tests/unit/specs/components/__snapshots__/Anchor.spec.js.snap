// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Anchor Component document is undefined should be rendered correctly when document is undefined 1`] = `
<div>
  <div
    class=""
  >
    <div
      class="one-anchor-wrapper one-anchor-wrapper-normal"
      style="max-height:100vh"
    >
      <div
        class="one-anchor one-anchor-small"
      >
        <div
          class="one-anchor-first-level one-anchor-link one-anchor-link-not-active one-anchor-link-without-children"
        >
          <span
            class="one-anchor-link-title one-anchor-link-title-not-active"
            title="Basic demo"
          >
            <span>
              Basic demo
            </span>
          </span>
        </div>
        <div
          class="one-anchor-first-level one-anchor-link one-anchor-link-not-active one-anchor-link-without-children"
        >
          <span
            class="one-anchor-link-title one-anchor-link-title-not-active"
            title="Code demo"
          >
            <span>
              Code demo
            </span>
          </span>
        </div>
        <div
          class="one-anchor-first-level one-anchor-link one-anchor-link-not-active one-anchor-link-has-children"
        >
          <span
            class="one-anchor-link-title one-anchor-link-title-not-active"
            title="API"
          >
            <span>
              API
            </span>
          </span>
          <div
            class="one-anchor-link one-anchor-link-not-active one-anchor-link-without-children"
          >
            <span
              class="one-anchor-link-title one-anchor-link-title-not-active"
              title="Props"
            >
              <span>
                Props
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Anchor Component should be rendered correctly 1`] = `
<div>
  <div
    class=""
  >
    <div
      class="one-anchor-wrapper one-anchor-wrapper-normal"
      style="max-height:100vh"
    >
      <div
        class="one-anchor one-anchor-small"
      >
        <div
          class="one-anchor-first-level one-anchor-link one-anchor-link-not-active one-anchor-link-without-children"
        >
          <span
            class="one-anchor-link-title one-anchor-link-title-not-active"
            title="Basic demo"
          >
            <span>
              Basic demo
            </span>
          </span>
        </div>
        <div
          class="one-anchor-first-level one-anchor-link one-anchor-link-not-active one-anchor-link-without-children"
        >
          <span
            class="one-anchor-link-title one-anchor-link-title-not-active"
            title="Code demo"
          >
            <span>
              Code demo
            </span>
          </span>
        </div>
        <div
          class="one-anchor-first-level one-anchor-link one-anchor-link-not-active one-anchor-link-has-children"
        >
          <span
            class="one-anchor-link-title one-anchor-link-title-not-active"
            title="API"
          >
            <span>
              API
            </span>
          </span>
          <div
            class="one-anchor-link one-anchor-link-not-active one-anchor-link-without-children"
          >
            <span
              class="one-anchor-link-title one-anchor-link-title-not-active"
              title="Props"
            >
              <span>
                Props
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Anchor Component should be rendered correctly 2`] = `
<div>
  <div
    class=""
  >
    <div
      class="one-anchor-wrapper one-anchor-wrapper-normal"
      style="max-height:100vh"
    >
      <div
        class="one-anchor one-anchor-medium"
      >
        <div
          class="one-anchor-first-level one-anchor-link one-anchor-link-not-active one-anchor-link-without-children"
        >
          <span
            class="one-anchor-link-title one-anchor-link-title-not-active"
            title="Basic demo"
          >
            <span>
              Basic demo
            </span>
          </span>
        </div>
        <div
          class="one-anchor-first-level one-anchor-link one-anchor-link-not-active one-anchor-link-without-children"
        >
          <span
            class="one-anchor-link-title one-anchor-link-title-not-active"
            title="Code demo"
          >
            <span>
              Code demo
            </span>
          </span>
        </div>
        <div
          class="one-anchor-first-level one-anchor-link one-anchor-link-not-active one-anchor-link-has-children"
        >
          <span
            class="one-anchor-link-title one-anchor-link-title-not-active"
            title="API"
          >
            <span>
              API
            </span>
          </span>
          <div
            class="one-anchor-link one-anchor-link-not-active one-anchor-link-without-children"
          >
            <span
              class="one-anchor-link-title one-anchor-link-title-not-active"
              title="Props"
            >
              <span>
                Props
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Anchor Component should be rendered correctly 2 1`] = `
<div>
  <div
    class=""
  >
    <div
      class="one-anchor-wrapper one-anchor-wrapper-normal"
      style="max-height:calc(100vh - 10px)"
    >
      <div
        class="one-anchor one-anchor-small"
      >
        <div
          class="one-anchor-first-level one-anchor-link one-anchor-link-not-active one-anchor-link-without-children"
        >
          <span
            class="one-anchor-link-title one-anchor-link-title-not-active"
            title=""
          >
            <span />
          </span>
        </div>
        <div
          class="one-anchor-first-level one-anchor-link one-anchor-link-not-active one-anchor-link-without-children"
        >
          <a
            class="one-anchor-link-title one-anchor-link-title-not-active"
            href="#demo-code-operator"
            title=""
          >
            <span />
          </a>
        </div>
        <div
          class="one-anchor-first-level one-anchor-link one-anchor-link-not-active one-anchor-link-has-children"
        >
          <span
            class="one-anchor-link-title one-anchor-link-title-not-active"
            title=""
          >
            <span />
          </span>
          <div
            class="one-anchor-link one-anchor-link-not-active one-anchor-link-without-children"
          >
            <span
              class="one-anchor-link-title one-anchor-link-title-not-active"
              title=""
            >
              <span />
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Anchor Component should be rendered correctly 3 1`] = `
<div>
  <div
    class=""
  >
    <div
      class="one-anchor-wrapper one-anchor-wrapper-normal"
      style="max-height:100vh"
    >
      <div
        class="one-anchor one-anchor-small"
      >
        <div
          class="one-anchor-first-level one-anchor-link one-anchor-link-not-active one-anchor-link-without-children"
        >
          <span
            class="one-anchor-link-title one-anchor-link-title-not-active"
            title="Basic demo"
          >
            <span>
              Basic demo
            </span>
          </span>
        </div>
        <div
          class="one-anchor-first-level one-anchor-link one-anchor-link-not-active one-anchor-link-without-children"
        >
          <span
            class="one-anchor-link-title one-anchor-link-title-not-active"
            title="Code demo"
          >
            <span>
              Code demo
            </span>
          </span>
        </div>
        <div
          class="one-anchor-first-level one-anchor-link one-anchor-link-not-active one-anchor-link-has-children"
        >
          <span
            class="one-anchor-link-title one-anchor-link-title-not-active"
            title="API"
          >
            <span>
              API
            </span>
          </span>
          <div
            class="one-anchor-link one-anchor-link-not-active one-anchor-link-without-children"
          >
            <span
              class="one-anchor-link-title one-anchor-link-title-not-active"
              title="Props"
            >
              <span>
                Props
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`Anchor Component should be rendered correctly with no affix 1`] = `
<div
  class="one-anchor-wrapper one-anchor-wrapper-normal"
  style="max-height:100vh"
>
  <div
    class="one-anchor one-anchor-small"
  >
    <div
      class="one-anchor-first-level one-anchor-link one-anchor-link-not-active one-anchor-link-without-children"
    >
      <span
        class="one-anchor-link-title one-anchor-link-title-not-active"
        title="Basic demo"
      >
        <span>
          Basic demo
        </span>
      </span>
    </div>
    <div
      class="one-anchor-first-level one-anchor-link one-anchor-link-not-active one-anchor-link-without-children"
    >
      <span
        class="one-anchor-link-title one-anchor-link-title-not-active"
        title="Code demo"
      >
        <span>
          Code demo
        </span>
      </span>
    </div>
    <div
      class="one-anchor-first-level one-anchor-link one-anchor-link-not-active one-anchor-link-has-children"
    >
      <span
        class="one-anchor-link-title one-anchor-link-title-not-active"
        title="API"
      >
        <span>
          API
        </span>
      </span>
      <div
        class="one-anchor-link one-anchor-link-not-active one-anchor-link-without-children"
      >
        <span
          class="one-anchor-link-title one-anchor-link-title-not-active"
          title="Props"
        >
          <span>
            Props
          </span>
        </span>
      </div>
    </div>
  </div>
</div>
`;

exports[`Anchor Component update props 1`] = `
<div>
  <div
    class=""
  >
    <div
      class="one-anchor-wrapper one-anchor-wrapper-normal"
      style="max-height:100vh"
    >
      <div
        class="one-anchor one-anchor-medium"
      >
        <div
          class="one-anchor-first-level one-anchor-link one-anchor-link-not-active one-anchor-link-without-children"
        >
          <span
            class="one-anchor-link-title one-anchor-link-title-not-active"
            title="https://www.baidu.com"
          >
            <span>
              https://www.baidu.com
            </span>
          </span>
        </div>
      </div>
    </div>
  </div>
</div>
`;
