// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Collapse render correctly 1`] = `
<div
  class="one-collapse one-collapse-medium one-collapse-type-normal one-collapse-expand-icon-left"
>
  <div
    class="one-collapse-item one-collapse-item-not-active"
  >
    <div
      class="one-collapse-item-header"
      data-type="normal"
      tabindex="0"
    >
      <svg
        class="dls-icon one-collapse-angle-icon"
        fill="none"
        focusable="false"
        height="24"
        viewBox="0 0 10 24"
        width="10"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          clip-rule="evenodd"
          d="M10 12a1 1 0 0 1-.316.73l-8 7.5-1.368-1.46L7.538 12 .316 5.23l1.368-1.46 8 7.5A1 1 0 0 1 10 12z"
          fill="currentColor"
          fill-rule="evenodd"
        />
      </svg>
      <div
        class="one-collapse-item-title"
      >
        header
      </div>
    </div>
  </div>
</div>
`;
