/**
 * @file slider的单测
 * <AUTHOR>
 * @date 2020-09-02
 */

import {render, mount} from 'enzyme';
import React, {createRef} from 'react';
import Slider from '../../../../src/components/slider';
import mountTest from '../../../shared/mountTest';

describe('Slider Component', () => {
    mountTest(Slider);

    test('should render a Slider `small` component', () => {
        const ref = createRef();
        const wrapper = mount(
            <Slider defaultValue={30} size="small" ref={ref} />
        );
        expect(wrapper.props().size).toBe('small');
        wrapper.setProps({
            range: true,
            defaultValue: [10, 50]
        });
        ref.current.focus();
        ref.current.blur();
        expect(wrapper).toMatchSnapshot();
    });

    test('should show tooltip when hovering Slider handler', () => {
        const wrapper = mount(<Slider defaultValue={30} />);
        wrapper.find('.one-slider-handle').at(0).simulate('mouseEnter');
        expect(render(wrapper.find('Trigger').instance().getComponent())).toMatchSnapshot();
        wrapper.find('.one-slider-handle').at(0).simulate('mouseLeave');
        wrapper.setProps({
            disabled: true,
            tipFormatter: null
        });
        wrapper.update();
        wrapper.find('.one-slider-handle').at(0).simulate('mouseEnter');
        expect(render(wrapper.find('Trigger').instance().getComponent())).toMatchSnapshot();
    });

    test('should show correct placement tooltip when set tooltipPlacement', () => {
        const wrapper = mount(<Slider defaultValue={30} tooltipPlacement="left" />);
        wrapper.find('.one-slider-handle').at(0).simulate('mouseEnter');
        expect(render(wrapper.find('Trigger').instance().getComponent())).toMatchSnapshot();
        wrapper.find('.one-slider-handle').at(0).simulate('mouseLeave');
        expect(render(wrapper.find('Trigger').instance().getComponent())).toMatchSnapshot();
    });

    test('when tooltipVisible is true, tooltip should show always, or should never show', () => {
        let wrapper = mount(<Slider defaultValue={30} tooltipVisible />);
        expect(wrapper.find('.one-tooltip-content').at(0).hasClass('one-tooltip-hidden')).toBe(false);
        wrapper.find('.one-slider-handle').at(0).simulate('mouseEnter');
        expect(wrapper.find('.one-tooltip-content').at(0).hasClass('one-tooltip-hidden')).toBe(false);
        wrapper.find('.one-slider-handle').at(0).simulate('click');
        expect(wrapper.find('.one-tooltip-content').at(0).hasClass('one-tooltip-hidden')).toBe(false);
        wrapper = mount(<Slider defaultValue={30} tooltipVisible={false} />);
        expect(wrapper.find('.one-tooltip-content').length).toBe(0);
    });
});
