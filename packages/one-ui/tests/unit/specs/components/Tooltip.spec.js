/**
 * @file tooltip 组件单测
 * <AUTHOR>
 * @date 2020-09-09
 */
import React, {createRef} from 'react';
import {mount} from 'enzyme';
import Tooltip from '../../../../src/components/tooltip';
import Button from '../../../../src/components/button';
import {sleep} from '../../../utils';
import mountTest from '../../../../tests/shared/mountTest';

describe('Tooltip Components', () => {
    mountTest(Tooltip);


    test('renders correctly', () => {
        const onVisibleChange = jest.fn();
        const wrapper = mount(
            <Tooltip
                title=""
                mouseEnterDelay={0}
                mouseLeaveDelay={0}
                onVisibleChange={onVisibleChange}
            >
                <div id="hello">Hello world!</div>
            </Tooltip>
        );
        expect(wrapper).toMatchSnapshot();
    });

    it('check `onVisibleChange` arguments', () => {
        const onVisibleChange = jest.fn();
        const ref = createRef();

        const wrapper = mount(
            <Tooltip
                title=""
                mouseEnterDelay={0}
                mouseLeaveDelay={0}
                onVisibleChange={onVisibleChange}
                ref={ref}
            >
                <div id="hello">Hello world!</div>
            </Tooltip>
        );

        // `title` is empty.
        const div = wrapper.find('#hello').at(0);
        div.simulate('mouseenter');
        expect(ref.current.state.visible).toBe(true);

        div.simulate('mouseleave');
        expect(ref.current.state.visible).toBe(true);

        // update `title` value.
        wrapper.setProps({title: 'Have a nice day!'});
        wrapper.find('#hello').simulate('mouseenter');
        expect(onVisibleChange).toHaveBeenLastCalledWith(true);
        expect(ref.current.state.visible).toBe(true);

        wrapper.find('#hello').simulate('mouseleave');
        expect(onVisibleChange).toHaveBeenLastCalledWith(false);
        expect(ref.current.state.visible).toBe(false);

        // add `visible` props.
        wrapper.setProps({visible: false});
        wrapper.find('#hello').simulate('mouseenter');
        expect(onVisibleChange).toHaveBeenLastCalledWith(true);
        const lastCount = onVisibleChange.mock.calls.length;
        expect(ref.current.state.visible).toBe(false);

        // always trigger onVisibleChange
        wrapper.simulate('mouseleave');
        expect(onVisibleChange.mock.calls.length).toBe(lastCount); // no change with lastCount
        expect(ref.current.state.visible).toBe(false);
    });

    it('should hide when mouse leave native disabled button', () => {
        const onVisibleChange = jest.fn();
        const ref = createRef();
        const wrapper = mount(
          <Tooltip
            title="xxxxx"
            mouseEnterDelay={0}
            mouseLeaveDelay={0}
            onVisibleChange={onVisibleChange}
            ref={ref}
          >
            <button type="button" disabled>
              Hello world!
            </button>
          </Tooltip>,
        );

        expect(wrapper.find('span')).toHaveLength(1);
        const button = wrapper.find('span').at(0);
        button.simulate('mouseenter');
        expect(onVisibleChange).toHaveBeenCalledWith(true);
        expect(ref.current.state.visible).toBe(true);

        button.simulate('mouseleave');
        expect(onVisibleChange).toHaveBeenCalledWith(false);
        expect(ref.current.state.visible).toBe(false);
    });

    it('should render disabled Button style properly', () => {
        const wrapper1 = mount(
          <Tooltip title="xxxxx">
            <Button disabled>Hello world!</Button>
          </Tooltip>,
        );
        const wrapper2 = mount(
          <Tooltip title="xxxxx">
            <Button disabled style={{display: 'block'}}>
              Hello world!
            </Button>
          </Tooltip>,
        );
        expect(wrapper1.find('span').first().getDOMNode().style.display).toBe('inline-block');
        expect(wrapper2.find('span').first().getDOMNode().style.display).toBe('block');
    });

    it('support other placement', async () => {
        const wrapper = mount(
            <Tooltip
                title="xxxxx"
                placement="bottomLeft"
                transitionName=""
                mouseEnterDelay={0}
                afterVisibleChange={visible => {
                    if (visible) {
                        expect(wrapper.find('Trigger').instance().props.popupPlacement).toBe('bottomLeft');
                    }
                }}
            >
                <span>Hello world!</span>
            </Tooltip>,
        );
        expect(wrapper.find('span')).toHaveLength(1);
        const button = wrapper.find('span').at(0);
        button.simulate('mouseenter');
    });

    it('other placement when mouse enter', async () => {
        const ref = createRef();
        const wrapper = mount(
          <Tooltip
            title="xxxxx"
            placement="topRight"
            prefixCls="tooltip"
            transitionName=""
            popupTransitionName=""
            mouseEnterDelay={0}
            ref={ref}
          >
            <span>Hello world!</span>
          </Tooltip>,
        );

        expect(wrapper.find('span')).toHaveLength(1);
        const button = wrapper.find('span').at(0);
        button.simulate('mouseenter');
        await sleep(500);
        expect(ref.current.tooltipRef.trigger.getRootDomNode().className).toContain('tooltip-open');
    });

    it('should works for mismatch placement', async () => {
        const ref = React.createRef();
        const wrapper = mount(
            <Tooltip
                title="xxxxx"
                align={{points: ['bc', 'tl']}}
                mouseEnterDelay={0}
                prefixCls="tooltip"
                ref={ref}
            >
                <span>Hello world!</span>
            </Tooltip>,
        );
        const button = wrapper.find('span').at(0);
        button.simulate('mouseenter');
        await sleep(600);
        expect(ref.current.tooltipRef.trigger.getRootDomNode().className).toContain('tooltip-open');
    });
});
