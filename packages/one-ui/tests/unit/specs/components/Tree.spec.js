/**
 * @file Tree 组件单测
 * <AUTHOR>
 * @date 2020-09-02
 */
import React, {Component} from 'react';
import {mount} from 'enzyme';
import mountTest from '../../../../tests/shared/mountTest';
import Tree from '../../../../src/components/tree/index';
import RawTree from '../../../../src/components/tree/tree';
import IconSvg from '../../../../src/components/iconSvg/index';
import Button from '../../../../src/components/button/index';

const {TreeNode} = Tree;
const {VirtualTreeNode} = Tree;

describe('Tree Components', () => {
    mountTest(Tree);
    it('Tree should render correctly 1', () => {
        const wrapper = mount(
            <Tree
                defaultExpandedKeys={['0-0-1']}
                defaultSelectedKeys={['0-0-1']}
                defaultCheckedKeys={['0-0-1']}
            >
                <TreeNode title="计划1" key="0-0">
                    <TreeNode title="单元1" key="0-0-0" disabled>
                        <TreeNode title="关键词1" key="0-0-0-0" />
                        <TreeNode title="关键词1" key="0-0-0-1" disabled />
                    </TreeNode>
                    <TreeNode title="单元2" key="0-0-1">
                        <TreeNode title="关键词3" key="0-0-1-0" />
                    </TreeNode>
                </TreeNode>
            </Tree>
        );
        expect(wrapper.render()).toMatchSnapshot();
    });
    it('Tree should render correctly in virtual tree', () => {
        class Demo extends Component {
            onSelect = (selectedKeys, info) => {
                console.log('selected', selectedKeys, info);
            };
            onCheck = (checkedKeys, info) => {
                console.log('onCheck', checkedKeys, info);
            };

            TreeRef = ref => {
                this.treeRef = ref;
            }

            render() {
                return (
                    <div>
                        <div>
                            <RawTree
                                defaultExpandedKeys={['0-0-0', '0-0-1']}
                                defaultSelectedKeys={['0-0-0', '0-0-1']}
                                defaultCheckedKeys={['0-0-0', '0-0-1']}
                                onSelect={this.onSelect}
                                onCheck={this.onCheck}
                                checkable
                                parentContainerHeight={200}
                                ref={this.TreeRef}
                                switcherIcon={() => {
                                    return <IconSvg type="times" />;
                                }}
                            >
                                <VirtualTreeNode title="计划1" key="0-0">
                                    <VirtualTreeNode title="单元1" key="0-0-0" >
                                        <VirtualTreeNode title="关键词1" key="0-0-0-0" disableCheckbox />
                                        <VirtualTreeNode title="关键词1" key="0-0-0-1" />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元2" key="0-0-1">
                                        <VirtualTreeNode title="关键词3" key="0-0-1-0" selectable={false} />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元3" key="0-0-3" disableCheckbox>
                                        <VirtualTreeNode
                                            title="关键词4"
                                            key="0-0-3-1"
                                            selectable={false}
                                            disableCheckbox
                                        />
                                        <VirtualTreeNode title="关键词5" key="0-0-3-2" selectable={false} />
                                        <VirtualTreeNode title="关键词6" key="0-0-3-3" selectable={false} />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元4" key="0-0-4" >
                                        <VirtualTreeNode title="关键词1" key="0-0-4-0" disableCheckbox />
                                        <VirtualTreeNode title="关键词1" key="0-0-4-1" />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元5" key="0-0-5">
                                        <VirtualTreeNode title="关键词3" key="0-0-5-0" selectable={false} />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元3" key="0-0-6">
                                        <VirtualTreeNode
                                            title="关键词4"
                                            key="0-0-6-1"
                                            selectable={false}
                                            disableCheckbox
                                        />
                                        <VirtualTreeNode title="关键词5" key="0-0-6-2" selectable={false} />
                                        <VirtualTreeNode title="关键词6" key="0-0-6-3" selectable={false} />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元1" key="0-0-7" >
                                        <VirtualTreeNode title="关键词1" key="0-0-7-0" disableCheckbox />
                                        <VirtualTreeNode title="关键词1" key="0-0-7-1" />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元2" key="0-0-8">
                                        <VirtualTreeNode title="关键词3" key="0-0-8-0" selectable={false} />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元3" key="0-0-9">
                                        <VirtualTreeNode title="关键词4" key="0-0-9-1" selectable={false} />
                                        <VirtualTreeNode title="关键词5" key="0-0-9-2" selectable={false} />
                                        <VirtualTreeNode title="关键词6" key="0-0-9-3" selectable={false} />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元3" key="0-0-10">
                                        <VirtualTreeNode title="关键词4" key="0-0-10-1" selectable={false} />
                                        <VirtualTreeNode title="关键词5" key="0-0-10-2" selectable={false} />
                                        <VirtualTreeNode title="关键词6" key="0-0-10-3" selectable={false} />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元1" key="0-0-11" >
                                        <VirtualTreeNode title="关键词1" key="0-0-11-0" disableCheckbox />
                                        <VirtualTreeNode title="关键词1" key="0-0-11-1" />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元2" key="0-0-12">
                                        <VirtualTreeNode title="关键词3" key="0-0-12-0" selectable={false} />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元3" key="0-0-13">
                                        <VirtualTreeNode title="关键词4" key="0-0-13-1" selectable={false} />
                                        <VirtualTreeNode title="关键词5" key="0-0-13-2" selectable={false} />
                                        <VirtualTreeNode title="关键词6" key="0-0-13-3" selectable={false} />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元3" key="0-0-14">
                                        <VirtualTreeNode title="关键词4" key="0-0-14-1" selectable={false} />
                                        <VirtualTreeNode title="关键词5" key="0-0-14-2" selectable={false} />
                                        <VirtualTreeNode title="关键词6" key="0-0-14-3" selectable={false} />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元1" key="0-0-15" >
                                        <VirtualTreeNode title="关键词1" key="0-0-15-0" />
                                        <VirtualTreeNode title="关键词1" key="0-0-15-1" />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元2" key="0-0-16">
                                        <VirtualTreeNode title="关键词3" key="0-0-16-0" selectable={false} />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元3" key="0-0-17">
                                        <VirtualTreeNode title="关键词4" key="0-0-17-1" selectable={false} />
                                        <VirtualTreeNode title="关键词5" key="0-0-17-2" selectable={false} />
                                        <VirtualTreeNode title="关键词6" key="0-0-17-3" selectable={false} />
                                    </VirtualTreeNode>
                                </VirtualTreeNode>
                            </RawTree>
                        </div>
                        <Button className="button-get-tree-state">点击获取树的state</Button>
                    </div>
                );
            }
        }
        const wrapper = mount(
            <Demo />
        );
        expect(wrapper.render()).toMatchSnapshot();
        wrapper
            .find('.button-get-tree-state')
            .at(0)
            .simulate('click');
        wrapper.setProps({switcherIcon: <IconSvg type="times" />});
        wrapper.setProps({selectedKeys: ['0-0-0']});
    });
    it('Tree should render correctly in virtual tree', () => {
        // eslint-disable-next-line react/no-multi-comp
        class Demo extends Component {
            onSelect = (selectedKeys, info) => {
                console.log('selected', selectedKeys, info);
            };
            onCheck = (checkedKeys, info) => {
                console.log('onCheck', checkedKeys, info);
            };

            TreeRef = ref => {
                this.treeRef = ref;
            }

            render() {
                return (
                    <div>
                        <div>
                            <RawTree
                                defaultExpandedKeys={['0-0-0', '0-0-1']}
                                defaultSelectedKeys={['0-0-0', '0-0-1']}
                                defaultCheckedKeys={['0-0-0', '0-0-1']}
                                onSelect={this.onSelect}
                                onCheck={this.onCheck}
                                checkable
                                parentContainerHeight={200}
                                ref={this.TreeRef}
                                switcherIcon={<IconSvg type="times" />}
                            >
                                <VirtualTreeNode title="计划1" key="0-0">
                                    <VirtualTreeNode title="单元1" key="0-0-0" >
                                        <VirtualTreeNode title="关键词1" key="0-0-0-0" disableCheckbox />
                                        <VirtualTreeNode title="关键词1" key="0-0-0-1" />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元2" key="0-0-1">
                                        <VirtualTreeNode title="关键词3" key="0-0-1-0" selectable={false} />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元3" key="0-0-3" disableCheckbox>
                                        <VirtualTreeNode
                                            title="关键词4"
                                            key="0-0-3-1"
                                            selectable={false}
                                            disableCheckbox
                                        />
                                        <VirtualTreeNode title="关键词5" key="0-0-3-2" selectable={false} />
                                        <VirtualTreeNode title="关键词6" key="0-0-3-3" selectable={false} />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元4" key="0-0-4" >
                                        <VirtualTreeNode title="关键词1" key="0-0-4-0" disableCheckbox />
                                        <VirtualTreeNode title="关键词1" key="0-0-4-1" />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元5" key="0-0-5">
                                        <VirtualTreeNode title="关键词3" key="0-0-5-0" selectable={false} />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元3" key="0-0-6">
                                        <VirtualTreeNode
                                            title="关键词4"
                                            key="0-0-6-1"
                                            selectable={false}
                                            disableCheckbox
                                        />
                                        <VirtualTreeNode title="关键词5" key="0-0-6-2" selectable={false} />
                                        <VirtualTreeNode title="关键词6" key="0-0-6-3" selectable={false} />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元1" key="0-0-7" >
                                        <VirtualTreeNode title="关键词1" key="0-0-7-0" disableCheckbox />
                                        <VirtualTreeNode title="关键词1" key="0-0-7-1" />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元2" key="0-0-8">
                                        <VirtualTreeNode title="关键词3" key="0-0-8-0" selectable={false} />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元3" key="0-0-9">
                                        <VirtualTreeNode title="关键词4" key="0-0-9-1" selectable={false} />
                                        <VirtualTreeNode title="关键词5" key="0-0-9-2" selectable={false} />
                                        <VirtualTreeNode title="关键词6" key="0-0-9-3" selectable={false} />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元3" key="0-0-10">
                                        <VirtualTreeNode title="关键词4" key="0-0-10-1" selectable={false} />
                                        <VirtualTreeNode title="关键词5" key="0-0-10-2" selectable={false} />
                                        <VirtualTreeNode title="关键词6" key="0-0-10-3" selectable={false} />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元1" key="0-0-11" >
                                        <VirtualTreeNode title="关键词1" key="0-0-11-0" disableCheckbox />
                                        <VirtualTreeNode title="关键词1" key="0-0-11-1" />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元2" key="0-0-12">
                                        <VirtualTreeNode title="关键词3" key="0-0-12-0" selectable={false} />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元3" key="0-0-13">
                                        <VirtualTreeNode title="关键词4" key="0-0-13-1" selectable={false} />
                                        <VirtualTreeNode title="关键词5" key="0-0-13-2" selectable={false} />
                                        <VirtualTreeNode title="关键词6" key="0-0-13-3" selectable={false} />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元3" key="0-0-14">
                                        <VirtualTreeNode title="关键词4" key="0-0-14-1" selectable={false} />
                                        <VirtualTreeNode title="关键词5" key="0-0-14-2" selectable={false} />
                                        <VirtualTreeNode title="关键词6" key="0-0-14-3" selectable={false} />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元1" key="0-0-15" >
                                        <VirtualTreeNode title="关键词1" key="0-0-15-0" />
                                        <VirtualTreeNode title="关键词1" key="0-0-15-1" />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元2" key="0-0-16">
                                        <VirtualTreeNode title="关键词3" key="0-0-16-0" selectable={false} />
                                    </VirtualTreeNode>
                                    <VirtualTreeNode title="单元3" key="0-0-17">
                                        <VirtualTreeNode title="关键词4" key="0-0-17-1" selectable={false} />
                                        <VirtualTreeNode title="关键词5" key="0-0-17-2" selectable={false} />
                                        <VirtualTreeNode title="关键词6" key="0-0-17-3" selectable={false} />
                                    </VirtualTreeNode>
                                </VirtualTreeNode>
                            </RawTree>
                        </div>
                        <Button className="button-get-tree-state">点击获取树的state</Button>
                    </div>
                );
            }
        }
        const wrapper = mount(
            <Demo />
        );
        expect(wrapper.render()).toMatchSnapshot();
        wrapper
            .find('.button-get-tree-state')
            .at(0)
            .simulate('click');
    });
    it('render correctly 3', () => {
        const wrapper = mount(
            <Tree
                defaultExpandedKeys={['0-0-1']}
                defaultSelectedKeys={['0-0-1']}
                defaultCheckedKeys={['0-0-1']}
                size="small"
            >
                <TreeNode title="计划1" key="0-0">
                    <TreeNode title="单元1" key="0-0-0" disabled>
                        <TreeNode title="关键词1" key="0-0-0-0" />
                        <TreeNode title="关键词1" key="0-0-0-1" disabled />
                    </TreeNode>
                    <TreeNode title="单元2" key="0-0-1">
                        <TreeNode title="关键词3" key="0-0-1-0" />
                    </TreeNode>
                </TreeNode>
            </Tree>
        );
        expect(wrapper.render()).toMatchSnapshot();
    });
    it('render correctly 4', () => {
        // eslint-disable-next-line react/no-multi-comp
        class Demo2 extends Component {
            state = {
                expandedKeys: ['0-0-1'],
                selectedKeys: ['0-0-1'],
                checkedKeys: ['0-0-1']
            }
            onSelect = (selectedKeys, info) => {
                console.log('selected', selectedKeys, info);
                this.setState({
                    selectedKeys
                });
            };
            onCheck = (checkedKeys, info) => {
                console.log('onCheck', checkedKeys, info);
                this.setState({
                    checkedKeys
                });
            };
            onExpand = (expandedKeys, info) => {
                console.log('onExpanded', expandedKeys, info);
                this.setState({
                    expandedKeys
                });
            }
            renderFolderIcon = key => {
                const expandedKeys = this.state.expandedKeys;
                if (expandedKeys.indexOf(key) > -1) {
                    return <IconSvg type="folder-open" />;
                }
                return <IconSvg type="folder" />;
            }
            render() {
                return (
                    <div>
                        <br />
                        <br />
                        图标均为自定义，这个示例只是一个示例
                        <br />
                        <br />
                        <div>
                            <Tree
                                expandedKeys={this.state.expandedKeys}
                                selectedKeys={this.state.selectedKeys}
                                checkedKeys={this.state.checkedKeys}
                                onSelect={this.onSelect}
                                onCheck={this.onCheck}
                                onExpand={this.onExpand}
                                showIcon
                            >
                                <TreeNode title="计划1" key="0-0" icon={this.renderFolderIcon('0-0')}>
                                    <TreeNode title="单元1" key="0-0-0" icon={this.renderFolderIcon('0-0-0')}>
                                        <TreeNode title="关键词1" key="0-0-0-0" icon={<IconSvg type="file" />} />
                                        <TreeNode title="关键词1" key="0-0-0-1" icon={<IconSvg type="file" />} />
                                    </TreeNode>
                                    <TreeNode title="单元2" key="0-0-1" icon={this.renderFolderIcon('0-0-1')}>
                                        <TreeNode title="关键词3" key="0-0-1-0" icon={<IconSvg type="file" />} />
                                    </TreeNode>
                                </TreeNode>
                            </Tree>
                        </div>
                        <br />
                        <br />
                    </div>
                );
            }
        }
        const wrapper = mount(
            <Demo2 />
        );
        expect(wrapper.render()).toMatchSnapshot();
    });
    it('render correctly 5', () => {
        // eslint-disable-next-line react/no-multi-comp
        class Demo3 extends React.Component {
            state = {
                treeData: [
                    {title: 'Expand to load', key: '0'},
                    {title: 'Expand to load', key: '1'},
                    {title: 'Tree Node', key: '2', isLeaf: true}
                ]
            };
            onLoadData = treeNode => new Promise(resolve => {
                if (treeNode.props.children) {
                    resolve();
                    return;
                }
                setTimeout(() => {
                    treeNode.props.dataRef.children = [
                        {title: 'Child Node', key: `${treeNode.props.eventKey}-0`},
                        {title: 'Child Node', key: `${treeNode.props.eventKey}-1`}
                    ];
                    this.setState({
                        treeData: [...this.state.treeData]
                    });
                    resolve();
                }, 2000);
            });
            renderTreeNodes = data => data.map(item => {
                if (item.children) {
                    return (
                        <TreeNode title={item.title} key={item.key} dataRef={item}>
                            {this.renderTreeNodes(item.children)}
                        </TreeNode>
                    );
                }
                return <TreeNode key={item.key} {...item} dataRef={item} />;
            });
            render() {
                return (<Tree
                    switcherIcon={(<IconSvg type="loading" />)}
                    loadData={this.onLoadData}
                >
                    {this.renderTreeNodes(this.state.treeData)}
                </Tree>);
            }
        }
        const wrapper = mount(
            <Demo3 />
        );
        expect(wrapper.render()).toMatchSnapshot();
        wrapper
            .find('.one-tree-switcher')
            .at(1)
            .simulate('click');
    });
    it('render tree switch icon with functional', () => {
        // eslint-disable-next-line react/no-multi-comp
        class Demo4 extends React.Component {
            state = {
                treeData: [
                    {title: 'Expand to load', key: '0'},
                    {title: 'Expand to load', key: '1'},
                    {title: 'Tree Node', key: '2', isLeaf: true}
                ]
            };
            onLoadData = treeNode => new Promise(resolve => {
                if (treeNode.props.children) {
                    resolve();
                    return;
                }
                setTimeout(() => {
                    treeNode.props.dataRef.children = [
                        {title: 'Child Node', key: `${treeNode.props.eventKey}-0`},
                        {title: 'Child Node', key: `${treeNode.props.eventKey}-1`}
                    ];
                    this.setState({
                        treeData: [...this.state.treeData]
                    });
                    resolve();
                }, 2000);
            });
            renderTreeNodes = data => data.map(item => {
                if (item.children) {
                    return (
                        <TreeNode title={item.title} key={item.key} dataRef={item}>
                            {this.renderTreeNodes(item.children)}
                        </TreeNode>
                    );
                }
                return <TreeNode key={item.key} {...item} dataRef={item} />;
            });
            render() {
                return (<Tree
                    switcherIcon={() => {
                        return <IconSvg type="times" />;
                    }}
                    loadData={this.onLoadData}
                >
                    {this.renderTreeNodes(this.state.treeData)}
                </Tree>);
            }
        }
        const wrapper = mount(
            <Demo4 />
        );
        expect(wrapper.render()).toMatchSnapshot();
        wrapper
            .find('.one-tree-switcher')
            .at(1)
            .simulate('click');
    });
});