/**
 * @file Alert.spec.js
 * <AUTHOR> <EMAIL>
 * @date 2020-09-03
 */
import {mount} from 'enzyme';
import React from 'react';
import Alert from '../../../../src/components/alert/index';
import mountTest from '../../../../tests/shared/mountTest';

describe('Alert Component', () => {
    mountTest(Alert);

    test('should create a Alert component with base props', () => {
        const wrapper = mount(<Alert type="success" title="success title" content="success content" />);
        expect(wrapper.props().type).toBe('success');
        expect(wrapper.props().title).toBe('success title');
        expect(wrapper.props().content).toBe('success content');

        expect(wrapper).toMatchSnapshot();
    });

    test('should show showIcon when has showIcon props', () => {
        const wrapper = mount(<Alert size="small" type="info" showIcon />);
        expect(wrapper.props().showIcon).toBe(true);
        expect(wrapper.props().size).toBe('small');
        expect(wrapper).toMatchSnapshot();
    });

    test('should close when has click close icon', () => {
        const wrapper = mount(<Alert type="success" closable />);
        wrapper
            .find('.one-alert-close-icon')
            .simulate('click');
        expect(wrapper.find('.one-alert').length).toBe(0);
    });

    test('should rende ok icon is string', () => {
        const wrapper = mount(<Alert type="success" closable icon="xxx" />);
        expect(wrapper).toMatchSnapshot();
    });
    test('should rende ok icon is react element', () => {
        const wrapper = mount(<Alert type="success" closable icon={<div>啦啦啦</div>} />);
        expect(wrapper).toMatchSnapshot();
    });

    test('should has error when type is custom', () => {
        const wrapper = mount(<Alert type="xxx" closable />);
        expect(wrapper).toMatchSnapshot();
    });
});


describe('Alert Page Component', () => {
    mountTest(Alert.Page);

    test('should create a Alert Page component', () => {
        const dataSource = [
            <Alert key={1} content="带有通知性质的通知条" type="success" closable />,
            <Alert key={2} content="带有通知性质的通知条" type="info" closable />,
            <Alert key={3} content="带有通知性质的通知条" type="error" closable />,
            <Alert key={4} content="带有通知性质的通知条" type="warning" closable />
        ];

        const wrapper = mount(<Alert.Page dataSource={dataSource} />);

        expect(wrapper).toMatchSnapshot();
    });

    test('should support page change', () => {

        class AlertPage extends React.Component {

            state = {
                dataSource: [
                    <Alert key={1} content="带有通知性质的通知条" type="success" closable />,
                    <Alert key={2} content="带有通知性质的通知条" type="info" closable />,
                    <Alert key={3} content="带有通知性质的通知条" type="error" closable />,
                    <Alert key={4} content="带有通知性质的通知条" type="warning" closable />
                ]
            }

            onClose = index => {
                const {dataSource} = this.state;
                dataSource.splice(index, 1);
                this.setState({dataSource: [...dataSource]});
            };

            render() {
                return (
                    <Alert.Page dataSource={this.state.dataSource} onClose={this.onClose} />
                );
            }
        }

        const wrapper = mount(<AlertPage />);
        wrapper.find('.one-alert-close-icon').simulate('click');
        expect(wrapper.find('.one-alert-page-count > span').text()).toBe('1/3');
        wrapper.find('.one-alert-close-icon').simulate('click');
        wrapper.find('.one-alert-close-icon').simulate('click');
        expect(wrapper.find('.one-alert-page-count > span').length).toBe(0);
        wrapper.find('.one-alert-close-icon').simulate('click');
        expect(wrapper.find('.one-alert-page').length).toBe(0);
    });

    test('should support activeIndex for alertPage', () => {
        const onPrevChange = jest.fn();
        const onNextChange = jest.fn();
        const onClose = jest.fn();

        const dataSource = [
            <Alert key={1} content="带有通知性质的通知条" type="success" closable />,
            <Alert key={2} content="带有通知性质的通知条" type="info" closable />,
            <Alert key={3} content="带有通知性质的通知条" type="error" closable />,
            <Alert key={4} content="带有通知性质的通知条" type="warning" closable />
        ];

        // eslint-disable-next-line max-len
        const wrapper = mount(<Alert.Page dataSource={dataSource} activeIndex={2} onPrevChange={onPrevChange} onNextChange={onNextChange} onClose={onClose} />);
        expect(wrapper.find('.one-alert-error').length).toBe(1);
        expect(wrapper.find('.one-alert-page-count > span').text()).toBe('3/4');
        expect(wrapper.find('.one-alert-page-count-prev').at(1).simulate('click'));
        expect(onPrevChange).toHaveBeenCalled();
        expect(wrapper.find('.one-alert-page-count-next').at(1).simulate('click'));
        expect(onNextChange).toHaveBeenCalled();
        wrapper.find('.one-alert-close-icon').simulate('click');
        expect(onClose).toHaveBeenCalled();
    });

    test('should not support onPrevChange when activeIndex is the first or not set', () => {
        const onPrevChange = jest.fn();
        const onNextChange = jest.fn();
        const onClose = jest.fn();

        const dataSource = [
            <Alert key={1} content="带有通知性质的通知条" type="success" closable />,
            <Alert key={2} content="带有通知性质的通知条" type="info" closable />,
            <Alert key={3} content="带有通知性质的通知条" type="error" closable />,
            <Alert key={4} content="带有通知性质的通知条" type="warning" closable />
        ];

        // eslint-disable-next-line max-len
        const wrapper = mount(<Alert.Page dataSource={dataSource} onPrevChange={onPrevChange} onNextChange={onNextChange} onClose={onClose} />);
        expect(wrapper.find('.one-alert-page-count-prev').at(1).simulate('click'));
        expect(onPrevChange).not.toHaveBeenCalled();
        expect(wrapper.find('.one-alert-page-count-next').at(1).simulate('click'));
        expect(onNextChange).toHaveBeenCalled();
        // eslint-disable-next-line max-len
        const wrapper2 = mount(<Alert.Page dataSource={dataSource} activeIndex={0} onPrevChange={onPrevChange} onNextChange={onNextChange} onClose={onClose} />);
        expect(wrapper2.find('.one-alert-page-count-prev').at(1).simulate('click'));
        expect(onPrevChange).not.toHaveBeenCalled();
        expect(wrapper2.find('.one-alert-page-count-next').at(1).simulate('click'));
        expect(onNextChange).toHaveBeenCalled();

        wrapper.find('.one-alert-close-icon').simulate('click');
        expect(onClose).toHaveBeenCalled();
    });

    test('should not support onNextChange when activeIndex is the last one', () => {
        const onPrevChange = jest.fn();
        const onNextChange = jest.fn();
        const onClose = jest.fn();

        const dataSource = [
            <Alert key={1} content="带有通知性质的通知条" type="success" closable />,
            <Alert key={2} content="带有通知性质的通知条" type="info" closable />,
            <Alert key={3} content="带有通知性质的通知条" type="error" closable />,
            <Alert key={4} content="带有通知性质的通知条" type="warning" closable />
        ];

        // eslint-disable-next-line max-len
        const wrapper = mount(<Alert.Page dataSource={dataSource} activeIndex={3} onPrevChange={onPrevChange} onNextChange={onNextChange} onClose={onClose} />);
        expect(wrapper.find('.one-alert-page-count-prev').at(1).simulate('click'));
        expect(onPrevChange).toHaveBeenCalled();
        expect(wrapper.find('.one-alert-page-count-next').at(1).simulate('click'));
        expect(onNextChange).not.toHaveBeenCalled();

        wrapper.find('.one-alert-close-icon').simulate('click');
        expect(onClose).toHaveBeenCalled();
    });

});
