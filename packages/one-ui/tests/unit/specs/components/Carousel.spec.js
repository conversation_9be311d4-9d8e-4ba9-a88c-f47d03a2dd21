
import React, { createRef } from 'react';
import {mount} from 'enzyme';
import Carousel from '../../../../src/components/carousel/index';
import mountTest from '../../../shared/mountTest';

describe('Carousel Component', () => {
    mountTest(Carousel);
    beforeEach(() => {
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    it('should has innerSlider', () => {
        const ref = createRef();
        const wrapper = mount(
            <Carousel ref={ref}>
                <div />
            </Carousel>,
        );
        const {innerSlider} = ref.current;
        const innerSliderFromRefs = ref.current.slick.innerSlider;
        expect(innerSlider).toBe(innerSliderFromRefs);
        expect(typeof innerSlider.slickNext).toBe('function');
    });
    it('should has prev, next and go function', () => {
        const ref = createRef();
        const wrapper = mount(
            <Carousel ref={ref}>
                <div>1</div>
                <div>2</div>
                <div>3</div>
            </Carousel>,
        );
        const {prev, next, goTo} = ref.current;
        expect(typeof prev).toBe('function');
        expect(typeof next).toBe('function');
        expect(typeof goTo).toBe('function');
        expect(ref.current.slick.innerSlider.state.currentSlide).toBe(0);
        ref.current.goTo(2);
        jest.runAllTimers();
        expect(ref.current.slick.innerSlider.state.currentSlide).toBe(2);
        ref.current.prev();
        jest.runAllTimers();
        expect(ref.current.slick.innerSlider.state.currentSlide).toBe(1);
        ref.current.next();
        jest.runAllTimers();
        expect(ref.current.slick.innerSlider.state.currentSlide).toBe(2);
    });
    it('should trigger autoPlay after window resize', async () => {
        jest.useRealTimers();
        const ref = createRef()
        const wrapper = mount(
            <Carousel autoplay sliderMode="hide" ref={ref}>
                <div>1</div>
                <div>2</div>
                <div>3</div>
            </Carousel>,
        );
        const spy = jest.spyOn(ref.current.slick.innerSlider, 'autoPlay');
        window.resizeTo(1000);
        expect(spy).not.toHaveBeenCalled();
        await new Promise(resolve => setTimeout(resolve, 500));
        expect(spy).toHaveBeenCalled();
    });

    it('cancel resize listener when unmount', async () => {
        const ref = createRef();
        const wrapper = mount(
            <Carousel autoplay sliderMode="number" ref={ref}>
                <div>1</div>
                <div>2</div>
                <div>3</div>
            </Carousel>,
        );
        wrapper.simulate('mouseenter');
        wrapper.find('.one-carousel-slick-change-next').at(0).simulate('click');
        const {onWindowResized} = ref.current;
        const spy2 = jest.spyOn(window, 'removeEventListener');
        wrapper.unmount();
        expect(spy2).toHaveBeenCalledWith('resize', onWindowResized);
    });
    describe('should works for dotPosition', () => {
        ['left', 'right', 'top', 'bottom'].forEach(dotPosition => {
            it(dotPosition, () => {
                const wrapper = mount(
                    <Carousel dotPosition={dotPosition} width={300} >
                        <div />
                    </Carousel>,
                );
                expect(wrapper.render()).toMatchSnapshot();
            });
        });
    });
    describe('should works for customSuffix', () => {
        ['left', 'right', 'top', 'bottom'].forEach(dotPosition => {
            it(dotPosition, () => {
                const wrapper = mount(
                    <Carousel customSuffix={(<div>custom</div>)} width={300} >
                        <div />
                    </Carousel>,
                );
                expect(wrapper.render()).toMatchSnapshot();
            });
        });
    });
});