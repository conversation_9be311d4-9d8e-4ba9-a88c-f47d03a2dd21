/**
 * @file Tag.spec.js
 * <AUTHOR> <EMAIL>
 * @date 2020-09-03
 */
import {mount} from 'enzyme';
import React from 'react';
import RTag from '../../../../src/components/tag/index';
import Tag from '../../../../src/components/tag/tag';
import TagGroup from '../../../../src/components/tag/group';
import TagEditableGroup from '../../../../src/components/tag/editableGroup';
import mountTest from '../../../shared/mountTest';

describe('Tag Component', () => {
    mountTest(RTag);

    test('should show tipTag', () => {
        const onClose = jest.fn();

        const wrapper = mount(<Tag tipTag="success" onClose={onClose}>success</Tag>);
        expect(wrapper.props().tipTag).toBe('success');
        expect(wrapper).toMatchSnapshot();
        wrapper.find('.one-tag').at(0).simulate('click');
        expect(onClose).not.toHaveBeenCalled();
    });

    test('should be closable', () => {
        const onClose = jest.fn();
        const wrapper = mount(<Tag closable onClose={onClose} />);

        expect(wrapper).toMatchSnapshot();
        expect(wrapper.find('.one-tag-close-icon').length).toBe(2);
        wrapper.find('.one-tag-close-icon').at(1).simulate('click');
        expect(onClose).toHaveBeenCalled();
    });

    test('should not clickable when is disabled', () => {
        const onChange = jest.fn();
        const wrapper = mount(<Tag tipTag="success" checkable disabled onChange={onChange}>success</Tag>);

        expect(wrapper).toMatchSnapshot();
        expect(wrapper.find('.one-tag-success').length).toBe(1);
        wrapper.find('.one-tag-success').at(0).simulate('click');
        expect(onChange).not.toHaveBeenCalled();
    });

    test('should not clickable when is disabled', () => {
        const onChange = jest.fn();
        const wrapper = mount(<Tag tipTag="success" checkable onChange={onChange}>success</Tag>);

        expect(wrapper.find('.one-tag-success').length).toBe(1);
        wrapper.find('.one-tag-success').at(0).simulate('click');
        expect(onChange).toHaveBeenCalled();
    });

});

describe('TagGroup Component', () => {
    mountTest(TagGroup);

    const dataSource = [
        {
            label: '标签1',
            value: '111'
        },
        {
            label: '标签2',
            value: '222'
        },
        {
            label: '标签3',
            value: '333'
        }
    ];

    test('should support controlled value', () => {
        class Demo extends React.Component {
            state = {
                value: ['111']
            }

            onChange = selectedValue => {
                this.setState({
                    value: selectedValue
                });
            };

            render() {

                return (
                    <TagGroup dataSource={dataSource} value={this.state.value} onChange={this.onChange} />
                );
            }
        }
        const wrapper = mount(<Demo />);
        wrapper.find('.one-tag').at(1).simulate('change');
        expect(wrapper.find('.one-tag').at(1).hasClass('one-tag-inverse'));
    });

});

describe('TagEditableGroup Component', () => {
    mountTest(TagEditableGroup);

    test('should support controlled', () => {
        // eslint-disable-next-line react/no-multi-comp
        class Demo extends React.Component {
            state = {
                dataSource: [
                    {
                        label: '标签1',
                        value: '111'
                    },
                    {
                        label: '标签2',
                        value: '222'
                    },
                    {
                        label: '标签3',
                        value: '333'
                    }
                ]
            }

            onInputConfirm = (dataSource, InputValue) => {
                dataSource.push({
                    value: InputValue,
                    label: InputValue
                });
                this.setState({
                    dataSource
                });
            };

            onClose = (dataSource, value) => {
                this.setState({
                    dataSource: dataSource.filter(tag => tag.value !== value)
                });
            }

            render() {
                return (
                    <TagEditableGroup
                        dataSource={this.state.dataSource}
                        onClose={this.onClose}
                        onInputConfirm={this.onInputConfirm}
                    />
                );
            }
        }
        const wrapper = mount(<Demo />);

        expect(wrapper).toMatchSnapshot();
        wrapper.find('.one-tag-close-icon').at(1).simulate('click');
        expect(wrapper.find('.one-tag').length).toBe(3);
        wrapper.find('.one-tag-add-icon').at(1).simulate('click');
        wrapper.find('input').simulate('change', {target: {value: 'hello'}});
        wrapper.find('input').simulate('blur');
        expect(wrapper.find('.one-tag').length).toBe(4);
        expect(wrapper).toMatchSnapshot();

    });

    test('should support uncontrolled', () => {
        const dataSource = [
            {
                label: '标签1',
                value: '111'
            },
            {
                label: '标签2',
                value: '222'
            },
            {
                label: '标签3',
                value: '333'
            }
        ];
        const wrapper = mount(<TagEditableGroup defaultDataSource={dataSource} />);

        expect(wrapper).toMatchSnapshot();
        wrapper.find('.one-tag-close-icon').at(1).simulate('click');
        expect(wrapper.find('.one-tag').length).toBe(3);
    });
});