/**
 * @file Link.spec.js
 * <AUTHOR> <EMAIL>
 * @date 2020-09-03
 */
import {mount} from 'enzyme';
import React from 'react';
import Link from '../../../../src/components/link/index';
import TextLink from '../../../../src/components/link/textLink';
import mountTest from '../../../shared/mountTest';

describe('Link Component', () => {
    mountTest(Link);

    test('should create a Link component with `strong` props', () => {
        const wrapper = mount(<Link type="strong" />);
        expect(wrapper.props().type).toBe('strong');
        expect(wrapper).toMatchSnapshot();
    });

    test('should create a Link component with `a` tag', () => {
        const wrapper = mount(<Link isAtag />);
        expect(wrapper.find('a').length).toBe(1);
        expect(wrapper.render()).toMatchSnapshot();
    });

    test('should support disabled state', () => {
        const wrapper = mount(<Link disabled />);
        expect(wrapper.props().disabled).toBe(true);
        expect(wrapper).toMatchSnapshot();
    });

    test('should support target href props', () => {
        const wrapper = mount(<Link target="_blank" toUrl="https://baidu.com" />);
        expect(wrapper.render()).toMatchSnapshot();
    });

    // not pass
    test('should not clickable when Link is loading', () => {
        const onClick = jest.fn();
        const wrapper = mount(
            <TextLink loading onClick={onClick}>
                Link
            </TextLink>,
        );
        wrapper.find('.one-text-link').simulate('click');
    });

    // not pass
    test('should not clickable when Link toUrl is undefined', () => {
        const onClick = jest.fn();
        const logSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

        const wrapper = mount(
            <TextLink toUrl={undefined} onClick={onClick}>
                Link
            </TextLink>,
        );
        expect(wrapper.find('.one-text-link')).toHaveLength(1);
        wrapper.find('.one-text-link').simulate('click');

        expect(logSpy).toHaveBeenLastCalledWith('window open success');

    });

    test('should handle click', () => {
        const onClick = jest.fn();
        const logSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

        const wrapper = mount(<TextLink toUrl="https://baidu.com" onClick={onClick} />);
        expect(wrapper.find('.one-text-link')).toHaveLength(1);
        wrapper.find('.one-text-link').simulate('click');

        expect(logSpy).toHaveBeenLastCalledWith('window open success');
    });

    test('should handle click if a tag is disabled', () => {
        const onClick = jest.fn();
        const logSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

        const wrapper = mount(<TextLink isAtag toUrl="https://baidu.com" disabled />);
        expect(wrapper.find('.one-text-link')).toHaveLength(1);
        wrapper.find('.one-text-link').simulate('click');

        expect(logSpy).toHaveBeenLastCalledWith('window open success');
    });
});
