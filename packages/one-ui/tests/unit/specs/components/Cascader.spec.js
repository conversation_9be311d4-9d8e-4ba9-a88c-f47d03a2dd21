/**
 * @file cascader UT test
 * <AUTHOR>
 * @date 2020-09-09
 */
import {render, mount} from 'enzyme';
import React, {createRef} from 'react';
import Cascader from '../../../../src/components/cascader';
import mountTest from '../../../shared/mountTest';
import {sleep} from '../../../utils';

const options = [
    {
        value: 'zhejiang',
        label: 'Zhejiang',
        children: [
            {
                value: 'hangzhou',
                label: 'Hangzhou',
                children: [
                    {
                        value: 'xihu',
                        label: 'West Lake'
                    }
                ]
            }
        ]
    },
    {
        value: 'jiangsu',
        label: '<PERSON><PERSON>',
        children: [
            {
                value: 'nanjing',
                label: 'Nanjing',
                children: [
                    {
                        value: 'zhonghuamen',
                        label: 'Zhong Hua Men'
                    }
                ]
            }
        ]
    }
];

function filter(inputValue, path) {
    return path.some(option => option.label.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
}

describe('Cascader Component', () => {
    mountTest(Cascader);
    it('popup correctly when panel is hidden', () => {
        const wrapper = mount(<Cascader options={options} />);
        expect(render(wrapper.find('Trigger').at(0).instance().getComponent())).toMatchSnapshot();
    });
    it('popup correctly when panel is open', () => {
        const onPopupVisibleChange = jest.fn();
        const wrapper = mount(
            <Cascader options={options} onPopupVisibleChange={onPopupVisibleChange} />,
        );
        wrapper.simulate('click');
        expect(render(wrapper.find('Trigger').at(0).instance().getComponent())).toMatchSnapshot();
        expect(onPopupVisibleChange).toHaveBeenCalledWith(true);
    });
    it('support controlled mode', () => {
        const wrapper = mount(<Cascader options={options} />);
        wrapper.setProps({
            value: ['zhejiang', 'hangzhou', 'xihu']
        });
        expect(wrapper.render()).toMatchSnapshot();
    });
    it('popup correctly with defaultValue', () => {
        const wrapper = mount(<Cascader options={options} defaultValue={['zhejiang', 'hangzhou']} />);
        wrapper.simulate('click');
        expect(render(wrapper.find('Trigger').at(0).instance().getComponent())).toMatchSnapshot();
    });
    it('should support popupVisible', () => {
        const wrapper = mount(<Cascader options={options} defaultValue={['zhejiang', 'hangzhou']} />);
        expect(wrapper.find('Trigger').at(0).instance().getComponent().props.visible).toBe(false);
        wrapper.setProps({popupVisible: true});
        wrapper.update();
        expect(wrapper.find('Trigger').at(0).instance().getComponent().props.visible).toBe(true);
    });
    it('can be selected', async () => {
        const onChange = jest.fn();
        const wrapper = mount(<Cascader options={options} onChange={onChange} />);
        wrapper.simulate('click');
        let popupWrapper = mount(wrapper.find('Trigger').at(0).instance().getComponent());
        popupWrapper
            .find('.one-cascader-pane-menu')
            .at(0)
            .find('.one-cascader-pane-menu-item')
            .at(0)
            .simulate('click');
        expect(render(wrapper.find('Trigger').at(0).instance().getComponent())).toMatchSnapshot();
        popupWrapper
            .find('.one-cascader-pane-menu')
            .at(1)
            .find('.one-cascader-pane-menu-item')
            .at(0)
            .simulate('click');
        expect(render(wrapper.find('Trigger').at(0).instance().getComponent())).toMatchSnapshot();
        popupWrapper
            .find('.one-cascader-pane-menu')
            .at(2)
            .find('.one-cascader-pane-menu-item')
            .at(0)
            .simulate('click');
        expect(render(wrapper.find('Trigger').at(0).instance().getComponent())).toMatchSnapshot();
        expect(onChange).toHaveBeenCalledWith(['zhejiang', 'hangzhou', 'xihu'], expect.anything());
    });
    it('should work with `Cascader[showSearch]`', () => {
        const ref = createRef();
        const wrapper = mount(<Cascader options={options} showSearch ref={ref} />);
        wrapper.simulate('click');
        wrapper.find('input').simulate('change', {target: {value: '123'}});
        expect(ref.current.state.query).toBe('123');
        wrapper.find('input').simulate('keydown', {keyCode: 8});
        // Simulate onKeyDown will not trigger onChange by default, so the value is still '123'
        expect(ref.current.state.query).toBe('123');
    });
    it('should highlight keyword and filter when search in Cascader', () => {
        const ref = createRef();
        const wrapper = mount(<Cascader options={options} showSearch={{filter}} ref={ref} />);
        wrapper.simulate('click');
        wrapper.find('input').simulate('change', {target: {value: 'z'}});
        expect(ref.current.state.query).toBe('z');
        const popupWrapper = mount(wrapper.find('Trigger').at(0).instance().getComponent());
        expect(popupWrapper.render()).toMatchSnapshot();
    });
    it('should highlight keyword and filter when search in Cascader with same field name of label and value', () => {
        const customOptions = [
            {
                name: 'Zhejiang',
                value: 'Zhejiang',
                children: [
                    {
                        name: 'Hangzhou',
                        value: 'Hangzhou',
                        children: [
                            {
                                name: 'West Lake',
                                value: 'West Lake'
                            },
                            {
                                name: 'Xia Sha',
                                value: 'Xia Sha',
                                disabled: true
                            }
                        ]
                    }
                ]
            }
        ];
        function customFilter(inputValue, path) {
            return path.some(option => option.name.toLowerCase().indexOf(inputValue.toLowerCase()) > -1);
        }
        const ref = createRef();
        const wrapper = mount(
            <Cascader
                options={customOptions}
                fieldNames={{label: 'name', value: 'name'}}
                showSearch={{filter: customFilter}}
                ref={ref}
            />
        );
        wrapper.simulate('click');
        wrapper.find('input').simulate('change', {target: {value: 'z'}});
        expect(ref.current.state.query).toBe('z');
        const popupWrapper = mount(wrapper.find('Trigger').at(0).instance().getComponent());
        expect(popupWrapper.render()).toMatchSnapshot();
    });
    it('should render not found content', () => {
        const ref = createRef();
        const wrapper = mount(<Cascader options={options} showSearch={{filter}} ref={ref} />);
        wrapper.simulate('click');
        wrapper.find('input').simulate('change', {target: {value: '__notfoundkeyword__'}});
        expect(ref.current.state.query).toBe('__notfoundkeyword__');
        const popupWrapper = mount(wrapper.find('Trigger').at(0).instance().getComponent());
        expect(popupWrapper.render()).toMatchSnapshot();
    });

    it('should support to clear selection', async () => {
        const wrapper = mount(<Cascader options={options} defaultValue={['zhejiang', 'hangzhou']} />);
        expect(wrapper.find('.one-cascader-picker-label').text()).toBe('ZhejiangHangzhou');
        wrapper.find('.one-cascader-picker-clear').at(0).simulate('click');
        await sleep(300);
        expect(wrapper.find('.one-cascader-picker-label').length).toBe(0);
        wrapper.unmount();
    });

    it('should clear search input when clear selection', () => {
        const ref = createRef();
        const wrapper = mount(
            <Cascader options={options} defaultValue={['zhejiang', 'hangzhou']} showSearch ref={ref} />,
        );
        wrapper.simulate('click');
        wrapper.find('input').simulate('change', {target: {value: 'xxx'}});
        expect(ref.current.state.query).toBe('xxx');
        wrapper.find('.one-cascader-picker-clear').at(0).simulate('click');
        expect(ref.current.state.query).toBe('');
    });
    it('should change filtered item when options are changed', () => {
        const wrapper = mount(<Cascader options={options} showSearch={{filter}} />);
        wrapper.simulate('click');
        wrapper.find('input').simulate('change', {target: {value: 'a'}});
        expect(wrapper.find('.one-cascader-pane-menu-item').length).toBe(2);
        wrapper.setProps({options: [options[0]]});
        expect(wrapper.find('.one-cascader-pane-menu-item').length).toBe(1);
    });
    describe('`options` prop', () => {
        it('should work with same value', () => {
            const options = [{
                value: '热门',
                label: '热门',
                children: [
                    {
                        value: '杭州',
                        label: '杭州'
                    }
                ]}, {
                value: '浙江',
                label: '浙江',
                children: [
                    {
                        value: '杭州',
                        label: '杭州'
                    }
                ]}
            ];
            const onChange = jest.fn();
            const wrapper = mount(
                <Cascader options={options} onChange={onChange} />
            );
            wrapper.simulate('click');
            wrapper
                .find('.one-cascader-pane-menu')
                .at(0)
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            wrapper
                .find('.one-cascader-pane-menu')
                .at(1)
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(onChange).toHaveBeenCalledWith(['热门', '杭州'], [options[0], options[0].children[0]]);
            wrapper.simulate('click');
            wrapper
                .find('.one-cascader-pane-menu')
                .at(0)
                .find('.one-cascader-pane-menu-item')
                .at(1)
                .simulate('click');
            wrapper
                .find('.one-cascader-pane-menu')
                .at(1)
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(onChange).toHaveBeenCalledWith(['浙江', '杭州'], [options[1], options[1].children[0]]);
        });
        it('should work if it\'s empty', () => {
            let wrapper = mount(
                <Cascader options={null} />
            );
            wrapper.simulate('click');
            let text = wrapper
                .find('.one-cascader-pane-menus')
                .text();
            expect(text).toBe('未找到合适的选项');
            wrapper.setProps({
                options: []
            });
            wrapper.simulate('click');
            text = wrapper
                .find('.one-cascader-pane-menus')
                .text();
            expect(text).toBe('未找到合适的选项');
            wrapper.setProps({
                options: undefined
            });
            wrapper.simulate('click');
            text = wrapper
                .find('.one-cascader-pane-menus')
                .text();
            expect(text).toBe('未找到合适的选项');
            wrapper = mount(
                <Cascader options={null} inputIsControlled />
            );
            wrapper.simulate('click');
            text = wrapper
                .find('.one-cascader-pane-menus')
                .text();
            expect(text).toBe('未找到合适的选项');
        });
    });
    describe('`value` prop', () => {
        it('should work if it\'s empty', () => {
            const wrapper = mount(
                <Cascader options={options} value={null} />
            );
            wrapper.simulate('click');
            wrapper
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(wrapper.find('.one-cascader-pane-menu').length).toBe(2);
            wrapper.setProps({
                value: []
            });
            wrapper.update();
            wrapper
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(wrapper.find('.one-cascader-pane-menu').length).toBe(2);
            wrapper.update();
            wrapper.setProps({
                value: undefined
            });
            wrapper
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(wrapper.find('.one-cascader-pane-menu').length).toBe(2);
        });
    });
    describe('`defaultValue` prop', () => {
        it('should work if it\'s empty', () => {
            let wrapper = mount(
                <Cascader options={options} defaultValue={null} />
            );
            wrapper.simulate('click');
            wrapper
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(wrapper.find('.one-cascader-pane-menu').length).toBe(2);
            wrapper = mount(
                <Cascader options={options} defaultValue={[]} />
            );
            wrapper.simulate('click');
            wrapper
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(wrapper.find('.one-cascader-pane-menu').length).toBe(2);
            wrapper = mount(
                <Cascader options={options} defaultValue={undefined} />
            );
            wrapper.simulate('click');
            wrapper
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(wrapper.find('.one-cascader-pane-menu').length).toBe(2);
        });
    });
    describe('`loadData` prop', () => {
        it('should work with correct params', () => {
            const options = [{
                value: '热门',
                label: '热门',
                children: [
                    {
                        value: '杭州',
                        label: '杭州',
                        isLeaf: false,
                        children: []
                    }
                ]}, {
                value: '浙江',
                label: '浙江',
                children: [
                    {
                        value: '杭州',
                        label: '杭州',
                        isLeaf: false
                    }
                ]}
            ];
            const loadData = jest.fn();
            let wrapper = mount(
                <Cascader options={options} loadData={loadData} />
            );
            wrapper.simulate('click');
            wrapper
                .find('.one-cascader-pane-menu')
                .at(0)
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            wrapper
                .find('.one-cascader-pane-menu')
                .at(1)
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(loadData).toHaveBeenCalledWith([options[0], options[0].children[0]]);
            wrapper.simulate('click');
            wrapper
                .find('.one-cascader-pane-menu')
                .at(0)
                .find('.one-cascader-pane-menu-item')
                .at(1)
                .simulate('click');
            wrapper
                .find('.one-cascader-pane-menu')
                .at(1)
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(loadData).toHaveBeenCalledWith([options[1], options[1].children[0]]);
        });
        it('should work with `loadOnSelect`', () => {
            const options = [{
                value: '热门',
                label: '热门',
                children: [
                    {
                        value: '杭州',
                        label: '杭州',
                        isLeaf: false,
                        children: [{label: 'x', value: 'x'}]
                    }
                ]}, {
                value: '浙江',
                label: '浙江',
                children: [
                    {
                        value: '杭州',
                        label: '杭州',
                        isLeaf: false
                    }
                ]}
            ];
            const loadData = jest.fn();
            let wrapper = mount(
                <Cascader options={options} loadOnSelect loadData={loadData} />
            );
            wrapper.simulate('click');
            wrapper
                .find('.one-cascader-pane-menu')
                .at(0)
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            wrapper
                .find('.one-cascader-pane-menu')
                .at(1)
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(loadData).toHaveBeenCalledWith([options[0], options[0].children[0]]);
            loadData.mockReset();
            wrapper.setProps({
                loadOnSelect: false
            });
            wrapper
                .find('.one-cascader-pane-menu')
                .at(0)
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            wrapper
                .find('.one-cascader-pane-menu')
                .at(1)
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(loadData).not.toHaveBeenCalled();
        });
    });
    describe('`changeOnSelect` prop', () => {
        it('should work with correct params', () => {
            const options = [{
                value: '热门',
                label: '热门',
                children: [
                    {
                        value: '杭州',
                        label: '杭州',
                        isLeaf: false
                    }
                ]}, {
                value: '浙江',
                label: '浙江',
                children: [
                    {
                        value: '杭州',
                        label: '杭州',
                        isLeaf: false
                    }
                ]}
            ];
            const onChange = jest.fn();
            let wrapper = mount(
                <Cascader options={options} changeOnSelect onChange={onChange} />
            );
            wrapper.simulate('click');
            wrapper
                .find('.one-cascader-pane-menu')
                .at(0)
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(onChange).toHaveBeenCalledWith(['热门'], [options[0]]);
            wrapper
                .find('.one-cascader-pane-menu')
                .at(1)
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(onChange).toHaveBeenCalledWith(['热门', '杭州'], [options[0], options[0].children[0]]);
            wrapper.simulate('click');
            wrapper
                .find('.one-cascader-pane-menu')
                .at(0)
                .find('.one-cascader-pane-menu-item')
                .at(1)
                .simulate('click');
            expect(onChange).toHaveBeenCalledWith(['浙江'], [options[1]]);
            wrapper
                .find('.one-cascader-pane-menu')
                .at(1)
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(onChange).toHaveBeenCalledWith(['浙江', '杭州'], [options[1], options[1].children[0]]);
        });
    });
});