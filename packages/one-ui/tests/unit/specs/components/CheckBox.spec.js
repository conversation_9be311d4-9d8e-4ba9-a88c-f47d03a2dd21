/**
 * @file CheckBox.spec.js
 * <AUTHOR> <EMAIL>
 * @date 2020-09-03
 */
import {mount, render} from 'enzyme';
import React from 'react';
import CheckBox from '../../../../src/components/checkbox/index';
import ProviderConfig from '../../../../src/components/providerConfig/index';
import mountTest from '../../../../tests/shared/mountTest';
import focusTest from '../../../../tests/shared/focusTest';


describe('CheckBox Component', () => {
    mountTest(CheckBox);
    focusTest(CheckBox);

    test('responses hover events', () => {
        const onMouseEnter = jest.fn();
        const onMouseLeave = jest.fn();

        const wrapper = mount(<CheckBox onMouseEnter={onMouseEnter} onMouseLeave={onMouseLeave} />);

        wrapper.find('label').simulate('mouseenter');
        expect(onMouseEnter).toHaveBeenCalled();

        wrapper.find('label').simulate('mouseleave');
        expect(onMouseLeave).toHaveBeenCalled();
    });
});

describe('CheckBox Button Component', () => {
    mountTest(CheckBox.Button);
    test('should work basically', () => {
        const onChange = jest.fn();
        const wrapper = mount(
            <CheckBox.Group onChange={onChange}>
                <CheckBox.Button value={1} key={1} >Apple</CheckBox.Button>,
                <CheckBox.Button value={2} key={2} >Pear</CheckBox.Button>,
                <CheckBox.Button value={3} key={3} >Orange</CheckBox.Button>
            </CheckBox.Group>,
        );
        wrapper
            .find('.one-checkbox-button-input')
            .at(0)
            .simulate('change');
        expect(onChange).toHaveBeenCalledWith([1]);
        wrapper
            .find('.one-checkbox-button-input')
            .at(1)
            .simulate('change');
        expect(onChange).toHaveBeenCalledWith([1, 2]);
        wrapper
            .find('.one-checkbox-button-input')
            .at(2)
            .simulate('change');
        expect(onChange).toHaveBeenCalledWith([1, 2, 3]);
        wrapper
            .find('.one-checkbox-button-input')
            .at(1)
            .simulate('change');
        expect(onChange).toHaveBeenCalledWith([1, 3]);
    });
});

describe('CheckBox Group Component', () => {
    mountTest(CheckBox.Group);

    test('should work basically', () => {
        const onChange = jest.fn();
        const wrapper = mount(
            <CheckBox.Group options={['Apple', 'Pear', 'Orange']} onChange={onChange} />,
        );
        wrapper
            .find('.one-checkbox-input')
            .at(0)
            .simulate('change');
        expect(onChange).toHaveBeenCalledWith(['Apple']);
        wrapper
            .find('.one-checkbox-input')
            .at(1)
            .simulate('change');
        expect(onChange).toHaveBeenCalledWith(['Apple', 'Pear']);
        wrapper
            .find('.one-checkbox-input')
            .at(2)
            .simulate('change');
        expect(onChange).toHaveBeenCalledWith(['Apple', 'Pear', 'Orange']);
        wrapper
            .find('.one-checkbox-input')
            .at(1)
            .simulate('change');
        expect(onChange).toHaveBeenCalledWith(['Apple', 'Orange']);
    });

    test('does not trigger onChange callback of both Checkbox and CheckboxGroup when CheckboxGroup is disabled', () => {
        const onChangeGroup = jest.fn();

        const options = [{label: 'Apple', value: 'Apple'}, {label: 'Pear', value: 'Pear'}];

        const groupWrapper = mount(
            <CheckBox.Group options={options} onChange={onChangeGroup} disabled />,
        );
        groupWrapper
            .find('.one-checkbox-input')
            .at(0)
            .simulate('change');
        expect(onChangeGroup).not.toHaveBeenCalled();
        groupWrapper
            .find('.one-checkbox-input')
            .at(1)
            .simulate('change');
        expect(onChangeGroup).not.toHaveBeenCalled();
    });

    test('does not prevent onChange callback from Checkbox when CheckboxGroup is not disabled', () => {
        const onChangeGroup = jest.fn();

        const options = [
            {label: 'Apple', value: 'Apple'},
            {label: 'Orange', value: 'Orange', disabled: true}
        ];

        const groupWrapper = mount(<CheckBox.Group options={options} onChange={onChangeGroup} />);
        groupWrapper
            .find('.one-checkbox-input')
            .at(0)
            .simulate('change');
        expect(onChangeGroup).toHaveBeenCalledWith(['Apple']);
        groupWrapper
            .find('.one-checkbox-input')
            .at(1)
            .simulate('change');
        expect(onChangeGroup).toHaveBeenCalledWith(['Apple']);
    });

    test('passes prefixCls down to checkbox', () => {
        const options = [{label: 'Apple', value: 'Apple'}, {label: 'Orange', value: 'Orange'}];

        const wrapper = render(<ProviderConfig prefixCls="my"><CheckBox.Group options={options} /></ProviderConfig>);

        expect(wrapper).toMatchSnapshot();
    });

    test('should be controlled by value', () => {
        const options = [{label: 'Apple', value: 'Apple'}, {label: 'Orange', value: 'Orange'}];
        const ref = React.createRef();
        const wrapper = mount(<CheckBox.Group ref={ref} options={options} />);

        expect(ref.current.state.value).toEqual([]);
        wrapper.setProps({value: ['Apple']});
        expect(ref.current.state.value).toEqual(['Apple']);
    });
});
