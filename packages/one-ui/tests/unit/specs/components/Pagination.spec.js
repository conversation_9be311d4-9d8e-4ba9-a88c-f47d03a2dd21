/**
 * <AUTHOR>
 * @file 分页器的单元测试
 * date 2020-08-24
 */
import {mount, render} from 'enzyme';
import React, {PureComponent} from 'react';
import Pagination from '../../../../src/components/pagination';
import mountTest from '../../../shared/mountTest';
import ProviderConfig from '../../../../src/components/providerConfig';

describe('Pagination', () => {
    mountTest(Pagination);
    it('should be rendered correctly', () => {
        const wrapper = mount(
            <ProviderConfig size="small">
                <Pagination total={200} />
            </ProviderConfig>,
        );
        expect(render(wrapper)).toMatchSnapshot();
    });
    it('should be rendered correctly', () => {
        const wrapper = mount(
            <Pagination total={200} />
        );
        expect(render(wrapper)).toMatchSnapshot();
    });
    it('should be rendered correctly', () => {
        const wrapper = mount(
            <Pagination total={200} defaultPageNo={1} defaultPageSize={10} />
        );
        expect(render(wrapper)).toMatchSnapshot();
    });
    it('should pass disabled to prev and next buttons', () => {
        const wrapper = mount(<Pagination defaultPageNo={1} total={50} showPageJumper={false} />);
        expect(wrapper.find('button').at(0).props().disabled).toBe(true);
    });
    it('should autometically be small when size is not specified', () => {
        const wrapper = mount(<Pagination showSizeChange={false} />);
        expect(wrapper.find('div').at(0).hasClass('one-pagination-medium')).toBe(true);
    });
    it('hide pagination only one page', () => {
        const wrapper = mount(<Pagination hideOnSinglePage />);
        expect(render(wrapper)).toMatchSnapshot();
    });
    it('should onChange called when pageSize change', () => {
        const onChange = jest.fn();
        const onShowSizeChange = jest.fn();
        const wrapper = mount(
            <Pagination
                defaultPageNo={1}
                total={500}
                onPageNoChange={onChange}
                onPageSizeChange={onShowSizeChange}
            />,
        );
        wrapper.find('.one-select').simulate('click');
        expect(document.body.getElementsByClassName('one-select-dropdown-menu-item').length).toBe(3);
        wrapper.find('.one-select-dropdown-menu-item').at(1).simulate('click');
        expect(onShowSizeChange).toHaveBeenCalledWith({
            target: {
                value: 50
            }
        });
    });
    it('should onChange called when page no change', () => {
        const onPageNoChange = jest.fn();
        const wrapper = mount(
            <Pagination
                defaultPageNo={1}
                total={500}
                onPageNoChange={onPageNoChange}
            />,
        );
        expect(wrapper.find('button').at(1).hasClass('one-pagination-pager-item-active')).toBe(true);
        wrapper.find('button').at(3).find('span').simulate('click');
        expect(onPageNoChange).toHaveBeenCalledWith({
            target: {
                value: 3
            }
        });
    });
    it('should onChange called when page no change', () => {
        const onPageNoChange = jest.fn();
        const wrapper = mount(
            <Pagination
                defaultPageNo={1}
                total={500}
                onPageNoChange={onPageNoChange}
            />,
        );
        expect(wrapper.find('button').at(1).hasClass('one-pagination-pager-item-active')).toBe(true);
        wrapper.find('button').at(3).find('span').simulate('click');
        expect(onPageNoChange).toHaveBeenCalledWith({
            target: {
                value: 3
            }
        });
    });
    it('should onChange called when page no change', () => {
        const onPageNoChange = jest.fn();
        const wrapper = mount(
            <Pagination
                defaultPageNo={1}
                total={500}
                onPageNoChange={onPageNoChange}
            />,
        );
        expect(wrapper.find('button').at(1).hasClass('one-pagination-pager-item-active')).toBe(true);
        wrapper.find('button').at(3).find('span').simulate('click');
        wrapper.find('button').at(0).find('span').simulate('click');
        expect(onPageNoChange).toHaveBeenCalledWith({
            target: {
                value: 2
            }
        });
    });
    it('should onChange called when page no change', () => {
        const onPageNoChange = jest.fn();
        const wrapper = mount(
            <Pagination
                total={200}
                onPageNoChange={onPageNoChange}
            />,
        );
        wrapper.find('button').at(8).find('span').simulate('click');
        wrapper.find('button').at(6).find('span').simulate('click');
        wrapper.find('button').at(2).find('span').simulate('click');
        wrapper.find('input').simulate('change', {target: {value: '10'}});
        wrapper.find('.one-pagination-jumper-confirm').at(0).simulate('click');
        expect(onPageNoChange).toHaveBeenCalledWith({
            target: {
                value: 10
            }
        });
    });
    it('should onChange called when page no change', () => {
        const onPageNoChange = jest.fn();
        const wrapper = mount(
            <Pagination
                defaultPageNo={1}
                total={500}
                onPageNoChange={onPageNoChange}
            />,
        );
        expect(wrapper.find('button').at(1).hasClass('one-pagination-pager-item-active')).toBe(true);
        wrapper.find('button').at(3).find('span').simulate('click');
        wrapper.find('input').simulate('change', {target: {value: -1}});
        wrapper.find('.one-pagination-jumper-confirm').at(0).simulate('click');
        expect(onPageNoChange).toHaveBeenCalledWith({
            target: {
                value: 1
            }
        });
    });
    it('should onChange called when page no change', () => {
        const onPageNoChange = jest.fn();
        const wrapper = mount(
            <Pagination
                defaultPageNo={1}
                total={500}
                onPageNoChange={onPageNoChange}
            />,
        );
        expect(wrapper.find('button').at(1).hasClass('one-pagination-pager-item-active')).toBe(true);
        wrapper.find('button').at(3).find('span').simulate('click');
        wrapper.find('input').simulate('change', {target: {value: 100}});
        wrapper.find('.one-pagination-jumper-confirm').at(0).simulate('click');
        expect(onPageNoChange).toHaveBeenCalledWith({
            target: {
                value: 25
            }
        });
    });
    it('should onChange called when page no change', () => {
        const onPageNoChange = jest.fn();
        const wrapper = mount(
            <Pagination
                pageNo={5}
                total={200}
                onPageNoChange={onPageNoChange}
            />,
        );
        expect(wrapper.find('button').at(4).hasClass('one-pagination-pager-item-active')).toBe(true);
        wrapper.find('button').at(3).find('span').simulate('click');
        expect(onPageNoChange).toHaveBeenCalledWith({
            target: {
                value: 4
            }
        });
    });
    it('should onChange called when page no change', () => {
        const onPageNoChange = jest.fn();
        const wrapper = mount(
            <Pagination
                pageNo={500}
                total={200}
                onPageNoChange={onPageNoChange}
            />,
        );
        wrapper.find('button').at(3).find('span').simulate('click');
        expect(onPageNoChange).toHaveBeenCalledWith({
            target: {
                value: 6
            }
        });
    });
    it('total change', () => {
        class Normal extends PureComponent {
            state = {
                pageNo: 1,
                pageSize: 10,
                total: 200
            };
            onShowSizeChange = e => {
                this.setState({
                    pageSize: +e.target.value,
                    pageNo: 1,
                    total: 200
                });
            }
            componentDidMount() {
                this.setState({
                    total: 500,
                    pageSize: 20,
                    pageNo: 2
                });
            }
            onChange = e => {
                this.setState({
                    pageNo: +e.target.value
                });
            }
            render() {
                const state = this.state;
                return (
                    <Pagination
                        onPageSizeChange={this.onShowSizeChange}
                        total={state.total}
                        pageNo={state.pageNo}
                        pageSize={state.pageSize}
                        pageSizeOptions={['10', '20', '30']}
                        onPageNoChange={this.onChange}
                    />
                );
            }
        }
        const wrapper = mount(
            <Normal />,
        );
        expect(render(wrapper)).toMatchSnapshot();
    });
    it('total change2', () => {
        // eslint-disable-next-line react/no-multi-comp
        class Normal2 extends PureComponent {
            state = {
                pageNo: 1,
                pageSize: 10,
                total: 200
            };
            onShowSizeChange = e => {
                this.setState({
                    pageSize: +e.target.value,
                    pageNo: 1,
                    total: 200
                });
            }
            componentDidMount() {
                this.setState({
                    total: 500,
                    pageSize: 20,
                    pageNo: 2
                });
            }
            onChange = e => {
                this.setState({
                    pageNo: +e.target.value
                });
            }
            render() {
                const state = this.state;
                return (
                    <Pagination
                        onPageSizeChange={this.onShowSizeChange}
                        total={state.total}
                        pageSize={state.pageSize}
                        pageSizeOptions={['10', '20', '30']}
                        onPageNoChange={this.onChange}
                    />
                );
            }
        }
        const wrapper = mount(
            <Normal2 />,
        );
        expect(render(wrapper)).toMatchSnapshot();
    });
    it('should onChange called when page no change', () => {
        const onPageNoChange = jest.fn();
        const wrapper = mount(
            <Pagination
                defaultPageNo={1}
                total={500}
                onPageNoChange={onPageNoChange}
            />,
        );
        expect(wrapper.find('button').at(1).hasClass('one-pagination-pager-item-active')).toBe(true);
        wrapper.find('button').at(3).find('span').simulate('click');
        wrapper.find('input').simulate('change', {target: {value: 100}});
        wrapper.find('input').simulate('keyDown', {keyCode: 13});
        expect(onPageNoChange).toHaveBeenCalledWith({
            target: {
                value: 25
            }
        });
    });
    it('should onChange called when page no change', () => {
        const onPageNoChange = jest.fn();
        const wrapper = mount(
            <Pagination
                defaultPageNo={1}
                total={200}
                onPageNoChange={onPageNoChange}
            />,
        );
        expect(wrapper.find('button').at(1).hasClass('one-pagination-pager-item-active')).toBe(true);
        wrapper.find('button').at(7).find('span').simulate('click');
        expect(onPageNoChange).toHaveBeenCalledWith({
            target: {
                value: 10
            }
        });
    });
});