/**
 * @file datePicker UT
 * <AUTHOR>
 * @date 2020-09-10
 */
import {mount} from 'enzyme';
import React from 'react';
import DatePicker from '../../../../src/components/datePicker/index';
import RDatePicker from '../../../../src/components/datePicker/datePicker';
import RRangePicker from '../../../../src/components/datePicker/rangePicker';
import RMonthPicker from '../../../../src/components/datePicker/monthPicker';
import mountTest from '../../../shared/mountTest';

describe('DatePicker Component', () => {
    mountTest(DatePicker);
    it('should open layer when click button', () => {
        const _Date = Date;
        const date = new Date('2022-03-16 08:00:00');
        Date = jest.fn((...args) => args.length ? new _Date(...args) : date);
        Date.now = jest.fn(() => date.getTime());
        const wrapper = mount(<DatePicker />);
        wrapper.find('.one-date-picker-title').at(1).simulate('click');
        const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        expect(popupWrapper.render()).toMatchSnapshot();
    });
    it('should open layer when click button with default Value', () => {
        const wrapper = mount(<DatePicker defaultValue="2019-09-01" />);
        wrapper.find('.one-button').at(0).simulate('click');
        const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        expect(popupWrapper.render()).toMatchSnapshot();
    });
    it('should open layer when click button with controlled Value', () => {
        const wrapper = mount(<DatePicker value="2019-09-01" />);
        wrapper.find('.one-button').at(0).simulate('click');
        const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        wrapper.setProps({
            value: '2019-09-02'
        });
        wrapper.update();
        expect(popupWrapper.render()).toMatchSnapshot();
    });
    it('should open layer when click button with validateMinDate and validateMaxDate', () => {
        const wrapper = mount(
            <DatePicker value="2019-09-01" validateMinDate="2020-08-01" validateMaxDate="2020-10-11" />
        );
        wrapper.find('.one-button').at(0).simulate('click');
        const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        wrapper.setProps({
            validateMinDate: '2020-08-02',
            validateMaxDate: '2020-10-14'
        });
        wrapper.update();
        expect(popupWrapper.render()).toMatchSnapshot();
    });
    it('should open layer when click button with visible controlled', () => {
        const ref = React.createRef();
        const wrapper = mount(<DatePicker ref={ref} visible={false} />);
        wrapper.find('.one-button').at(0).simulate('click');
        mount(wrapper.find('Trigger').instance().getComponent());
        wrapper.setProps({
            visible: true
        });
        wrapper.update();
        expect(ref.current.state.visible).toBe(true);
    });
    it('should open layer when click button with visible controlled', () => {
        const onChange = jest.fn();
        const wrapper = mount(<DatePicker onChange={onChange} defaultValue="2020-09-10" />);
        wrapper.find('.one-button').at(0).simulate('click');
        const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        popupWrapper
            .find('.one-date-picker-body-month-item')
            .at(0)
            .find('span')
            .at(0)
            .simulate('click');
        expect(onChange).toHaveBeenCalledWith('2020/08/31');
    });
    it('should delete is ok', () => {
        const onDelete = jest.fn();
        const wrapper = mount(<DatePicker onDelete={onDelete} defaultValue="2020-09-10" showDeleteIcon />);
        wrapper.find('.one-button').at(0).simulate('mouseEnter');
        expect(wrapper.find('.one-date-picker-button-icon-close').exists()).toBe(true);
        wrapper.find('.one-date-picker-button-icon-close').at(0).simulate('click');
        expect(onDelete).toHaveBeenCalledWith();
    });
    it('validat is Ok with error', () => {
        const validator = () => {
            return 'error';
        };
        const ref = React.createRef();
        // eslint-disable-next-line react/jsx-no-bind
        const wrapper = mount(<RDatePicker ref={ref} validator={validator} value="2020-09-10" />);
        wrapper.find('.one-button').at(0).simulate('click');
        const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        popupWrapper
            .find('.one-date-picker-body-month-item')
            .at(0)
            .find('span')
            .at(0)
            .simulate('click');
        expect(ref.current.state.errorMessage).toEqual('error');
    });
    it('validat is Ok with no error', () => {
        const validator = () => {
            return '';
        };
        // eslint-disable-next-line react/jsx-no-bind
        const wrapper = mount(<DatePicker validator={validator} defaultValue="2020-09-10" />);
        wrapper.find('.one-button').at(0).simulate('click');
        const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        popupWrapper
            .find('.one-date-picker-body-month-item')
            .at(0)
            .find('span')
            .at(0)
            .simulate('click');
        expect(wrapper.find('.one-date-picker-error-message').exists()).toBe(false);
    });
    it('disabled', () => {
        const wrapper = mount(<DatePicker disabled defaultValue="2020-09-10" />);
        wrapper.find('.one-button').at(0).simulate('click');
        expect(wrapper.render()).toMatchSnapshot();
    });
});

describe('DatePicker.RangePicker Component', () => {
    mountTest(DatePicker.RangePicker);
    it('should open layer when click button', () => {
        const _Date = Date;
        const date = new Date('2022-03-16 08:00:00');
        Date = jest.fn((...args) => args.length ? new _Date(...args) : date);
        Date.now = jest.fn(() => date.getTime());
        const wrapper = mount(<DatePicker.RangePicker />);
        wrapper.find('.one-date-picker-title').at(1).simulate('click');
        const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        expect(popupWrapper.render()).toMatchSnapshot();
    });
    it('should open layer when click button with default Value', () => {
        const wrapper = mount(<DatePicker.RangePicker defaultValue={['2019-09-01', '2019-09-10']} />);
        wrapper.find('.one-button').at(0).simulate('click');
        const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        expect(popupWrapper.render()).toMatchSnapshot();
    });
    it('should open layer when click button with controlled Value', () => {
        const wrapper = mount(<DatePicker.RangePicker value={['2019-09-01', '2019-09-10']} />);
        wrapper.find('.one-button').at(0).simulate('click');
        const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        wrapper.setProps({
            value: ['2019-09-01', '2019-09-13']
        });
        wrapper.update();
        expect(popupWrapper.render()).toMatchSnapshot();
    });
    it('should open layer when click button with validateMinDate and validateMaxDate', () => {
        const wrapper = mount(
            <DatePicker.RangePicker
                value={['2019-10-01', '2019-10-07']}
                validateMinDate="2019-09-01"
                validateMaxDate="2020-10-11"
            />);
        wrapper.find('.one-button').at(0).simulate('click');
        const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        wrapper.setProps({
            validateMinDate: '2018-08-02',
            validateMaxDate: '2020-10-14'
        });
        wrapper.update();
        expect(popupWrapper.render()).toMatchSnapshot();
    });
    it('should open layer when click button with visible controlled', () => {
        const wrapper = mount(<DatePicker.RangePicker visible={false} />);
        wrapper.find('.one-button').at(0).simulate('click');
        const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        wrapper.setProps({
            visible: true
        });
        wrapper.update();
        expect(popupWrapper.render()).toMatchSnapshot();
    });
    it('should open layer when click button with visible controlled', () => {
        const onChange = jest.fn();
        const wrapper = mount(
            <DatePicker.RangePicker
                onChange={onChange}
                defaultValue={['2020-09-08', '2020-09-10']}
            />
        );
        wrapper.find('.one-button').at(0).simulate('click');
        const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        popupWrapper
            .find('.one-date-picker-body-month-item')
            .at(0)
            .find('span')
            .at(0)
            .simulate('click');
        popupWrapper
            .find('.one-date-picker-body-month-item')
            .at(1)
            .find('span')
            .at(0)
            .simulate('click');
        expect(onChange).toHaveBeenCalledWith(['2020/07/28', '2020/08/31']);
    });
    it('validat is Ok with error', () => {
        const validator = () => {
            return 'error';
        };
        const ref = React.createRef();
        // eslint-disable-next-line react/jsx-no-bind
        const wrapper = mount(<RRangePicker ref={ref} validator={validator} value={['2020-09-08', '2020-09-11']} />);
        wrapper.find('.one-button').at(0).simulate('click');
        const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        popupWrapper
            .find('.one-date-picker-body-month-item')
            .at(0)
            .find('span')
            .at(0)
            .simulate('click');
        popupWrapper
            .find('.one-date-picker-body-month-item')
            .at(1)
            .find('span')
            .at(0)
            .simulate('click');
        expect(ref.current.state.errorMessage).toEqual('error');
    });
    it('should delete is ok', () => {
        const onDelete = jest.fn();
        const wrapper = mount(<DatePicker.RangePicker
            onDelete={onDelete}
            defaultValue={['2020-09-10', '2020-09-11']}
            showDeleteIcon
        />);
        wrapper.find('.one-button').at(0).simulate('mouseEnter');
        expect(wrapper.find('.one-date-picker-button-icon-close').exists()).toBe(true);
        wrapper.find('.one-date-picker-button-icon-close').at(0).simulate('click');
        expect(onDelete).toHaveBeenCalledWith();
    });
    it('disabled', () => {
        const wrapper = mount(<DatePicker.RangePicker disabled defaultValue={['2020-09-10', '2020-09-11']} />);
        wrapper.find('.one-button').at(0).simulate('click');
        expect(wrapper.render()).toMatchSnapshot();
    });
});

describe('DatePicker.MonthPicker Component', () => {
    mountTest(DatePicker.MonthPicker);
    it('should open layer when click button', () => {
        const wrapper = mount(<DatePicker.MonthPicker />);
        wrapper.find('.one-date-picker-title').at(1).simulate('click');
        const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        expect(popupWrapper.render()).toMatchSnapshot();
    });
    it('should open layer when click button with default Value', () => {
        const wrapper = mount(<DatePicker.MonthPicker defaultValue="2019-09" />);
        wrapper.find('.one-button').at(0).simulate('click');
        const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        expect(popupWrapper.render()).toMatchSnapshot();
    });
    it('should open layer when click button with controlled Value', () => {
        const wrapper = mount(<DatePicker.MonthPicker value="2019-09" />);
        wrapper.find('.one-button').at(0).simulate('click');
        const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        wrapper.setProps({
            value: '2019-08'
        });
        wrapper.update();
        expect(popupWrapper.render()).toMatchSnapshot();
    });
    it('should open layer when click button with validateMinDate and validateMaxDate', () => {
        const wrapper = mount(<DatePicker.MonthPicker validateMinDate="2020-08" validateMaxDate="2020-10" />);
        wrapper.find('.one-button').at(0).simulate('click');
        const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        wrapper.setProps({
            validateMinDate: '2020-07',
            validateMaxDate: '2020-11'
        });
        wrapper.update();
        expect(popupWrapper.render()).toMatchSnapshot();
    });
    it('should open layer when click button with visible controlled', () => {
        const wrapper = mount(<DatePicker.MonthPicker visible={false} />);
        wrapper.find('.one-button').at(0).simulate('click');
        const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        wrapper.setProps({
            visible: true
        });
        wrapper.update();
        expect(popupWrapper.render()).toMatchSnapshot();
    });
    it('should open layer when click button with visible controlled', () => {
        const onChange = jest.fn();
        const wrapper = mount(<DatePicker.MonthPicker onChange={onChange} defaultValue="2020-09" />);
        wrapper.find('.one-button').at(0).simulate('click');
        const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        popupWrapper
            .find('.one-date-picker-month-container-item')
            .at(0)
            .simulate('click');
        expect(onChange).toHaveBeenCalledWith('2020-01');
    });
    it('should delete is ok', () => {
        const onDelete = jest.fn();
        const wrapper = mount(<DatePicker.MonthPicker onDelete={onDelete} defaultValue="2020-09" showDeleteIcon />);
        wrapper.find('.one-button').at(0).simulate('mouseEnter');
        expect(wrapper.find('.one-date-picker-button-icon-close').exists()).toBe(true);
        wrapper.find('.one-date-picker-button-icon-close').at(0).simulate('click');
        expect(onDelete).toHaveBeenCalledWith();
    });
    it('validat is Ok with error', () => {
        const validator = () => {
            return 'error';
        };
        const ref = React.createRef();
        // eslint-disable-next-line react/jsx-no-bind
        const wrapper = mount(<RMonthPicker ref={ref} validator={validator} value="2020-09-10" />);
        wrapper.find('.one-button').at(0).simulate('click');
        const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        popupWrapper
            .find('.one-date-picker-month-container-item')
            .at(0)
            .simulate('click');
        expect(ref.current.state.errorMessage).toEqual('error');
    });
    it('validat is Ok with no error', () => {
        const validator = () => {
            return '';
        };
        // eslint-disable-next-line react/jsx-no-bind
        const wrapper = mount(<RMonthPicker validator={validator} defaultValue="2020-09-10" />);
        wrapper.find('.one-button').at(0).simulate('click');
        const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        popupWrapper
            .find('.one-date-picker-month-container-item')
            .at(0)
            .simulate('click');
        expect(wrapper.find('.one-date-picker-error-message').exists()).toBe(false);
    });
    it('disabled', () => {
        const wrapper = mount(<DatePicker.MonthPicker disabled defaultValue="2020-09" />);
        wrapper.find('.one-button').at(0).simulate('click');
        expect(wrapper.render()).toMatchSnapshot();
    });
});
