/**
 * @file anchor的单测
 * <AUTHOR>
 * @date 2020-08-31
 */
import React, {Component} from 'react';
import {mount, render, shallow} from 'enzyme';
import {sleep} from '../../../utils';
import Anchor from '../../../../src/components/anchor/index';
import mountTest from '../../../shared/mountTest';

const {Link} = Anchor;

function createGetContainer(id) {
    return () => {
        const container = document.getElementById(id);
        if (container == null) {
            throw new Error();
        }
        return container;
    };
}

function createDiv() {
    const root = document.createElement('div');
    document.body.appendChild(root);
    return root;
}

let idCounter = 0;
const getHashUrl = () => `Anchor-API-${idCounter++}`;

describe('Anchor Component', () => {
    mountTest(Anchor);
    const getBoundingClientRectMock = jest.spyOn(
        HTMLHeadingElement.prototype,
        'getBoundingClientRect',
    );
    const getClientRectsMock = jest.spyOn(HTMLHeadingElement.prototype, 'getClientRects');
    beforeAll(() => {
        getBoundingClientRectMock.mockReturnValue({
            width: 100,
            height: 100,
            top: 1000
        });
        getClientRectsMock.mockReturnValue({length: 1});
    });
    afterAll(() => {
        getBoundingClientRectMock.mockRestore();
        getClientRectsMock.mockRestore();
    });
    test('should be rendered correctly', () => {
        const wrapper = mount(
            <Anchor size="small">
                <Link href="#demo-instances" title="Basic demo" key="1" />
                <Link href="#demo-code-operator" title="Code demo" key="2" />
                <Link href="#api" title="API" key="3">
                    <Link href="#api-header-title" title="Props" key="4" />
                </Link>
            </Anchor>
        );
        expect(render(wrapper)).toMatchSnapshot();
        expect(wrapper.find('.one-anchor').at(0).hasClass('one-anchor-small')).toBe(true);
    });
    test('should be rendered correctly with no affix', () => {
        const wrapper = mount(
            <Anchor size="small" affix={false}>
                <Link href="#demo-instances" title="Basic demo" key="1" />
                <Link href="#demo-code-operator" title="Code demo" key="2" />
                <Link href="#api" title="API" key="3">
                    <Link href="#api-header-title" title="Props" key="4" />
                </Link>
            </Anchor>
        );
        expect(render(wrapper)).toMatchSnapshot();
        expect(wrapper.find('.one-anchor').at(0).hasClass('one-anchor-small')).toBe(true);
    });
    test('should be rendered correctly 2', () => {
        const wrapper = mount(
            <Anchor size="small" offsetTop={10}>
                <Link href="#demo-instances" key="1" />
                <Link isALabel href="#demo-code-operator" key="2" />
                <Link href="#api" key="3">
                    <Link href="#api-header-title" key="4" />
                </Link>
            </Anchor>
        );
        expect(render(wrapper)).toMatchSnapshot();
        expect(wrapper.find('.one-anchor').at(0).hasClass('one-anchor-small')).toBe(true);
    });
    test('should be rendered correctly 3', () => {
        global.document = undefined;
        const wrapper = mount(
            <Anchor size="small">
                <Link href="#demo-instances" title="Basic demo" key="1" />
                <Link href="#demo-code-operator" title="Code demo" key="2" />
                <Link href="#api" title="API" key="3">
                    <Link href="#api-header-title" title="Props" key="4" />
                </Link>
            </Anchor>
        );
        expect(render(wrapper)).toMatchSnapshot();
        expect(wrapper.find('.one-anchor').at(0).hasClass('one-anchor-small')).toBe(true);
    });
    test('should be rendered correctly', () => {
        const wrapper = mount(
            <Anchor size="medium">
                <Link href="#demo-instances" title="Basic demo" key="1" />
                <Link href="#demo-code-operator" title="Code demo" key="2" />
                <Link href="#api" title="API" key="3">
                    <Link href="#api-header-title" title="Props" key="4" />
                </Link>
            </Anchor>
        );
        expect(render(wrapper)).toMatchSnapshot();
        expect(wrapper.find('.one-anchor').at(0).hasClass('one-anchor-medium')).toBe(true);
    });
    test('Anchor render perfectly', () => {
        const hash = getHashUrl();
        const ref = React.createRef();
        const wrapper = mount(
            <Anchor ref={ref}>
                <Link isALabel href={`#${hash}`} title={hash} />
            </Anchor>,
        );
        wrapper.find(`a[href="#${hash}"]`).simulate('click');
        ref.current.handleScroll();
        expect(ref.current.state).not.toBe(null);
    });
    test('Anchor render perfectly for complete href - click', () => {
        const hash = getHashUrl();
        const ref = React.createRef();
        const wrapper = mount(
            <Anchor ref={ref}>
                <Link isALabel href={`http://www.example.com/#${hash}`} title={hash} />
            </Anchor>,
        );
        wrapper.find(`a[href="http://www.example.com/#${hash}"]`).simulate('click');
        expect(ref.current.state.activeLink).toBe(`http://www.example.com/#${hash}`);
    });
    test('Anchor render perfectly for complete href - hash router', async () => {
        const root = createDiv();
        const ref = React.createRef();
        mount(<div id="/faq?locale=en#Q1">Q1</div>, {attachTo: root});
        const wrapper = mount(
            <Anchor ref={ref}>
                <Link isALabel href="/#/faq?locale=en#Q1" title="Q1" />
            </Anchor>,
        );

        ref.current.handleScrollTo('/#/faq?locale=en#Q1');
        expect(ref.current.state.activeLink).toBe('/#/faq?locale=en#Q1');
    });
    test('Anchor render perfectly for complete href - scroll', () => {
        const hash = getHashUrl();
        const root = createDiv();
        const ref = React.createRef();
        mount(<div id={hash}>Hello</div>, {attachTo: root});
        const wrapper = mount(
            <Anchor ref={ref}>
                <Link isALabel href={`http://www.example.com/#${hash}`} title={hash} />
            </Anchor>,
        );
        ref.current.handleScroll();
        expect(ref.current.state.activeLink).toBe(`http://www.example.com/#${hash}`);
    });
    test('Anchor render perfectly for complete href - scrollTo', async () => {
        const hash = getHashUrl();
        const root = createDiv();
        const ref = React.createRef();
        mount(<div id={`#${hash}`}>Hello</div>, {attachTo: root});
        const wrapper = mount(
            <Anchor ref={ref}>
                <Link isALabel href={`##${hash}`} title={hash} />
            </Anchor>,
        );
        ref.current.handleScrollTo(`##${hash}`);
        expect(ref.current.state.activeLink).toBe(`##${hash}`);
    });
    it('should unregister link when unmount children', async () => {
        const hash = getHashUrl();
        const ref = React.createRef();
        const wrapper = mount(
            <Anchor ref={ref}>
                <Link isALabel href={`#${hash}`} title={hash} />
            </Anchor>,
        );
        expect(ref.current.links).toEqual([`#${hash}`]);
        wrapper.setProps({children: null});
        expect(ref.current.links).toEqual([]);
    });
    it('should remove listener when unmount', async () => {
        const hash = getHashUrl();
        const ref = React.createRef();
        const wrapper = mount(
            <Anchor ref={ref}>
                <Link href={`#${hash}`} title={hash} />
            </Anchor>,
        );
        const removeListenerSpy = jest.spyOn(ref.current.scrollEvent, 'remove');
        wrapper.unmount();
        expect(removeListenerSpy).toHaveBeenCalled();
    });
    it('update props', () => {
        class Normal extends Component {
            state = {
                link: 'https://www.baidu.com'
            }

            componentDidMount() {
                this.setState({
                    link: 'https://www.baidu2.com'
                });
            }

            render() {
                return (
                    <Anchor>
                        <Link href={this.state.link} title={this.state.link} />
                    </Anchor>
                );
            }
        };
        const wrapper = mount(
            <Normal />
        );
        expect(render(wrapper)).toMatchSnapshot();
    });
    it('Anchor onClick event', () => {
        const hash = getHashUrl();
        let event;
        let link;
        const handleClick = (
            e,
            _link,
        ) => {
            event = e;
            link = _link;
        };
        const href = `#${hash}`;
        const title = hash;
        const ref = React.createRef();
        const wrapper = mount(
            <Anchor ref={ref} onClick={handleClick}>
                <Link isALabel href={href} title={title} />
            </Anchor>,
        );
        wrapper.find(`a[href="${href}"]`).simulate('click');
        ref.current.handleScroll();
        expect(event).not.toBe(undefined);
        expect(link).toEqual({href, title});
    });
    test('Different function returns the same DOM', async () => {
        const hash = getHashUrl();
        const root = createDiv();
        mount(<div id={hash}>Hello</div>, {attachTo: root});
        const getContainerA = createGetContainer(hash);
        const getContainerB = createGetContainer(hash);
        const ref = React.createRef();
        const wrapper = mount(
            <Anchor ref={ref} getContainer={getContainerA}>
                <Link href={`#${hash}`} title={hash} />
            </Anchor>
        );
        const removeListenerSpy = jest.spyOn(ref.current.scrollEvent, 'remove');
        await sleep(1000);
        wrapper.setProps({getContainer: getContainerB});
        expect(removeListenerSpy).not.toHaveBeenCalled();
    });

    test('Different function returns different DOM', async () => {
        const hash1 = getHashUrl();
        const hash2 = getHashUrl();
        const root = createDiv();
        mount(
            <div>
                <div id={hash1}>Hello</div>
                <div id={hash2}>World</div>
            </div>,
            {attachTo: root},
        );
        const getContainerA = createGetContainer(hash1);
        const getContainerB = createGetContainer(hash2);
        const ref = React.createRef();
        const wrapper = mount(
            <Anchor ref={ref} getContainer={getContainerA}>
                <Link href={`#${hash1}`} title={hash1} />
                <Link href={`#${hash2}`} title={hash2} />
            </Anchor>
        );
        const removeListenerSpy = jest.spyOn(ref.current.scrollEvent, 'remove');
        expect(removeListenerSpy).not.toHaveBeenCalled();
        await sleep(1000);
        wrapper.setProps({getContainer: getContainerB});
        expect(removeListenerSpy).toHaveBeenCalled();
    });
});

describe('Anchor Component document is undefined', () => {
    beforeAll(() => {
        Object.defineProperty(global, 'document', {});
    });
    test('should be rendered correctly when document is undefined', () => {
        const wrapper = shallow(
            <Anchor size="small">
                <Link href="#demo-instances" title="Basic demo" key="1" />
                <Link href="#demo-code-operator" title="Code demo" key="2" />
                <Link href="#api" title="API" key="3">
                    <Link href="#api-header-title" title="Props" key="4" />
                </Link>
            </Anchor>
        );
        expect(render(wrapper)).toMatchSnapshot();
    });
});