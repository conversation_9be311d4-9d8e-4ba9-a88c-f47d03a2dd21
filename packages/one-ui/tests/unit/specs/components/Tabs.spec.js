/**
 * @file tabs的单测
 * <AUTHOR>
 * @date 2020-09-02
 */

import {mount} from 'enzyme';
import React from 'react';
import Tabs from '../../../../src/components/tabs/index';
import mountTest from '../../../../tests/shared/mountTest';

const {TabPane} = Tabs;

describe('Tabs Component', () => {
    mountTest(Tabs, {
        activeKey: '1'
    });

    let wrapper;
    let handleAdd = jest.fn();
    let handleDelete = jest.fn();

    beforeAll(() => {
        wrapper = mount(
            <Tabs
                type="simple"
                activeKey="1"
                size="medium"
                showAdd
                onAdd={handleAdd}
                onDelete={handleDelete}
            >
                <TabPane tab="Tab 1" key="1" closable>
                    Content of Tab Pane 1
                </TabPane>
                <TabPane tab="Tab 2" key="2" disabled>
                    Content of Tab Pane 1
                </TabPane>
            </Tabs>
        );
    });

    // has console error
    test('should render a Tabs component', () => {
        expect(wrapper).toMatchSnapshot();
    });

    test('should render a Tabs component with type card', () => {
        const wrapper = mount(<Tabs
            type="card"
            defaultActiveKey="1"
            size="medium"
        >
            <TabPane tab="Tab 1" key="1" closable>
                Content of Tab Pane 1
            </TabPane>
            <TabPane tab="Tab 2" key="2" disabled closable>
                Content of Tab Pane 1
            </TabPane>
            <TabPane tab="Tab 3" key="3" closable>
                Content of Tab Pane 1
            </TabPane>
        </Tabs>);
        wrapper.find('.one-tabs-title').at(0).simulate('click');
        wrapper.setProps({
            activeKey: '2'
        });
        wrapper.find('.one-tabs-title').at(1).simulate('click');
        wrapper.find('.one-tabs-title').at(2).simulate('click');
        wrapper.find('.one-tabs-tab-item').at(1).find('.one-tabs-close-icon').at(0).simulate('click');
        expect(wrapper).toMatchSnapshot();
    });

    test('should create a Tabs component with `simple medium` props', () => {
        expect(wrapper.props().type).toBe('simple');
        expect(wrapper.props().size).toBe('medium');
    });

    test('should create a Tabs support close TabPane', () => {
        expect(wrapper.find('.one-tabs-close-icon').length).toBe(3);
        wrapper.find('.one-tabs-close-icon').at(0).simulate('click');
        return new Promise(resolve => setTimeout(resolve, 300)) // animation timeout
            .then(() => expect(handleDelete).toHaveBeenCalledWith('1'));
    });

    test('should create a Tabs include disabled TabPane', () => {
        expect(wrapper.find('.one-tabs-title-disabled').length).toBe(1);
    });

    test('should create a Tabs support add TabPane', () => {
        wrapper.find('.one-tabs-bar-add').first().simulate('click');
        expect(handleAdd.mock.calls.length).toBe(1);
    });
});
