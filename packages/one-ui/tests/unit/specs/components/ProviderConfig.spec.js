/**
 * @file Button.spec.js
 * <AUTHOR> <EMAIL>
 * @date 2020-09-03
 */
import {mount} from 'enzyme';
import React from 'react';
import ProviderConfig from '../../../../src/components/providerConfig';
import Button from '../../../../src/components/button';
import mountTest from '../../../shared/mountTest';

describe('ProviderConfig Component', () => {
    mountTest(ProviderConfig);

    test('ProviderConfig without config', () => {
        const ref = React.createRef();
        mount(
            <ProviderConfig>
                <Button ref={ref} type="primary" size="large" />
            </ProviderConfig>
        );
        expect(ref.current.props.size).toBe('large');
        expect(ref.current.props.prefixCls).toBe('one-button');
    });

    test('ProviderConfig with config', () => {
        const ref = React.createRef();
        const ref2 = React.createRef();
        mount(
            <ProviderConfig size="small" prefixCls="test">
                <Button ref={ref} type="primary" />
                <Button ref={ref2} type="primary" size="large" prefixCls="test2" />
            </ProviderConfig>
        );
        expect(ref.current.props.size).toBe('small');
        expect(ref.current.props.prefixCls).toBe('test-button');
        expect(ref2.current.props.size).toBe('large');
        expect(ref2.current.props.prefixCls).toBe('test2');
    });
});
