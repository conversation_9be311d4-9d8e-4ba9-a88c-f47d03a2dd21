/**
 * @file Timer Picker的UT 单测
 * <AUTHOR>
 * @date 2020-09-16
 */
import React from 'react';
import {mount} from 'enzyme';
import mountTest from '../../../shared/mountTest';
import TimePicker from '../../../../src/components/timePicker';

describe('TimerPicker Components', () => {
    mountTest(TimePicker);
    it('Tree should render correctly', () => {
        const wrapper = mount(<TimePicker format={null} />);
        wrapper.setProps({
            showHour: false
        });
        wrapper.update();
        wrapper.setProps({
            showMinute: false
        });
        wrapper.setProps({
            showHour: true,
            showMinute: true,
            showSecond: false
        });
        wrapper.update();
        wrapper.find('.one-input').simulate('change', {target: {value: '12:23'}});
        expect(wrapper.render()).toMatchSnapshot();
    });
    it('Tree should render correctly no show second', () => {
        const wrapper = mount(<TimePicker showSecond={false} format={null} use12Hours disabledHours={() => [1, 3]} />);
        wrapper.find('.one-input').simulate('change', {target: {value: '12:23'}});
        wrapper.find('.one-input').simulate('focus');
        wrapper.find('.one-input').simulate('blur');
        expect(wrapper.render()).toMatchSnapshot();
    });
    it('Tree should render correctly use 12 hours', () => {
        const wrapper = mount(
            <TimePicker
                use12Hours
                value="11:23:45"
                format={null}
                placeholder="请输入时间"
            />
        );
        wrapper.setProps({
            value: '12:23:56'
        });
        wrapper.update();
        wrapper.setProps({
            showHour: false
        });
        wrapper.update();
        wrapper.setProps({
            showMinute: false
        });
        wrapper.setProps({
            showHour: true,
            showMinute: true,
            showSecond: false
        });
        wrapper.update();
        wrapper.setProps({
            open: true
        });
        wrapper.update();
        wrapper.find('.one-input').simulate('change', {target: {value: '12:23'}});
        wrapper.find('.one-input').simulate('keydown', {keyCode: 27});
        wrapper.find('.one-input').simulate('keydown', {keyCode: 40});
        wrapper.update();
        wrapper.find('.one-input').simulate('change', {target: {value: '12:24'}});
        wrapper.find('.one-time-picker-icon-close').at(0).simulate('click');
        wrapper.setProps({
            errorMessage: '报错报错'
        });
        expect(wrapper.render()).toMatchSnapshot();
    });
});
