/**
 * @file Radio.spec.js
 * <AUTHOR> <EMAIL>
 * @date 2020-09-03
 */
import {render, mount} from 'enzyme';
import React from 'react';
import RRadio from '../../../../src/components/radio/index';
import Radio from '../../../../src/components/radio/radio';
import RadioGroup from '../../../../src/components/radio/group';
import RadioButton from '../../../../src/components/radio/button';
import mountTest from '../../../../tests/shared/mountTest';
import focusTest from '../../../../tests/shared/focusTest';
import {createRef} from 'react';

describe('Radio Component', () => {
    mountTest(RRadio);
    focusTest(Radio);

    test('should render correctly', () => {
        const wrapper = render(<Radio className="customized">Test</Radio>);
        expect(wrapper).toMatchSnapshot();
    });

    test('responses hover events', () => {
        const onMouseEnter = jest.fn();
        const onMouseLeave = jest.fn();

        const wrapper = mount(<Radio onMouseEnter={onMouseEnter} onMouseLeave={onMouseLeave} />);

        wrapper.find('label').simulate('mouseenter');
        expect(onMouseEnter).toHaveBeenCalled();

        wrapper.find('label').simulate('mouseleave');
        expect(onMouseLeave).toHaveBeenCalled();
    });
});

describe('Radio Group Component', () => {
    mountTest(RadioGroup);

    function createRadioGroup(props, ref) {
        return (
            <RadioGroup {...props} ref={ref}>
                <Radio value="A">A</Radio>
                <Radio value="B">B</Radio>
                <Radio value="C">C</Radio>
            </RadioGroup>
        );
    }

    test('responses hover events', () => {
        const onMouseEnter = jest.fn();
        const onMouseLeave = jest.fn();

        const wrapper = mount(
            <RadioGroup onMouseEnter={onMouseEnter} onMouseLeave={onMouseLeave}>
                <Radio />
            </RadioGroup>,
        );

        wrapper
            .find('div')
            .at(0)
            .simulate('mouseenter');
        expect(onMouseEnter).toHaveBeenCalled();

        wrapper
            .find('div')
            .at(0)
            .simulate('mouseleave');
        expect(onMouseLeave).toHaveBeenCalled();
    });

    test('fire change events when value changes', () => {
        const onChange = jest.fn();

        const ref = createRef();
        const wrapper = mount(
            createRadioGroup({
                onChange
            }, ref),
        );
        const radios = wrapper.find('input');

        // uncontrolled component
        ref.current.setState({value: 'B'});
        radios.at(0).simulate('change');
        expect(onChange.mock.calls.length).toBe(1);

        // controlled component
        wrapper.setProps({value: 'A'});
        radios.at(1).simulate('change');
        expect(onChange.mock.calls.length).toBe(2);
    });
});

describe('Radio Button Component', () => {
    mountTest(RadioButton);

    test('Trigger onChange when both of radioButton and radioGroup exists', () => {
        const onChange = jest.fn();
        const ref = createRef();
        const wrapper = mount(
            <RadioGroup onChange={onChange} ref={ref}>
                <RadioButton value="A">A</RadioButton>
                <RadioButton value="B">B</RadioButton>
                <RadioButton value="C">C</RadioButton>
            </RadioGroup>,
        );
        const radios = wrapper.find('input');

        // uncontrolled component
        ref.current.setState({value: 'B'});
        radios.at(0).simulate('change');
        expect(onChange.mock.calls.length).toBe(1);

        // controlled component
        wrapper.setProps({value: 'A'});
        radios.at(1).simulate('change');
        expect(onChange.mock.calls.length).toBe(2);
    });
});
