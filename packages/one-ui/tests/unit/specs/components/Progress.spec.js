/**
 * @file Progress.spec.js
 * <AUTHOR> <EMAIL>
 * @date 2020-09-03
 */
import {mount} from 'enzyme';
import React from 'react';
import RProgress from '../../../../src/components/progress/index';
import Progress from '../../../../src/components/progress/progress';
import mountTest from '../../../shared/mountTest';

describe('Progress Component', () => {
    mountTest(RProgress);

    test('render round progress', () => {
        const wrapper = mount(<Progress type="circle" percent={75} />);
        expect(wrapper.props().type).toBe('circle');
        expect(wrapper.render()).toMatchSnapshot();
    });

    test('render different size progress', () => {
        const wrapper = mount(<Progress size='small' percent={30} />);
        expect(wrapper.props().size).toBe('small');
        expect(wrapper.render()).toMatchSnapshot();
    });

    test('render different status progress', () => {
        const wrapper = mount(<Progress status='exception' percent={30} />);
        expect(wrapper.props().status).toBe('exception');
        expect(wrapper.render()).toMatchSnapshot();
    });

    test('render out-of-range progress', () => {
        const wrapper = mount(<Progress percent={120} />);
        expect(wrapper.render()).toMatchSnapshot();
    });

    test('render out-of-range progress with info', () => {
        const wrapper = mount(<Progress percent={120} showInfo />);
        expect(wrapper.render()).toMatchSnapshot();
    });

    test('render negative progress', () => {
        const wrapper = mount(<Progress percent={-20} />);
        expect(wrapper.render()).toMatchSnapshot();
    });

    test('should handle retry operation', () => {
        const onRetry = jest.fn();
        const wrapper = mount(<Progress percent={20} status="exception" onRetry={onRetry} />);
        wrapper.find('svg[type="refresh"]').simulate('click');
        expect(onRetry).toHaveBeenCalled();
    });

    test('should handle cancel operation', () => {
        const onCancel = jest.fn();
        const wrapper = mount(<Progress percent={20} onCancel={onCancel} />);
        wrapper.find('svg[type="close"]').simulate('click');
        expect(onCancel).toHaveBeenCalled();
    });

});