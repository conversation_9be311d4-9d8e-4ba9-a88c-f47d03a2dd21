/**
 * @file Drawer UT
 * <AUTHOR>
 * @date 2020-09-15
 */
import React, {createRef, PureComponent} from 'react';
import {render, mount} from 'enzyme';
import Drawer from '../../../../src/components/drawer';
import Button from '../../../../src/components/button';
import mountTest from '../../../shared/mountTest';

class NoramlDrawer extends PureComponent {
    state = {
        visible: false,
        childrenDrawer: false
    };

    showDrawer = () => {
        this.setState({
            visible: true
        });
    };

    onClose = () => {
        this.setState({
            visible: false
        });
    };

    showChildrenDrawer = () => {
        this.setState({
            childrenDrawer: true
        });
    };

    onChildrenDrawerClose = () => {
        this.setState({
            childrenDrawer: false
        });
    };

    render() {
        return (
            <div>
                <Button type="primary" onClick={this.showDrawer} className="drawer-button">
                    Open drawer
                </Button>
                <Drawer
                    title="Multi-level drawer"
                    width={520}
                    closable={false}
                    onClose={this.onClose}
                    visible={this.state.visible}
                    placement={this.props.placement}
                >
                    <Button type="primary" onClick={this.showChildrenDrawer} className="drawer-button-2">
                        Two-level drawer
                    </Button>
                    <Drawer
                        title="Two-level Drawer"
                        width={320}
                        onClose={this.onChildrenDrawerClose}
                        visible={this.state.childrenDrawer}
                        onCancel={this.onChildrenDrawerClose}
                        hideDefaultFooter
                    >
                        This is two-level drawer
                        <div
                            style={{
                                position: 'absolute',
                                bottom: 0,
                                width: '100%',
                                padding: '24px',
                                textAlign: 'left',
                                left: 0,
                                background: '#fff',
                                borderRadius: '0 0 4px 4px'
                            }}
                        >
                            <Button
                                style={{
                                    marginRight: 12
                                }}
                                size="small"
                                onClick={this.onClose}
                                className="demo-close-2"
                            >
                                Cancel
                            </Button>
                            <Button onClick={this.onClose} type="primary" size="small">
                                Submit
                            </Button>
                        </div>
                    </Drawer>
                    <div
                        style={{
                            position: 'absolute',
                            bottom: 0,
                            width: '100%',
                            padding: '24px',
                            textAlign: 'left',
                            left: 0,
                            background: '#fff',
                            borderRadius: '0 0 4px 4px'
                        }}
                    >
                        <Button
                            style={{
                                marginRight: 12
                            }}
                            size="small"
                            onClick={this.onClose}
                            className="demo-close-1"
                        >
                            Cancel
                        </Button>
                        <Button onClick={this.onClose} type="primary" size="small">
                            Submit
                        </Button>
                    </div>
                </Drawer>
            </div>
        );
    }
}

describe('Drawer', () => {
    mountTest(Drawer);
    it('render correctly', () => {
        const wrapper = render(
            <Drawer visible={false} width={400} destroyOnClose>
                Here is content of Drawer
            </Drawer>
        );
        expect(wrapper).toMatchSnapshot();
    });
    it('render top drawer', () => {
        const wrapper = render(
            <Drawer visible height={400} placement="top">
                Here is content of Drawer
            </Drawer>,
        );
        expect(wrapper).toMatchSnapshot();
    });

    it('have a title', () => {
        const wrapper = render(
            <Drawer visible title="Test Title">
                Here is content of Drawer
            </Drawer>,
        );
        expect(wrapper).toMatchSnapshot();
    });
    it('closable is false', () => {
        const wrapper = render(
            <Drawer visible closable={false}>
                Here is content of Drawer
            </Drawer>,
        );
        expect(wrapper).toMatchSnapshot();
    });
    it('destroyOnClose is true', () => {
        const wrapper = mount(
            <Drawer destroyOnClose visible maskClosable={false}>
                Here is content of Drawer
            </Drawer>,
        );
        wrapper.find('.one-drawer-mask').simulate('click');
        expect(wrapper).toMatchSnapshot();
    });
    it('className is test_drawer', () => {
        const ref = createRef();
        const wrapper = mount(
            <Drawer visible mask={false} className="test_drawer" ref={ref}>
                Here is content of Drawer
            </Drawer>,
        );
        ref.current.pull();
        expect(wrapper).toMatchSnapshot();
    });
    it('have a footer', () => {
        const wrapper = mount(
            <Drawer
                visible
                closable={false}
                footer={[<Button key="1">123</Button>]}
            >
                Here is content of Drawer
            </Drawer>,
        );
        wrapper.find('.one-drawer-mask').simulate('click');
        expect(wrapper).toMatchSnapshot();
    });
    it('have a footer', () => {
        const wrapper = mount(
            <Drawer
                closable
                visible
                footer={[<Button key="1">123</Button>]}
                onClose={() => {}}
            >
                Here is content of Drawer
            </Drawer>,
        );
        wrapper.find('.one-drawer-close').at(0).simulate('click');
        expect(wrapper).toMatchSnapshot();
    });
    it('have a footer', () => {
        const wrapper = mount(
            <Drawer
                closable
                visible
                footer={[<Button key="1">123</Button>]}
                onClose={() => {}}
                maskClosable={false}
            >
                Here is content of Drawer
            </Drawer>,
        );
        wrapper.find('.one-drawer-close').at(0).simulate('click');
        expect(wrapper).toMatchSnapshot();
    });
    it('can open', () => {
        const wrapper = mount(<NoramlDrawer placement="left" />);
        wrapper.find('.drawer-button').at(0).simulate('click');
        wrapper.find('.drawer-button-2').at(0).simulate('click');
        wrapper.find('.demo-close-2').at(0).simulate('click');
        wrapper.find('.demo-close-1').at(0).simulate('click');
    });
    it('can open', () => {
        const wrapper = mount(<NoramlDrawer placement="top" />);
        wrapper.find('.drawer-button').at(0).simulate('click');
        wrapper.find('.drawer-button-2').at(0).simulate('click');
        wrapper.find('.demo-close-2').at(0).simulate('click');
        wrapper.find('.demo-close-1').at(0).simulate('click');
    });
});