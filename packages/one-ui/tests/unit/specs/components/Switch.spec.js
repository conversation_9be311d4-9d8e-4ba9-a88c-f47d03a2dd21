/**
 * @file Switch组件单元测试
 * <AUTHOR>
 * @date 2020-09-03
 */
import React from 'react';
import {mount} from 'enzyme';
import mountTest from '../../../../tests/shared/mountTest';
import Switch from '../../../../src/components/switch/index';
import RawSwitch from '../../../../src/components/switch/switch';
import focusTest from '../../../../tests/shared/focusTest';

describe('Switch Components', () => {
    mountTest(RawSwitch);
    focusTest(RawSwitch);
    test('should be rendered correctly', async () => {
        const wrapper = mount(<Switch />);
        expect(wrapper.find('.one-switch').hasClass('one-switch-medium')).toBe(true);
        wrapper.find('.one-switch').getDOMNode().click();
        await new Promise(resolve => setTimeout(resolve, 0));
        expect(wrapper.render()).toMatchSnapshot();
    });

    test('should be rendered correctly with large size', async () => {
        const wrapper = mount(<Switch size="large" />);
        expect(wrapper.find('.one-switch').hasClass('one-switch-medium')).toBe(true);
        wrapper.find('.one-switch').getDOMNode().click();
        await new Promise(resolve => setTimeout(resolve, 0));
        expect(wrapper.render()).toMatchSnapshot();
    });

    test('should be rendered correctly with small size', async () => {
        const wrapper = mount(<Switch size="small" />);
        expect(wrapper.find('.one-switch').hasClass('one-switch-small')).toBe(true);
        wrapper.find('.one-switch').getDOMNode().click();
        await new Promise(resolve => setTimeout(resolve, 0));
        expect(wrapper.render()).toMatchSnapshot();
    });

    test('should be rendered correctly with xsmall size', async () => {
        const wrapper = mount(<Switch size="xsmall" />);
        expect(wrapper.find('.one-switch').hasClass('one-switch-xsmall')).toBe(true);
        wrapper.find('.one-switch').simulate('click');
        await new Promise(resolve => setTimeout(resolve, 0));
        expect(wrapper.find('.one-switch').hasClass('one-switch-checked')).toBe(true);
        expect(wrapper.render()).toMatchSnapshot();
    });

    test('should be rendered correctly with disabled', async () => {
        const wrapper = mount(<Switch disabled />);
        expect(wrapper.find('.one-switch').hasClass('one-switch-disabled')).toBe(true);
        wrapper.find('.one-switch').getDOMNode().click();
        await new Promise(resolve => setTimeout(resolve, 0));
        expect(wrapper.render()).toMatchSnapshot();
    });
});