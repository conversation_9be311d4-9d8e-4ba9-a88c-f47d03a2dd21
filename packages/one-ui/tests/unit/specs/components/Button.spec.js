/**
 * @file Button.spec.js
 * <AUTHOR> <EMAIL>
 * @date 2020-09-03
 */
import {mount} from 'enzyme';
import React, {Component} from 'react';
import Button from '../../../../src/components/button/index';
import mountTest from '../../../shared/mountTest';

describe('Button Component', () => {
    mountTest(Button);

    test('should create a button component with base props', () => {
        const wrapper = mount(<Button type="primary" size="large" />);
        expect(wrapper.props().type).toBe('primary');
        expect(wrapper.props().size).toBe('large');
        expect(wrapper).toMatchSnapshot();
    });

    test('should create a button component with custom className', () => {
        const wrapper = mount(<Button className="two-ui" />);
        expect(wrapper.find('.two-ui').length).toBe(3);
        expect(wrapper).toMatchSnapshot();
    });

    test('should create a button component with custom style', () => {
        const wrapper = mount(<Button style={{marginTop: '20px'}} />);
        expect(wrapper.props().style).toMatchObject({marginTop: '20px'});
        expect(wrapper).toMatchSnapshot();
    });

    test('should support disabled state', () => {
        const wrapper = mount(<Button disabled />);
        expect(wrapper.props().disabled).toBe(true);
        expect(wrapper).toMatchSnapshot();
    });

    test('should support submit type', () => {
        const wrapper = mount(<Button htmlType="submit" />);
        expect(wrapper.props().htmlType).toBe('submit');
        expect(wrapper).toMatchSnapshot();
    });

    test('should support icon type', () => {
        const wrapper = mount(<Button icon="calendar" />);
        expect(wrapper.find('.one-button-has-icon').length).toBe(1);
        expect(wrapper).toMatchSnapshot();
    });

    test('should not clickable when button is loading', () => {
        const onClick = jest.fn();
        const wrapper = mount(
            <Button loading onClick={onClick}>
                button
            </Button>,
        );
        wrapper.simulate('click');
        expect(onClick).not.toHaveBeenCalledWith();
    });

    test('should be only Icon button', () => {
        const wrapper = mount(
            <Button type="primary" icon="calendar" size="large" style={{marginRight: '20px'}} />,
        );
        expect(wrapper).toMatchSnapshot();
    });

    test('should custom Icon button', () => {

        const Icon = <div>icon</div>;
        const wrapper = mount(
            <Button type="primary" icon={Icon} />,
        );
        expect(wrapper).toMatchSnapshot();
    });

    test('only Icon button is loading', () => {
        const wrapper = mount(
            <Button type="primary" icon="calendar" loading />,
        );
       expect(wrapper).toMatchSnapshot();
    });

    test('should not clickable when button is readonly', () => {
        const onClick = jest.fn();
        const wrapper = mount(
            <Button readOnly onClick={onClick}>
                button
            </Button>,
        );
        wrapper.simulate('click');
        expect(onClick).not.toHaveBeenCalledWith();
    });

    test('should handle click', () => {
        class DefaultButton extends Component {
            state = {
                loading: false
            };

            handleClick = () => {
                this.setState({loading: true});
            };

            render() {
                const {loading} = this.state;
                return (
                    <Button loading={loading} onClick={this.handleClick}>
                        Button
                    </Button>
                );
            }
        }

        const wrapper = mount(<DefaultButton />);
        wrapper.simulate('click');
        expect(wrapper.find('.one-button-normal-loading').length).toBe(1);
    });

});
