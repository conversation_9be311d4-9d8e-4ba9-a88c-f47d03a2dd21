/**
 * @file dropdown组件的UT
 * <AUTHOR>
 * @date 2020-09-15
 */
import React from 'react';
import {mount} from 'enzyme';
import Dropdown from '../../../../src/components/dropdown/index';
import Button from '../../../../src/components/button/index';
import Menu from '../../../../src/components/menu/index';
import mountTest from '../../../shared/mountTest';

const options = [
    {
        label: '操作命令1',
        value: 'operation1'
    },
    {
        label: '操作命令2',
        value: 'operation2'
    },
    {
        divider: true
    },
    {
        label: '操作命令3',
        value: 'operation3'
    },
    {
        label: '操作命令1',
        value: 'operation4',
        disabled: true
    }
];

describe('Dropdown Component', () => {
    mountTest(Dropdown.Button, {
        options
    });
    test('renders dropdown correctly', () => {
        const wrapper = mount(
            <Dropdown
                overlay={(
                    <Menu>
                        <Menu.Item>1st menu item</Menu.Item>
                        <Menu.Item>2nd menu item</Menu.Item>
                        <Menu.SubMenu title="sub menu">
                            <Menu.Item>3rd menu item</Menu.Item>
                            <Menu.Item>4th menu item</Menu.Item>
                        </Menu.SubMenu>
                        <Menu.SubMenu title="disabled sub menu" disabled>
                            <Menu.Item>5d menu item</Menu.Item>
                            <Menu.Item>6th menu item</Menu.Item>
                        </Menu.SubMenu>
                    </Menu>
                )}
                trigger={['contextMenu']}
            >
                <Button>自定义触发按钮</Button>
            </Dropdown>
        );
        wrapper.setProps({overlay: <div>xx</div>});
        wrapper.update();
        wrapper.setProps({disabled: true});
        wrapper.update();
        // expect(wrapper).toMatchSnapshot();
    });
    test('renders dropdown.button correctly', () => {
        const onVisibleChange = jest.fn();
        const wrapper = mount(
            <Dropdown.Button
                options={options}
                title="下拉菜单名称"
                trigger={['click']}
                className="demo-wrapper"
                onVisibleChange={onVisibleChange}
                placement="bottomRight"
            />
        );
        wrapper.find('.demo-wrapper .one-dropdown-trigger').at(0).simulate('click');
        // const popupWrapper = mount(wrapper.find('Trigger').instance().getComponent());
        wrapper.find('.one-dropdown-menu-item').at(0).simulate('click');
        wrapper.setProps({options: [
            {
                label: '操作命令1',
                value: 'operation1',
                children: [{
                    label: '子层级1',
                    value: 'sublevel-1'
                }, {
                    label: '子层级2',
                    value: 'sublevel-2'
                }, {
                    label: '子层级3',
                    value: 'sublevel-3'
                }, {
                    label: '子层级4',
                    value: 'sublevel-4'
                }]
            },
            {
                label: '操作命令2',
                value: 'operation2',
                children: [{
                    label: '子层级2-1',
                    value: 'sublevel-1-2',
                    divider: true
                }, {
                    label: '子层级2-2',
                    value: 'sublevel-2-2',
                    disabled: true
                }, {
                    label: '子层级3-3',
                    value: 'sublevel-3-3'
                }, {
                    label: '子层级4-4',
                    value: 'sublevel-4-4'
                }]
            }
        ]});
        wrapper.update();
        wrapper.setProps({
            options: [
                {
                    value: '1',
                    label: '分类一',
                    groupChildren: [{
                        label: '操作命令1',
                        value: 'operation1',
                        divider: true
                    }, {
                        label: '操作命令2',
                        value: 'operation2'
                    }]
                },
                {
                    divider: true
                },
                {
                    value: '2',
                    label: '分类二',
                    groupChildren: [{
                        label: '操作命令3',
                        value: 'operation3'
                    }, {
                        label: '操作命令4',
                        value: 'operation4',
                        disabled: true
                    }]
                }
            ]
        });
        wrapper.update();
        wrapper.setProps({
            showSearch: true,
            searchWidth: 300
        });
        wrapper.update();
        wrapper.setProps({
            type: 'primary'
        });
        wrapper.update();
        wrapper.setProps({
            textLink: true,
            width: 200,
            visible: true
        });
        wrapper.update();
        // expect(wrapper).toMatchSnapshot();
    });

    test('renders dropdown.button correctly with search', () => {
        const onSearchChange = jest.fn();
        const onClickSearch = jest.fn();
        const wrapper = mount(
            <Dropdown.Button
                options={options}
                showSearch
                onSearchChange={onSearchChange}
                searchPlaceholder="请输入..."
                notFound="没有找到需要的计划"
                className="demo-wrapper-search"
                onClickSearch={onClickSearch}
                trigger={['click']}
            />
        );
        wrapper.find('.demo-wrapper-search .one-dropdown-trigger').simulate('click');
        wrapper.find('.one-search-box').simulate('change', {target: {value: 'hello'}});
        wrapper.find('.one-search-box-icon-close').at(0).simulate('click');
        wrapper.find('.one-search-box').simulate('change', {target: {value: 'hello'}});
        wrapper.find('.one-search-box-search-icon').at(0).simulate('click');
    });
});