/**
 * @file 折叠面板组件的UT
 * @file <EMAIL>
 * @date 2020-09-08
 */
import React from 'react';
import {mount} from 'enzyme';
import mountTest from '../../../shared/mountTest';
import Collapse from '../../../../src/components/collapse/index';
import {sleep} from '../../../utils';

const Panel = Collapse.Panel;

describe('Collapse', () => {
    mountTest(Collapse);
    it('render correctly', () => {
        const wrapper = mount(
            <Collapse>
                <Collapse.Panel header="header" />
            </Collapse>,
        );
        expect(wrapper.render()).toMatchSnapshot();
    });
    it('could be expand and collapse', async () => {
        const wrapper = mount(
            <Collapse>
                <Collapse.Panel header="This is panel header 1" key="1">
                    content
                </Collapse.Panel>
                <Collapse.Panel key="2">
                    content
                </Collapse.Panel>
            </Collapse>
        );
        expect(wrapper.find('.one-collapse-item').at(0).hasClass('one-collapse-item-active')).toBe(false);
        wrapper.find('.one-collapse-item-header').at(0).simulate('click');
        wrapper.update();
        await sleep(400);
        wrapper.update();
        expect(wrapper.find('.one-collapse-item').at(0).hasClass('one-collapse-item-active')).toBe(true);
    });
    it('could be expand and collapse with accordion', async () => {
        const text = `
            A dog is a type of domesticated animal.
            Known for its loyalty and faithfulness,
            it can be found as a welcome guest in many households across the world.
            `;
        const wrapper = mount(
            <Collapse accordion>
                <Panel header="This is panel header 1" key="1">
                    <p>{text}</p>
                </Panel>
                <Panel header="This is panel header 2" key="2">
                    <p>{text}</p>
                </Panel>
                <Panel header="This is panel header 3" key="3" disabled>
                    <p>{text}</p>
                </Panel>
                <Panel header="This is panel header 4" key="4">
                    <p>{text}</p>
                </Panel>
            </Collapse>
        );
        expect(wrapper.find('.one-collapse-item').at(0).hasClass('one-collapse-item-active')).toBe(false);
        wrapper.find('.one-collapse-item-header').at(0).simulate('click');
        wrapper.update();
        await sleep(400);
        wrapper.update();
        expect(wrapper.find('.one-collapse-item').at(0).hasClass('one-collapse-item-active')).toBe(true);
        wrapper.find('.one-collapse-item-header').at(1).simulate('click');
        wrapper.update();
        await sleep(400);
        wrapper.update();
        expect(wrapper.find('.one-collapse-item').at(1).hasClass('one-collapse-item-active')).toBe(true);
    });
    it('could be expand and collapse with controlled key', async () => {
        const text = `
            A dog is a type of domesticated animal.
            Known for its loyalty and faithfulness,
            it can be found as a welcome guest in many households across the world.
            `;
        const wrapper = mount(
            <Collapse activeKey={['1']}>
                <Panel header="This is panel header 1" key="1">
                    <p>{text}</p>
                </Panel>
                <Panel header="This is panel header 2" key="2">
                    <p>{text}</p>
                </Panel>
                <Panel header="This is panel header 3" key="3" disabled>
                    <p>{text}</p>
                </Panel>
                <Panel header="This is panel header 4" key="4">
                    <p>{text}</p>
                </Panel>
            </Collapse>
        );
        expect(wrapper.find('.one-collapse-item').at(0).hasClass('one-collapse-item-active')).toBe(true);
        wrapper.find('.one-collapse-item').at(1).simulate('click');
        wrapper.setProps({activeKey: ['2']});
        await sleep(400);
        expect(wrapper.find('.one-collapse-item').at(1).hasClass('one-collapse-item-active')).toBe(true);
    });
    it('could be close pane with controlled key', async () => {
        const text = `
            A dog is a type of domesticated animal.
            Known for its loyalty and faithfulness,
            it can be found as a welcome guest in many households across the world.
            `;
        const wrapper = mount(
            <Collapse>
                <Panel header="This is panel header 1" key="1">
                    <p>{text}</p>
                </Panel>
                <Panel header="This is panel header 2" key="2">
                    <p>{text}</p>
                </Panel>
                <Panel header="This is panel header 3" key="3" disabled>
                    <p>{text}</p>
                </Panel>
                <Panel header="This is panel header 4" key="4">
                    <p>{text}</p>
                </Panel>
            </Collapse>
        );
        expect(wrapper.find('.one-collapse-item').at(0).hasClass('one-collapse-item-active')).toBe(false);
        wrapper.find('.one-collapse-item-header').at(0).simulate('click');
        wrapper.update();
        await sleep(400);
        expect(wrapper.find('.one-collapse-item').at(0).hasClass('one-collapse-item-active')).toBe(true);
        wrapper.find('.one-collapse-item-header').at(0).simulate('click');
        wrapper.update();
        await sleep(400);
        expect(wrapper.find('.one-collapse-item').at(0).hasClass('one-collapse-item-active')).toBe(false);
    });
    it('could be close pane with controlled key and fragment', async () => {
        const text = `
            A dog is a type of domesticated animal.
            Known for its loyalty and faithfulness,
            it can be found as a welcome guest in many households across the world.
            `;
        const wrapper = mount(
            <Collapse>
                <React.Fragment>
                    <Panel header="This is panel header 1" key="1">
                        <p>{text}</p>
                    </Panel>
                    <Panel header="This is panel header 2" key="2">
                        <p>{text}</p>
                    </Panel>
                    <Panel header="This is panel header 3" key="3" disabled>
                        <p>{text}</p>
                    </Panel>
                    <Panel header="This is panel header 4" key="4">
                        <p>{text}</p>
                    </Panel>
                </React.Fragment>
            </Collapse>
        );
        expect(wrapper.find('.one-collapse-item').at(0).hasClass('one-collapse-item-active')).toBe(false);
        wrapper.find('.one-collapse-item-header').at(0).simulate('click');
        wrapper.update();
        await sleep(400);
        expect(wrapper.find('.one-collapse-item').at(0).hasClass('one-collapse-item-active')).toBe(true);
        wrapper.find('.one-collapse-item-header').at(0).simulate('click');
        wrapper.update();
        await sleep(400);
        expect(wrapper.find('.one-collapse-item').at(0).hasClass('one-collapse-item-active')).toBe(false);
    });
});

describe('Collapse with blur and focus test', () => {
    let container;
    beforeEach(() => {
        container = document.createElement('div');
        document.body.appendChild(container);
    });
    afterEach(() => {
        document.body.removeChild(container);
    });
    test('focus() and onFocus', async () => {
        const text = `
            A dog is a type of domesticated animal.
            Known for its loyalty and faithfulness,
            it can be found as a welcome guest in many households across the world.
            `;
        const wrapper = mount((
            <div>
                <Collapse>
                    <Panel header="This is panel header 1" key="1">
                        <p>{text}</p>
                    </Panel>
                    <Panel header="This is panel header 2" key="2">
                        <p>{text}</p>
                    </Panel>
                    <Panel header="This is panel header 3" key="3" disabled>
                        <p>{text}</p>
                    </Panel>
                    <Panel header="This is panel header 4" key="4">
                        <p>{text}</p>
                    </Panel>
                </Collapse>
                <div className="demo-test-1">空白</div>
            </div>
        ), {attachTo: container});
        wrapper.find('.one-collapse-item-header').at(0).simulate('keypress', {keyCode: 13});
        wrapper.update();
        await sleep(400);
        wrapper.find('.one-collapse-item-header').at(0).simulate('click');
        wrapper.update();
        await sleep(400);
        expect(wrapper.find('.one-collapse-item').at(0).hasClass('one-collapse-item-active')).toBe(false);
        wrapper.find('.one-collapse-item-header').at(0).simulate('click');
        wrapper.update();
        await sleep(400);
        expect(wrapper.find('.one-collapse-item').at(0).hasClass('one-collapse-item-active')).toBe(true);
        wrapper.find('.one-collapse-item-header').at(2).simulate('click');
        wrapper.update();
        await sleep(400);
        expect(wrapper.find('.one-collapse-item').at(0).hasClass('one-collapse-item-active')).toBe(true);
        wrapper.find('.one-collapse-item-header').at(0).simulate('click');
        wrapper.update();
        await sleep(400);
        expect(wrapper.find('.one-collapse-item').at(0).hasClass('one-collapse-item-active')).toBe(false);
        wrapper.find('.one-collapse-item-header').at(0).simulate('keypress', {keyCode: 32});
        wrapper.update();
        await sleep(400);
        expect(wrapper.find('.one-collapse-item').at(0).hasClass('one-collapse-item-active')).toBe(true);
        wrapper.find('.one-collapse-item-header').at(0).simulate('keypress', {which: 32});
        wrapper.update();
        await sleep(400);
        expect(wrapper.find('.one-collapse-item').at(0).hasClass('one-collapse-item-active')).toBe(false);
        wrapper.find('.one-collapse-item-header').at(0).simulate('keypress', {key: ' '});
        wrapper.update();
        await sleep(400);
        expect(wrapper.find('.one-collapse-item').at(0).hasClass('one-collapse-item-active')).toBe(true);
        wrapper.find('.one-collapse-item-header').at(0).simulate('keypress', {which: 13});
        wrapper.update();
        await sleep(400);
        expect(wrapper.find('.one-collapse-item').at(0).hasClass('one-collapse-item-active')).toBe(false);
        wrapper.find('.one-collapse-item-header').at(0).simulate('keypress', {key: 'Enter'});
        wrapper.update();
        await sleep(400);
        expect(wrapper.find('.one-collapse-item').at(0).hasClass('one-collapse-item-active')).toBe(true);
    });
});
