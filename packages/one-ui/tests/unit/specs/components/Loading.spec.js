/**
 * @file Loading.spec.js
 * <AUTHOR> <EMAIL>
 * @date 2020-09-03
 */
import {mount, render} from 'enzyme';
import React from 'react';
import Loading from '../../../../src/components/loading/index';
import mountTest from '../../../shared/mountTest';

describe('Loading Component', () => {
    mountTest(Loading);

    it('should render base props', () => {
        const wrapper = mount(<Loading size="medium" type="strong" />);

        expect(wrapper.props().size).toBe('medium');
        expect(wrapper.props().type).toBe('strong');
    });

    it('should render custom CustomIconNode when it\'s set', () => {
        const CustomIconNode = <div className="custom-icon" />;
        const wrapper = render(<Loading CustomIconNode={CustomIconNode} />);
        expect(wrapper).toMatchSnapshot();
    });

    it('should be controlled by loading', () => {
        const wrapper = mount(<Loading loading={false} />);
        expect(wrapper.props().loading).toBe(false);

        wrapper.setProps({loading: true});
        expect(wrapper.props().loading).toBe(true);
    });

    it('should render ok with children', () => {
        const wrapper = mount(<Loading size="medium" type="strong" tip="xx">啦啦啦</Loading>);

        expect(wrapper.props().size).toBe('medium');
        expect(wrapper.props().type).toBe('strong');
    });

});