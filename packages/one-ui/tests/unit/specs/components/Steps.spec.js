/**
 * @file steps的单测
 * <AUTHOR>
 * @date 2020-09-02
 */
import React, {Component} from 'react';
import {mount, render} from 'enzyme';
import Steps from '../../../../src/components/steps/index';
import Button from '../../../../src/components/button/index';
import IconSvg from '../../../../src/components/iconSvg/index';
import mountTest from '../../../shared/mountTest';

const Step = Steps.Step;

describe('Steps Component', () => {
    mountTest(Steps);
    test('should be rendered correctly', () => {
        class Normal extends Component {
            state = {step: 0};
            handleAdd = () => {
                this.setState(prevState => ({
                    step: prevState.step + 1
                }));
            }
            handleMinus = () => {
                this.setState(prevState => ({
                    step: prevState.step - 1
                }));
            }
            onClickStep = step => {
                --step;
                this.setState({
                    step
                });
            }
            render() {
                return (
                    <div>
                        <Steps current={this.state.step} onClickStep={this.onClickStep}>
                            <Step title="设置推广计划" />
                            <Step title="设置推广单元" description="描述文案二" />
                            <Step title="提交和预览" description="描述文案三" />
                        </Steps>
                        <div style={{marginTop: 60}}>
                            {
                                this.state.step < 3 && (
                                    <div style={{marginRight: 20, display: 'inline-block'}}>
                                        <Button className="demo-button-next" onClick={this.handleAdd}>下一步</Button>
                                    </div>
                                )
                            }
                            {
                                this.state.step > 0 && (
                                    <Button className="demo-button-prev" onClick={this.handleMinus}>前一步</Button>
                                )
                            }
                        </div>
                    </div>
                );
            }
        }
        const wrapper = mount(
            <Normal />
        );
        expect(render(wrapper)).toMatchSnapshot();
        wrapper.find('.demo-button-next').at(0).simulate('click');
        expect(wrapper.find('.one-steps-item').at(0).hasClass('one-steps-item-finish')).toBe(true);
    });
    test('should be rendered correctly 2', () => {
        const wrapper = mount(
            <Steps current={2}>
                <Step title="设置推广计划" description="描述文案" status="error" />
                <Step title="设置推广单元" description="描述文案" status="error" />
                <Step title="提交和预览" description="描述文案" status="wait" />
            </Steps>
        );
        expect(render(wrapper)).toMatchSnapshot();
        expect(wrapper.find('.one-steps-item').at(0).hasClass('one-steps-item-error')).toBe(true);
    });
    test('should be rendered correctly 3', () => {
        const wrapper = mount(
            <Steps current={2} status="error" style={{width: '10px'}}>
                <Step title="设置推广计划" description="描述文案" />
                <Step title="设置推广单元" description="描述文案" />
                <Step title="提交和预览" description="描述文案" />
            </Steps>
        );
        expect(render(wrapper)).toMatchSnapshot();
        expect(wrapper.find('.one-steps-item').at(1).hasClass('one-steps-next-error')).toBe(true);
    });
    test('should be rendered correctly 4', () => {
        const wrapper = mount(
            <Steps current={2} style={{width: '10px'}}>
                <Step title="设置推广计划" description="描述文案" status="finish" />
                <Step title="设置推广计划2" description="描述文案" status="finish" icons={null} />
                <Step title="设置推广单元" description="描述文案" status="error" itemWidth="10px" adjustMarginRight="10px" />
                <Step
                    title="提交和预览"
                    description="描述文案"
                    status="error"
                    icon={(
                        <IconSvg type="times" />
                    )}
                    iconClassName="dls-icon-times"
                />
            </Steps>
        );
        expect(render(wrapper)).toMatchSnapshot();
        expect(wrapper.find('.one-steps-item').at(0).hasClass('one-steps-item-finish')).toBe(true);
    });
    test('should be rendered correctly 5', () => {
        let event = 0;
        const onClickStep = e => {
            event = e;
        };
        const wrapper = mount(
            <Steps current={0} onClickStep={onClickStep}>
                <Step title="设置推广计划" description="描述文案" />
                <Step title="设置推广单元" description="设置推广单元" status="wait" />
                <Step title="提交和预览" description="描述文案三" showTipWhenHover hoverTip="描述文案三" />
            </Steps>
        );
        expect(wrapper.find('.one-steps-item').at(0).hasClass('one-steps-item-is-current')).toBe(true);
        wrapper.find('.one-steps-item').at(1).simulate('click');
        // TODO: 当前接口类型与数字都有问题
        expect(event).toBe('2');
        wrapper.setProps({current: 2});
        expect(wrapper.find('.one-steps-item').at(2).hasClass('one-steps-item-is-current')).toBe(true);
    });
});