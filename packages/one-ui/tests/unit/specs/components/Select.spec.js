/**
 * @file Select.spec.js
 * @<NAME_EMAIL>
 * @date 2020-09-03
 */
import {render, mount} from 'enzyme';
import React from 'react';
import Select from '../../../../src/components/select';
const {Option, OptGroup} = Select;

import mountTest from '../../../shared/mountTest';
import focusTest from '../../../shared/focusTest';
describe('Select Component', () => {
    mountTest(Select);
    focusTest(Select);

    const select = (
        <Select
            className="select-test"
            value="2"
            placeholder="Select a number"
            showArrow
            showSearch
            size="small"
            width={200}
        >
            <OptGroup label="manager">
                <Option className="option-test" value="Banana">
                    Banana
                </Option>
                <Option value="apple">apple</Option>
            </OptGroup>
            <OptGroup label="fruit">
                <Option value="pear">pear</Option>
            </OptGroup>
        </Select>
    );

    test('renders correctly', () => {
        const wrapper = render(select);
        expect(wrapper).toMatchSnapshot();
    });
    test('renders correctly in multiple mode', () => {
        const children = [];
        for (let i = 10; i < 36; i++) {
            children.push(<Option key={i.toString(36) + i}>{i.toString(36) + i}</Option>);
        }
        const wrapper = mount(<Select
            mode="multiple"
            placeholder="Please select"
            defaultValue={['a10', 'c12']}
            style={{width: '400px'}}
        >
            {children}
        </Select>);
        expect(wrapper).toMatchSnapshot();
    });
    test('renders correctly in combobox mode', () => {
        const children = [];
        for (let i = 10; i < 36; i++) {
            children.push(<Option key={i.toString(36) + i}>{i.toString(36) + i}</Option>);
        }
        const wrapper = mount(<Select
            mode="combobox"
            placeholder="Please select"
            defaultValue={['a10', 'c12']}
            style={{width: '400px'}}
        >
            {children}
        </Select>);
        expect(wrapper).toMatchSnapshot();
    });
    test('renders correctly in multiple mode in errorMessage', () => {
        const children = [];
        for (let i = 10; i < 36; i++) {
            children.push(<Option key={i.toString(36) + i}>{i.toString(36) + i}</Option>);
        }
        const wrapper = mount(<Select
            mode="multiple"
            placeholder="Please select"
            defaultValue={['a10', 'c12']}
            style={{width: '400px'}}
            errorMessage="报错报错"
            multipleRenderTargetMode="custom"
            customRenderTarget={value => {
                return `这是一个自定义target的下拉选择${value[0]}等${value.length}个`;
            }}
        >
            {children}
        </Select>);
        expect(wrapper).toMatchSnapshot();
    });
    test('should ok use on change in single mode', () => {
        const onChange = jest.fn();
        const wrapper = mount(
            <Select onChange={onChange}>
                <Option value="1">1</Option>
                <Option value="2">2</Option>
                <Option value="3">3</Option>
            </Select>,
        );
        wrapper.find('.one-select').simulate('click');
        wrapper.update();
        const dropdownWrapper = mount(
            wrapper
                .find('Trigger')
                .instance()
                .getComponent(),
        );
        expect(dropdownWrapper.find('.one-select-dropdown-menu-item').length).toBe(3);
        dropdownWrapper.find('.one-select-dropdown-menu-item').at(0).simulate('click');
        expect(onChange).toHaveBeenCalled();
    });
    test('should ok use on change in multiple mode', () => {
        const onChange = jest.fn();
        const children = [];
        for (let i = 10; i < 36; i++) {
            children.push(<Option key={i.toString(36) + i}>{i.toString(36) + i}</Option>);
        }
        const wrapper = mount(
            <Select onChange={onChange} mode="multiple" maxTagCount={1}>
                {children}
            </Select>,
        );
        wrapper.find('.one-select').simulate('click');
        wrapper.update();
        const dropdownWrapper = mount(
            wrapper
                .find('Trigger')
                .instance()
                .getComponent(),
        );
        expect(dropdownWrapper.find('.one-select-dropdown-menu-item').length).toBe(26);
        dropdownWrapper.find('.one-select-dropdown-menu-item').at(0).simulate('click');
        dropdownWrapper.find('.one-select-dropdown-menu-item').at(1).simulate('click');
        dropdownWrapper.find('.one-select-dropdown-menu-item').at(2).simulate('click');
        expect(onChange).toHaveBeenCalled();
    });
    test('Option with CheckboxText', () => {
        const onChange = jest.fn();
        const wrapper = mount(
            <Select
                mode="multiple"
                onChange={onChange}
            >
                <Option key="1">1</Option>
                <Option key="2">
                    <Select.CheckboxText value="2" source={['2']} />2
                </Option>
            </Select>
        );
        wrapper.find('.one-select').simulate('click');
        expect(wrapper.find('.one-select-dropdown-menu-item .one-checkbox').length).toBe(2);
        expect(wrapper.find('.one-select-dropdown-menu-item input').at(1).prop('checked')).toBe(true);
        wrapper.find('.one-select-dropdown-menu-item').at(1).simulate('click');
        expect(onChange).toHaveBeenCalled();
    });
});