/**
 * @file toast 组件单测
 * <AUTHOR>
 * @date 2020-09-17
 */
import React from 'react';
import {mount} from 'enzyme';
import Toast from '../../../../src/components/toast/index';

describe('toast', () => {
    beforeEach(() => {
        jest.useFakeTimers();
    });

    afterEach(() => {
        Toast.destroy();
        jest.useRealTimers();
    });

    it('should be able to hide manually', () => {
        const hide1 = Toast.info('whatever', 0);
        const hide2 = Toast.info('whatever', 0);
        expect(document.querySelectorAll('.one-toast-notice').length).toBe(2);
        hide1();
        jest.runAllTimers();
        expect(document.querySelectorAll('.one-toast-notice').length).toBe(0);
        hide2();
        jest.runAllTimers();
        expect(document.querySelectorAll('.one-toast-notice').length).toBe(0);
    });

    it('should be able to remove manually with a unique key', () => {
        const key1 = 'key1';
        const key2 = 'key2';
        Toast.info({content: 'toast1', key: 'key1', duration: 0});
        Toast.info({content: 'toast2', key: 'key2', duration: 0});
        expect(document.querySelectorAll('.one-toast-notice').length).toBe(2);
        Toast.destroy(key1);
        jest.runAllTimers();
        expect(document.querySelectorAll('.one-toast-notice').length).toBe(0);
        Toast.destroy(key2);
        jest.runAllTimers();
        expect(document.querySelectorAll('.one-toast-notice').length).toBe(0);
    });

    it('should be able to destroy globally', () => {
        Toast.info('whatever', 0);
        Toast.info('whatever', 0);
        expect(document.querySelectorAll('.one-toast').length).toBe(1);
        expect(document.querySelectorAll('.one-toast-notice').length).toBe(2);
        Toast.destroy();
        expect(document.querySelectorAll('.one-toast').length).toBe(0);
        expect(document.querySelectorAll('.one-toast-notice').length).toBe(0);
    });

    it('should not need to use duration argument when using the onClose arguments', () => {
        Toast.success();
    });

    it('should have the default duration when using the onClose arguments', () => {
        jest.useRealTimers();
        const defaultDuration = 3;
        const now = Date.now();
        Toast.info('whatever', () => {
        // calculate the approximately duration value
            const aboutDuration = parseInt((Date.now() - now) / 1000, 10);
            expect(aboutDuration).toBe(defaultDuration);
        });
    });

    it('should be called like promise', done => {
        jest.useRealTimers();
        const defaultDuration = 3;
        const now = Date.now();
        Toast.info('whatever').then(() => {
        // calculate the approximately duration value
            const aboutDuration = parseInt((Date.now() - now) / 1000, 10);
            expect(aboutDuration).toBe(defaultDuration);
            done();
        });
    });

    it('should hide Toast correctly', () => {
        let hide;
        class Test extends React.Component {
            componentDidMount() {
                hide = Toast.loading('Action in progress..', 0, () => {}, false);
            }

            render() {
                return <div>test</div>;
            }
        }
        mount(<Test />);
        expect(document.querySelectorAll('.one-toast-notice').length).toBe(1);
        hide();
        jest.runAllTimers();
        expect(document.querySelectorAll('.one-toast-notice').length).toBe(0);
    });

    it('should destroy toast correctly', () => {
        class Test extends React.Component {
            componentDidMount() {
                Toast.loading('Action in progress1..', 0);
                Toast.loading('Action in progress2..', 0);
                setTimeout(() => Toast.destroy(), 1000);
            }

            render() {
                return <div>test</div>;
            }
        }
        mount(<Test />);
        expect(document.querySelectorAll('.one-toast-notice').length).toBe(2);
        jest.runAllTimers();
        expect(document.querySelectorAll('.one-toast-notice').length).toBe(0);
    });

    it('should support update Toast content with a unique key', () => {
        const key = 'updatable';
        class Test extends React.Component {
            componentDidMount() {
                Toast.loading({content: 'Loading...', key});
                // Testing that content of the Toast should be updated.
                setTimeout(() => Toast.success({content: 'Loaded', key}), 1000);
                setTimeout(() => Toast.destroy(), 3000);
            }

            render() {
                    return <div>test</div>;
                }
        }

        mount(<Test />);
        expect(document.querySelectorAll('.one-toast-notice').length).toBe(1);
        jest.advanceTimersByTime(1500);
        expect(document.querySelectorAll('.one-toast-notice').length).toBe(2);
        jest.runAllTimers();
        expect(document.querySelectorAll('.one-toast-notice').length).toBe(0);
    });

    it('render warning', () => {
        const key = 'updatable';
        class Test extends React.Component {
            componentDidMount() {
                Toast.loading({content: 'Loading...', key});
                // Testing that content of the Toast should be updated.
                setTimeout(() => Toast.warning({content: 'Loaded', key}), 1000);
                setTimeout(() => Toast.destroy(), 3000);
            }
            render() {
                return <div>test</div>;
            }
        }
        mount(<Test />);
    });

    it('render toast config', () => {
        class Test extends React.Component {
            componentDidMount() {
                Toast.config({duration: 100, top: '30%', prefixCls: '', size: '', getContainer: ''});
            }
            render() {
                return <div>test</div>;
            }
        }
        mount(<Test />);
    });

    it('update Toast content with a unique key and cancel manually', () => {
        const key = 'updatable';
        class Test extends React.Component {
            componentDidMount() {
                const hideLoading = Toast.loading({content: 'Loading...', key, duration: 0});
                // Testing that content of the Toast should be cancel manually.
                setTimeout(hideLoading, 1000);
            }

            render() {
                return <div>test</div>;
            }
        }

        mount(<Test />);
        expect(document.querySelectorAll('.one-toast-notice').length).toBe(0);
        jest.advanceTimersByTime(1500);
        expect(document.querySelectorAll('.one-toast-notice').length).toBe(0);
    });

    it('should not throw error when pass null', () => {
        Toast.error(null);
    });
});
