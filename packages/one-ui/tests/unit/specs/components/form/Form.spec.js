/**
 * @file: Form.spec.js
 * @Date: 2020-08-21 15:32:27
 * @Last Modified by: cheng<PERSON><PERSON><PERSON>
 * @Last Modified time: 2020-09-16 13:05:47
 */

import React, {forwardRef, createRef} from 'react';
import {mount, render} from 'enzyme';
import mountTest from '../../../../shared/mountTest';
import Form from '../../../../../src/components/form';
import {commonMount} from './common/util';
import Input from '../../../../../src/components/input';
import Transfer from '../../../../../src/components/transfer';
import Button from '../../../../../src/components/button';
import {sleep} from '../../../../utils';

describe('Form', () => {
    mountTest(Form);
    mountTest(Form.Item);

    it('hideRequiredMark', () => {
        const wrapper = mount(<Form hideRequiredMark />);
        expect(wrapper.find('form').hasClass('one-form-hide-required-mark')).toBe(true);
    });

    it('Form.Item should support data-*、aria-* and custom attribute', () => {
        const wrapper = render(
            <Form>
                <Form.Item data-text="123" aria-hidden="true" cccc="bbbb">
                   测试自定义属性
                </Form.Item>
            </Form>
        );
        expect(wrapper).toMatchSnapshot();
    });

    describe('wrappedComponentRef', () => {
        it('warns on functional component', () => {
            const majorVersion = React.version.split('.')[0];
            if (majorVersion <= 15) {
                return;
            }
            const spy = jest.spyOn(console, 'error').mockImplementation(() => {});
            const Wrapped = Form.create()(() => <Form />);
            mount(<Wrapped wrappedComponentRef={() => {}} />);
            expect(spy).toHaveBeenCalled();
            expect(console.error).toHaveBeenCalled();
            spy.mockReset();
            spy.mockRestore();
        });

        it('get instance on class component', () => {
            class TestForm extends React.Component {
                render() {
                    return <Form />;
                }
            }
            const Wrapped = Form.create()(TestForm);
            const {formWrapper} = commonMount(Wrapped);
            expect(formWrapper).toBeInstanceOf(TestForm);
        });
    });
    describe('FormItem', () => {
        it('FormItem: generate snapshot when validates fields', done => {
            let wrapper;
            const TestForm = props => (
                <Form>
                    <Form.Item>
                        {props.form.getFieldDecorator('test', {
                            rules: [
                                {
                                    validator: (rule, value, callback) => {
                                        setTimeout(() => {
                                            callback();
                                            expect(wrapper.render()).toMatchSnapshot(); // after validate
                                            done();
                                        }, 100);
                                    },
                                },
                            ],
                        })(<input allowClear />)}
                    </Form.Item>
                </Form>
            );
            const Wrapped = Form.create()(TestForm);
            wrapper = mount(<Wrapped />);
            expect(wrapper.render()).toMatchSnapshot(); // before validate
            wrapper.find('input').simulate('change', {target: {value: 'test'}});
            expect(wrapper.render()).toMatchSnapshot(); // validating
        });
    });

    describe('Form.Field', () => {
        const TestForm = forwardRef(({showNo, ...props}, ref) => {
            const stores = {
                '1': {
                    key: '1',
                    title: '门店1'
                },
                '2': {
                    key: '2',
                    title: '门店2'
                },
                '10': {
                    key: '10',
                    title: '门店3',
                    children: ['11', '12']
                },
                '11': {
                    key: '11',
                    title: '门店31'
                },
                '12': {
                    key: '12',
                    title: '门店32'
                }
            };

            const candidateList = ['1', '2', '10'];

            return (
                <Form {...props} ref={ref}>
                    <Form.Field
                        label="姓名"
                        tip="最少5个字符、最长10个字符"
                        name="name"
                        rules={[
                            {required: true, message: '请输入用户名'},
                            {min: 5, message: '最少5个字符'},
                            {max: 10, message: '最长10个字符'}
                        ]}
                    >
                        <Input placeholder="Name" />
                    </Form.Field>
                    <Form.Field
                        label="年龄"
                        help="18岁以上"
                        helpPosition="side"
                        name="age"
                        rules={[
                            {required: true, message: '请输入年龄'},
                            {pattern: /\d+/, message: '请输入数字'}
                        ]}
                    >
                        <Input placeholder="Age" />
                    </Form.Field>
                    <Form.Field label="地址">
                        <Form.Field
                            help="街道"
                            helpPosition="top"
                            name="street"
                            abstract
                            rules={[{required: true, message: '请输入街道'}]}
                        >
                            <Input placeholder="Street" width={150} />
                        </Form.Field>
                        {showNo !== false
                            && <Form.Field
                                help="门牌号"
                                helpPosition="top"
                                name="no"
                                abstract
                                rules={[{required: true, message: '请输入门牌号'}]}
                            >
                                <Input placeholder="No." width={150} />
                            </Form.Field>
                        }
                    </Form.Field>
                    <Form.Field
                        label="异步校验"
                        help="通常用于请求服务端进行校验"
                        name="pwd"
                        rules={[
                            {required: true, message: '请输入123'},
                            {validator: (rule, value) => {
                                return new Promise((resolve, reject) => {
                                    if (!value || value.trim() === '' || value.trim() === '123') {
                                        resolve();
                                        return;
                                    }
                                    setTimeout(() => {
                                        reject(['服务端校验错误，请输入123']);
                                    }, 3000);
                                });
                            }}
                        ]}
                    >
                        <Input placeholder="Async" />
                    </Form.Field>
                    <Form.Field label="门店">
                        <Transfer
                            allDataMap={stores}
                            candidateList={candidateList}
                            SelectedItem={({itemKey, title}) => (
                                <div style={{display: 'flex'}}>
                                    <div style={{width: 60}}>{title}</div>
                                    <Form.Field
                                        name={`store[${itemKey}]`}
                                        abstract
                                        rules={[
                                            {required: true, message: `请输入【${title}】地址`}
                                        ]}
                                    >
                                        <Input size="small" width={120} />
                                    </Form.Field>
                                </div>
                            )}
                        />
                    </Form.Field>
                    <Form.Field actions label="">
                        <Button type="primary" htmlType="submit">Submit</Button>
                    </Form.Field>
                </Form>
            );
        });
        test('snapshot', () => {
            const form = mount(<TestForm />);
            expect(form).toMatchSnapshot();
        });

        test('`tip` prop', () => {
            const form = mount(<TestForm />);
            expect(form.find('Message.one-form-field-tip').length).toBe(1);
            expect(form.find('Message.one-form-field-tip').prop('children')).toBe('最少5个字符、最长10个字符');
        });

        test('`helpPosition` prop', () => {
            const form = mount(<TestForm />);
            expect(
                form.find('FormField[helpPosition="side"] .one-form-field-content Message[type="aux"]').length
            ).toBe(1);
            expect(
                form.find('FormField[helpPosition="side"] .one-form-field-messages Message[type="aux"]').length
            ).toBe(0);

            expect(
                form.find('FormField[helpPosition="bottom"] .one-form-field-message Message[type="aux"]').length
            ).toBe(1);

            expect(
                form.find('FormField[helpPosition="top"] .one-form-field-main Message[type="aux"]').length
            ).toBe(2);
            expect(
                form.find('FormField[helpPosition="top"] .one-form-field-messages Message[type="aux"]').length
            ).toBe(0);
        });

        test('`abstract` prop', () => {
            const form = mount(<TestForm />);
            expect(form.find('FormField[abstract=true]').length).toBe(2);
            form.setProps({showNo: false});
            expect(form.find('FormField[abstract=true]').length).toBe(1);
        });

        test('validate', async () => {
            const form = mount(<TestForm />);
            const input = form.find('FormField').at(0).find('input');
            input.simulate('change');
            await sleep(50);
            form.update();
            expect(form.find('FormField').at(0).find('Message[type="error"]').text()).toBe('请输入用户名');

            input.getDOMNode().value = 'test';
            input.simulate('change');
            await sleep(50);
            form.update();
            expect(form.find('FormField').at(0).find('Message[type="error"]').text()).toBe('最少5个字符');

            const input2 = form.find('FormField[label="异步校验"]').at(0).find('input');
            expect(
                form.find('FormField[label="异步校验"]').at(0).find('Loading[loading=true]').length
            ).toBe(0);
            input2.getDOMNode().value = 'test';
            input2.simulate('change');
            expect(
                form.find('FormField[label="异步校验"]').at(0).find('Message[type="aux"]').text()
            ).toBe('通常用于请求服务端进行校验');
            expect(
                form.find('FormField[label="异步校验"]').at(0).find('Loading[loading=true]').length
            ).toBe(1);
            await sleep(3500);
            form.update();
            expect(
                form.find('FormField[label="异步校验"]').at(0).find('Loading[loading=true]').length
            ).toBe(0);
            expect(
                form.find('FormField[label="异步校验"]').at(0).find('Message[type="error"]').text()
            ).toBe('服务端校验错误，请输入123');
        });

        test('validate `abstract` field', async () => {
            const form = mount(<TestForm />);
            const input = form.find('FormField[abstract=true]').at(0).find('input');
            input.simulate('change');
            await sleep(50);
            form.update();
            expect(form.find('FormField[abstract=true]').at(0).find('Message[type="error"]').length).toBe(0);
            expect(
                form.find('FormField[abstract=true]').at(0).parents('FormField').find('Message[type="error"]').length
            ).toBe(1);
        });

        test('`onSubmit` prop', () => {
            const onSubmit = jest.fn();
            const form = mount(<TestForm onSubmit={onSubmit} />);
            form.simulate('submit');
            expect(onSubmit).toBeCalled();
        });

        test('`onReset` prop', () => {
            const onReset = jest.fn();
            const form = mount(<TestForm onReset={onReset} />);
            form.simulate('reset');
            expect(onReset).toBeCalled();
        });

        test('`onFinishFailed` prop', async () => {
            const onFinishFailed = jest.fn();
            const instance = createRef();
            const form = mount(<TestForm ref={instance} onFinishFailed={onFinishFailed} />);
            const values = {age: 1, name: 'name', no: 'no', pwd: '123', street: 'street'};
            instance.current.setFieldsValue(values);
            form.simulate('submit');
            await sleep(3500);
            expect(onFinishFailed).toBeCalledWith(
                {
                    "name": {"errors": [{"field": "name", "fieldValue": "name", "message": "最少5个字符"}]}
                },
                values
            );
        });

        test('`onFinish` prop', async () => {
            const values = {age: 1, name: 'account', no: 'no', pwd: '123', street: 'street'};
            const onFinish = jest.fn();
            const instance = createRef();
            const form = mount(<TestForm ref={instance} onFinish={onFinish} />);
            instance.current.setFieldsValue(values);
            form.simulate('submit');
            await sleep(3500);
            expect(onFinish).toBeCalledWith(values);
        });
    });
});
