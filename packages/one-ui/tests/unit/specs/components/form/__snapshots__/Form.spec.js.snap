// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Form Form.Field snapshot 1`] = `
<ForwardRef>
  <ForwardRef
    className=""
    colon={false}
    density="default"
    labelAlign="right"
    labelPosition="side"
    layout="default"
    prefixCls="one-form"
    size="medium"
  >
    <BaseForm
      fieldNameProp="id"
      formPropName="form"
      mapProps={[Function]}
      size="medium"
    >
      <form
        autoComplete="off"
        className="one-form one-form-default one-form-label-position-side one-form-medium one-form-type-default"
        onReset={[Function]}
        onSubmit={[Function]}
      >
        <FormField
          helpPosition="bottom"
          label="姓名"
          messageDisplay="simple"
          name="name"
          prefixCls="one-form"
          rules={
            Array [
              Object {
                "message": "请输入用户名",
                "required": true,
              },
              Object {
                "message": "最少5个字符",
                "min": 5,
              },
              Object {
                "max": 10,
                "message": "最长10个字符",
              },
            ]
          }
          tip="最少5个字符、最长10个字符"
          validateFirst={true}
        >
          <div
            className="one-form-field"
          >
            <div
              className="one-form-field-label one-form-field-label-required"
            >
              <label
                title="姓名"
              >
                姓名
              </label>
              <Message
                className="one-form-field-tip"
                display="popup"
                icon={<IconQuestionCircle />}
                prefixCls="one-message"
                size="small"
                type="aux"
              >
                <div
                  className="one-message one-message-small one-message-type-aux one-message-display-popup one-form-field-tip"
                >
                  <div
                    className="one-message-icon"
                  >
                    <Popover
                      content={
                        <div
                          className="one-message-content"
                        >
                          最少5个字符、最长10个字符
                        </div>
                      }
                      placement="top"
                    >
                      <Popover
                        content={
                          <div
                            className="one-message-content"
                          >
                            最少5个字符、最长10个字符
                          </div>
                        }
                        mouseEnterDelay={0.1}
                        mouseLeaveDelay={0.1}
                        overlayStyle={Object {}}
                        placement="top"
                        prefixCls="one-popover"
                        size="medium"
                        trigger="hover"
                      >
                        <Tooltip
                          content={
                            <div
                              className="one-message-content"
                            >
                              最少5个字符、最长10个字符
                            </div>
                          }
                          mouseEnterDelay={0.1}
                          mouseLeaveDelay={0.1}
                          overlay={
                            <React.Fragment>
                              <div
                                className="one-popover-inner-content"
                              >
                                <div
                                  className="one-message-content"
                                >
                                  最少5个字符、最长10个字符
                                </div>
                              </div>
                            </React.Fragment>
                          }
                          overlayClassName="one-popover-medium"
                          overlayStyle={Object {}}
                          placement="top"
                          prefixCls="one-popover"
                          size="medium"
                          trigger="hover"
                        >
                          <Tooltip
                            arrowPointAtCenter={false}
                            autoAdjustOverflow={true}
                            content={
                              <div
                                className="one-message-content"
                              >
                                最少5个字符、最长10个字符
                              </div>
                            }
                            mouseEnterDelay={0.1}
                            mouseLeaveDelay={0.1}
                            overlay={
                              <React.Fragment>
                                <div
                                  className="one-popover-inner-content"
                                >
                                  <div
                                    className="one-message-content"
                                  >
                                    最少5个字符、最长10个字符
                                  </div>
                                </div>
                              </React.Fragment>
                            }
                            overlayClassName="one-popover-medium"
                            overlayStyle={Object {}}
                            placement="top"
                            prefixCls="one-popover"
                            size="medium"
                            transitionName="zoom-big-fast"
                            trigger="hover"
                            type="light"
                          >
                            <Tooltip
                              arrowPointAtCenter={false}
                              autoAdjustOverflow={true}
                              builtinPlacements={
                                Object {
                                  "bottom": Object {
                                    "offset": Array [
                                      0,
                                      4,
                                    ],
                                    "overflow": Object {
                                      "adjustX": 1,
                                      "adjustY": 1,
                                    },
                                    "points": Array [
                                      "tc",
                                      "bc",
                                    ],
                                    "targetOffset": Array [
                                      0,
                                      0,
                                    ],
                                  },
                                  "bottomLeft": Object {
                                    "offset": Array [
                                      0,
                                      4,
                                    ],
                                    "overflow": Object {
                                      "adjustX": 1,
                                      "adjustY": 1,
                                    },
                                    "points": Array [
                                      "tl",
                                      "bl",
                                    ],
                                    "targetOffset": Array [
                                      0,
                                      0,
                                    ],
                                  },
                                  "bottomRight": Object {
                                    "offset": Array [
                                      0,
                                      4,
                                    ],
                                    "overflow": Object {
                                      "adjustX": 1,
                                      "adjustY": 1,
                                    },
                                    "points": Array [
                                      "tr",
                                      "br",
                                    ],
                                    "targetOffset": Array [
                                      0,
                                      0,
                                    ],
                                  },
                                  "left": Object {
                                    "offset": Array [
                                      -4,
                                      0,
                                    ],
                                    "overflow": Object {
                                      "adjustX": 1,
                                      "adjustY": 1,
                                    },
                                    "points": Array [
                                      "cr",
                                      "cl",
                                    ],
                                    "targetOffset": Array [
                                      0,
                                      0,
                                    ],
                                  },
                                  "leftBottom": Object {
                                    "offset": Array [
                                      -4,
                                      0,
                                    ],
                                    "overflow": Object {
                                      "adjustX": 1,
                                      "adjustY": 1,
                                    },
                                    "points": Array [
                                      "br",
                                      "bl",
                                    ],
                                    "targetOffset": Array [
                                      0,
                                      0,
                                    ],
                                  },
                                  "leftTop": Object {
                                    "offset": Array [
                                      -4,
                                      0,
                                    ],
                                    "overflow": Object {
                                      "adjustX": 1,
                                      "adjustY": 1,
                                    },
                                    "points": Array [
                                      "tr",
                                      "tl",
                                    ],
                                    "targetOffset": Array [
                                      0,
                                      0,
                                    ],
                                  },
                                  "right": Object {
                                    "offset": Array [
                                      4,
                                      0,
                                    ],
                                    "overflow": Object {
                                      "adjustX": 1,
                                      "adjustY": 1,
                                    },
                                    "points": Array [
                                      "cl",
                                      "cr",
                                    ],
                                    "targetOffset": Array [
                                      0,
                                      0,
                                    ],
                                  },
                                  "rightBottom": Object {
                                    "offset": Array [
                                      4,
                                      0,
                                    ],
                                    "overflow": Object {
                                      "adjustX": 1,
                                      "adjustY": 1,
                                    },
                                    "points": Array [
                                      "bl",
                                      "br",
                                    ],
                                    "targetOffset": Array [
                                      0,
                                      0,
                                    ],
                                  },
                                  "rightTop": Object {
                                    "offset": Array [
                                      4,
                                      0,
                                    ],
                                    "overflow": Object {
                                      "adjustX": 1,
                                      "adjustY": 1,
                                    },
                                    "points": Array [
                                      "tl",
                                      "tr",
                                    ],
                                    "targetOffset": Array [
                                      0,
                                      0,
                                    ],
                                  },
                                  "top": Object {
                                    "offset": Array [
                                      0,
                                      -4,
                                    ],
                                    "overflow": Object {
                                      "adjustX": 1,
                                      "adjustY": 1,
                                    },
                                    "points": Array [
                                      "bc",
                                      "tc",
                                    ],
                                    "targetOffset": Array [
                                      0,
                                      0,
                                    ],
                                  },
                                  "topLeft": Object {
                                    "offset": Array [
                                      0,
                                      -4,
                                    ],
                                    "overflow": Object {
                                      "adjustX": 1,
                                      "adjustY": 1,
                                    },
                                    "points": Array [
                                      "bl",
                                      "tl",
                                    ],
                                    "targetOffset": Array [
                                      0,
                                      0,
                                    ],
                                  },
                                  "topRight": Object {
                                    "offset": Array [
                                      0,
                                      -4,
                                    ],
                                    "overflow": Object {
                                      "adjustX": 1,
                                      "adjustY": 1,
                                    },
                                    "points": Array [
                                      "br",
                                      "tr",
                                    ],
                                    "targetOffset": Array [
                                      0,
                                      0,
                                    ],
                                  },
                                }
                              }
                              content={
                                <div
                                  className="one-message-content"
                                >
                                  最少5个字符、最长10个字符
                                </div>
                              }
                              mouseEnterDelay={0.1}
                              mouseLeaveDelay={0.1}
                              onVisibleChange={[Function]}
                              overlay={
                                <React.Fragment>
                                  <div
                                    className="one-popover-inner-content"
                                  >
                                    <div
                                      className="one-message-content"
                                    >
                                      最少5个字符、最长10个字符
                                    </div>
                                  </div>
                                </React.Fragment>
                              }
                              overlayClassName="one-popover-medium one-popover-light"
                              overlayStyle={Object {}}
                              placement="top"
                              prefixCls="one-popover"
                              size="medium"
                              transitionName="zoom-big-fast"
                              trigger="hover"
                              type="light"
                              visible={false}
                            >
                              <Trigger
                                action="hover"
                                afterPopupVisibleChange={[Function]}
                                arrowPointAtCenter={false}
                                autoAdjustOverflow={true}
                                autoDestroy={false}
                                blurDelay={0.15}
                                builtinPlacements={
                                  Object {
                                    "bottom": Object {
                                      "offset": Array [
                                        0,
                                        4,
                                      ],
                                      "overflow": Object {
                                        "adjustX": 1,
                                        "adjustY": 1,
                                      },
                                      "points": Array [
                                        "tc",
                                        "bc",
                                      ],
                                      "targetOffset": Array [
                                        0,
                                        0,
                                      ],
                                    },
                                    "bottomLeft": Object {
                                      "offset": Array [
                                        0,
                                        4,
                                      ],
                                      "overflow": Object {
                                        "adjustX": 1,
                                        "adjustY": 1,
                                      },
                                      "points": Array [
                                        "tl",
                                        "bl",
                                      ],
                                      "targetOffset": Array [
                                        0,
                                        0,
                                      ],
                                    },
                                    "bottomRight": Object {
                                      "offset": Array [
                                        0,
                                        4,
                                      ],
                                      "overflow": Object {
                                        "adjustX": 1,
                                        "adjustY": 1,
                                      },
                                      "points": Array [
                                        "tr",
                                        "br",
                                      ],
                                      "targetOffset": Array [
                                        0,
                                        0,
                                      ],
                                    },
                                    "left": Object {
                                      "offset": Array [
                                        -4,
                                        0,
                                      ],
                                      "overflow": Object {
                                        "adjustX": 1,
                                        "adjustY": 1,
                                      },
                                      "points": Array [
                                        "cr",
                                        "cl",
                                      ],
                                      "targetOffset": Array [
                                        0,
                                        0,
                                      ],
                                    },
                                    "leftBottom": Object {
                                      "offset": Array [
                                        -4,
                                        0,
                                      ],
                                      "overflow": Object {
                                        "adjustX": 1,
                                        "adjustY": 1,
                                      },
                                      "points": Array [
                                        "br",
                                        "bl",
                                      ],
                                      "targetOffset": Array [
                                        0,
                                        0,
                                      ],
                                    },
                                    "leftTop": Object {
                                      "offset": Array [
                                        -4,
                                        0,
                                      ],
                                      "overflow": Object {
                                        "adjustX": 1,
                                        "adjustY": 1,
                                      },
                                      "points": Array [
                                        "tr",
                                        "tl",
                                      ],
                                      "targetOffset": Array [
                                        0,
                                        0,
                                      ],
                                    },
                                    "right": Object {
                                      "offset": Array [
                                        4,
                                        0,
                                      ],
                                      "overflow": Object {
                                        "adjustX": 1,
                                        "adjustY": 1,
                                      },
                                      "points": Array [
                                        "cl",
                                        "cr",
                                      ],
                                      "targetOffset": Array [
                                        0,
                                        0,
                                      ],
                                    },
                                    "rightBottom": Object {
                                      "offset": Array [
                                        4,
                                        0,
                                      ],
                                      "overflow": Object {
                                        "adjustX": 1,
                                        "adjustY": 1,
                                      },
                                      "points": Array [
                                        "bl",
                                        "br",
                                      ],
                                      "targetOffset": Array [
                                        0,
                                        0,
                                      ],
                                    },
                                    "rightTop": Object {
                                      "offset": Array [
                                        4,
                                        0,
                                      ],
                                      "overflow": Object {
                                        "adjustX": 1,
                                        "adjustY": 1,
                                      },
                                      "points": Array [
                                        "tl",
                                        "tr",
                                      ],
                                      "targetOffset": Array [
                                        0,
                                        0,
                                      ],
                                    },
                                    "top": Object {
                                      "offset": Array [
                                        0,
                                        -4,
                                      ],
                                      "overflow": Object {
                                        "adjustX": 1,
                                        "adjustY": 1,
                                      },
                                      "points": Array [
                                        "bc",
                                        "tc",
                                      ],
                                      "targetOffset": Array [
                                        0,
                                        0,
                                      ],
                                    },
                                    "topLeft": Object {
                                      "offset": Array [
                                        0,
                                        -4,
                                      ],
                                      "overflow": Object {
                                        "adjustX": 1,
                                        "adjustY": 1,
                                      },
                                      "points": Array [
                                        "bl",
                                        "tl",
                                      ],
                                      "targetOffset": Array [
                                        0,
                                        0,
                                      ],
                                    },
                                    "topRight": Object {
                                      "offset": Array [
                                        0,
                                        -4,
                                      ],
                                      "overflow": Object {
                                        "adjustX": 1,
                                        "adjustY": 1,
                                      },
                                      "points": Array [
                                        "br",
                                        "tr",
                                      ],
                                      "targetOffset": Array [
                                        0,
                                        0,
                                      ],
                                    },
                                  }
                                }
                                content={
                                  <div
                                    className="one-message-content"
                                  >
                                    最少5个字符、最长10个字符
                                  </div>
                                }
                                defaultPopupVisible={false}
                                destroyPopupOnHide={false}
                                focusDelay={0}
                                getDocument={[Function]}
                                getPopupClassNameFromAlign={[Function]}
                                hideAction={Array []}
                                mask={false}
                                maskClosable={true}
                                mouseEnterDelay={0.1}
                                mouseLeaveDelay={0.1}
                                onPopupAlign={[Function]}
                                onPopupVisibleChange={[Function]}
                                overlay={
                                  <React.Fragment>
                                    <div
                                      className="one-popover-inner-content"
                                    >
                                      <div
                                        className="one-message-content"
                                      >
                                        最少5个字符、最长10个字符
                                      </div>
                                    </div>
                                  </React.Fragment>
                                }
                                popup={
                                  Array [
                                    <div
                                      className="one-popover-arrow"
                                    />,
                                    <Content
                                      overlay={
                                        <React.Fragment>
                                          <div
                                            className="one-popover-inner-content"
                                          >
                                            <div
                                              className="one-message-content"
                                            >
                                              最少5个字符、最长10个字符
                                            </div>
                                          </div>
                                        </React.Fragment>
                                      }
                                      prefixCls="one-popover"
                                    />,
                                  ]
                                }
                                popupAlign={
                                  Object {
                                    "ignoreShake": true,
                                  }
                                }
                                popupClassName="one-popover-medium one-popover-light"
                                popupMotion={null}
                                popupPlacement="top"
                                popupStyle={Object {}}
                                popupVisible={false}
                                prefixCls="one-popover"
                                showAction={Array []}
                                size="medium"
                                type="light"
                                visible={false}
                              >
                                <IconQuestionCircle
                                  key="trigger"
                                  onMouseEnter={[Function]}
                                  onMouseLeave={[Function]}
                                >
                                  <svg
                                    className="dls-icon "
                                    dangerouslySetInnerHTML={
                                      Object {
                                        "__html": "<path fill=\\"currentColor\\" d=\\"M12.054 18.34a1.242 1.242 0 1 0 0-2.484 1.242 1.242 0 0 0 0 2.484zM8.3 9.7a3.7 3.7 0 1 1 5.813 3.038c-.029.021-.064.05-.106.084-.12.1-.28.247-.438.431-.33.385-.569.835-.569 1.307h-2c0-1.148.57-2.048 1.05-2.608a5.739 5.739 0 0 1 .819-.78l-.004-.008a1.82 1.82 0 0 0 .08-.05l.011-.009.072-.052.009-.006A1.69 1.69 0 0 0 13.7 9.7a1.7 1.7 0 1 0-3.4 0h-2z\\"/><path fill=\\"currentColor\\" fill-rule=\\"evenodd\\" d=\\"M23 12c0 6.075-4.925 11-11 11S1 18.075 1 12 5.925 1 12 1s11 4.925 11 11zm-2 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0z\\" clip-rule=\\"evenodd\\"/>",
                                      }
                                    }
                                    fill="none"
                                    focusable={false}
                                    height={24}
                                    onMouseEnter={[Function]}
                                    onMouseLeave={[Function]}
                                    viewBox="0 0 24 24"
                                    width={24}
                                    xmlns="http://www.w3.org/2000/svg"
                                  />
                                </IconQuestionCircle>
                              </Trigger>
                            </Tooltip>
                          </Tooltip>
                        </Tooltip>
                      </Popover>
                    </Popover>
                  </div>
                </div>
              </Message>
            </div>
            <div
              className="one-form-field-main"
            >
              <div
                className="one-form-field-content"
                key="Content"
              >
                <Input
                  className=""
                  data-__field={
                    Object {
                      "name": "name",
                    }
                  }
                  data-__meta={
                    Object {
                      "name": "name",
                      "originalProps": Object {
                        "className": "",
                        "placeholder": "Name",
                      },
                      "ref": null,
                      "rules": Array [
                        Object {
                          "message": "请输入用户名",
                          "required": true,
                        },
                        Object {
                          "message": "最少5个字符",
                          "min": 5,
                        },
                        Object {
                          "max": 10,
                          "message": "最长10个字符",
                        },
                      ],
                      "trigger": "onChange",
                      "validate": Array [
                        Object {
                          "rules": Array [
                            Object {
                              "message": "请输入用户名",
                              "required": true,
                            },
                            Object {
                              "message": "最少5个字符",
                              "min": 5,
                            },
                            Object {
                              "max": 10,
                              "message": "最长10个字符",
                            },
                          ],
                          "trigger": Array [
                            "onChange",
                          ],
                        },
                      ],
                      "validateFirst": true,
                      "valuePropName": "value",
                    }
                  }
                  id="name"
                  onChange={[Function]}
                  placeholder="Name"
                  size="medium"
                >
                  <Input
                    className=""
                    countMode="cn"
                    data-__field={
                      Object {
                        "name": "name",
                      }
                    }
                    data-__meta={
                      Object {
                        "name": "name",
                        "originalProps": Object {
                          "className": "",
                          "placeholder": "Name",
                        },
                        "ref": null,
                        "rules": Array [
                          Object {
                            "message": "请输入用户名",
                            "required": true,
                          },
                          Object {
                            "message": "最少5个字符",
                            "min": 5,
                          },
                          Object {
                            "max": 10,
                            "message": "最长10个字符",
                          },
                        ],
                        "trigger": "onChange",
                        "validate": Array [
                          Object {
                            "rules": Array [
                              Object {
                                "message": "请输入用户名",
                                "required": true,
                              },
                              Object {
                                "message": "最少5个字符",
                                "min": 5,
                              },
                              Object {
                                "max": 10,
                                "message": "最长10个字符",
                              },
                            ],
                            "trigger": Array [
                              "onChange",
                            ],
                          },
                        ],
                        "validateFirst": true,
                        "valuePropName": "value",
                      }
                    }
                    disabled={false}
                    errorLocation="right"
                    errorMessage={null}
                    filterArray={Array []}
                    getLength={null}
                    id="name"
                    isErrorHTML={false}
                    isRequired={false}
                    maxLen={null}
                    maxLenErrorMessage=""
                    minLen={null}
                    minLenErrorMessage=""
                    onChange={[Function]}
                    onClear={[Function]}
                    originInputProps={Object {}}
                    placeholder="Name"
                    prefixCls="one-input"
                    readOnly={false}
                    requiredErrorMessage=""
                    showClear={false}
                    showErrorMessage={true}
                    size="medium"
                    style={Object {}}
                    type="normal"
                    width={null}
                  >
                    <div
                      className="one-input-all-container one-input-all-container-medium one-input-all-container-normal"
                    >
                      <div
                        className="one-input-detail"
                        style={Object {}}
                      >
                        <div
                          className="one-input-content"
                        >
                          <input
                            className="one-input one-main one-input-medium"
                            data-__field={
                              Object {
                                "name": "name",
                              }
                            }
                            data-__meta={
                              Object {
                                "name": "name",
                                "originalProps": Object {
                                  "className": "",
                                  "placeholder": "Name",
                                },
                                "ref": null,
                                "rules": Array [
                                  Object {
                                    "message": "请输入用户名",
                                    "required": true,
                                  },
                                  Object {
                                    "message": "最少5个字符",
                                    "min": 5,
                                  },
                                  Object {
                                    "max": 10,
                                    "message": "最长10个字符",
                                  },
                                ],
                                "trigger": "onChange",
                                "validate": Array [
                                  Object {
                                    "rules": Array [
                                      Object {
                                        "message": "请输入用户名",
                                        "required": true,
                                      },
                                      Object {
                                        "message": "最少5个字符",
                                        "min": 5,
                                      },
                                      Object {
                                        "max": 10,
                                        "message": "最长10个字符",
                                      },
                                    ],
                                    "trigger": Array [
                                      "onChange",
                                    ],
                                  },
                                ],
                                "validateFirst": true,
                                "valuePropName": "value",
                              }
                            }
                            data-type="input"
                            disabled={false}
                            id="name"
                            onBlur={[Function]}
                            onChange={[Function]}
                            onCompositionEnd={[Function]}
                            onCompositionStart={[Function]}
                            onFocus={[Function]}
                            onKeyDown={[Function]}
                            placeholder="Name"
                            readOnly={false}
                            size="medium"
                            style={
                              Object {
                                "paddingRight": null,
                              }
                            }
                            type="normal"
                            value=""
                            width={null}
                          />
                        </div>
                      </div>
                    </div>
                  </Input>
                </Input>
              </div>
            </div>
          </div>
        </FormField>
        <FormField
          help="18岁以上"
          helpPosition="side"
          label="年龄"
          messageDisplay="simple"
          name="age"
          prefixCls="one-form"
          rules={
            Array [
              Object {
                "message": "请输入年龄",
                "required": true,
              },
              Object {
                "message": "请输入数字",
                "pattern": /\\\\d\\+/,
              },
            ]
          }
          validateFirst={true}
        >
          <div
            className="one-form-field"
          >
            <div
              className="one-form-field-label one-form-field-label-required"
            >
              <label
                title="年龄"
              >
                年龄
              </label>
            </div>
            <div
              className="one-form-field-main"
            >
              <div
                className="one-form-field-content"
                key="Content"
              >
                <Input
                  className=""
                  data-__field={
                    Object {
                      "name": "age",
                    }
                  }
                  data-__meta={
                    Object {
                      "name": "age",
                      "originalProps": Object {
                        "className": "",
                        "placeholder": "Age",
                      },
                      "ref": null,
                      "rules": Array [
                        Object {
                          "message": "请输入年龄",
                          "required": true,
                        },
                        Object {
                          "message": "请输入数字",
                          "pattern": /\\\\d\\+/,
                        },
                      ],
                      "trigger": "onChange",
                      "validate": Array [
                        Object {
                          "rules": Array [
                            Object {
                              "message": "请输入年龄",
                              "required": true,
                            },
                            Object {
                              "message": "请输入数字",
                              "pattern": /\\\\d\\+/,
                            },
                          ],
                          "trigger": Array [
                            "onChange",
                          ],
                        },
                      ],
                      "validateFirst": true,
                      "valuePropName": "value",
                    }
                  }
                  id="age"
                  onChange={[Function]}
                  placeholder="Age"
                  size="medium"
                >
                  <Input
                    className=""
                    countMode="cn"
                    data-__field={
                      Object {
                        "name": "age",
                      }
                    }
                    data-__meta={
                      Object {
                        "name": "age",
                        "originalProps": Object {
                          "className": "",
                          "placeholder": "Age",
                        },
                        "ref": null,
                        "rules": Array [
                          Object {
                            "message": "请输入年龄",
                            "required": true,
                          },
                          Object {
                            "message": "请输入数字",
                            "pattern": /\\\\d\\+/,
                          },
                        ],
                        "trigger": "onChange",
                        "validate": Array [
                          Object {
                            "rules": Array [
                              Object {
                                "message": "请输入年龄",
                                "required": true,
                              },
                              Object {
                                "message": "请输入数字",
                                "pattern": /\\\\d\\+/,
                              },
                            ],
                            "trigger": Array [
                              "onChange",
                            ],
                          },
                        ],
                        "validateFirst": true,
                        "valuePropName": "value",
                      }
                    }
                    disabled={false}
                    errorLocation="right"
                    errorMessage={null}
                    filterArray={Array []}
                    getLength={null}
                    id="age"
                    isErrorHTML={false}
                    isRequired={false}
                    maxLen={null}
                    maxLenErrorMessage=""
                    minLen={null}
                    minLenErrorMessage=""
                    onChange={[Function]}
                    onClear={[Function]}
                    originInputProps={Object {}}
                    placeholder="Age"
                    prefixCls="one-input"
                    readOnly={false}
                    requiredErrorMessage=""
                    showClear={false}
                    showErrorMessage={true}
                    size="medium"
                    style={Object {}}
                    type="normal"
                    width={null}
                  >
                    <div
                      className="one-input-all-container one-input-all-container-medium one-input-all-container-normal"
                    >
                      <div
                        className="one-input-detail"
                        style={Object {}}
                      >
                        <div
                          className="one-input-content"
                        >
                          <input
                            className="one-input one-main one-input-medium"
                            data-__field={
                              Object {
                                "name": "age",
                              }
                            }
                            data-__meta={
                              Object {
                                "name": "age",
                                "originalProps": Object {
                                  "className": "",
                                  "placeholder": "Age",
                                },
                                "ref": null,
                                "rules": Array [
                                  Object {
                                    "message": "请输入年龄",
                                    "required": true,
                                  },
                                  Object {
                                    "message": "请输入数字",
                                    "pattern": /\\\\d\\+/,
                                  },
                                ],
                                "trigger": "onChange",
                                "validate": Array [
                                  Object {
                                    "rules": Array [
                                      Object {
                                        "message": "请输入年龄",
                                        "required": true,
                                      },
                                      Object {
                                        "message": "请输入数字",
                                        "pattern": /\\\\d\\+/,
                                      },
                                    ],
                                    "trigger": Array [
                                      "onChange",
                                    ],
                                  },
                                ],
                                "validateFirst": true,
                                "valuePropName": "value",
                              }
                            }
                            data-type="input"
                            disabled={false}
                            id="age"
                            onBlur={[Function]}
                            onChange={[Function]}
                            onCompositionEnd={[Function]}
                            onCompositionStart={[Function]}
                            onFocus={[Function]}
                            onKeyDown={[Function]}
                            placeholder="Age"
                            readOnly={false}
                            size="medium"
                            style={
                              Object {
                                "paddingRight": null,
                              }
                            }
                            type="normal"
                            value=""
                            width={null}
                          />
                        </div>
                      </div>
                    </div>
                  </Input>
                </Input>
                <Message
                  className="one-form-field-help one-form-field-help-position-side"
                  display="simple"
                  key="Help"
                  prefixCls="one-message"
                  size="small"
                  type="aux"
                >
                  <div
                    className="one-message one-message-small one-message-type-aux one-message-display-simple one-form-field-help one-form-field-help-position-side"
                  >
                    <div
                      className="one-message-content"
                    >
                      18岁以上
                    </div>
                  </div>
                </Message>
              </div>
            </div>
          </div>
        </FormField>
        <FormField
          helpPosition="bottom"
          label="地址"
          messageDisplay="simple"
          prefixCls="one-form"
          validateFirst={true}
        >
          <div
            className="one-form-field"
          >
            <div
              className="one-form-field-label"
            >
              <label
                title="地址"
              >
                地址
              </label>
            </div>
            <div
              className="one-form-field-main"
            >
              <div
                className="one-form-field-content"
                key="Content"
              >
                <FormField
                  abstract={true}
                  help="街道"
                  helpPosition="top"
                  messageDisplay="simple"
                  name="street"
                  prefixCls="one-form"
                  rules={
                    Array [
                      Object {
                        "message": "请输入街道",
                        "required": true,
                      },
                    ]
                  }
                  validateFirst={true}
                >
                  <div
                    className="one-form-field one-form-field-abstract"
                  >
                    <div
                      className="one-form-field-main"
                    >
                      <Message
                        className="one-form-field-help one-form-field-help-position-top"
                        display="simple"
                        key="Help"
                        prefixCls="one-message"
                        size="small"
                        type="aux"
                      >
                        <div
                          className="one-message one-message-small one-message-type-aux one-message-display-simple one-form-field-help one-form-field-help-position-top"
                        >
                          <div
                            className="one-message-content"
                          >
                            街道
                          </div>
                        </div>
                      </Message>
                      <div
                        className="one-form-field-content"
                        key="Content"
                      >
                        <Input
                          className=""
                          data-__field={
                            Object {
                              "name": "street",
                            }
                          }
                          data-__meta={
                            Object {
                              "name": "street",
                              "originalProps": Object {
                                "className": "",
                                "placeholder": "Street",
                                "width": 150,
                              },
                              "ref": null,
                              "rules": Array [
                                Object {
                                  "message": "请输入街道",
                                  "required": true,
                                },
                              ],
                              "trigger": "onChange",
                              "validate": Array [
                                Object {
                                  "rules": Array [
                                    Object {
                                      "message": "请输入街道",
                                      "required": true,
                                    },
                                  ],
                                  "trigger": Array [
                                    "onChange",
                                  ],
                                },
                              ],
                              "validateFirst": true,
                              "valuePropName": "value",
                            }
                          }
                          id="street"
                          onChange={[Function]}
                          placeholder="Street"
                          size="medium"
                          width={150}
                        >
                          <Input
                            className=""
                            countMode="cn"
                            data-__field={
                              Object {
                                "name": "street",
                              }
                            }
                            data-__meta={
                              Object {
                                "name": "street",
                                "originalProps": Object {
                                  "className": "",
                                  "placeholder": "Street",
                                  "width": 150,
                                },
                                "ref": null,
                                "rules": Array [
                                  Object {
                                    "message": "请输入街道",
                                    "required": true,
                                  },
                                ],
                                "trigger": "onChange",
                                "validate": Array [
                                  Object {
                                    "rules": Array [
                                      Object {
                                        "message": "请输入街道",
                                        "required": true,
                                      },
                                    ],
                                    "trigger": Array [
                                      "onChange",
                                    ],
                                  },
                                ],
                                "validateFirst": true,
                                "valuePropName": "value",
                              }
                            }
                            disabled={false}
                            errorLocation="right"
                            errorMessage={null}
                            filterArray={Array []}
                            getLength={null}
                            id="street"
                            isErrorHTML={false}
                            isRequired={false}
                            maxLen={null}
                            maxLenErrorMessage=""
                            minLen={null}
                            minLenErrorMessage=""
                            onChange={[Function]}
                            onClear={[Function]}
                            originInputProps={Object {}}
                            placeholder="Street"
                            prefixCls="one-input"
                            readOnly={false}
                            requiredErrorMessage=""
                            showClear={false}
                            showErrorMessage={true}
                            size="medium"
                            style={Object {}}
                            type="normal"
                            width={150}
                          >
                            <div
                              className="one-input-all-container one-input-all-container-medium one-input-all-container-normal"
                            >
                              <div
                                className="one-input-detail"
                                style={
                                  Object {
                                    "width": 150,
                                  }
                                }
                              >
                                <div
                                  className="one-input-content"
                                >
                                  <input
                                    className="one-input one-main one-input-medium"
                                    data-__field={
                                      Object {
                                        "name": "street",
                                      }
                                    }
                                    data-__meta={
                                      Object {
                                        "name": "street",
                                        "originalProps": Object {
                                          "className": "",
                                          "placeholder": "Street",
                                          "width": 150,
                                        },
                                        "ref": null,
                                        "rules": Array [
                                          Object {
                                            "message": "请输入街道",
                                            "required": true,
                                          },
                                        ],
                                        "trigger": "onChange",
                                        "validate": Array [
                                          Object {
                                            "rules": Array [
                                              Object {
                                                "message": "请输入街道",
                                                "required": true,
                                              },
                                            ],
                                            "trigger": Array [
                                              "onChange",
                                            ],
                                          },
                                        ],
                                        "validateFirst": true,
                                        "valuePropName": "value",
                                      }
                                    }
                                    data-type="input"
                                    disabled={false}
                                    id="street"
                                    onBlur={[Function]}
                                    onChange={[Function]}
                                    onCompositionEnd={[Function]}
                                    onCompositionStart={[Function]}
                                    onFocus={[Function]}
                                    onKeyDown={[Function]}
                                    placeholder="Street"
                                    readOnly={false}
                                    size="medium"
                                    style={
                                      Object {
                                        "paddingRight": null,
                                      }
                                    }
                                    type="normal"
                                    value=""
                                    width={150}
                                  />
                                </div>
                              </div>
                            </div>
                          </Input>
                        </Input>
                      </div>
                    </div>
                  </div>
                </FormField>
                <FormField
                  abstract={true}
                  help="门牌号"
                  helpPosition="top"
                  messageDisplay="simple"
                  name="no"
                  prefixCls="one-form"
                  rules={
                    Array [
                      Object {
                        "message": "请输入门牌号",
                        "required": true,
                      },
                    ]
                  }
                  validateFirst={true}
                >
                  <div
                    className="one-form-field one-form-field-abstract"
                  >
                    <div
                      className="one-form-field-main"
                    >
                      <Message
                        className="one-form-field-help one-form-field-help-position-top"
                        display="simple"
                        key="Help"
                        prefixCls="one-message"
                        size="small"
                        type="aux"
                      >
                        <div
                          className="one-message one-message-small one-message-type-aux one-message-display-simple one-form-field-help one-form-field-help-position-top"
                        >
                          <div
                            className="one-message-content"
                          >
                            门牌号
                          </div>
                        </div>
                      </Message>
                      <div
                        className="one-form-field-content"
                        key="Content"
                      >
                        <Input
                          className=""
                          data-__field={
                            Object {
                              "name": "no",
                            }
                          }
                          data-__meta={
                            Object {
                              "name": "no",
                              "originalProps": Object {
                                "className": "",
                                "placeholder": "No.",
                                "width": 150,
                              },
                              "ref": null,
                              "rules": Array [
                                Object {
                                  "message": "请输入门牌号",
                                  "required": true,
                                },
                              ],
                              "trigger": "onChange",
                              "validate": Array [
                                Object {
                                  "rules": Array [
                                    Object {
                                      "message": "请输入门牌号",
                                      "required": true,
                                    },
                                  ],
                                  "trigger": Array [
                                    "onChange",
                                  ],
                                },
                              ],
                              "validateFirst": true,
                              "valuePropName": "value",
                            }
                          }
                          id="no"
                          onChange={[Function]}
                          placeholder="No."
                          size="medium"
                          width={150}
                        >
                          <Input
                            className=""
                            countMode="cn"
                            data-__field={
                              Object {
                                "name": "no",
                              }
                            }
                            data-__meta={
                              Object {
                                "name": "no",
                                "originalProps": Object {
                                  "className": "",
                                  "placeholder": "No.",
                                  "width": 150,
                                },
                                "ref": null,
                                "rules": Array [
                                  Object {
                                    "message": "请输入门牌号",
                                    "required": true,
                                  },
                                ],
                                "trigger": "onChange",
                                "validate": Array [
                                  Object {
                                    "rules": Array [
                                      Object {
                                        "message": "请输入门牌号",
                                        "required": true,
                                      },
                                    ],
                                    "trigger": Array [
                                      "onChange",
                                    ],
                                  },
                                ],
                                "validateFirst": true,
                                "valuePropName": "value",
                              }
                            }
                            disabled={false}
                            errorLocation="right"
                            errorMessage={null}
                            filterArray={Array []}
                            getLength={null}
                            id="no"
                            isErrorHTML={false}
                            isRequired={false}
                            maxLen={null}
                            maxLenErrorMessage=""
                            minLen={null}
                            minLenErrorMessage=""
                            onChange={[Function]}
                            onClear={[Function]}
                            originInputProps={Object {}}
                            placeholder="No."
                            prefixCls="one-input"
                            readOnly={false}
                            requiredErrorMessage=""
                            showClear={false}
                            showErrorMessage={true}
                            size="medium"
                            style={Object {}}
                            type="normal"
                            width={150}
                          >
                            <div
                              className="one-input-all-container one-input-all-container-medium one-input-all-container-normal"
                            >
                              <div
                                className="one-input-detail"
                                style={
                                  Object {
                                    "width": 150,
                                  }
                                }
                              >
                                <div
                                  className="one-input-content"
                                >
                                  <input
                                    className="one-input one-main one-input-medium"
                                    data-__field={
                                      Object {
                                        "name": "no",
                                      }
                                    }
                                    data-__meta={
                                      Object {
                                        "name": "no",
                                        "originalProps": Object {
                                          "className": "",
                                          "placeholder": "No.",
                                          "width": 150,
                                        },
                                        "ref": null,
                                        "rules": Array [
                                          Object {
                                            "message": "请输入门牌号",
                                            "required": true,
                                          },
                                        ],
                                        "trigger": "onChange",
                                        "validate": Array [
                                          Object {
                                            "rules": Array [
                                              Object {
                                                "message": "请输入门牌号",
                                                "required": true,
                                              },
                                            ],
                                            "trigger": Array [
                                              "onChange",
                                            ],
                                          },
                                        ],
                                        "validateFirst": true,
                                        "valuePropName": "value",
                                      }
                                    }
                                    data-type="input"
                                    disabled={false}
                                    id="no"
                                    onBlur={[Function]}
                                    onChange={[Function]}
                                    onCompositionEnd={[Function]}
                                    onCompositionStart={[Function]}
                                    onFocus={[Function]}
                                    onKeyDown={[Function]}
                                    placeholder="No."
                                    readOnly={false}
                                    size="medium"
                                    style={
                                      Object {
                                        "paddingRight": null,
                                      }
                                    }
                                    type="normal"
                                    value=""
                                    width={150}
                                  />
                                </div>
                              </div>
                            </div>
                          </Input>
                        </Input>
                      </div>
                    </div>
                  </div>
                </FormField>
              </div>
            </div>
          </div>
        </FormField>
        <FormField
          help="通常用于请求服务端进行校验"
          helpPosition="bottom"
          label="异步校验"
          messageDisplay="simple"
          name="pwd"
          prefixCls="one-form"
          rules={
            Array [
              Object {
                "message": "请输入123",
                "required": true,
              },
              Object {
                "validator": [Function],
              },
            ]
          }
          validateFirst={true}
        >
          <div
            className="one-form-field"
          >
            <div
              className="one-form-field-label one-form-field-label-required"
            >
              <label
                title="异步校验"
              >
                异步校验
              </label>
            </div>
            <div
              className="one-form-field-main"
            >
              <div
                className="one-form-field-content"
                key="Content"
              >
                <Input
                  className=""
                  data-__field={
                    Object {
                      "name": "pwd",
                    }
                  }
                  data-__meta={
                    Object {
                      "name": "pwd",
                      "originalProps": Object {
                        "className": "",
                        "placeholder": "Async",
                      },
                      "ref": null,
                      "rules": Array [
                        Object {
                          "message": "请输入123",
                          "required": true,
                        },
                        Object {
                          "validator": [Function],
                        },
                      ],
                      "trigger": "onChange",
                      "validate": Array [
                        Object {
                          "rules": Array [
                            Object {
                              "message": "请输入123",
                              "required": true,
                            },
                            Object {
                              "validator": [Function],
                            },
                          ],
                          "trigger": Array [
                            "onChange",
                          ],
                        },
                      ],
                      "validateFirst": true,
                      "valuePropName": "value",
                    }
                  }
                  id="pwd"
                  onChange={[Function]}
                  placeholder="Async"
                  size="medium"
                >
                  <Input
                    className=""
                    countMode="cn"
                    data-__field={
                      Object {
                        "name": "pwd",
                      }
                    }
                    data-__meta={
                      Object {
                        "name": "pwd",
                        "originalProps": Object {
                          "className": "",
                          "placeholder": "Async",
                        },
                        "ref": null,
                        "rules": Array [
                          Object {
                            "message": "请输入123",
                            "required": true,
                          },
                          Object {
                            "validator": [Function],
                          },
                        ],
                        "trigger": "onChange",
                        "validate": Array [
                          Object {
                            "rules": Array [
                              Object {
                                "message": "请输入123",
                                "required": true,
                              },
                              Object {
                                "validator": [Function],
                              },
                            ],
                            "trigger": Array [
                              "onChange",
                            ],
                          },
                        ],
                        "validateFirst": true,
                        "valuePropName": "value",
                      }
                    }
                    disabled={false}
                    errorLocation="right"
                    errorMessage={null}
                    filterArray={Array []}
                    getLength={null}
                    id="pwd"
                    isErrorHTML={false}
                    isRequired={false}
                    maxLen={null}
                    maxLenErrorMessage=""
                    minLen={null}
                    minLenErrorMessage=""
                    onChange={[Function]}
                    onClear={[Function]}
                    originInputProps={Object {}}
                    placeholder="Async"
                    prefixCls="one-input"
                    readOnly={false}
                    requiredErrorMessage=""
                    showClear={false}
                    showErrorMessage={true}
                    size="medium"
                    style={Object {}}
                    type="normal"
                    width={null}
                  >
                    <div
                      className="one-input-all-container one-input-all-container-medium one-input-all-container-normal"
                    >
                      <div
                        className="one-input-detail"
                        style={Object {}}
                      >
                        <div
                          className="one-input-content"
                        >
                          <input
                            className="one-input one-main one-input-medium"
                            data-__field={
                              Object {
                                "name": "pwd",
                              }
                            }
                            data-__meta={
                              Object {
                                "name": "pwd",
                                "originalProps": Object {
                                  "className": "",
                                  "placeholder": "Async",
                                },
                                "ref": null,
                                "rules": Array [
                                  Object {
                                    "message": "请输入123",
                                    "required": true,
                                  },
                                  Object {
                                    "validator": [Function],
                                  },
                                ],
                                "trigger": "onChange",
                                "validate": Array [
                                  Object {
                                    "rules": Array [
                                      Object {
                                        "message": "请输入123",
                                        "required": true,
                                      },
                                      Object {
                                        "validator": [Function],
                                      },
                                    ],
                                    "trigger": Array [
                                      "onChange",
                                    ],
                                  },
                                ],
                                "validateFirst": true,
                                "valuePropName": "value",
                              }
                            }
                            data-type="input"
                            disabled={false}
                            id="pwd"
                            onBlur={[Function]}
                            onChange={[Function]}
                            onCompositionEnd={[Function]}
                            onCompositionStart={[Function]}
                            onFocus={[Function]}
                            onKeyDown={[Function]}
                            placeholder="Async"
                            readOnly={false}
                            size="medium"
                            style={
                              Object {
                                "paddingRight": null,
                              }
                            }
                            type="normal"
                            value=""
                            width={null}
                          />
                        </div>
                      </div>
                    </div>
                  </Input>
                </Input>
              </div>
              <div
                className="one-form-field-message"
                key="Messages"
              >
                <Message
                  className="one-form-field-help one-form-field-help-position-bottom"
                  display="simple"
                  key="Help"
                  prefixCls="one-message"
                  size="small"
                  type="aux"
                >
                  <div
                    className="one-message one-message-small one-message-type-aux one-message-display-simple one-form-field-help one-form-field-help-position-bottom"
                  >
                    <div
                      className="one-message-content"
                    >
                      通常用于请求服务端进行校验
                    </div>
                  </div>
                </Message>
              </div>
            </div>
          </div>
        </FormField>
        <FormField
          helpPosition="bottom"
          label="门店"
          messageDisplay="simple"
          prefixCls="one-form"
          validateFirst={true}
        >
          <div
            className="one-form-field"
          >
            <div
              className="one-form-field-label"
            >
              <label
                title="门店"
              >
                门店
              </label>
            </div>
            <div
              className="one-form-field-main"
            >
              <div
                className="one-form-field-content"
                key="Content"
              >
                <Transfer
                  SelectedItem={[Function]}
                  allDataMap={
                    Object {
                      "1": Object {
                        "key": "1",
                        "title": "门店1",
                      },
                      "10": Object {
                        "children": Array [
                          "11",
                          "12",
                        ],
                        "key": "10",
                        "title": "门店3",
                      },
                      "11": Object {
                        "key": "11",
                        "title": "门店31",
                      },
                      "12": Object {
                        "key": "12",
                        "title": "门店32",
                      },
                      "2": Object {
                        "key": "2",
                        "title": "门店2",
                      },
                    }
                  }
                  candidateList={
                    Array [
                      "1",
                      "2",
                      "10",
                    ]
                  }
                >
                  <Transfer
                    CustomCandidatePane={[Function]}
                    CustomSelectedPane={[Function]}
                    SelectedItem={[Function]}
                    allDataMap={
                      Object {
                        "1": Object {
                          "key": "1",
                          "title": "门店1",
                        },
                        "10": Object {
                          "children": Array [
                            "11",
                            "12",
                          ],
                          "key": "10",
                          "title": "门店3",
                        },
                        "11": Object {
                          "key": "11",
                          "title": "门店31",
                        },
                        "12": Object {
                          "key": "12",
                          "title": "门店32",
                        },
                        "2": Object {
                          "key": "2",
                          "title": "门店2",
                        },
                      }
                    }
                    candidateFooterProps={Object {}}
                    candidateItemProps={Object {}}
                    candidateList={
                      Array [
                        "1",
                        "2",
                        "10",
                      ]
                    }
                    candidateTreeStyle={Object {}}
                    className=""
                    handleCandidateExpand={[Function]}
                    handleDelete={[Function]}
                    handleDeleteAll={[Function]}
                    handleLevelChange={[Function]}
                    handleSearch={[Function]}
                    handleSelect={[Function]}
                    handleSelectAll={[Function]}
                    handleSelectedExpand={[Function]}
                    isShowLevel={false}
                    isShowLevelSelect={false}
                    levelKey={null}
                    levelOptions={null}
                    loading={false}
                    loadingText="加载中..."
                    maxSelectedNum={null}
                    mergeChecked="downwards"
                    onSearchBoxBlur={[Function]}
                    onSearchBoxFocus={[Function]}
                    onSearchChange={[Function]}
                    placeholder="请搜索"
                    prefixCls="one-transfer"
                    searchRenderProps={Object {}}
                    selectedItemProps={Object {}}
                    selectedTreeStyle={Object {}}
                    showCandidateFooter={false}
                    showCandidateNum={true}
                    showSearchBox={true}
                    showSelectAll={true}
                    showSelectedNum={true}
                    size="medium"
                    treeName=""
                    useVirtualScroll={false}
                  >
                    <div
                      className="one-transfer one-transfer-medium"
                    >
                      <div
                        className="one-transfer-select"
                        style={Object {}}
                      >
                        <CandidateTitle
                          CustomCandidatePane={[Function]}
                          CustomSelectedPane={[Function]}
                          SelectedItem={[Function]}
                          allDataMap={
                            Object {
                              "1": Object {
                                "key": "1",
                                "title": "门店1",
                              },
                              "10": Object {
                                "children": Array [
                                  "11",
                                  "12",
                                ],
                                "key": "10",
                                "title": "门店3",
                              },
                              "11": Object {
                                "key": "11",
                                "title": "门店31",
                              },
                              "12": Object {
                                "key": "12",
                                "title": "门店32",
                              },
                              "2": Object {
                                "key": "2",
                                "title": "门店2",
                              },
                            }
                          }
                          candidateFooterProps={Object {}}
                          candidateItemProps={Object {}}
                          candidateList={
                            Array [
                              "1",
                              "2",
                              "10",
                            ]
                          }
                          candidateTreeStyle={Object {}}
                          className=""
                          handleCandidateExpand={[Function]}
                          handleDelete={[Function]}
                          handleDeleteAll={[Function]}
                          handleLevelChange={[Function]}
                          handleSearch={[Function]}
                          handleSelect={[Function]}
                          handleSelectAll={[Function]}
                          handleSelectedExpand={[Function]}
                          isShowLevel={false}
                          isShowLevelSelect={false}
                          levelKey={null}
                          levelOptions={null}
                          loading={false}
                          loadingText="加载中..."
                          maxSelectedNum={null}
                          mergeChecked="downwards"
                          onSearchBoxBlur={[Function]}
                          onSearchBoxFocus={[Function]}
                          onSearchChange={[Function]}
                          onSelectAll={[Function]}
                          placeholder="请搜索"
                          prefixCls="one-transfer"
                          searchRenderProps={Object {}}
                          selectedItemProps={Object {}}
                          selectedList={Array []}
                          selectedTreeStyle={Object {}}
                          showCandidateFooter={false}
                          showCandidateNum={true}
                          showSearchBox={true}
                          showSelectAll={true}
                          showSelectedNum={true}
                          size="medium"
                          treeName=""
                          useVirtualScroll={false}
                        >
                          <div
                            className="one-transfer-pane-title"
                          >
                            <TitleRender
                              CustomCandidatePane={[Function]}
                              CustomSelectedPane={[Function]}
                              SelectedItem={[Function]}
                              allDataMap={
                                Object {
                                  "1": Object {
                                    "key": "1",
                                    "title": "门店1",
                                  },
                                  "10": Object {
                                    "children": Array [
                                      "11",
                                      "12",
                                    ],
                                    "key": "10",
                                    "title": "门店3",
                                  },
                                  "11": Object {
                                    "key": "11",
                                    "title": "门店31",
                                  },
                                  "12": Object {
                                    "key": "12",
                                    "title": "门店32",
                                  },
                                  "2": Object {
                                    "key": "2",
                                    "title": "门店2",
                                  },
                                }
                              }
                              candidateFooterProps={Object {}}
                              candidateItemProps={Object {}}
                              candidateList={
                                Array [
                                  "1",
                                  "2",
                                  "10",
                                ]
                              }
                              candidateNum={4}
                              candidateTreeStyle={Object {}}
                              className=""
                              handleCandidateExpand={[Function]}
                              handleDelete={[Function]}
                              handleDeleteAll={[Function]}
                              handleLevelChange={[Function]}
                              handleSearch={[Function]}
                              handleSelect={[Function]}
                              handleSelectAll={[Function]}
                              handleSelectedExpand={[Function]}
                              isShowLevel={false}
                              isShowLevelSelect={false}
                              levelKey={null}
                              levelOptions={null}
                              loading={false}
                              loadingText="加载中..."
                              maxSelectedNum={null}
                              mergeChecked="downwards"
                              onSearchBoxBlur={[Function]}
                              onSearchBoxFocus={[Function]}
                              onSearchChange={[Function]}
                              onSelectAll={[Function]}
                              placeholder="请搜索"
                              prefixCls="one-transfer"
                              searchRenderProps={Object {}}
                              selectedItemProps={Object {}}
                              selectedList={Array []}
                              selectedNum={0}
                              selectedTreeStyle={Object {}}
                              showCandidateFooter={false}
                              showCandidateNum={true}
                              showSearchBox={true}
                              showSelectAll={true}
                              showSelectedNum={false}
                              size="medium"
                              title="可选"
                              treeName=""
                              useVirtualScroll={false}
                            >
                              <span
                                className="one-transfer-pane-title-text"
                                title="可选"
                              >
                                <span
                                  className="one-transfer-pane-title-text-text"
                                >
                                  <span>
                                    可选
                                  </span>
                                </span>
                                <span
                                  className="one-transfer-pane-title-text-number"
                                >
                                  <span>
                                    (
                                    4
                                    )
                                  </span>
                                </span>
                              </span>
                            </TitleRender>
                            <Button
                              className="one-transfer-add-all-button"
                              disabled={null}
                              onClick={[Function]}
                              size="medium"
                              type="text-strong"
                            >
                              <Button
                                className="one-transfer-add-all-button"
                                disabled={null}
                                htmlType="button"
                                icon=""
                                loading={false}
                                name=""
                                onClick={[Function]}
                                prefixCls="one-button"
                                readOnly={false}
                                readonly={false}
                                size="medium"
                                type="text-strong"
                              >
                                <button
                                  className="one-button one-transfer-add-all-button one-main one-button-text-strong one-button-medium"
                                  disabled={null}
                                  name=""
                                  onClick={[Function]}
                                  type="button"
                                >
                                  <span>
                                    全选
                                  </span>
                                </button>
                              </Button>
                            </Button>
                          </div>
                        </CandidateTitle>
                        <div
                          className="one-transfer-select-main"
                        >
                          <CommonSearchRender
                            handleLevelChange={[Function]}
                            isShowLevelSelect={false}
                            levelKey={null}
                            levelOptions={null}
                            onSelectFocus={[Function]}
                            prefixCls="one-transfer"
                            searchBoxProps={
                              Object {
                                "onBlur": [Function],
                                "onChange": [Function],
                                "onClearClick": [Function],
                                "onFocus": [Function],
                                "onSearch": [Function],
                                "placeholder": "请搜索",
                                "size": "medium",
                                "value": "",
                              }
                            }
                          >
                            <div
                              className="one-transfer-search-box-bar"
                            >
                              <SearchBox
                                onBlur={[Function]}
                                onChange={[Function]}
                                onClearClick={[Function]}
                                onFocus={[Function]}
                                onSearch={[Function]}
                                placeholder="请搜索"
                                size="medium"
                                value=""
                              >
                                <SearchBox
                                  autoFocus={false}
                                  customRender={null}
                                  defaultQuery={Object {}}
                                  disabled={false}
                                  dropdownHeight="auto"
                                  isShowSearchIcon={true}
                                  onBlur={[Function]}
                                  onChange={[Function]}
                                  onClearClick={[Function]}
                                  onFocus={[Function]}
                                  onSearch={[Function]}
                                  options={Array []}
                                  placeholder="请搜索"
                                  prefixCls="one-search-box"
                                  searchIconType="icon"
                                  showCloseIcon={true}
                                  showSearchIcon={true}
                                  size="medium"
                                  type="text"
                                  value=""
                                >
                                  <div
                                    className="one-search-box-container one-search-box-container-icon one-search-box-container-medium"
                                    style={Object {}}
                                  >
                                    <Overlay
                                      overlay={
                                        <Menu
                                          className="one-search-box-layer-menu"
                                          onClick={[Function]}
                                          selectable={false}
                                          size="medium"
                                          style={
                                            Object {
                                              "height": "auto",
                                            }
                                          }
                                        />
                                      }
                                      overlayClassName="one-search-box-layer"
                                      visible={false}
                                    >
                                      <Overlay
                                        buttonProps={Object {}}
                                        disabled={false}
                                        dropdownMatchSelectWidth={true}
                                        header=""
                                        onVisibleChange={[Function]}
                                        overlay={
                                          <Menu
                                            className="one-search-box-layer-menu"
                                            onClick={[Function]}
                                            selectable={false}
                                            size="medium"
                                            style={
                                              Object {
                                                "height": "auto",
                                              }
                                            }
                                          />
                                        }
                                        overlayClassName="one-search-box-layer"
                                        prefixCls="one-overlay"
                                        transitionName="one-transition-slide-down"
                                        trigger="hover"
                                        visible={false}
                                      >
                                        <InnerOverlay
                                          align={
                                            Object {
                                              "ignoreShake": true,
                                            }
                                          }
                                          buttonProps={Object {}}
                                          destroyPopUpOnHide={false}
                                          disabled={false}
                                          dropdownMatchSelectWidth={true}
                                          header=""
                                          mouseEnterDelay={0}
                                          mouseLeaveDelay={0.1}
                                          onVisibleChange={[Function]}
                                          overlay={
                                            <Menu
                                              className="one-search-box-layer-menu"
                                              onClick={[Function]}
                                              selectable={false}
                                              size="medium"
                                              style={
                                                Object {
                                                  "height": "auto",
                                                }
                                              }
                                            />
                                          }
                                          overlayClassName="one-search-box-layer"
                                          popupPlacement="bottomLeft"
                                          prefixCls="one-overlay"
                                          transitionName="one-transition-slide-down"
                                          trigger="hover"
                                          visible={false}
                                        >
                                          <Trigger
                                            action="hover"
                                            afterPopupVisibleChange={[Function]}
                                            autoDestroy={false}
                                            blurDelay={0.15}
                                            builtinPlacements={
                                              Object {
                                                "bottom": Object {
                                                  "offset": Array [
                                                    0,
                                                    4,
                                                  ],
                                                  "overflow": Object {
                                                    "adjustX": 1,
                                                    "adjustY": 1,
                                                  },
                                                  "points": Array [
                                                    "tc",
                                                    "bc",
                                                  ],
                                                },
                                                "bottomLeft": Object {
                                                  "offset": Array [
                                                    0,
                                                    4,
                                                  ],
                                                  "overflow": Object {
                                                    "adjustX": 1,
                                                    "adjustY": 1,
                                                  },
                                                  "points": Array [
                                                    "tl",
                                                    "bl",
                                                  ],
                                                },
                                                "bottomRight": Object {
                                                  "offset": Array [
                                                    0,
                                                    4,
                                                  ],
                                                  "overflow": Object {
                                                    "adjustX": 1,
                                                    "adjustY": 1,
                                                  },
                                                  "points": Array [
                                                    "tr",
                                                    "br",
                                                  ],
                                                },
                                                "left": Object {
                                                  "offset": Array [
                                                    -4,
                                                    0,
                                                  ],
                                                  "overflow": Object {
                                                    "adjustX": 1,
                                                    "adjustY": 1,
                                                  },
                                                  "points": Array [
                                                    "cr",
                                                    "cl",
                                                  ],
                                                },
                                                "leftBottom": Object {
                                                  "offset": Array [
                                                    -4,
                                                    0,
                                                  ],
                                                  "points": Array [
                                                    "br",
                                                    "cl",
                                                  ],
                                                },
                                                "leftTop": Object {
                                                  "offset": Array [
                                                    -4,
                                                    0,
                                                  ],
                                                  "points": Array [
                                                    "tr",
                                                    "cl",
                                                  ],
                                                },
                                                "right": Object {
                                                  "offset": Array [
                                                    4,
                                                    0,
                                                  ],
                                                  "overflow": Object {
                                                    "adjustX": 1,
                                                    "adjustY": 1,
                                                  },
                                                  "points": Array [
                                                    "cl",
                                                    "cr",
                                                  ],
                                                },
                                                "rightBottom": Object {
                                                  "offset": Array [
                                                    4,
                                                    0,
                                                  ],
                                                  "points": Array [
                                                    "bl",
                                                    "cr",
                                                  ],
                                                },
                                                "rightTop": Object {
                                                  "offset": Array [
                                                    4,
                                                    0,
                                                  ],
                                                  "points": Array [
                                                    "tl",
                                                    "cr",
                                                  ],
                                                },
                                                "top": Object {
                                                  "offset": Array [
                                                    0,
                                                    -4,
                                                  ],
                                                  "overflow": Object {
                                                    "adjustX": 1,
                                                    "adjustY": 1,
                                                  },
                                                  "points": Array [
                                                    "bc",
                                                    "tc",
                                                  ],
                                                },
                                                "topLeft": Object {
                                                  "offset": Array [
                                                    0,
                                                    -4,
                                                  ],
                                                  "overflow": Object {
                                                    "adjustX": 1,
                                                    "adjustY": 1,
                                                  },
                                                  "points": Array [
                                                    "bl",
                                                    "tl",
                                                  ],
                                                },
                                                "topRight": Object {
                                                  "offset": Array [
                                                    0,
                                                    -4,
                                                  ],
                                                  "overflow": Object {
                                                    "adjustX": 1,
                                                    "adjustY": 1,
                                                  },
                                                  "points": Array [
                                                    "br",
                                                    "tr",
                                                  ],
                                                },
                                              }
                                            }
                                            buttonProps={Object {}}
                                            defaultPopupVisible={false}
                                            destroyPopupOnHide={false}
                                            disabled={false}
                                            focusDelay={0}
                                            getDocument={[Function]}
                                            getPopupClassNameFromAlign={[Function]}
                                            header=""
                                            hideAction={Array []}
                                            mask={false}
                                            maskClosable={true}
                                            mouseEnterDelay={0}
                                            mouseLeaveDelay={0.1}
                                            onPopupAlign={[Function]}
                                            onPopupVisibleChange={[Function]}
                                            overlay={
                                              <Menu
                                                className="one-search-box-layer-menu"
                                                onClick={[Function]}
                                                selectable={false}
                                                size="medium"
                                                style={
                                                  Object {
                                                    "height": "auto",
                                                  }
                                                }
                                              />
                                            }
                                            popup={[Function]}
                                            popupAlign={
                                              Object {
                                                "ignoreShake": true,
                                              }
                                            }
                                            popupClassName=""
                                            popupPlacement="bottomLeft"
                                            popupStyle={Object {}}
                                            popupTransitionName="one-transition-slide-down"
                                            popupVisible={false}
                                            prefixCls="one-overlay"
                                            showAction={Array []}
                                            visible={false}
                                          >
                                            <div
                                              className="one-search-box-detail one-main one-overlay-close"
                                              key="trigger"
                                              onMouseEnter={[Function]}
                                              onMouseLeave={[Function]}
                                            >
                                              <input
                                                autoFocus={false}
                                                className="one-search-box one-search-box-medium"
                                                disabled={false}
                                                onBlur={[Function]}
                                                onChange={[Function]}
                                                onCompositionEnd={[Function]}
                                                onCompositionStart={[Function]}
                                                onFocus={[Function]}
                                                onKeyDown={[Function]}
                                                placeholder="请搜索"
                                                style={
                                                  Object {
                                                    "paddingRight": 4,
                                                  }
                                                }
                                                type="text"
                                                value=""
                                              />
                                              <div
                                                className="one-search-box-icon-wrap"
                                              >
                                                <IconTimesCircle
                                                  className="one-search-box-icon one-search-box-icon-close"
                                                  onClick={[Function]}
                                                >
                                                  <svg
                                                    className="dls-icon one-search-box-icon one-search-box-icon-close"
                                                    dangerouslySetInnerHTML={
                                                      Object {
                                                        "__html": "<path fill=\\"currentColor\\" d=\\"m8.5 7.086 3.5 3.5 3.5-3.5L16.914 8.5l-3.5 3.5 3.5 3.5-1.414 1.414-3.5-3.5-3.5 3.5L7.086 15.5l3.5-3.5-3.5-3.5L8.5 7.086z\\"/><path fill=\\"currentColor\\" fill-rule=\\"evenodd\\" d=\\"M1 12C1 5.925 5.925 1 12 1s11 4.925 11 11-4.925 11-11 11S1 18.075 1 12zm11-9a9 9 0 1 0 0 18 9 9 0 0 0 0-18z\\" clip-rule=\\"evenodd\\"/>",
                                                      }
                                                    }
                                                    fill="none"
                                                    focusable={false}
                                                    height={24}
                                                    onClick={[Function]}
                                                    viewBox="0 0 24 24"
                                                    width={24}
                                                    xmlns="http://www.w3.org/2000/svg"
                                                  />
                                                </IconTimesCircle>
                                                <span>
                                                  <Button
                                                    className="one-search-box-search-icon"
                                                    disabled={false}
                                                    icon="search"
                                                    onClick={[Function]}
                                                    size="medium"
                                                    type="link"
                                                  >
                                                    <Button
                                                      className="one-search-box-search-icon"
                                                      disabled={false}
                                                      htmlType="button"
                                                      icon="search"
                                                      loading={false}
                                                      name=""
                                                      onClick={[Function]}
                                                      prefixCls="one-button"
                                                      readOnly={false}
                                                      readonly={false}
                                                      size="medium"
                                                      type="link"
                                                    >
                                                      <button
                                                        className="one-button one-search-box-search-icon one-main one-button-text one-button-medium one-button-icon-only one-button-has-icon"
                                                        disabled={false}
                                                        name=""
                                                        onClick={[Function]}
                                                        type="button"
                                                      >
                                                        <IconSvg
                                                          className="one-button-icon"
                                                          suffixCls="iconSvg"
                                                          type="search"
                                                        >
                                                          <IconSearch
                                                            className="one-iconSvg one-iconSvg-search one-button-icon"
                                                          >
                                                            <svg
                                                              className="dls-icon one-iconSvg one-iconSvg-search one-button-icon"
                                                              dangerouslySetInnerHTML={
                                                                Object {
                                                                  "__html": "<path fill=\\"currentColor\\" fill-rule=\\"evenodd\\" d=\\"M11.2 1.4c-5.412 0-9.8 4.388-9.8 9.8 0 5.412 4.388 9.8 9.8 9.8a9.76 9.76 0 0 0 6.187-2.2l3.473 3.42 1.403-1.426-3.462-3.408A9.76 9.76 0 0 0 21 11.2c0-5.412-4.388-9.8-9.8-9.8zm-7.8 9.8a7.8 7.8 0 1 1 15.6 0 7.8 7.8 0 0 1-15.6 0z\\" clip-rule=\\"evenodd\\"/>",
                                                                }
                                                              }
                                                              fill="none"
                                                              focusable={false}
                                                              height={24}
                                                              viewBox="0 0 24 24"
                                                              width={24}
                                                              xmlns="http://www.w3.org/2000/svg"
                                                            />
                                                          </IconSearch>
                                                        </IconSvg>
                                                      </button>
                                                    </Button>
                                                  </Button>
                                                </span>
                                              </div>
                                            </div>
                                          </Trigger>
                                        </InnerOverlay>
                                      </Overlay>
                                    </Overlay>
                                  </div>
                                </SearchBox>
                              </SearchBox>
                            </div>
                          </CommonSearchRender>
                          <div
                            className="one-transfer-select-pane one-transfer-candidate-pane"
                          >
                            <CandidatePane
                              CustomCandidatePane={[Function]}
                              CustomSelectedPane={[Function]}
                              SelectedItem={[Function]}
                              allDataMap={
                                Object {
                                  "1": Object {
                                    "key": "1",
                                    "title": "门店1",
                                  },
                                  "10": Object {
                                    "children": Array [
                                      "11",
                                      "12",
                                    ],
                                    "key": "10",
                                    "title": "门店3",
                                  },
                                  "11": Object {
                                    "key": "11",
                                    "title": "门店31",
                                  },
                                  "12": Object {
                                    "key": "12",
                                    "title": "门店32",
                                  },
                                  "2": Object {
                                    "key": "2",
                                    "title": "门店2",
                                  },
                                }
                              }
                              candidateFooterProps={Object {}}
                              candidateItemProps={Object {}}
                              candidateList={
                                Array [
                                  "1",
                                  "2",
                                  "10",
                                ]
                              }
                              candidateTreeRef={[Function]}
                              candidateTreeStyle={Object {}}
                              className=""
                              expandedCandidateKeys={Array []}
                              handleCandidateExpand={[Function]}
                              handleDelete={[Function]}
                              handleDeleteAll={[Function]}
                              handleLevelChange={[Function]}
                              handleSearch={[Function]}
                              handleSelect={[Function]}
                              handleSelectAll={[Function]}
                              handleSelectedExpand={[Function]}
                              isShowLevel={false}
                              isShowLevelSelect={false}
                              levelKey={null}
                              levelOptions={null}
                              loading={false}
                              loadingText="加载中..."
                              maxSelectedNum={null}
                              mergeChecked="downwards"
                              onCheck={[Function]}
                              onExpand={[Function]}
                              onSearchBoxBlur={[Function]}
                              onSearchBoxFocus={[Function]}
                              onSearchChange={[Function]}
                              onSelect={[Function]}
                              parentRelationMap={
                                Object {
                                  "11": "10",
                                  "12": "10",
                                }
                              }
                              placeholder="请搜索"
                              prefixCls="one-transfer"
                              searchRenderProps={Object {}}
                              searchValue=""
                              selectedItemProps={Object {}}
                              selectedList={Array []}
                              selectedTreeStyle={Object {}}
                              showCandidateFooter={false}
                              showCandidateNum={true}
                              showSearchBox={true}
                              showSelectAll={true}
                              showSelectedNum={true}
                              size="medium"
                              treeName=""
                              useVirtualScroll={false}
                              virtualListHeight={null}
                            >
                              <Tree
                                checkActionEqualSelet={true}
                                checkStrictly={false}
                                checkable={true}
                                checkedKeys={Array []}
                                className="one-transfer-candidate-tree"
                                expandedKeys={Array []}
                                key="candidate-tree"
                                onCheck={[Function]}
                                onExpand={[Function]}
                                onSelect={[Function]}
                                parentContainerHeight={null}
                                prefixCls="one-tree"
                                selectable={true}
                                selectedKeys={Array []}
                                showIcon={false}
                                size="medium"
                              >
                                <Tree
                                  autoExpandParent={false}
                                  checkActionEqualSelet={true}
                                  checkStrictly={false}
                                  checkable={true}
                                  checkedKeys={Array []}
                                  className="one-transfer-candidate-tree one-tree-icon-hide one-tree-medium"
                                  defaultCheckedKeys={Array []}
                                  defaultExpandAll={false}
                                  defaultExpandParent={true}
                                  defaultExpandedKeys={Array []}
                                  defaultSelectedKeys={Array []}
                                  disabled={false}
                                  expandedKeys={Array []}
                                  multiple={false}
                                  onCheck={[Function]}
                                  onExpand={[Function]}
                                  onSelect={[Function]}
                                  parentContainerHeight={null}
                                  prefixCls="one-tree"
                                  selectable={true}
                                  selectedKeys={Array []}
                                  showIcon={false}
                                  showLine={false}
                                  size="medium"
                                  switcherIcon={[Function]}
                                >
                                  <ul
                                    className="one-tree one-transfer-candidate-tree one-tree-icon-hide one-tree-medium"
                                    role="tree"
                                    unselectable="on"
                                  >
                                    <TreeNode
                                      checkable={true}
                                      checked={false}
                                      disabledAllWhenNodeDisabled={false}
                                      eventKey="1"
                                      expanded={false}
                                      halfChecked={false}
                                      isFileNode={false}
                                      key="1"
                                      loaded={false}
                                      loading={false}
                                      pos="0-0"
                                      selected={false}
                                      size="medium"
                                      title={
                                        <CommonItemRender
                                          prefixCls="one-transfer"
                                          relationText=""
                                          searchValue=""
                                          title="门店1"
                                        />
                                      }
                                    >
                                      <li
                                        className="one-tree-treenode-switcher-close"
                                        role="treeitem"
                                      >
                                        <div
                                          className="one-tree-treenode-container"
                                        >
                                          <span
                                            className="one-tree-switcher one-tree-switcher-noop"
                                          >
                                            <Component
                                              checkable={true}
                                              checked={false}
                                              disabledAllWhenNodeDisabled={false}
                                              eventKey="1"
                                              expanded={false}
                                              halfChecked={false}
                                              isFileNode={false}
                                              isLeaf={true}
                                              loaded={false}
                                              loading={false}
                                              pos="0-0"
                                              selected={false}
                                              size="medium"
                                              title={
                                                <CommonItemRender
                                                  prefixCls="one-transfer"
                                                  relationText=""
                                                  searchValue=""
                                                  title="门店1"
                                                />
                                              }
                                            />
                                          </span>
                                          <span
                                            className="one-tree-treenode-container-title"
                                          >
                                            <span
                                              className="one-tree-checkbox"
                                              onClick={[Function]}
                                            >
                                              <Checkbox
                                                checked={false}
                                                indeterminate={false}
                                                size="medium"
                                              >
                                                <Checkbox
                                                  checked={false}
                                                  direction="row"
                                                  indeterminate={false}
                                                  mode="basic"
                                                  prefixCls="one-checkbox"
                                                  size="medium"
                                                >
                                                  <label
                                                    className="one-checkbox-wrapper one-checkbox-wrapper-row one-checkbox-wrapper-medium"
                                                  >
                                                    <Checkbox
                                                      checked={false}
                                                      className=""
                                                      defaultChecked={false}
                                                      indeterminate={false}
                                                      mode="basic"
                                                      onBlur={[Function]}
                                                      onChange={[Function]}
                                                      onFocus={[Function]}
                                                      prefixCls="one-checkbox"
                                                      type="checkbox"
                                                    >
                                                      <span
                                                        className="one-checkbox"
                                                      >
                                                        <input
                                                          checked={false}
                                                          className="one-checkbox-input"
                                                          onBlur={[Function]}
                                                          onChange={[Function]}
                                                          onFocus={[Function]}
                                                          type="checkbox"
                                                        />
                                                        <span
                                                          className="one-checkbox-inner"
                                                        />
                                                      </span>
                                                    </Checkbox>
                                                  </label>
                                                </Checkbox>
                                              </Checkbox>
                                            </span>
                                            <span
                                              className="one-tree-node-content-wrapper one-tree-node-content-wrapper-normal"
                                              onClick={[Function]}
                                              onContextMenu={[Function]}
                                              onDoubleClick={[Function]}
                                              onMouseEnter={[Function]}
                                              onMouseLeave={[Function]}
                                              title=""
                                            >
                                              <span
                                                className="one-tree-title"
                                              >
                                                <CommonItemRender
                                                  key="1"
                                                  prefixCls="one-transfer"
                                                  relationText=""
                                                  searchValue=""
                                                  title="门店1"
                                                >
                                                  <span
                                                    className="one-transfer-item-title"
                                                    title="门店1"
                                                  >
                                                    <span
                                                      key="normal-0"
                                                    >
                                                      门店1
                                                    </span>
                                                    <span
                                                      className="one-transfer-relation-text"
                                                    >
                                                       
                                                    </span>
                                                  </span>
                                                </CommonItemRender>
                                              </span>
                                            </span>
                                          </span>
                                        </div>
                                      </li>
                                    </TreeNode>
                                    <TreeNode
                                      checkable={true}
                                      checked={false}
                                      disabledAllWhenNodeDisabled={false}
                                      eventKey="2"
                                      expanded={false}
                                      halfChecked={false}
                                      isFileNode={false}
                                      key="2"
                                      loaded={false}
                                      loading={false}
                                      pos="0-1"
                                      selected={false}
                                      size="medium"
                                      title={
                                        <CommonItemRender
                                          prefixCls="one-transfer"
                                          relationText=""
                                          searchValue=""
                                          title="门店2"
                                        />
                                      }
                                    >
                                      <li
                                        className="one-tree-treenode-switcher-close"
                                        role="treeitem"
                                      >
                                        <div
                                          className="one-tree-treenode-container"
                                        >
                                          <span
                                            className="one-tree-switcher one-tree-switcher-noop"
                                          >
                                            <Component
                                              checkable={true}
                                              checked={false}
                                              disabledAllWhenNodeDisabled={false}
                                              eventKey="2"
                                              expanded={false}
                                              halfChecked={false}
                                              isFileNode={false}
                                              isLeaf={true}
                                              loaded={false}
                                              loading={false}
                                              pos="0-1"
                                              selected={false}
                                              size="medium"
                                              title={
                                                <CommonItemRender
                                                  prefixCls="one-transfer"
                                                  relationText=""
                                                  searchValue=""
                                                  title="门店2"
                                                />
                                              }
                                            />
                                          </span>
                                          <span
                                            className="one-tree-treenode-container-title"
                                          >
                                            <span
                                              className="one-tree-checkbox"
                                              onClick={[Function]}
                                            >
                                              <Checkbox
                                                checked={false}
                                                indeterminate={false}
                                                size="medium"
                                              >
                                                <Checkbox
                                                  checked={false}
                                                  direction="row"
                                                  indeterminate={false}
                                                  mode="basic"
                                                  prefixCls="one-checkbox"
                                                  size="medium"
                                                >
                                                  <label
                                                    className="one-checkbox-wrapper one-checkbox-wrapper-row one-checkbox-wrapper-medium"
                                                  >
                                                    <Checkbox
                                                      checked={false}
                                                      className=""
                                                      defaultChecked={false}
                                                      indeterminate={false}
                                                      mode="basic"
                                                      onBlur={[Function]}
                                                      onChange={[Function]}
                                                      onFocus={[Function]}
                                                      prefixCls="one-checkbox"
                                                      type="checkbox"
                                                    >
                                                      <span
                                                        className="one-checkbox"
                                                      >
                                                        <input
                                                          checked={false}
                                                          className="one-checkbox-input"
                                                          onBlur={[Function]}
                                                          onChange={[Function]}
                                                          onFocus={[Function]}
                                                          type="checkbox"
                                                        />
                                                        <span
                                                          className="one-checkbox-inner"
                                                        />
                                                      </span>
                                                    </Checkbox>
                                                  </label>
                                                </Checkbox>
                                              </Checkbox>
                                            </span>
                                            <span
                                              className="one-tree-node-content-wrapper one-tree-node-content-wrapper-normal"
                                              onClick={[Function]}
                                              onContextMenu={[Function]}
                                              onDoubleClick={[Function]}
                                              onMouseEnter={[Function]}
                                              onMouseLeave={[Function]}
                                              title=""
                                            >
                                              <span
                                                className="one-tree-title"
                                              >
                                                <CommonItemRender
                                                  key="2"
                                                  prefixCls="one-transfer"
                                                  relationText=""
                                                  searchValue=""
                                                  title="门店2"
                                                >
                                                  <span
                                                    className="one-transfer-item-title"
                                                    title="门店2"
                                                  >
                                                    <span
                                                      key="normal-0"
                                                    >
                                                      门店2
                                                    </span>
                                                    <span
                                                      className="one-transfer-relation-text"
                                                    >
                                                       
                                                    </span>
                                                  </span>
                                                </CommonItemRender>
                                              </span>
                                            </span>
                                          </span>
                                        </div>
                                      </li>
                                    </TreeNode>
                                    <TreeNode
                                      checkable={true}
                                      checked={false}
                                      disabledAllWhenNodeDisabled={false}
                                      eventKey="10"
                                      expanded={false}
                                      halfChecked={false}
                                      isFileNode={false}
                                      key="10"
                                      loaded={false}
                                      loading={false}
                                      pos="0-2"
                                      selected={false}
                                      size="medium"
                                      title={
                                        <CommonItemRender
                                          prefixCls="one-transfer"
                                          relationText=""
                                          searchValue=""
                                          title="门店3"
                                        >
                                          11
                                          12
                                        </CommonItemRender>
                                      }
                                    >
                                      <li
                                        className="one-tree-treenode-switcher-close"
                                        role="treeitem"
                                      >
                                        <div
                                          className="one-tree-treenode-container"
                                        >
                                          <span
                                            className="one-tree-switcher one-tree-switcher_close"
                                            onClick={[Function]}
                                          >
                                            <Component
                                              checkable={true}
                                              checked={false}
                                              disabledAllWhenNodeDisabled={false}
                                              eventKey="10"
                                              expanded={false}
                                              halfChecked={false}
                                              isFileNode={false}
                                              isLeaf={false}
                                              loaded={false}
                                              loading={false}
                                              pos="0-2"
                                              selected={false}
                                              size="medium"
                                              title={
                                                <CommonItemRender
                                                  prefixCls="one-transfer"
                                                  relationText=""
                                                  searchValue=""
                                                  title="门店3"
                                                >
                                                  11
                                                  12
                                                </CommonItemRender>
                                              }
                                            >
                                              <IconCaretRight
                                                className="one-tree-node-switch-arrow"
                                              >
                                                <svg
                                                  className="dls-icon one-tree-node-switch-arrow"
                                                  dangerouslySetInnerHTML={
                                                    Object {
                                                      "__html": "<path fill=\\"currentColor\\" d=\\"M7.5 4.688a.8.8 0 0 1 1.307-.619l8.558 7.002a1.2 1.2 0 0 1 0 1.858L8.807 19.93a.8.8 0 0 1-1.307-.62V4.689z\\"/>",
                                                    }
                                                  }
                                                  fill="none"
                                                  focusable={false}
                                                  height={24}
                                                  viewBox="0 0 24 24"
                                                  width={24}
                                                  xmlns="http://www.w3.org/2000/svg"
                                                />
                                              </IconCaretRight>
                                            </Component>
                                          </span>
                                          <span
                                            className="one-tree-treenode-container-title"
                                          >
                                            <span
                                              className="one-tree-checkbox"
                                              onClick={[Function]}
                                            >
                                              <Checkbox
                                                checked={false}
                                                indeterminate={false}
                                                size="medium"
                                              >
                                                <Checkbox
                                                  checked={false}
                                                  direction="row"
                                                  indeterminate={false}
                                                  mode="basic"
                                                  prefixCls="one-checkbox"
                                                  size="medium"
                                                >
                                                  <label
                                                    className="one-checkbox-wrapper one-checkbox-wrapper-row one-checkbox-wrapper-medium"
                                                  >
                                                    <Checkbox
                                                      checked={false}
                                                      className=""
                                                      defaultChecked={false}
                                                      indeterminate={false}
                                                      mode="basic"
                                                      onBlur={[Function]}
                                                      onChange={[Function]}
                                                      onFocus={[Function]}
                                                      prefixCls="one-checkbox"
                                                      type="checkbox"
                                                    >
                                                      <span
                                                        className="one-checkbox"
                                                      >
                                                        <input
                                                          checked={false}
                                                          className="one-checkbox-input"
                                                          onBlur={[Function]}
                                                          onChange={[Function]}
                                                          onFocus={[Function]}
                                                          type="checkbox"
                                                        />
                                                        <span
                                                          className="one-checkbox-inner"
                                                        />
                                                      </span>
                                                    </Checkbox>
                                                  </label>
                                                </Checkbox>
                                              </Checkbox>
                                            </span>
                                            <span
                                              className="one-tree-node-content-wrapper one-tree-node-content-wrapper-close"
                                              onClick={[Function]}
                                              onContextMenu={[Function]}
                                              onDoubleClick={[Function]}
                                              onMouseEnter={[Function]}
                                              onMouseLeave={[Function]}
                                              title=""
                                            >
                                              <span
                                                className="one-tree-title"
                                              >
                                                <CommonItemRender
                                                  key="10"
                                                  prefixCls="one-transfer"
                                                  relationText=""
                                                  searchValue=""
                                                  title="门店3"
                                                >
                                                  <span
                                                    className="one-transfer-item-title"
                                                    title="门店3"
                                                  >
                                                    <span
                                                      key="normal-0"
                                                    >
                                                      门店3
                                                    </span>
                                                    <span
                                                      className="one-transfer-relation-text"
                                                    >
                                                       
                                                    </span>
                                                  </span>
                                                </CommonItemRender>
                                              </span>
                                            </span>
                                          </span>
                                        </div>
                                      </li>
                                    </TreeNode>
                                  </ul>
                                </Tree>
                              </Tree>
                            </CandidatePane>
                          </div>
                        </div>
                      </div>
                      <div
                        className="one-transfer-select"
                        style={Object {}}
                      >
                        <SelectedTitle
                          CustomCandidatePane={[Function]}
                          CustomSelectedPane={[Function]}
                          SelectedItem={[Function]}
                          allDataMap={
                            Object {
                              "1": Object {
                                "key": "1",
                                "title": "门店1",
                              },
                              "10": Object {
                                "children": Array [
                                  "11",
                                  "12",
                                ],
                                "key": "10",
                                "title": "门店3",
                              },
                              "11": Object {
                                "key": "11",
                                "title": "门店31",
                              },
                              "12": Object {
                                "key": "12",
                                "title": "门店32",
                              },
                              "2": Object {
                                "key": "2",
                                "title": "门店2",
                              },
                            }
                          }
                          candidateFooterProps={Object {}}
                          candidateItemProps={Object {}}
                          candidateList={
                            Array [
                              "1",
                              "2",
                              "10",
                            ]
                          }
                          candidateTreeStyle={Object {}}
                          className=""
                          handleCandidateExpand={[Function]}
                          handleDelete={[Function]}
                          handleDeleteAll={[Function]}
                          handleLevelChange={[Function]}
                          handleSearch={[Function]}
                          handleSelect={[Function]}
                          handleSelectAll={[Function]}
                          handleSelectedExpand={[Function]}
                          isShowLevel={false}
                          isShowLevelSelect={false}
                          levelKey={null}
                          levelOptions={null}
                          loading={false}
                          loadingText="加载中..."
                          maxSelectedNum={null}
                          mergeChecked="downwards"
                          onDeleteAll={[Function]}
                          onSearchBoxBlur={[Function]}
                          onSearchBoxFocus={[Function]}
                          onSearchChange={[Function]}
                          parentRelationMap={
                            Object {
                              "11": "10",
                              "12": "10",
                            }
                          }
                          placeholder="请搜索"
                          prefixCls="one-transfer"
                          searchRenderProps={Object {}}
                          selectedItemProps={Object {}}
                          selectedList={Array []}
                          selectedTreeStyle={Object {}}
                          showCandidateFooter={false}
                          showCandidateNum={true}
                          showSearchBox={true}
                          showSelectAll={true}
                          showSelectedNum={true}
                          size="medium"
                          treeName=""
                          useVirtualScroll={false}
                        >
                          <div
                            className="one-transfer-pane-title"
                          >
                            <TitleRender
                              CustomCandidatePane={[Function]}
                              CustomSelectedPane={[Function]}
                              SelectedItem={[Function]}
                              allDataMap={
                                Object {
                                  "1": Object {
                                    "key": "1",
                                    "title": "门店1",
                                  },
                                  "10": Object {
                                    "children": Array [
                                      "11",
                                      "12",
                                    ],
                                    "key": "10",
                                    "title": "门店3",
                                  },
                                  "11": Object {
                                    "key": "11",
                                    "title": "门店31",
                                  },
                                  "12": Object {
                                    "key": "12",
                                    "title": "门店32",
                                  },
                                  "2": Object {
                                    "key": "2",
                                    "title": "门店2",
                                  },
                                }
                              }
                              candidateFooterProps={Object {}}
                              candidateItemProps={Object {}}
                              candidateList={
                                Array [
                                  "1",
                                  "2",
                                  "10",
                                ]
                              }
                              candidateTreeStyle={Object {}}
                              className=""
                              handleCandidateExpand={[Function]}
                              handleDelete={[Function]}
                              handleDeleteAll={[Function]}
                              handleLevelChange={[Function]}
                              handleSearch={[Function]}
                              handleSelect={[Function]}
                              handleSelectAll={[Function]}
                              handleSelectedExpand={[Function]}
                              isShowLevel={false}
                              isShowLevelSelect={false}
                              levelKey={null}
                              levelOptions={null}
                              loading={false}
                              loadingText="加载中..."
                              maxSelectedNum={null}
                              mergeChecked="downwards"
                              onDeleteAll={[Function]}
                              onSearchBoxBlur={[Function]}
                              onSearchBoxFocus={[Function]}
                              onSearchChange={[Function]}
                              parentRelationMap={
                                Object {
                                  "11": "10",
                                  "12": "10",
                                }
                              }
                              placeholder="请搜索"
                              prefixCls="one-transfer"
                              searchRenderProps={Object {}}
                              selectedItemProps={Object {}}
                              selectedList={Array []}
                              selectedNum={0}
                              selectedTreeStyle={Object {}}
                              showCandidateFooter={false}
                              showCandidateNum={true}
                              showSearchBox={true}
                              showSelectAll={true}
                              showSelectedNum={true}
                              size="medium"
                              title="已选"
                              treeName=""
                              useVirtualScroll={false}
                            >
                              <span
                                className="one-transfer-pane-title-text"
                                title="已选"
                              >
                                <span
                                  className="one-transfer-pane-title-text-text"
                                >
                                  <span>
                                    已选
                                  </span>
                                </span>
                              </span>
                            </TitleRender>
                            <Button
                              className="one-transfer-delete-all"
                              disabled={true}
                              onClick={[Function]}
                              size="medium"
                              type="text-strong"
                            >
                              <Button
                                className="one-transfer-delete-all"
                                disabled={true}
                                htmlType="button"
                                icon=""
                                loading={false}
                                name=""
                                onClick={[Function]}
                                prefixCls="one-button"
                                readOnly={false}
                                readonly={false}
                                size="medium"
                                type="text-strong"
                              >
                                <button
                                  className="one-button one-transfer-delete-all one-main one-button-text-strong one-button-medium one-button-text-strong-disabled"
                                  disabled={true}
                                  name=""
                                  onClick={[Function]}
                                  type="button"
                                >
                                  <span>
                                    清空
                                  </span>
                                </button>
                              </Button>
                            </Button>
                          </div>
                        </SelectedTitle>
                        <div
                          className="one-transfer-select-main"
                        >
                          <div
                            className="one-transfer-select-pane"
                          >
                            <SelectedPane
                              CustomCandidatePane={[Function]}
                              CustomSelectedPane={[Function]}
                              SelectedItem={[Function]}
                              allDataMap={
                                Object {
                                  "1": Object {
                                    "key": "1",
                                    "title": "门店1",
                                  },
                                  "10": Object {
                                    "children": Array [
                                      "11",
                                      "12",
                                    ],
                                    "key": "10",
                                    "title": "门店3",
                                  },
                                  "11": Object {
                                    "key": "11",
                                    "title": "门店31",
                                  },
                                  "12": Object {
                                    "key": "12",
                                    "title": "门店32",
                                  },
                                  "2": Object {
                                    "key": "2",
                                    "title": "门店2",
                                  },
                                }
                              }
                              candidateFooterProps={Object {}}
                              candidateItemProps={Object {}}
                              candidateList={
                                Array [
                                  "1",
                                  "2",
                                  "10",
                                ]
                              }
                              candidateTreeStyle={Object {}}
                              className=""
                              expandedSelectedKeys={Array []}
                              getSelectedTreeRef={[Function]}
                              handleCandidateExpand={[Function]}
                              handleDelete={[Function]}
                              handleDeleteAll={[Function]}
                              handleLevelChange={[Function]}
                              handleSearch={[Function]}
                              handleSelect={[Function]}
                              handleSelectAll={[Function]}
                              handleSelectedExpand={[Function]}
                              isShowLevel={false}
                              isShowLevelSelect={false}
                              levelKey={null}
                              levelOptions={null}
                              loading={false}
                              loadingText="加载中..."
                              maxSelectedNum={null}
                              mergeChecked="downwards"
                              onDelete={[Function]}
                              onExpand={[Function]}
                              onSearchBoxBlur={[Function]}
                              onSearchBoxFocus={[Function]}
                              onSearchChange={[Function]}
                              parentRelationMap={
                                Object {
                                  "11": "10",
                                  "12": "10",
                                }
                              }
                              placeholder="请搜索"
                              prefixCls="one-transfer"
                              searchRenderProps={Object {}}
                              selectedItemProps={Object {}}
                              selectedList={Array []}
                              selectedTreeStyle={Object {}}
                              showCandidateFooter={false}
                              showCandidateNum={true}
                              showSearchBox={true}
                              showSelectAll={true}
                              showSelectedNum={true}
                              size="medium"
                              treeName=""
                              useVirtualScroll={false}
                              virtualListHeight={null}
                            >
                              <Tree
                                checkStrictly={false}
                                checkable={false}
                                className="one-transfer-selected-tree"
                                expandedKeys={Array []}
                                onExpand={[Function]}
                                parentContainerHeight={null}
                                prefixCls="one-tree"
                                selectedKeys={Array []}
                                showIcon={false}
                                size="medium"
                              >
                                <Tree
                                  autoExpandParent={false}
                                  checkStrictly={false}
                                  checkable={false}
                                  className="one-transfer-selected-tree one-tree-icon-hide one-tree-medium"
                                  defaultCheckedKeys={Array []}
                                  defaultExpandAll={false}
                                  defaultExpandParent={true}
                                  defaultExpandedKeys={Array []}
                                  defaultSelectedKeys={Array []}
                                  disabled={false}
                                  expandedKeys={Array []}
                                  multiple={false}
                                  onExpand={[Function]}
                                  parentContainerHeight={null}
                                  prefixCls="one-tree"
                                  selectable={true}
                                  selectedKeys={Array []}
                                  showIcon={false}
                                  showLine={false}
                                  size="medium"
                                  switcherIcon={[Function]}
                                >
                                  <ul
                                    className="one-tree one-transfer-selected-tree one-tree-icon-hide one-tree-medium"
                                    role="tree"
                                    unselectable="on"
                                  />
                                </Tree>
                              </Tree>
                            </SelectedPane>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Transfer>
                </Transfer>
              </div>
            </div>
          </div>
        </FormField>
        <FormField
          actions={true}
          helpPosition="bottom"
          label=""
          messageDisplay="simple"
          prefixCls="one-form"
          validateFirst={true}
        >
          <div
            className="one-form-field one-form-field-actions"
          >
            <div
              className="one-form-field-label"
            >
              <label
                title=""
              />
            </div>
            <div
              className="one-form-field-main"
            >
              <div
                className="one-form-field-content"
                key="Content"
              >
                <Button
                  htmlType="submit"
                  type="primary"
                >
                  <Button
                    disabled={false}
                    htmlType="submit"
                    icon=""
                    loading={false}
                    name=""
                    onClick={[Function]}
                    prefixCls="one-button"
                    readOnly={false}
                    readonly={false}
                    size="medium"
                    type="primary"
                  >
                    <button
                      className="one-button one-main one-button-primary one-button-medium"
                      disabled={false}
                      name=""
                      onClick={[Function]}
                      type="submit"
                    >
                      <span>
                        Submit
                      </span>
                    </button>
                  </Button>
                </Button>
              </div>
            </div>
          </div>
        </FormField>
      </form>
    </BaseForm>
  </ForwardRef>
</ForwardRef>
`;

exports[`Form Form.Item should support data-*、aria-* and custom attribute 1`] = `
<form
  autocomplete="off"
  class="one-form one-form-default one-form-label-position-side one-form-medium one-form-type-default"
>
  <div
    class="one-row one-form-item"
  >
    <div
      class="one-form-item-control-wrapper"
    >
      <div
        class="one-form-item-control"
      >
        <span
          class="one-form-item-children"
        >
          测试自定义属性
        </span>
      </div>
    </div>
  </div>
</form>
`;

exports[`Form FormItem FormItem: generate snapshot when validates fields 1`] = `
<form
  autocomplete="off"
  class="one-form one-form-default one-form-label-position-side one-form-legacy one-form-medium one-form-type-default"
>
  <div
    class="one-row one-form-item"
  >
    <div
      class="one-form-item-control-wrapper"
    >
      <div
        class="one-form-item-control"
      >
        <span
          class="one-form-item-children"
        >
          <input
            data-__field="[object Object]"
            data-__meta="[object Object]"
            id="test"
            value=""
          />
        </span>
      </div>
    </div>
  </div>
</form>
`;

exports[`Form FormItem FormItem: generate snapshot when validates fields 2`] = `
<form
  autocomplete="off"
  class="one-form one-form-default one-form-label-position-side one-form-legacy one-form-medium one-form-type-default"
>
  <div
    class="one-row one-form-item"
  >
    <div
      class="one-form-item-control-wrapper"
    >
      <div
        class="one-form-item-control one-form-is-validating one-form-item-explain-left"
      >
        <span
          class="one-form-item-children"
        >
          <input
            data-__field="[object Object]"
            data-__meta="[object Object]"
            id="test"
            value="test"
          />
        </span>
      </div>
    </div>
  </div>
</form>
`;

exports[`Form FormItem FormItem: generate snapshot when validates fields 3`] = `
<form
  autocomplete="off"
  class="one-form one-form-default one-form-label-position-side one-form-legacy one-form-medium one-form-type-default"
>
  <div
    class="one-row one-form-item"
  >
    <div
      class="one-form-item-control-wrapper"
    >
      <div
        class="one-form-item-control one-form-is-validating one-form-item-explain-left"
      >
        <span
          class="one-form-item-children"
        >
          <input
            data-__field="[object Object]"
            data-__meta="[object Object]"
            id="test"
            value="test"
          />
        </span>
      </div>
    </div>
  </div>
</form>
`;
