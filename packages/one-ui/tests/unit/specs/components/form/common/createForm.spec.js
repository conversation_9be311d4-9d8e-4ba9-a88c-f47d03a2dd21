/**
 * @file: createForm.spec.js
 * @Date: 2020-08-21 15:32:27
 * @Last Modified by: cheng<PERSON><PERSON><PERSON>
 * @Last Modified time: 2020-08-26 14:48:02
 */

import React from 'react';
import {mount} from 'enzyme';
import {commonMount} from './util';
import Form from '../../../../../../src/components/form';
import {sleep} from '../../../../../utils';

const {createFormField, create: createForm} =  Form;

describe('validateMessage', () => {
    it('works', async () => {
        class Test extends React.Component {
            render() {
                const {getFieldProps} = this.props.form;
                return (
                    <div>
                        <input {...getFieldProps('exampleField', {
                            rules: [{
                                required: true
                            }]
                        })}
                        />
                    </div>
                );
            }
        }
        const Wrapped = createForm({
            validateMessages: {
                required: '%s required!'
            }
        })(Test);
        const {form, wrapper} = commonMount(Wrapped);
        wrapper.find('input').simulate('change');
        await sleep(50);
        expect(form.getFieldError('exampleField').length).toBe(1);
        expect(form.getFieldError('exampleField')[0]).toMatchSnapshot();
    });
});

class FormContent extends React.Component {
    render() {
        const {getFieldProps} = this.props.form;
        return (
            <form>
                <input {...getFieldProps('user.name')} />
                <input {...getFieldProps('user.age')} type="number" />
                <input {...getFieldProps('agreement')} type="checkbox" />
            </form>
        );
    }
}
describe('onFieldsChange', () => {
    it('trigger `onFieldsChange` when value change', () => {
        const onFieldsChange = jest.fn();
        const Test = createForm({
            onFieldsChange
        })(FormContent);
        const wrapper = mount(<Test />);
        wrapper.find('input').first().simulate('change', {target: {value: 'Jack'}});
        // change fields
        expect(onFieldsChange.mock.calls[0][1]).toMatchSnapshot();
        // all fields
        expect(onFieldsChange.mock.calls[0][2]).toMatchSnapshot();
    });

    it('trigger `onFieldsChange` when `setFields`', () => {
        const onFieldsChange = jest.fn();
        const Test = createForm({
            onFieldsChange
        })(FormContent);
        const {form} = commonMount(Test);
        form.setFields({user: {name: {value: 'Jack'}}});
        expect(onFieldsChange.mock.calls[0][1]).toMatchSnapshot();
        expect(onFieldsChange.mock.calls[0][2]).toMatchSnapshot();
    });

    it('fields in arguemnts can be passed to `mapPropsToFields` directly', () => {
        const Test = createForm({
            onFieldsChange(props, changed, all) {
                props.onChange(all);
            },
            mapPropsToFields({fields}) {
                return fields;
            }
        })(FormContent);
        class TestWrapper extends React.Component {
            constructor(props) {
                super(props);
                this.state = {
                    fields: {}
                };
            }
            handleFieldsChange = fields => {
                this.setState({fields});
            }
            render() {
                return (
                    <Test
                        ref="test"
                        fields={this.state.fields}
                        onChange={this.handleFieldsChange}
                    />
                );
            }
        }
        const wrapper = mount(<TestWrapper />);
        wrapper.find('input').at(0).simulate('change', {target: {value: 'Jack'}});
        wrapper.find('input').at(1).simulate('change', {target: {value: 18}});
        wrapper.find('input').at(2)
        .simulate('change', {target: {type: 'checkbox', checked: true}});
        expect(wrapper).toMatchSnapshot();
    });
});

describe('onValuesChange', () => {
    it('trigger `onValuesChange` when value change', () => {
        const onValuesChange = jest.fn();
        const Test = createForm({
            onValuesChange
        })(FormContent);
        const wrapper = mount(<Test />);
        wrapper.find('input').first().simulate('change', {target: {value: 'Jack'}});
        expect(onValuesChange.mock.calls[0][1]).toMatchSnapshot();
        expect(onValuesChange.mock.calls[0][2]).toMatchSnapshot();
    });

    it('trigger `onValuesChange` when `setFieldsValue`', () => {
        const onValuesChange = jest.fn();
        const Test = createForm({
            onValuesChange
        })(FormContent);
        const {form} = commonMount(Test);
        form.setFieldsValue({user: {name: 'Jack'}});
        expect(onValuesChange.mock.calls[0][1]).toMatchSnapshot();
        expect(onValuesChange.mock.calls[0][2]).toMatchSnapshot();
    });
});

describe('mapProps', () => {
    it('works', () => {
        const Test = createForm({
            mapProps(props) {
                return {
                    ...props,
                    x: props.x + 1
                };
            }
        })(class extends React.Component {
            render() {
                return null;
            }
        });
        const {formWrapper} = commonMount(Test, {x: 2});
        expect(formWrapper.props.x).toBe(3);
    });
});

describe('mapPropsToFields', () => {
    it('works', () => {
        const Test = createForm({
            mapPropsToFields({formState}) {
                return {
                    user: {
                        name: createFormField({
                            value: formState.userName
                        }),
                        age: createFormField({
                            value: formState.userAge
                        })
                    },
                    agreement: createFormField({
                        value: formState.agreement
                    })
                };
            }
        })(FormContent);
        const {form, wrapper} = commonMount(Test, {
            formState: {
                userName: 'Jack',
                userAge: 18,
                agreement: false
            }
        });
        expect(form.getFieldValue('user.name')).toBe('Jack');
        expect(form.getFieldValue('user.age')).toBe(18);
        expect(form.getFieldValue('agreement')).toBe(false);
        wrapper.setProps({formState: {userName: 'Rose', userAge: 19, agreement: true}});
        expect(form.getFieldValue('user.name')).toBe('Rose');
        expect(form.getFieldValue('user.age')).toBe(19);
        expect(form.getFieldValue('agreement')).toBe(true);
    });

    it('returned value will replace current fields', () => {
        const Test = createForm({
            mapPropsToFields(props) {
                return {
                    field1: createFormField({
                        value: props.formState.field1
                    })
                };
            }
        })(class extends React.Component {
            render() {
                const {getFieldProps} = this.props.form;
                return (
                <form>
                    <input {...getFieldProps('field1')} />
                    <input {...getFieldProps('field2')} />
                </form>
                );
            }
        });
        const {form, wrapper} = commonMount(Test, {formState: {field1: '2'}});
        wrapper.find('input').first().simulate('change', {target: {value: '3'}});
        expect(form.getFieldValue('field1')).toBe('3');
        wrapper.setProps({formState: {field1: '1'}});
        expect(form.getFieldValue('field1')).toBe('1');
        expect(form.getFieldValue('field2')).toBe(undefined);
    });
});