/**
 * @file: createDOMForm.spec.js
 * @Date: 2020-08-21 15:32:27
 * @Last Modified by: cheng<PERSON><PERSON><PERSON>
 * @Last Modified time: 2020-08-26 14:32:01
 */

import React from 'react';
import scrollIntoView from 'dom-scroll-into-view';
import createDOMForm from '../../../../../../src/components/form/common/createBaseForm';
import {commonMount} from './util';

jest.mock('dom-scroll-into-view', () => jest.fn());

class Test extends React.Component {
    render() {
        const {form, name} = this.props;
        const {getFieldDecorator} = form;
        return (
          <div>
            {getFieldDecorator(name, {
                rules: [{
                    required: true
                }]
            })(<textarea style={{overflowY: 'auto'}} />)}
          </div>
        );
    }
}

const Form = createDOMForm({})(Test);

describe('validateFieldsAndScroll', () => {
    beforeEach(() => {
        scrollIntoView.mockClear();
    });
    it('works on overflowY auto element', done => {
        const {form, wrapper} = commonMount(Form, {
            name: 'normal'
        }, {attachTo: document.body});
        form.validateFieldsAndScroll(() => {
            expect(scrollIntoView.mock.calls[0][1].tagName).not.toBe('TEXTAREA');
            wrapper.detach();
            done();
        });
    });

    it('works with nested fields', done => {
        const {form, wrapper} = commonMount(Form, {
            name: 'a.b.c'
        }, {attachTo: document.body});
        form.validateFieldsAndScroll(() => {
            expect(scrollIntoView).toHaveBeenCalled();
            wrapper.detach();
            done();
        });
    });
});
