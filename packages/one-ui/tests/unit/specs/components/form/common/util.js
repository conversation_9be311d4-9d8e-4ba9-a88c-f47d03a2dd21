/**
 * @file: util.js
 * @Date: 2020-08-21 15:32:27
 * @Last Modified by: chengxiao01
 * @Last Modified time: 2020-08-26 14:50:33
 */

import {mount} from 'enzyme';
import React from 'react';

export const commonMount = (Comp, props = {}, options) => {
    const {withRef = true, ...restProps} = props;
    const finProps = {
        ...restProps
    };
    let formWrapper = null;
    if (withRef) {
        finProps.wrappedComponentRef = node => {
            formWrapper = node;
        };
    }
    const wrapper = mount(
        <Comp
            {...finProps}
        />
    , options);
    if (withRef) {
        const form = formWrapper.props.form;
        return {
            formWrapper,
            wrapper,
            form
        };
    }
    return wrapper;
};
