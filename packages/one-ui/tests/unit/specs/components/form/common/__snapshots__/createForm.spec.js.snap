// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`onFieldsChange fields in arguemnts can be passed to \`mapPropsToFields\` directly 1`] = `
<TestWrapper>
  <ForwardRef
    fields={
      Object {
        "agreement": Field {
          "dirty": false,
          "name": "agreement",
          "touched": true,
          "value": true,
        },
        "user": Object {
          "age": Field {
            "dirty": false,
            "name": "user.age",
            "touched": true,
            "value": 18,
          },
          "name": Field {
            "dirty": false,
            "name": "user.name",
            "touched": true,
            "value": "Jack",
          },
        },
      }
    }
    onChange={[Function]}
  >
    <BaseForm
      LegacyForm={[Function]}
      fieldNameProp="id"
      fields={
        Object {
          "agreement": Field {
            "dirty": false,
            "name": "agreement",
            "touched": true,
            "value": true,
          },
          "user": Object {
            "age": Field {
              "dirty": false,
              "name": "user.age",
              "touched": true,
              "value": 18,
            },
            "name": Field {
              "dirty": false,
              "name": "user.name",
              "touched": true,
              "value": "Jack",
            },
          },
        }
      }
      formPropName="form"
      mapProps={[Function]}
      mapPropsToFields={[Function]}
      onChange={[Function]}
      onFieldsChange={[Function]}
      size="medium"
    >
      <FormContent
        fieldNameProp="id"
        fields={
          Object {
            "agreement": Field {
              "dirty": false,
              "name": "agreement",
              "touched": true,
              "value": true,
            },
            "user": Object {
              "age": Field {
                "dirty": false,
                "name": "user.age",
                "touched": true,
                "value": 18,
              },
              "name": Field {
                "dirty": false,
                "name": "user.name",
                "touched": true,
                "value": "Jack",
              },
            },
          }
        }
        form={
          Object {
            "getFieldDecorator": [Function],
            "getFieldError": [Function],
            "getFieldInstance": [Function],
            "getFieldProps": [Function],
            "getFieldSuccess": [Function],
            "getFieldValue": [Function],
            "getFieldWarning": [Function],
            "getFieldsError": [Function],
            "getFieldsSuccess": [Function],
            "getFieldsValue": [Function],
            "getFieldsWarning": [Function],
            "isFieldTouched": [Function],
            "isFieldValidating": [Function],
            "isFieldsTouched": [Function],
            "isFieldsValidating": [Function],
            "isFormValidating": [Function],
            "isSubmitting": [Function],
            "resetFields": [Function],
            "scrollToField": [Function],
            "setFields": [Function],
            "setFieldsError": [Function],
            "setFieldsInitialValue": [Function],
            "setFieldsValue": [Function],
            "submit": [Function],
            "validateFields": [Function],
            "validateFieldsAndScroll": [Function],
            "validateForm": [Function],
          }
        }
        mapPropsToFields={[Function]}
        onChange={[Function]}
        onFieldsChange={[Function]}
        size="medium"
      >
        <form>
          <input
            data-__field={
              Object {
                "dirty": false,
                "name": "user.name",
                "touched": true,
                "value": "Jack",
              }
            }
            data-__meta={
              Object {
                "name": "user.name",
                "trigger": "onChange",
                "validate": Array [],
                "valuePropName": "value",
              }
            }
            id="user.name"
            onChange={[Function]}
            value="Jack"
          />
          <input
            data-__field={
              Object {
                "dirty": false,
                "name": "user.age",
                "touched": true,
                "value": 18,
              }
            }
            data-__meta={
              Object {
                "name": "user.age",
                "trigger": "onChange",
                "validate": Array [],
                "valuePropName": "value",
              }
            }
            id="user.age"
            onChange={[Function]}
            type="number"
            value={18}
          />
          <input
            data-__field={
              Object {
                "dirty": false,
                "name": "agreement",
                "touched": true,
                "value": true,
              }
            }
            data-__meta={
              Object {
                "name": "agreement",
                "trigger": "onChange",
                "validate": Array [],
                "valuePropName": "value",
              }
            }
            id="agreement"
            onChange={[Function]}
            type="checkbox"
            value={true}
          />
        </form>
      </FormContent>
    </BaseForm>
  </ForwardRef>
</TestWrapper>
`;

exports[`onFieldsChange trigger \`onFieldsChange\` when \`setFields\` 1`] = `
Object {
  "user": Object {
    "name": Object {
      "name": "user.name",
      "value": "Jack",
    },
  },
}
`;

exports[`onFieldsChange trigger \`onFieldsChange\` when \`setFields\` 2`] = `
Object {
  "agreement": Field {
    "dirty": false,
    "name": "agreement",
    "value": undefined,
  },
  "user": Object {
    "age": Field {
      "dirty": false,
      "name": "user.age",
      "value": undefined,
    },
    "name": Field {
      "value": "Jack",
    },
  },
}
`;

exports[`onFieldsChange trigger \`onFieldsChange\` when value change 1`] = `
Object {
  "user": Object {
    "name": Object {
      "dirty": false,
      "name": "user.name",
      "touched": true,
      "value": "Jack",
    },
  },
}
`;

exports[`onFieldsChange trigger \`onFieldsChange\` when value change 2`] = `
Object {
  "agreement": Field {
    "dirty": false,
    "name": "agreement",
    "value": undefined,
  },
  "user": Object {
    "age": Field {
      "dirty": false,
      "name": "user.age",
      "value": undefined,
    },
    "name": Field {
      "dirty": false,
      "name": "user.name",
      "touched": true,
      "value": "Jack",
    },
  },
}
`;

exports[`onValuesChange trigger \`onValuesChange\` when \`setFieldsValue\` 1`] = `
Object {
  "user": Object {
    "name": "Jack",
  },
}
`;

exports[`onValuesChange trigger \`onValuesChange\` when \`setFieldsValue\` 2`] = `
Object {
  "agreement": undefined,
  "user": Object {
    "age": undefined,
    "name": "Jack",
  },
}
`;

exports[`onValuesChange trigger \`onValuesChange\` when value change 1`] = `
Object {
  "user": Object {
    "name": "Jack",
  },
}
`;

exports[`onValuesChange trigger \`onValuesChange\` when value change 2`] = `
Object {
  "agreement": undefined,
  "user": Object {
    "age": undefined,
    "name": "Jack",
  },
}
`;

exports[`validateMessage works 1`] = `"exampleField required!"`;
