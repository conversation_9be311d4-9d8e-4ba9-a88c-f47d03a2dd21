/**
 * @file: getFieldProps.spec.js
 * @Date: 2020-08-21 15:32:27
 * @Last Modified by: cheng<PERSON><PERSON><PERSON>
 * @Last Modified time: 2020-09-16 11:41:32
 */

import React from 'react';
import createForm from '../../../../../../src/components/form/common/createBaseForm';
import {commonMount} from './util';
import {sleep} from '../../../../../utils';

describe('fieldName', () => {
    it('support disordered array', () => {
        const Test = createForm()(
            class extends React.Component {
                render() {
                    const {getFieldProps} = this.props.form;
                    return (
                        <div>
                        <input
                            {...getFieldProps('array[1]', {
                                rules: [{required: true}]
                            })}
                        />
                        <input
                            {...getFieldProps('array[0]', {
                                rules: [{required: true}]
                            })}
                        />
                        </div>
                    );
                }
            }
        );
        const {form} = commonMount(Test);
        expect(() => form.validateFields()).not.toThrow();
    });
});

describe('initialValue', () => {
    it('initialValue works for ok', () => {
        const Test = createForm()(
            class extends React.Component {
                render() {
                    const {getFieldProps} = this.props.form;
                    return <input {...getFieldProps('normal', {initialValue: '1'})} />;
                }
            }
        );
        const {form, wrapper} = commonMount(Test);
        expect(form.getFieldValue('normal')).toBe('1');
        wrapper.find('input').simulate('change', {target: {value: '2'}});
        expect(form.getFieldValue('normal')).toBe('2');
        form.resetFields();
        expect(form.getFieldValue('normal')).toBe('1');
    });
});

describe('getValueProps', () => {
    it('getValueProps works for ok', () => {
        const Test = createForm()(
            class extends React.Component {
                render() {
                    const {getFieldProps} = this.props.form;
                    return (
                        <input
                            {...getFieldProps('normal', {
                                getValueProps(v) {
                                    return {value: `${v}1`};
                                }
                            })}
                        />
                    );
                }
            }
        );
        const {form, wrapper} = commonMount(Test);
        wrapper.find('input').simulate('change', {target: {value: '2'}});
        expect(form.getFieldValue('normal')).toBe('2');
        expect(form.getFieldInstance('normal').value).toBe('21');
    });
});

describe('getValueFromEvent', () => {
    it('getValueFromEvent works for ok', () => {
        const Test = createForm()(
            class extends React.Component {
                render() {
                    const {getFieldProps} = this.props.form;
                    return (
                        <input
                            {...getFieldProps('normal', {
                                getValueFromEvent(e) {
                                    return `${e.target.value}1`;
                                }
                            })}
                        />
                    );
                }
            }
        );
        const {form, wrapper} = commonMount(Test);
        wrapper.find('input').simulate('change', {target: {value: '2'}});
        expect(form.getFieldValue('normal')).toBe('21');
    });
});

describe('normalize', () => {
    it('normalize works for ok', () => {
        const Test = createForm()(
            class extends React.Component {
                toUpper = v => v && v.toUpperCase()
                render() {
                    const {getFieldProps} = this.props.form;
                    return (
                        <input
                        {...getFieldProps('normal', {
                            normalize: this.toUpper
                        })}
                        />
                    );
                }
            }
        );
        const {form, wrapper} = commonMount(Test);
        wrapper.find('input').simulate('change', {target: {value: 'a'}});
        expect(form.getFieldValue('normal')).toBe('A');
        expect(form.getFieldInstance('normal').value).toBe('A');
    });
});

describe('validate', () => {
    it('change or blur trigger validate ', async () => {
        const Test = createForm()(
            class extends React.Component {
                render() {
                    const {getFieldProps} = this.props.form;
                    return (
                        <input
                            {...getFieldProps('normal', {
                                validate: [{
                                    trigger: 'onBlur',
                                    rules: [{
                                        required: true
                                    }]
                                }]
                            })}
                        />
                    );
                }
            }
        );
        const {wrapper, form} = commonMount(Test);
        expect(form.getFieldValue('normal')).toBe(undefined);
        wrapper.find('input').simulate('change', {target: {value: ''}});
        expect(form.getFieldValue('normal')).toBe('');
        expect(form.getFieldError('normal')).toBe(undefined);
        wrapper.find('input').simulate('blur', {target: {value: ''}});
        await sleep(50);
        expect(form.getFieldValue('normal')).toBe('');
        expect(form.getFieldError('normal')).toEqual(['normal is required']);
        wrapper.find('input').simulate('blur', {target: {value: '1'}});
        await sleep(50);
        expect(form.getFieldValue('normal')).toBe('1');
        expect(form.getFieldError('normal')).toBe(undefined);
    });

    it('support jsx message', () => {
        const Test = createForm()(
            class extends React.Component {
                render() {
                    const {getFieldProps} = this.props.form;
                    return (
                        <input
                            {...getFieldProps('required', {
                                rules: [{
                                    required: true,
                                    message: <b>1</b>
                                }]
                            })}
                        />
                    );
                }
            }
        );
        const {wrapper, form} = commonMount(Test);
        wrapper.find('input').simulate('change');
        expect(form.getFieldError('required')).toMatchSnapshot();
    });
});

describe('hidden', () => {
    it('hidden field will not participate in the verification', callback => {
        const Test = createForm()(
            class extends React.Component {
                render() {
                    const {getFieldProps} = this.props.form;
                    return (
                        <input
                            {...getFieldProps('normal', {
                                hidden: true,
                                initialValue: '',
                                rules: [{required: true}]
                            })}
                        />
                    );
                }
            }
        );
        const {form} = commonMount(Test);
        expect(form.getFieldsValue()).toEqual({});
        form.validateFields((errors, values) => {
            expect(errors).toBe(null);
            expect(values).toEqual({});
            callback();
        });
    });

    it('can be set', () => {
        const Test = createForm()(
            class extends React.Component {
                render() {
                    const {getFieldProps} = this.props.form;
                    return (
                        <input
                            {...getFieldProps('normal', {
                                hidden: true,
                                initialValue: ''
                            })}
                        />
                    );
                }
            }
        );
        const {form} = commonMount(Test);
        expect(form.getFieldsValue(['normal'])).toEqual({normal: ''});
        form.setFieldsValue({normal: 'Hello world!'});
        expect(form.getFieldsValue(['normal'])).toEqual({normal: 'Hello world!'});
    });
});
