/**
 * @file Input.spec.js
 * <AUTHOR> <EMAIL>
 * @date 2020-09-03
 */
import {mount} from 'enzyme';
import React from 'react';
import Input from '../../../../src/components/input/input';
import Form from '../../../../src/components/form/index';
import mountTest from '../../../../tests/shared/mountTest';
import focusTest from '../../../../tests/shared/focusTest';

describe('Input Component', () => {
    focusTest(Input);
    mountTest(Input);

    test('should support maxLength', () => {
        const wrapper = mount(<Input maxLen={3} />);
        expect(wrapper).toMatchSnapshot();
    });
});

describe('Input As Form control', () => {
    test('should be reset when wrapped in form.getFieldDecorator without initialValue', () => {
        class Demo extends React.Component {
            reset = () => {
                const {form} = this.props;
                form.resetFields();
            };

            render() {
                const {
                    form: {getFieldDecorator}
                } = this.props;
                return (
                    <Form>
                        <Form.Item>{getFieldDecorator('input', {
                            getValueFromEvent: e => {
                                return e.value;
                            }
                        })(<Input />)}</Form.Item>
                        <button type="button" onClick={this.reset}>
                            reset
                        </button>
                    </Form>
                );
            }
        }
        const DemoForm = Form.create()(Demo);
        const wrapper = mount(<DemoForm />);
        wrapper.find('input').simulate('change', {target: {value: '111'}});
        expect(wrapper.find('input').prop('value')).toBe('111');
        wrapper.find('button').simulate('click');
        expect(wrapper.find('input').prop('value')).toBe('');
    });
});

describe('Input allow clear', () => {
    test('should normal clear when click clear icon', () => {
        const wrapper = mount(<Input showClear />);
        wrapper.find('input').simulate('change', {target: {value: 'hello'}});
        expect(wrapper.find('input').getDOMNode().value).toEqual('hello');
        expect(wrapper).toMatchSnapshot();
        wrapper
            .find('.one-input-clear-icon')
            .at(0)
            .simulate('click');
        expect(wrapper).toMatchSnapshot();
        expect(wrapper.find('input').getDOMNode().value).toEqual('');

    });

    test('should trigger event correctly', () => {
        const wrapper = mount(<Input showClear defaultValue="trigger" />);
        wrapper
            .find('.one-input-clear-icon')
            .at(0)
            .simulate('click');
        expect(
            wrapper
                .find('input')
                .at(0)
                .getDOMNode().value,
        ).toBe('');
    });

    test('should trigger event correctly on controlled mode', () => {
        const wrapper = mount(<Input showClear value="controlled" />);
        wrapper
            .find('.one-input-clear-icon')
            .at(0)
            .simulate('click');
        expect(
            wrapper
                .find('input')
                .at(0)
                .getDOMNode().value,
        ).toBe('controlled');
    });

    test('should not support showClear when it is disabled', () => {
        const wrapper = mount(<Input showClear defaultValue="111" disabled />);
        expect(wrapper).toMatchSnapshot();
        expect(wrapper.find('.one-input-clear-icon').length).toBe(0);
    });
});
