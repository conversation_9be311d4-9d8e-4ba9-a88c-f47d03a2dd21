/**
 * @file 导航的单测
 * <AUTHOR>
 * @date 2020-08-25
 */
import {mount, render} from 'enzyme';
import React, {PureComponent} from 'react';
import Nav from '../../../../src/components/nav/index';
import mountTest from '../../../shared/mountTest';

const datasource = [
    {
        label: '导航1',
        key: '1'
    },
    {
        label: '导航2',
        key: '2'
    },
    {
        label: '导航3',
        key: '3'
    },
    {
        label: '导航4',
        key: '4'
    },
    {
        label: '导航5',
        key: '5',
        disabled: true
    },
    {
        label: '导航6',
        key: '6'
    },
    {
        label: '导航7',
        key: '7'
    },
    {
        label: '导航8',
        key: '8'
    },
    {
        label: '导航9',
        key: '9',
        overlay: {
            overlayElement: <div>啦啦啦</div>,
            hiddenPlacement: 'left',
            overlayProps: {},
            className: ''
        }
    }
];

describe('Nav', () => {
    mountTest(Nav, {defaultValue: '1'});
    it('should be rendered correctly', () => {
        class Normal extends PureComponent {
            state = {
                width: 100,
                value: '2'
            };

            componentDidMount() {
                this.setState({
                    value: '3'
                });
            }

            render() {
                return (
                    <div style={{
                        width: `${this.state.width}px`
                    }}
                    >
                        <Nav value={this.state.value} dataSource={datasource} />
                    </div>
                );
            }
        }
        const wrapper = mount(<Normal />);
        expect(render(wrapper)).toMatchSnapshot();
    });
    it('should be rendered correctly2', () => {
        const onChange = jest.fn();
        const wrapper = mount(<Nav defaultValue="1" dataSource={datasource} onChange={onChange} />);
        expect(render(wrapper)).toMatchSnapshot();
        wrapper.find('.one-nav-item').at(2).simulate('click');
        expect(onChange).toHaveBeenCalledWith({
            item: {
                index: 2,
                key: '3',
                label: '导航3',
                prefixCls: 'one-nav'
            },
            target: {
                value: '3'
            }
        });
    });
    it('should be rendered correctly 3', () => {
        const onVisibleChange = jest.fn();
        const wrapper = mount(
            <div style={{width: '160px'}}>
                <Nav defaultValue="1" dataSource={datasource} onVisibleChange={onVisibleChange} />
            </div>
        );
        wrapper.find('.one-nav-overflow-indicator').at(0).simulate('click');
        wrapper.find('.one-nav-item-disabled').at(0).simulate('click');
    });
});