/**
 * @file IconSvg.spec.js
 * <AUTHOR> <EMAIL>
 * @date 2020-09-03
 */
import {mount, render} from 'enzyme';
import React from 'react';
import IconSvg from '../../../../src/components/iconSvg/index';

describe('IconSvg Component', () => {

    test('should create a IconSvg component with `arrow-down` type', () => {
        const wrapper = mount(<IconSvg type="arrow-down" />);
        expect(wrapper.props().type).toBe('arrow-down');
        expect(wrapper.render()).toMatchSnapshot();
    });

    test('should create null component with error type', () => {
        const wrapper = mount(<IconSvg type="test" />);
        expect(wrapper.render()).toMatchSnapshot();
    });

    test('should warning without type', () => {
        const errorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
        render(<IconSvg />);
        expect(errorSpy).toHaveBeenLastCalledWith(
            // eslint-disable-next-line max-len
            `Warning: Failed prop type: The prop \`type\` is marked as required in \`IconSvg\`, but its value is \`undefined\`.
    in IconSvg`
        );
    });

});
