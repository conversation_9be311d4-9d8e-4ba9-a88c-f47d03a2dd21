/**
 * @file NumberInput.spec.js
 * <AUTHOR> hua<PERSON><EMAIL>
 * @date 2020-09-03
 */

import React from 'react';
import {describe, expect, test, jest} from '@jest/globals';
import {mount} from 'enzyme';
import NumberInput, {NumberInputProps} from '../../../../src/components/numberInput';
import mountTest from '../../../shared/mountTest';
import focusTest from '../../../shared/focusTest';

describe('NumberInput Component', () => {
    mountTest(NumberInput);
    focusTest(NumberInput);

    const NumberInputProps: NumberInputProps = {
        tailLabel: '元',
        max: 5,
        min: 1,
        step: 1,
        placeholder: '请输入...',
        defaultValue: 5,
        type: 'int'
    };

    test('should support base props', () => {
        const wrapper = mount(<NumberInput {...NumberInputProps} />);
        expect(wrapper).toMatchSnapshot();
    });

    test('should not show showTips when set false', () => {
        const wrapper = mount(<NumberInput {...NumberInputProps} showTip={false} />);
        expect(wrapper.find('.one-number-input-tip').length).toBe(0);
    });

    test('should not show tailLabel when set false', () => {
        const wrapper = mount(<NumberInput {...NumberInputProps} tailLabel="" />);
        expect(wrapper.find('.one-number-input-tail-label').length).toBe(0);
    });

    test('should handle add and minus correctly', () => {
        class Demo extends React.Component {
            state = {
                value: 1.1
            };

            onChange = e => {
                this.setState({
                    value: e.target.value
                });
            };

            render() {
                const NumberInputProps = {
                    max: 9.99,
                    min: 0.01,
                    step: 0.01,
                    value: this.state.value,
                    onChange: this.onChange
                };
                return <NumberInput {...NumberInputProps} />;
            }
        }

        const wrapper = mount(<Demo />);
        expect(wrapper).toMatchSnapshot();
        wrapper.find('.one-number-input-spin-buttons-container > [data-ui-cmd="add"]').simulate('click');
        expect(wrapper.find('input').prop('value')).toBe('1.11');
        wrapper.find('.one-number-input-spin-buttons-container > [data-ui-cmd="sub"]').simulate('click');
        expect(wrapper.find('input').prop('value')).toBe('1.1');
    });

    test('should handle add and minus correctly when mode is strong', () => {
        // eslint-disable-next-line react/no-multi-comp
        class Demo extends React.Component {
            state = {
                value: 1.1
            };

            onChange = e => {
                this.setState({
                    value: e.target.value
                });
            };

            render() {
                const NumberInputProps = {
                    max: 9.99,
                    min: 0.01,
                    step: 0.01,
                    value: this.state.value,
                    model: 'strong',
                    onChange: this.onChange
                };
                return <NumberInput {...NumberInputProps} />;
            }
        }

        const wrapper = mount(<Demo />);
        expect(wrapper).toMatchSnapshot();
        wrapper.find('.one-number-input-main-addon-before').at(0).simulate('click');
        expect(wrapper.find('input').prop('value')).toBe('1.09');
        wrapper.find('.one-number-input-main-addon-after').at(0).simulate('click');
        expect(wrapper.find('input').prop('value')).toBe('1.1');
    });

    test('should handle border value correctly', () => {
        const onChange = jest.fn();
        const wrapper = mount(<NumberInput {...NumberInputProps} onChange={onChange} />);
        wrapper.find('.one-number-input-spin-buttons-container > [data-ui-cmd="add"]').simulate('click');
        expect(wrapper.find('.one-number-input-icon-disabled').length).toBe(6);
        // expect(onChange).not.toHaveBeenCalled(); // 这里 disable 本不应该触发 click 先使用上面的
        wrapper.find('.one-number-input-spin-buttons-container > [data-ui-cmd="sub"]').simulate('click');
        expect(onChange).toHaveBeenCalled();
    });

    test('should not handle click when is disabled', () => {
        const onChange = jest.fn();
        const wrapper = mount(<NumberInput {...NumberInputProps} disabled onChange={onChange} />);
        wrapper.find('.one-number-input-spin-buttons-container > [data-ui-cmd="add"]').simulate('click');
        expect(onChange).not.toHaveBeenCalled();
        wrapper.find('.one-number-input-spin-buttons-container > [data-ui-cmd="sub"]').simulate('click');
        expect(onChange).not.toHaveBeenCalled();
        wrapper.find('input').simulate('change');
        expect(onChange).not.toHaveBeenCalled();
    });

    test('should not input float when type is int', () => {
        const onChange = jest.fn();
        const wrapper = mount(<NumberInput {...NumberInputProps} onChange={onChange} />);
        wrapper.find('input').simulate('change', {target: {value: 4.5}});
        expect(wrapper.find('input').prop('value')).toBe('4');
        wrapper.find('input').simulate('change', {target: {value: 5.5}});
        expect(wrapper.find('input').prop('value')).toBe('5');
    });

    test('`onChange` value', () => {
        const props = {
            onChange: jest.fn(),
            max: 10
        };
        const wrapper = mount(<NumberInput {...props} />);

        const expectOnChangeValue = (inputValue, onChangeValue = inputValue) => {
            wrapper.find('input').simulate('change', {
                target: {
                    value: inputValue
                }
            });
            expect(props.onChange).toBeCalledWith(expect.objectContaining({
                target: {
                    value: onChangeValue
                }
            }));
        };

        expectOnChangeValue('', '');
        expectOnChangeValue('1', '1');
        expectOnChangeValue('1.', '1');
        expectOnChangeValue('1.0', '1');
        expectOnChangeValue('1.01', '1.01');
        expectOnChangeValue('1.010', '1.01');
        expect(props.onChange).toBeCalledTimes(3);
        expectOnChangeValue('10.010', '1.01');
        wrapper.find('input').simulate('blur');
        expect(props.onChange).toBeCalledWith(expect.objectContaining({target: {value: '1.01'}}));
        expect(props.onChange).toBeCalledTimes(4);
    });

    test('`onChange` value is a valid number or empty string', () => {
        const props = {
            onChange: jest.fn(),
            max: 10,
            valueAsNumber: true
        };
        const wrapper = mount(<NumberInput {...props} />);

        const expectOnChangeValue = (inputValue, onChangeValue = inputValue) => {
            wrapper.find('input').simulate('change', {
                target: {
                    value: inputValue
                }
            });
            expect(props.onChange).toBeCalledWith(expect.objectContaining({
                target: {
                    value: onChangeValue
                }
            }));
        };

        expectOnChangeValue('', '');
        expectOnChangeValue('1', 1);
        expectOnChangeValue('1.', 1);
        expectOnChangeValue('1.0', 1);
        expectOnChangeValue('1.01', 1.01);
        expectOnChangeValue('1.010', 1.01);
        expect(props.onChange).toBeCalledTimes(3);
        expectOnChangeValue('10.010', 1.01);
        wrapper.find('input').simulate('blur');
        expect(props.onChange).toBeCalledWith(expect.objectContaining({target: {value: 1.01}}));
        expect(props.onChange).toBeCalledTimes(4);
    });
});
