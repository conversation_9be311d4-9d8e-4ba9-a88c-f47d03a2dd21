/**
 * @file cascader pane的单测
 * <AUTHOR>
 * @date 2020-08-18
 */
import {mount} from 'enzyme';
import React from 'react';
import CascaderPane from '../../../../src/components/cascaderPane/index';
import CascaderPaneInput from '../../../../src/components/cascaderPane/input';
import mountTest from '../../../shared/mountTest';

const options = [
    {
        value: 'beijing',
        label: '北京',
        title: '北京',
        children: [
            {
                value: 'haidian',
                label: '海淀区',
                children: [
                    {
                        value: 'houchangcun',
                        label: '后厂村'
                    }
                ]
            }
        ]
    },
    {
        value: 'hunan',
        label: '湖南省',
        disabled: true,
        children: [
            {
                value: 'changsha',
                label: '长沙市',
                children: [
                    {
                        value: 'yuelushan',
                        label: '岳麓山'
                    },
                    {
                        value: 'juzizhou',
                        label: '橘子洲'
                    },
                    {
                        value: 'hunanweishi',
                        label: '湖南卫视'
                    }
                ]
            }
        ]
    },
    {
        value: 'beijing2',
        label: '北京2',
        loading: true
    }
];

describe('Cascader Pane Component', () => {
    mountTest(CascaderPane);
    it('support controlled mode', () => {
        const wrapper = mount(<CascaderPane options={options} />);
        wrapper.setProps({
            value: ['zhejiang', 'hangzhou', 'xihu']
        });
        expect(wrapper.render()).toMatchSnapshot();
    });
    it('can be selected', () => {
        const onSelect = jest.fn();
        const wrapper = mount(<CascaderPane options={options} onSelect={onSelect} />);
        wrapper
            .find('.one-cascader-pane-menu')
            .at(0)
            .find('.one-cascader-pane-menu-item')
            .at(0)
            .simulate('click');
        expect(onSelect).toHaveBeenCalledWith(
            {'children': [{'children':
                [{'label': '后厂村', 'value': 'houchangcun'}],
            'label': '海淀区', 'value': 'haidian'}],
            'label': '北京', 'title': '北京', 'value': 'beijing'}, 0, ['beijing']
        );
        wrapper
            .find('.one-cascader-pane-menu')
            .at(1)
            .find('.one-cascader-pane-menu-item')
            .at(0)
            .simulate('click');
        expect(onSelect).toHaveBeenCalledWith(
            {'children':
            [{'label': '后厂村', 'value': 'houchangcun'}],
            'label': '海淀区', 'value': 'haidian'}, 1, ['beijing', 'haidian']
        );
        wrapper
            .find('.one-cascader-pane-menu')
            .at(2)
            .find('.one-cascader-pane-menu-item')
            .at(0)
            .simulate('click');
        expect(onSelect).toHaveBeenCalledWith(
            {'label': '后厂村', 'value': 'houchangcun'},
            2, ['beijing', 'haidian', 'houchangcun']
        );
    });
    it('render ok in checkbox mode', () => {
        const wrapper = mount(<CascaderPane options={options} showCheckbox />);
        expect(wrapper.render()).toMatchSnapshot();
    });
    it('can checked in checkbox mode', () => {
        const onCheckboxChange = jest.fn();
        const wrapper = mount(
            <CascaderPane
                options={options}
                showCheckbox
                onCheckboxChange={onCheckboxChange}
            />);
        wrapper
            .find('.one-cascader-pane-menu')
            .at(0)
            .find('.one-cascader-pane-menu-item')
            .at(0)
            .find('.one-checkbox-input')
            .at(0)
            .simulate('click');
        wrapper
            .find('.one-cascader-pane-menu')
            .at(0)
            .find('.one-cascader-pane-menu-item')
            .at(0)
            .find('.one-checkbox-input')
            .at(0)
            .simulate('click');
        wrapper.setProps({
            expandTrigger: 'hover'
        });
        wrapper.update();
        wrapper
            .find('.one-cascader-pane-menu')
            .at(0)
            .find('.one-cascader-pane-menu-item')
            .at(0).simulate('mouseenter');
        wrapper
            .find('.one-cascader-pane-menu')
            .at(0)
            .find('.one-cascader-pane-menu-item')
            .at(0).simulate('mouseleave');
        expect(onCheckboxChange).toHaveBeenCalledWith([]);
        wrapper.setProps({
            checkedKeys: ['beijing', 'haidian', 'houchangcun']
        });
        wrapper.update();
        wrapper
            .find('.one-cascader-pane-menu')
            .at(0)
            .find('.one-cascader-pane-menu-item')
            .at(0)
            .find('.one-checkbox-input')
            .at(0)
            .simulate('change');
        expect(onCheckboxChange).toHaveBeenCalledWith([]);
    });
    it('can use in input mode', () => {
        const onClickSearchItem = jest.fn();
        const wrapper = mount(
            <CascaderPane
                options={options}
                showCheckbox
                showSearch
                defaultSearchValue="辣辣"
                onClickSearchItem={onClickSearchItem}
                searchProps={{}}
            />);
        wrapper.find('.one-cascader-pane').at(0).find('input').at(0).simulate('change', {target: {value: '北京'}});
        expect(wrapper.find('.one-cascader-pane').at(0).find('input').at(0).prop('value')).toBe('北京');
        wrapper.find('.one-cascader-pane-menus-search-box-container').at(0)
            .find('.one-cascader-pane-menu-item').at(0).simulate('click');
        wrapper.find('.one-cascader-pane').at(0).find('input').at(0).simulate('change', {target: {value: ''}});
        wrapper.find('.one-cascader-pane').at(0).find('input').at(0).simulate('change', {target: {value: '湖南'}});
        wrapper.find('.one-cascader-pane-menus-search-box-container').at(0)
            .find('.one-cascader-pane-menu-item').at(0).simulate('click');
        expect(wrapper.render()).toMatchSnapshot();
    });
    it('can use in input with uncontrolled value', () => {
        const wrapper = mount(
            <CascaderPaneInput inputType="inline" />);
        wrapper.find('input').at(0).simulate('change', {target: {value: '北京'}});
        expect(wrapper.render()).toMatchSnapshot();
    });
    it('render ok in first column group mode', () => {
        const wrapper = mount(
            <CascaderPane
                options={options}
                value={[]}
                showCheckbox
                showSearch
                paneWidth={400}
                firstColumnGroup={[
                    {
                        label: '华北',
                        value: 'huabei',
                        children: ['beijing']
                    },
                    {
                        label: '华南',
                        value: 'huanan',
                        children: ['hunan']
                    }
                ]}
            />);
        wrapper.setProps({value: ['beijing']});
        wrapper.update();
        expect(wrapper.render()).toMatchSnapshot();
    });

    describe('`options` prop', () => {
        it('should work with same value', () => {
            const options = [{
                value: '热门',
                label: '热门',
                children: [
                    {
                        value: '杭州',
                        label: '杭州'
                    }
                ]}, {
                value: '浙江',
                label: '浙江',
                children: [
                    {
                        value: '杭州',
                        label: '杭州'
                    }
                ]}
            ];
            const onSelect = jest.fn();
            const wrapper = mount(
                <CascaderPane options={options} onSelect={onSelect} />
            );
            wrapper.simulate('click');
            wrapper
                .find('.one-cascader-pane-menu')
                .at(0)
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(onSelect).toHaveBeenCalledWith(options[0], 0, ['热门']);
            wrapper
                .find('.one-cascader-pane-menu')
                .at(1)
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(onSelect).toHaveBeenCalledWith(options[0].children[0], 1, ['热门', '杭州']);
            wrapper.simulate('click');
            wrapper
                .find('.one-cascader-pane-menu')
                .at(0)
                .find('.one-cascader-pane-menu-item')
                .at(1)
                .simulate('click');
            expect(onSelect).toHaveBeenCalledWith(options[1], 0, ['浙江']);
            wrapper
                .find('.one-cascader-pane-menu')
                .at(1)
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(onSelect).toHaveBeenCalledWith(options[1].children[0], 1, ['浙江', '杭州']);
        });
        it('should work if it\'s empty', () => {
            const wrapper = mount(
                <CascaderPane options={null} />
            );
            wrapper.simulate('click');
            let text = wrapper
                .find('.one-cascader-pane-menus')
                .text();
            expect(text).toBe('暂无结果');
            wrapper.setProps({
                options: []
            });
            wrapper.simulate('click');
            text = wrapper
                .find('.one-cascader-pane-menus')
                .text();
            expect(text).toBe('暂无结果');
            wrapper.setProps({
                options: undefined
            });
            wrapper.simulate('click');
            text = wrapper
                .find('.one-cascader-pane-menus')
                .text();
            expect(text).toBe('暂无结果');
        });
    });
    describe('`value` prop', () => {
        it('should work if it\'s empty', () => {
            const wrapper = mount(
                <CascaderPane options={options} value={null} />
            );
            wrapper.simulate('click');
            wrapper
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(wrapper.find('.one-cascader-pane-menu').length).toBe(2);
            wrapper.setProps({
                value: []
            });
            wrapper.update();
            wrapper
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(wrapper.find('.one-cascader-pane-menu').length).toBe(2);
            wrapper.update();
            wrapper.setProps({
                value: undefined
            });
            wrapper
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(wrapper.find('.one-cascader-pane-menu').length).toBe(2);
        });
    });
    describe('`defaultValue` prop', () => {
        it('should work if it\'s empty', () => {
            let wrapper = mount(
                <CascaderPane options={options} defaultValue={null} />
            );
            wrapper.simulate('click');
            wrapper
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(wrapper.find('.one-cascader-pane-menu').length).toBe(2);
            wrapper = mount(
                <CascaderPane options={options} defaultValue={[]} />
            );
            wrapper.simulate('click');
            wrapper
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(wrapper.find('.one-cascader-pane-menu').length).toBe(2);
            wrapper = mount(
                <CascaderPane options={options} defaultValue={undefined} />
            );
            wrapper.simulate('click');
            wrapper
                .find('.one-cascader-pane-menu-item')
                .at(0)
                .simulate('click');
            expect(wrapper.find('.one-cascader-pane-menu').length).toBe(2);
        });
    });
});

