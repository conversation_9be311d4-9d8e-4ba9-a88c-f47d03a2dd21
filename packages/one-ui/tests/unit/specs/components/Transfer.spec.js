/**
 * @file Transfer UT test
 * <AUTHOR>
 * @date 2020-09-17
 */
import {mount} from 'enzyme';
import React from 'react';
import { createRef } from 'react';
import Transfer from '../../../../src/components/transfer/index';

const allDataMap = {
    1: {
        key: 1,
        title: '计划1'
    },
    2: {
        key: 2,
        title: '计划2',
        children: []
    },
    3: {
        key: 3,
        title: '计划3'
    },
    4: {
        key: 4,
        title: '计划4'
    },
    5: {
        key: 5,
        title: '计划5'
    },
    6: {
        key: 6,
        title: '计划6'
    },
    7: {
        key: 7,
        title: '计划7'
    },
    8: {
        key: 8,
        title: '计划8'
    },
    9: {
        key: 9,
        title: '计划9'
    },
    10: {
        key: 10,
        title: '计划10',
        children: [11, 12, 13, 14]
    },
    11: {
        key: 11,
        title: '计划11'
    },
    12: {
        key: 12,
        title: '计划12'
    },
    13: {
        key: 13,
        title: '计划13'
    },
    14: {
        key: 14,
        title: '计划14',
        children: [15, 16]
    },
    15: {
        key: 15,
        title: '计划15'
    },
    16: {
        key: 16,
        title: '计划16'
    }
};
describe('Transfer Component', () => {
    it('render ok', () => {
        const props = {
            allDataMap,
            candidateList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            treeName: '计划',
            showSearchBox: false
        };
        const wrapper = mount(<Transfer {...props} />);
        wrapper.setProps({
            selectedList: [1, 2, 3]
        });
        wrapper.update();
        expect(wrapper.find('.one-transfer-selected-tree').at(0).find('li').length).toBe(3);
        wrapper.setProps({
            expandedCandidateKeys: ['1']
        });
        wrapper.update();
        wrapper.setProps({
            expandedSelectedKeys: ['1']
        });
        wrapper.update();
        wrapper.setProps({
            allDataMap: {
                ...allDataMap,
                30: {
                    key: 30,
                    title: '计划30'
                }
            }
        });
        wrapper.find('.one-transfer-candidate-tree').find('li')
            .at(5).find('.one-tree-node-content-wrapper').simulate('click');
        wrapper.setProps({
            selectedList: [1, 2, 3, 4]
        });
        expect(wrapper.find('.one-transfer-selected-tree').at(0).find('li').length).toBe(4);
        wrapper.find('.one-transfer-add-all-button').at(0).simulate('click');
        expect(wrapper.find('.one-transfer-selected-tree').at(0).find('li').length).toBe(4);
        wrapper.find('.one-transfer-delete-all').at(0).simulate('click');
        expect(wrapper.find('.one-transfer-selected-tree').at(0).find('li').length).toBe(4);
        expect(wrapper).toMatchSnapshot();
    });
    it('render ok in search mode', () => {
        const props = {
            allDataMap,
            candidateList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            treeName: '计划'
        };
        const wrapper = mount(<Transfer {...props} />);
        wrapper.find('.one-transfer-candidate-tree').find('li')
            .at(5).find('.one-tree-node-content-wrapper').simulate('click');
        expect(wrapper.find('.one-transfer-selected-tree').at(0).find('li').length).toBe(1);
        wrapper.find('.one-transfer-candidate-tree').find('li')
            .at(5).find('.one-tree-node-content-wrapper').simulate('click');
        expect(wrapper.find('.one-transfer-selected-tree').at(0).find('li').length).toBe(0);
        wrapper.find('.one-transfer-add-all-button').at(0).simulate('click');
        expect(wrapper.find('.one-transfer-selected-tree').at(0).find('li').length).toBe(16);
        wrapper.find('.one-transfer-delete-all').at(0).simulate('click');
        expect(wrapper.find('.one-transfer-selected-tree').at(0).find('li').length).toBe(0);
        wrapper.find('.one-transfer-candidate-tree').find('.one-tree-switcher_close').at(0).simulate('click');
        expect(wrapper.find('.one-transfer-candidate-tree').find('li').length).toBe(14);
        wrapper.find('.one-transfer-add-all-button').at(0).simulate('click');
        wrapper.find('.one-transfer-selected-tree').find('.one-tree-switcher_open').at(0).simulate('click');
        expect(wrapper.find('.one-transfer-selected-tree').find('li').length).toBe(10);
        wrapper.find('.one-transfer-selected-tree').find('li').at(2)
            .find('.one-tree-treenode-container-title').simulate('click');
        wrapper.find('.one-transfer-selected-tree')
            .find('li').at(2).find('.one-transfer-delete-item').at(0).simulate('click');
    });
    it('render ok in virtual list mode', () => {
        const props = {
            allDataMap: {
                ...allDataMap,
                17: {
                    key: 17,
                    title: '计划17'
                },
                18: {
                    key: 18,
                    title: '计划18'
                },
                19: {
                    key: 19,
                    title: '计划19'
                },
                20: {
                    key: 20,
                    title: '计划20'
                }
            },
            candidateList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 17, 18, 19, 20],
            treeName: '计划',
            useVirtualScroll: true,
            selectedList: [1, 2, 3, 4]
        };
        const wrapper = mount(<Transfer {...props} />);
        wrapper.unmount();
        expect(wrapper).toMatchSnapshot();
    });
    it('delete children when single item disabled', () => {
        const props = {
            allDataMap,
            candidateList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            treeName: '计划'
        };
        const wrapper = mount(<Transfer {...props} />);
        wrapper.find('.one-transfer-candidate-tree').find('li')
            .at(0).find('.one-tree-node-content-wrapper').simulate('click');
        wrapper.setProps({
            allDataMap: {
                ...allDataMap,
                1: {
                    key: 1,
                    title: '计划1',
                    disabled: true
                }
            },
            selectedList: [1]
        });
        wrapper.update();
        wrapper.find('.one-transfer-selected-tree').find('li')
            .at(0).find('.one-transfer-delete-item').at(0).simulate('click');
    });
    it('delete children when parent item disabled', () => {
        const props = {
            allDataMap,
            candidateList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            treeName: '计划'
        };
        const wrapper = mount(<Transfer {...props} />);
        wrapper.find('.one-transfer-candidate-tree').find('li')
            .at(9).find('.one-tree-node-content-wrapper').simulate('click');
        wrapper.setProps({
            allDataMap: {
                ...allDataMap,
                12: {
                    key: 12,
                    title: '计划12',
                    disabled: true
                }
            }
        });
        wrapper.update();
        wrapper.find('.one-transfer-selected-tree').find('li')
            .at(0).find('.one-transfer-delete-item').at(0).simulate('click');
        wrapper.find('.one-transfer-search-box-bar').find('input').simulate('focus');
        wrapper.find('.one-transfer-search-box-bar').find('input').simulate('change', {target: {value: '123'}});
        wrapper.find('.one-transfer-search-box-bar').find('input').simulate('blur');
        wrapper.setProps({
            searchValue: '123'
        });
        wrapper.find('.one-transfer-search-box-bar').find('.one-search-box-search-icon').at(0).simulate('click');
        wrapper.update();
        wrapper.setProps({
            searchValue: ''
        });
        wrapper.find('.one-transfer-candidate-tree').find('li')
            .at(0).find('.one-tree-node-content-wrapper').simulate('click');
        wrapper.find('.one-transfer-delete-all').at(0).simulate('click');
    });
    it('should render ok with CustomCandidatePane object', () => {
        const props = {
            allDataMap,
            candidateList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            treeName: '计划',
            CustomCandidatePane: <div>xx</div>
        };
        const wrapper = mount(<Transfer {...props} />);
        expect(wrapper).toMatchSnapshot();
    });
    it('should render ok with CustomCandidatePane function', () => {
        const props = {
            allDataMap,
            candidateList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            treeName: '计划',
            CustomCandidatePane: () => <div>xx</div>
        };
        const wrapper = mount(<Transfer {...props} />);
        expect(wrapper).toMatchSnapshot();
    });
    it('should render ok with CustomSelectedPane object', () => {
        const props = {
            allDataMap,
            candidateList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            treeName: '计划',
            CustomSelectedPane: <div>xx</div>
        };
        const wrapper = mount(<Transfer {...props} />);
        expect(wrapper).toMatchSnapshot();
    });
    it('should render ok with CustomSelectedPane function', () => {
        const props = {
            allDataMap,
            candidateList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            treeName: '计划',
            CustomSelectedPane: () => <div>xx</div>
        };
        const wrapper = mount(<Transfer {...props} />);
        expect(wrapper).toMatchSnapshot();
    });
    it('should render ok with BeforeSelectedPane object', () => {
        const props = {
            allDataMap,
            candidateList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            treeName: '计划',
            BeforeSelectedPane: <div>xx</div>
        };
        const wrapper = mount(<Transfer {...props} />);
        expect(wrapper).toMatchSnapshot();
    });
    it('should render ok with BeforeSelectedPane function', () => {
        const props = {
            allDataMap,
            candidateList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            treeName: '计划',
            BeforeSelectedPane: () => <div>xx</div>
        };
        const wrapper = mount(<Transfer {...props} />);
        expect(wrapper).toMatchSnapshot();
    });
    it('should render ok with showCandidateFooter', () => {
        const props = {
            allDataMap,
            candidateList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            treeName: '计划',
            showCandidateFooter: true
        };
        const wrapper = mount(<Transfer {...props} />);
        expect(wrapper).toMatchSnapshot();
    });
    it('should render ok with BeforeCandidatePane object', () => {
        const props = {
            allDataMap,
            candidateList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            treeName: '计划',
            BeforeCandidatePane: <div>xx</div>
        };
        const wrapper = mount(<Transfer {...props} />);
        expect(wrapper).toMatchSnapshot();
    });
    it('should render ok with BeforeCandidatePane function', () => {
        const props = {
            allDataMap,
            candidateList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            treeName: '计划',
            BeforeCandidatePane: () => <div>xx</div>
        };
        const wrapper = mount(<Transfer {...props} />);
        expect(wrapper).toMatchSnapshot();
    });

    test('`dataSource` & `onChange` & `value`', () => {
        const onChange = jest.fn();
        const props = {
            dataSource: [
                {key: 'g1', title: 'g1', children: [{key: 1, title: '1'}]},
                {key: 2, title: '2'},
                {key: 3, title: '3'}
            ],
            value: [1],
            expandedCandidateKeys: [1],
            onChange
        };
        const ref = createRef();
        const wrapper = mount(<Transfer {...props} ref={ref} />);
        expect(ref.current.state.candidateList).toEqual(['g1', 2, 3]);
        expect(wrapper.find('.one-transfer-candidate-tree').find('li').length).toBe(4);
        expect(wrapper.find('.one-transfer-selected-tree').find('li').length).toBe(1);
        expect(wrapper.find('.one-transfer-pane-title-text-number').at(0).text()).toBe('(3)');
        expect(wrapper.find('.one-transfer-pane-title-text-number').at(1).text()).toBe('(1)');
        wrapper.find('input.one-checkbox-input').at(2).simulate('click');
        expect(onChange).toBeCalledWith([1, 2]);
    });

    test('`mergeChecked prop`', () => {
        const onChange = jest.fn();
        const props = {
            dataSource: [
                {
                    key: 1,
                    title: '1',
                    children: [
                        {
                            key: 'c1',
                            title: 'c1'
                        }, {
                            key: 'c2',
                            title: 'c2'
                        }, {
                            key: 'c3',
                            title: 'c3',
                            children: [
                                {
                                    key: 'cc1',
                                    title: 'cc1'
                                }, {
                                    key: 'cc2',
                                    title: 'cc2'
                                }, {
                                    key: 'cc3',
                                    title: 'cc3'
                                }
                            ]
                        }
                    ]
                },
                {key: 2, title: '2'},
                {key: 3, title: '3'}
            ],
            value: [],
            expandedCandidateKeys: [1],
            onChange,
            mergeChecked: 'keep-all'
        };
        const ref = createRef();
        const wrapper = mount(<Transfer {...props} ref={ref} />);
        wrapper.find('input.one-checkbox-input').at(3).simulate('click');
        expect(onChange).toBeCalledWith(['c3', 'cc1', 'cc2', 'cc3']);
        expect(wrapper.find('button.one-transfer-add-all-button').length).toBe(1);
        wrapper.find('button.one-transfer-add-all-button').at(0).simulate('click');
        expect(onChange).toBeCalledWith(['c3', 'cc1', 'cc2', 'cc3', 1, 'c1', 'c2', 2, 3]);

        wrapper.setProps({
            mergeChecked: 'downwards',
            value: []
        });
        wrapper.find('input.one-checkbox-input').at(3).simulate('click');
        expect(onChange).toBeCalledWith(['cc1', 'cc2', 'cc3']);
        wrapper.find('button.one-transfer-add-all-button').at(0).simulate('click');
        expect(onChange).toBeCalledWith(['cc1', 'cc2', 'cc3', 'c1', 'c2', 2, 3]);

        wrapper.setProps({
            mergeChecked: 'upwards',
            value: []
        });
        wrapper.find('input.one-checkbox-input').at(3).simulate('click');
        expect(onChange).toBeCalledWith(['c3']);
        wrapper.find('button.one-transfer-add-all-button').at(0).simulate('click');
        expect(onChange).toBeCalledWith([1, 2, 3]);
    });
});