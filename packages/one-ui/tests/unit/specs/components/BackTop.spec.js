/**
 * @file BackTop的单元测试
 * <AUTHOR>
 * @date 2020-08-28
 */
import {mount} from 'enzyme';
import React from 'react';
import {sleep} from '../../../utils';
import BackTop from '../../../../src/components/backTop/index';
import RawBackTop from '../../../../src/components/backTop/backTop';
import mountTest from '../../../shared/mountTest';

describe('Badge Component', () => {
    mountTest(BackTop);
    it('should scroll to top after click it', async () => {
        const wrapper = mount(<RawBackTop visibilityHeight={-1} />);
        const scrollToSpy = jest.spyOn(window, 'scrollTo').mockImplementation((x, y) => {
            window.scrollY = y;
            window.pageYOffset = y;
            document.documentElement.scrollTop = y;
        });
        window.scrollTo(0, 1000);
        expect(document.documentElement.scrollTop).toBe(1000);
        wrapper.find('.one-back-top').at(1).simulate('click');
        await sleep(500);
        expect(document.documentElement.scrollTop).toBe(0);
        scrollToSpy.mockRestore();
    });
    it('support onClick', async () => {
        const onClick = jest.fn();
        const wrapper = mount(<BackTop onClick={onClick} visibilityHeight={-1} />);
        const scrollToSpy = jest.spyOn(window, 'scrollTo').mockImplementation((x, y) => {
            window.scrollY = y;
            window.pageYOffset = y;
        });
        document.dispatchEvent(new Event('scroll'));
        window.scrollTo(0, 400);
        wrapper.find('.one-back-top').at(1).simulate('click');
        expect(onClick).toHaveBeenCalled();
        scrollToSpy.mockRestore();
    });
    it('invalid target', async () => {
        const onClick = jest.fn();
        const wrapper = mount(
            <BackTop onClick={onClick} visible target={() => ({documentElement: {}})} />,
        );
        wrapper.find('.one-back-top').at(1).simulate('click');
        expect(onClick).toHaveBeenCalled();
    });
    it('valid dom', async () => {
        const wrapper = mount(
            <BackTop className="my-back-top" visibilityHeight={100} target={() => document} />
        );
        expect(wrapper).toMatchSnapshot();
    });
});

