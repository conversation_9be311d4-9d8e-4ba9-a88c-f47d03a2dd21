/**
 * @file Embedded 组件单元测试
 * <AUTHOR>
 * @date 2020-09-03
 */
import React from 'react';
import {mount} from 'enzyme';
import mountTest from '../../../../tests/shared/mountTest';
import Embedded from '../../../../src/components/embedded/index';
import Button from '../../../../src/components/button/index';

describe('Embedded Components', () => {
    mountTest(Embedded);

    test('should be rendered correctly', async () => {
        const logSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
        const wrapper = mount(
            <Embedded
                title="标题位置"
                hideDefaultFooter={false}
                onClose={() => {
                    console.log('close layer');
                }}
            >
                <div style={{height: 150, textAglin: 'center', background: '#eee'}}>自定义内容区域</div>
            </Embedded>
        );
        wrapper.find('.one-embedded-close').simulate('click');
        expect(logSpy).toHaveBeenLastCalledWith('close layer');
        expect(wrapper.render()).toMatchSnapshot();
    });

    test('should be rendered correctly with small size', async () => {
        const wrapper = mount(
            <Embedded
                title="标题位置"
                hideDefaultFooter={false}
                size="small"
            >
                <div style={{height: 150, textAglin: 'center', background: '#eee'}}>自定义内容区域</div>
            </Embedded>
        );
        expect(wrapper.find('.one-embedded').hasClass('one-embedded-small')).toBe(true);
        expect(wrapper.render()).toMatchSnapshot();
    });
    test('should be rendered correctly with horizontal type', async () => {
        const logSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
        const wrapper = mount(
            <Embedded.Horizon
                title="标题位置"
                hideDefaultFooter={false}
                size="small"
                onClose={() => {
                    console.log('close layer');
                }}
            >
                <div style={{height: 150, textAglin: 'center', background: '#eee'}}>自定义内容区域</div>
            </Embedded.Horizon>
        );
        wrapper.find('.one-embedded-horizon-close').simulate('click');
        expect(logSpy).toHaveBeenLastCalledWith('close layer');
        expect(wrapper.find('.one-embedded-horizon').length).toBe(0);
        expect(wrapper.render()).toMatchSnapshot();
    });
    test('should be rendered correctly with horizontal type and visible is true', async () => {
        const logSpy = jest.spyOn(console, 'log').mockImplementation(() => {});
        const wrapper = mount(
            <Embedded.Horizon
                title="标题位置"
                hideDefaultFooter={false}
                size="small"
                visible
                onClose={() => {
                    console.log('close layer');
                }}
            >
                <div style={{height: 150, textAglin: 'center', background: '#eee'}}>自定义内容区域</div>
            </Embedded.Horizon>
        );
        wrapper.find('.one-embedded-horizon-close').simulate('click');
        expect(logSpy).toHaveBeenLastCalledWith('close layer');
        expect(wrapper.find('.one-embedded-horizon').length).toBe(1);
        expect(wrapper.render()).toMatchSnapshot();
    });
    test('should be rendered correctly with horizontal type and visible is true', async () => {
        const wrapper = mount(
            <Embedded.Horizon
                title="标题位置"
                hideDefaultFooter={false}
                size="small"
                visible
                closable={false}
            >
                <div style={{height: 150, textAglin: 'center', background: '#eee'}}>自定义内容区域</div>
            </Embedded.Horizon>
        );
        expect(wrapper.find('.one-embedded-horizon-close').length).toBe(0);
        expect(wrapper.render()).toMatchSnapshot();
    });
    test('should be rendered correctly without footer', async () => {
        const wrapper = mount(
            <Embedded
                title="标题位置"
                hideDefaultFooter={false}
                size="small"
                footer={[<Button key="button" className="demo-button-1">新建</Button>]}
                closable={false}
            >
                <div style={{height: 150, textAglin: 'center', background: '#eee'}}>自定义内容区域</div>
            </Embedded>
        );
        expect(wrapper.find('.one-embedded').hasClass('one-embedded-small')).toBe(true);
        expect(wrapper.find('.demo-button-1').length).toBe(3);
        expect(wrapper.find('.one-embedded-close').length).toBe(0);
        expect(wrapper.render()).toMatchSnapshot();
    });
    test('should be rendered correctly with visible', async () => {
        const wrapper = mount(
            <Embedded
                title="标题位置"
                hideDefaultFooter={false}
                size="small"
                visible
                onClose={() => {}}
                okProps={{
                    style: {background: '#eee'}
                }}
                cancelProps={{
                    style: {background: '#eee'}
                }}
            >
                <div style={{height: 150, textAglin: 'center', background: '#eee'}}>自定义内容区域</div>
            </Embedded>
        );
        expect(wrapper.find('.one-embedded').hasClass('one-embedded-small')).toBe(true);
        wrapper.find('.one-embedded-close').simulate('click');
        expect(wrapper.find('.one-embedded').hasClass('one-embedded-small')).toBe(true);
        expect(wrapper.render()).toMatchSnapshot();
    });
    test('should be rendered correctly with visible is false', async () => {
        const wrapper = mount(
            <Embedded
                title="标题位置"
                hideDefaultFooter={false}
                size="small"
                visible={false}
                onClose={() => {}}
            >
                <div style={{height: 150, textAglin: 'center', background: '#eee'}}>自定义内容区域</div>
            </Embedded>
        );
        expect(wrapper.find('.one-embedded').length).toBe(0);
        expect(wrapper.render()).toMatchSnapshot();
    });
});
