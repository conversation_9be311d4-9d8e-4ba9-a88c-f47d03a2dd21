/**
 * @file Badge.spec.js
 * <AUTHOR> <EMAIL>
 * @date 2020-09-03
 */
import {mount, render} from 'enzyme';
import React from 'react';
import Badge from '../../../../src/components/badge/index';
import mountTest from '../../../shared/mountTest';

describe('Badge Component', () => {
    mountTest(Badge);

    test('badge should support int number', () => {
        let wrapper = render(<Badge count={5} />);
        expect(wrapper.find('.one-badge-count').text()).toBe('5');
        expect(wrapper).toMatchSnapshot();
    });

    test('badge should support float number', () => {
        let wrapper = render(<Badge count={3.5} />);
        expect(wrapper.find('.one-badge-count').text()).toBe('3.5');
        expect(wrapper).toMatchSnapshot();
    });

    it('badge dot not scaling count > 99', () => {
        const badge = mount(<Badge count={100} />);
        expect(badge.find('.one-badge-count').text()).toBe('99+');
    });

    test('badge should support custom badge overflowCount', () => {
        let wrapper = render(<Badge count={100} overflowCount={999} />);
        expect(wrapper.find('.one-badge-count').text()).toBe('100');
    });

    test('badge dot not showing count == 0', () => {
        const badge = mount(<Badge count={0} />);
        expect(badge.find('.one-badge-dot').length).toBe(0);
    });

    test('badge should support custom badge content', () => {
        let wrapper = render(<Badge textContent="hot" />);
        expect(wrapper).toMatchSnapshot();
    });

    test('badge should hide when visible is false', () => {
        let wrapper = render(<Badge visible={false} />);
        expect(wrapper).toMatchSnapshot();
    });

    it('should have an overridden textContent attribute', () => {
        const badge = mount(<Badge count={10} textContent="hot" />);
        expect(
            badge
                .find('.one-badge-count')
                .text()
        ).toEqual('hot');
    });

    test('should render when count is changed', () => {
        const wrapper = mount(<Badge count={9} />);
        wrapper.setProps({count: 10});
        jest.useFakeTimers();
        expect(wrapper).toMatchSnapshot();
        wrapper.setProps({count: 11});
        jest.useFakeTimers();
        expect(wrapper).toMatchSnapshot();
        wrapper.setProps({count: 11});
        jest.useFakeTimers();
        expect(wrapper).toMatchSnapshot();
        wrapper.setProps({count: 10});
        jest.useFakeTimers();
        expect(wrapper).toMatchSnapshot();
        jest.useFakeTimers();
        wrapper.setProps({count: 9});
        expect(wrapper).toMatchSnapshot();
    });

});