/**
 * @file Table组件的UT
 * <AUTHOR>
 * @date 2020-09-16
 */
import React, {createRef} from 'react';
import {mount} from 'enzyme';
import Table from '../../../../src/components/table/index';
import {sleep} from '../../../utils';

const columns = [{
    title: 'Name',
    dataIndex: 'name',
    key: 'name',
    render: text => text
}, {
    title: 'Age',
    dataIndex: 'age',
    key: 'age'
}, {
    title: 'Address',
    dataIndex: 'address',
    key: 'address'
}, {
    title: 'Tags',
    key: 'tags',
    dataIndex: 'tags',
    render: tags => (
        <span>
            {tags.map(tag => {
                let color = tag.length > 5 ? 'geekblue' : 'green';
                if (tag === 'loser') {
                    color = 'volcano';
                }
                return <div color={color} key={tag}>{tag.toUpperCase()}</div>;
            })}
        </span>
    )
}, {
    title: 'Action',
    key: 'action',
    render: (text, record) => (
        <span>
            <a href="">
                Invite
                {record.name}
            </a>
            <a href="">Delete</a>
        </span>
    )
}];

const data = [];
for (let i = 0; i < 10; i++) {
    data.push({
        key: `${i}`,
        name: `John Brown ${i}`,
        age: 100 - i,
        address: `New York No. ${i + 1} Lake Park`,
        tags: ['nice', 'developer']
    });
}
describe('Table Components', () => {

    it('Table should render correctly', () => {
        const ref = createRef();
        const wrapper = mount(
            <Table
                ref={ref}
                loading
                columns={columns}
                dataSource={data}
                pagination={false}
                locale={{
                    emptyText: '暂无数据'
                }}
            />);
        ref.current.updateColumnWidths(columns);
        expect(wrapper.render()).toMatchSnapshot();
    });

    it('Table should render correctly without columns and without dataSource', () => {
        const ref = createRef();
        const wrapper = mount(<Table ref={ref} loading columns={[]} dataSource={[]} pagination={false} />);
        ref.current.updateColumnWidths(columns);
        expect(wrapper.render()).toMatchSnapshot();
    });

    it('should have pagination in table', () => {
        const wrapper = mount(<Table columns={columns} dataSource={data} pagination={false} />);
        expect(wrapper.find('.one-table-pagination').exists()).toBe(false);
        wrapper.setProps({
            pagination: true
        });
        expect(wrapper.find('.one-table-pagination').exists()).toBe(true);
        // should have rowSelection
        wrapper.setProps({
            rowSelection: {
                selectedRowKeys: ['1'],
                onChange: () => {},
                getCheckboxProps: record => {
                    return {
                        disabled: false
                    };
                }
            }
        });
        expect(wrapper.find('.one-table-selection-column').exists()).toBe(true);
        wrapper.find('.one-table-selection-column').at(1)
            .find('.one-checkbox-input').simulate('change', {target: {checked: true}});
        wrapper.setProps({
            rowSelection: {
                selectedRowKeys: ['1'],
                onChange: () => {},
                getCheckboxProps: record => {
                    return {
                        disabled: +record.key % 3 === 0
                    };
                }
            }
        });
        wrapper.update();
        expect(wrapper.find('.one-table-selection-column').exists()).toBe(true);
        const newData = [...data];
        newData.push({
            key: '11',
            name: 'John Brown 11',
            age: 78,
            address: 'New York No. Lake Park',
            tags: ['nice', 'developer']
        });
        wrapper.setProps({
            dataSource: newData
        });
        wrapper.update();
        expect(wrapper.find('.one-table-tbody').find('tr').length).toBe(11);
        wrapper.setProps({
            rowSelection: {
                selectedRowKeys: ['1'],
                onChange: () => {},
                type: 'radio'
            }
        });
        wrapper.update();
        expect(wrapper.find('.one-table-selection-column').exists()).toBe(true);
    });
    it('can change pagination in table', async () => {
        const newData = [...data];
        for (let i = 10; i < 100; i++) {
            newData.push({
                key: `${i}`,
                name: `John Brown ${Math.floor(Math.random() * 100)} ${i}`,
                age: 100 - i,
                address: `123 New York No. ${i + 1} Lake Park`,
                tags: ['nice', 'developer']
            });
        }
        const ref= createRef();
        const wrapper = mount(<Table
            ref={ref}
            columns={columns}
            dataSource={newData}
            pagination={{
                // pageNo: 1,
                pageSize: 20,
                pageSizeOptions: ['2', '5', '10'],
                total: 100
            }}
        />);
        expect(wrapper.find('.one-table-pagination')
            .find('.one-pagination-pager-item-active').at(0).props()['data-key']).toBe(1);
        wrapper.find('.one-table-pagination').find('.one-pagination-pager-item').at(3).simulate('click');
        ref.current.handlePageChange({
            target: 2
        });
        ref.current.handlePageChange({
            target: 0
        });
        wrapper.setProps({
            pagination: {
                pageNo: 2,
                pageSize: 20,
                pageSizeOptions: ['2', '5', '10'],
                total: 100
            }
        });
        wrapper.update();
        await sleep(1000);
        expect(wrapper.find('.one-table-pagination')
            .find('.one-pagination-pager-item-active').at(0).props()['data-key']).toBe(2);
    });
    it('can sort', async () => {
        const columns = [{
            title: '受控排序',
            dataIndex: 'name',
            sorter: true
        }, {
            title: '受控排序',
            dataIndex: 'age',
            sorter: true
        }, {
            title: 'Address',
            dataIndex: 'address',
            customOperate: <div>辣辣</div>
        }];
        const data = [];
        for (let i = 0; i < 30; i++) {
            data.push({
                key: `${i}`,
                name: `Jim Red ${Math.floor(Math.random() * 1000)}`,
                age: i,
                address: 'London No. 2 Lake Park'
            });
        }
        const wrapper = mount(<Table
            columns={columns}
            dataSource={data}
            rowSelection={{
                selectedRowKeys: ['1'],
                onChange: () => {},
                type: 'radio'
            }}
        />);
        wrapper.find('.one-table-column-operate-sort-item').at(0).simulate('click');
        await sleep(1000);
        wrapper.find('.one-table-column-operate-sort-item').at(0).simulate('click');
        await sleep(1000);
        wrapper.find('.one-table-column-operate-sort-item').at(0).simulate('click');
        await sleep(1000);
    });
});