/**
 * @file uploader UT test
 * <AUTHOR>
 * @date 2020-09-17
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2020-09-17 19:50:03
 */
import React from 'react';
import {mount} from 'enzyme';
import {act} from 'react-dom/test-utils';
import mountTest from '../../../../tests/shared/mountTest';
import Uploader from '../../../../src/components/uploader/index';

describe('Uploader Component', () => {
    mountTest(Uploader);

    it('render ok', () => {
        const validator = () => 'xxx';
        const wrapper = mount(<Uploader
            multiple
            fileList={[]}
            maxSize={5 * 1024 * 1024}
            maxParallelFileNumber={2}
            uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
            validator={validator}
        />);
        wrapper.find('input').simulate('change', {
            target: {
                files: [
                    {name: 'foo.png'},
                    {name: 'foo2.png'},
                    {name: 'foo3.png'}
                ]
            }
        });
        expect(wrapper).toMatchSnapshot();
    });

    test('renders correctly', () => {
        const wrapper = mount(
            <Uploader
                suffixCls="uploader"
            >
                <input />
            </Uploader>
        );
        expect(wrapper).toMatchSnapshot();
    });

    it('beforeUpload can be falsy', done => {
        const props = {
            action: 'http://upload.com',
            beforeUpload: () => {},
            onChange: ({file}) => {
                if (file.status !== 'uploading') {
                    done();
                }
            }
        };

        const wrapper = mount(
            <Uploader {...props}>
                <button type="button">upload</button>
            </Uploader>,
        );

        wrapper.find('input').simulate('change', {
            target: {
                files: [{file: 'foo.png'}]
            }
        });
    });

    it('should not stop upload when return value of beforeUpload is false', done => {
        const fileList = [
            {
                uid: 'bar',
                name: 'bar.png'
            }
        ];
        const mockFile = new File(['foo'], 'foo.png', {
            type: 'image/png'
        });
        const data = jest.fn();
        const props = {
            action: 'http://upload.com',
            fileList,
            beforeUpload: () => false,
            data,
            onChange: ({file, fileList: updatedFileList}) => {
                expect(file instanceof File).toBe(true);
                expect(updatedFileList.map(f => f.name)).toEqual(['bar.png', 'foo.png']);
                expect(data).not.toHaveBeenCalled();
                done();
            }
        };
        const ref = React.createRef();
        const wrapper = mount(
            <Uploader {...props} ref={ref}>
                <button type="button">upload</button>
            </Uploader>,
        );

        wrapper.find('input').simulate('change', {
            target: {
                files: [mockFile]
            }
        });
        expect(ref.current.state.insertImage).toBe(false);
    });

    it('return when targetItem is null', () => {
        const fileList = [{uid: 'file'}];
        const ref = React.createRef();
        mount(
            <Uploader ref={ref} type="drag" fileList={fileList}>
                <button type="button">upload</button>
            </Uploader>,
        );
        expect(ref.current.onSuccess('', {uid: 'fileItem'})).toBe(undefined);
        expect(ref.current.onProgress('', {uid: 'fileItem'})).toBe(undefined);
        expect(ref.current.onError('', '', {uid: 'fileItem'})).toBe(undefined);
    });

    it('should replace file when targetItem already exists', () => {
        const fileList = [{uid: 'file', name: 'file'}];
        const ref = React.createRef();
        mount(
            <Uploader ref={ref} defaultFileList={fileList}>
                <button type="button">upload</button>
            </Uploader>,
        );
        ref.current.onStart({
            uid: 'file',
            name: 'file1',
            progressStep: 0,
            status: 'uploading'
        });
        expect(ref.current._fileList.length).toBe(1);
        expect(ref.current._fileList[0]).toEqual({
            name: 'file',
            uid: 'file',
            progressStep: 0,
            status: 'uploading'
        });
    });

    it('should sync file list with control mode', () => {
        let callTimes = 0;
        const customRequest = jest.fn(async options => {
            options.onProgress({percent: 0});
            const url = Promise.resolve('https://baidu.com');
            options.onProgress({percent: 100});
            options.onSuccess({}, {...options.file, url});
        });
        const Demo = () => {
            const [fileList, setFileList] = React.useState([]);
            const onChange = e => {
                const newFileList = Array.isArray(e) ? e : e.fileList;
                setFileList(newFileList);
                const file = newFileList[0];
                callTimes += 1;
                switch (callTimes) {
                    case 1:
                    case 2:
                        expect(file).toEqual(expect.objectContaining({errorMessage: ['上传的不是指定文件类型，请重试']}));
                        break;
                    case 3:
                        expect(file).toEqual(expect.objectContaining({status: 'uploading', percent: 100}));
                        break;
                    case 4:
                        expect(file).toEqual(expect.objectContaining({status: 'done', percent: 100}));
                        break;
                    default:
                    // Do nothing
                }
            };
            return (
                <Uploader
                    customRequest={customRequest}
                    onChange={onChange}
                    fileList={fileList}
                >
                    <button type="button">Upload</button>
                </Uploader>
            );
        };
        const wrapper = mount(<Demo />);
        act(() => {
            wrapper.find('input').simulate('change', {
                target: {
                    files: [{file: 'foo.png'}]
                }
            });
        });
    });
    it('exceed maxParallelFileNumber', () => {
        const ref = React.createRef();
        const wrapper = mount(<Uploader
            ref={ref}
            multiple
            fileList={[]}
            maxSize={5 * 1024 * 1024}
            maxParallelFileNumber={2}
            uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
        />);
        wrapper.find('input').simulate('change', {
            target: {
                files: [
                    {name: 'foo.png'},
                    {name: 'foo2.png'},
                    {name: 'foo3.png'}
                ]
            }
        });
        expect(ref.current.state.fileList.length).toBe(0);
    });
    it('exceed maxFileLength', () => {
        const ref = React.createRef();
        const wrapper = mount(<Uploader
            ref={ref}
            multiple
            fileList={[{name: 'foo.png'},
                {name: 'foo2.png'},
                {name: 'foo3.png'}]}
            maxSize={5 * 1024 * 1024}
            maxFileLength={3}
            uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
        />);
        wrapper.find('input').simulate('change', {
            target: {
                files: [
                    {name: 'foo.png'},
                    {name: 'foo2.png'},
                    {name: 'foo3.png'}
                ]
            }
        });
        expect(ref.current.state.fileList.length).toBe(3);
    });
    it('onRemove is ok', () => {
        const onRemove = jest.fn();
        const wrapper = mount(<Uploader
            defaultFileList={[{
                status: 'waiting',
                name: '这是一个等待上传文件.png'
            }, {
                status: 'uploading',
                name: '这是一个上传中的文件.png',
                progressStep: 30
            }, {
                status: 'success',
                name: '这是一个上传成功的文件.png'
            }, {
                status: 'error',
                name: '这是一个上传失败的文件.png',
                errorMessage: ['上传失败的原因是叭叭叭']
            }]}
            maxSize={5 * 1024 * 1024}
            onRemove={onRemove}
            uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
        />);
        wrapper.find('.one-uploader-file-item').at(0).simulate('mouseenter');
        wrapper.find('.one-uploader-file-item-control').at(0).simulate('click');
        expect(onRemove).toHaveBeenCalled();
        wrapper.setProps({
            useInForm: true
        });
        wrapper.update();
        wrapper.find('.one-uploader-file-item').at(0).simulate('mouseenter');
        wrapper.find('.one-uploader-file-item-control').at(0).simulate('click');
        expect(onRemove).toHaveBeenCalled();
    });
    it('render list type is image ok', () => {
        const date = new Date(1647399059908);
        Date = jest.fn(() => date);
        Date.now = jest.fn(() => date.getTime());
        const wrapper = mount(<Uploader
            defaultFileList={[{
                status: 'waiting',
                name: '这是一个等待上传文件.png'
            }, {
                status: 'uploading',
                name: '这是一个上传中的文件.png',
                progressStep: 30
            }, {
                status: 'success',
                name: '这是一个上传成功的文件.png'
            }, {
                status: 'error',
                name: '这是一个上传失败的文件.png',
                errorMessage: ['上传失败的原因是叭叭叭']
            }]}
            maxSize={5 * 1024 * 1024}
            uploadResquestUrl="https://www.mocky.io/v2/5cc8019d300000980a055e76"
            listType="image"
        />);
        wrapper.find('.one-uploader-anchor-image-button').simulate('click');
        wrapper.find('input').simulate('change', {
            target: {
                files: [
                    {name: 'foo.png'}
                ]
            }
        });
        wrapper.find('.one-uploader-image-item-error').at(0)
            .find('.one-uploader-image-item-card').at(0).simulate('mouseenter');
        wrapper.find('.one-uploader-image-item-error').at(0)
            .find('.one-uploader-image-item-card-operation-icons').at(0).find('svg').at(0).simulate('click');
        wrapper.find('input').simulate('change', {
            target: {
                files: [
                    {name: 'foo.png'}
                ]
            }
        });
        expect(wrapper).toMatchSnapshot();
    });

    test('image type `pickerIcon`&`pickerText`', () => {
        const wrapper = mount(<Uploader
            listType="image"
            pickerIcon={<i className="picker-icon">icon</i>}
            pickerText={<b className="picker-text">text</b>}
        />);
        expect(wrapper.find('.picker-icon').length).toBe(1);
        expect(wrapper.find('.picker-text').length).toBe(1);
        expect(wrapper).toMatchSnapshot();
    });

    test('`onPickerClick`', () => {
        const onPickerClick = jest.fn();
        const wrapper = mount(<Uploader
            listType="image"
            onPickerClick={onPickerClick}
        />);
        wrapper.find('.one-uploader-anchor-image-button').simulate('click');
        expect(onPickerClick).toHaveBeenCalled();
    });

    test('image type `helperText`&`helperTextPosition`', () => {
        const bottom = mount(<Uploader
            listType="image"
            helperText={<i className="help">help</i>}
            helperTextPosition="bottom"
        />);
        expect(bottom.find('.help').length).toBe(1);
        expect(bottom).toMatchSnapshot();
        const right = mount(<Uploader
            listType="image"
            helperText={<i className="help">help</i>}
            helperTextPosition="right"
        />);
        expect(right.find('.help').length).toBe(1);
        expect(right).toMatchSnapshot();
    });

    test('image type item `bottom`', () => {
        const wrapper = mount(<Uploader
            listType="image"
            fileList={[{footer: <i className="footer">footer</i>}]}
        />);
        expect(wrapper.find('.footer').length).toBe(1);
        expect(wrapper).toMatchSnapshot();
    });

    test('image type `pickerPosition`', () => {
        const top = mount(<Uploader
            listType="image"
            pickerPosition="top"
            fileList={[{}]}
        />);
        expect(top).toMatchSnapshot();
        const before = mount(<Uploader
            listType="image"
            pickerPosition="before"
            fileList={[{}]}
        />);
        expect(before).toMatchSnapshot();
        const after = mount(<Uploader
            listType="image"
            pickerPosition="after"
            fileList={[{}]}
        />);
        expect(after).toMatchSnapshot();
        const none = mount(<Uploader
            listType="image"
            pickerPosition="none"
            fileList={[{}]}
        />);
        expect(none).toMatchSnapshot();
    });

    test('file type `controls`', () => {
        const file = mount(<Uploader
            listType="file"
            fileList={[{}]}
            controls={(file, actions) => [{key: 'control', icon: <i className="control"></i>}, ...actions]}
        />);
        expect(file.find('button.one-uploader-file-item-control').length).toBe(2);
        expect(file.find('.control').length).toBe(1);
    });

    test('image type `controls`', () => {
        const image = mount(<Uploader
            listType="image"
            fileList={[{}]}
            controls={(file, actions) => [{icon: <i className="control"></i>}, ...actions]}
        />);
        expect(image.find('div.one-uploader-image-item-card-operation-icons>*').length).toBe(4);
        expect(image.find('.control').length).toBe(1);
    });
});
