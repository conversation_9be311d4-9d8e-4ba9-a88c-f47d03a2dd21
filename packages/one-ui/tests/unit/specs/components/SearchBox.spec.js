/**
 * @file SearchBox.spec.js
 * <AUTHOR> hua<PERSON><PERSON><PERSON>@baidu.com
 * @date 2020-09-03
 */
import {mount} from 'enzyme';
import React from 'react';
import SearchBox from '../../../../src/components/searchBox/search';
import mountTest from '../../../shared/mountTest';

describe('SearchBox Component', () => {
    mountTest(SearchBox);
    test('should support base props', () => {
        const wrapper = mount(<SearchBox size="medium" width={300} placeholder="请输入..." />);
        expect(wrapper.props().size).toBe('medium');
        expect(wrapper.props().width).toBe(300);
        expect(wrapper.render()).toMatchSnapshot();
        const wrapper2 = mount(<SearchBox searchIconType="button" />);
        expect(wrapper2.render()).toMatchSnapshot();
    });

    test('should support normal search', () => {
        const onSearch = jest.fn();
        const onPressEnter = jest.fn();
        const onKeyDown = jest.fn();
        // eslint-disable-next-line max-len
        const wrapper = mount(<SearchBox onSearch={onSearch} onPressEnter={onPressEnter} onKeyDown={onKeyDown} />);
        expect(wrapper.find('.one-search-box-search-icon').at(1).simulate('click'));
        expect(onSearch).toHaveBeenCalled();
        wrapper.find('input').simulate('keydown', {keyCode: 13});
        expect(onKeyDown).toHaveBeenCalled();
        wrapper.find('input').simulate('keypress', {key: 'Enter'});
        expect(onPressEnter).toHaveBeenCalled();

    });

    test('should not support search when is disabled', () => {
        const onSearch = jest.fn();
        const wrapper = mount(<SearchBox disabled onSearch={onSearch} />);
        expect(wrapper.find('.one-search-box-search-icon').at(1).simulate('click'));
        expect(onSearch).not.toHaveBeenCalled();
    });

    test('should trigger event correctly', () => {
        const wrapper = mount(<SearchBox showCloseIcon defaultValue="trigger" />);
        wrapper
            .find('.one-search-box-icon')
            .at(0)
            .simulate('click');

        expect(
            wrapper
                .find('input')
                .at(0)
                .prop('value'),
        ).toBe('');
    });

    test('should normal clear when click clear icon', () => {
        const onClearClick = jest.fn();
        const wrapper = mount(<SearchBox showCloseIcon onClearClick={onClearClick} />);
        wrapper.find('input').simulate('change', {target: {value: 'hello'}});
        expect(wrapper.find('input').prop('value')).toEqual('hello');
        expect(wrapper).toMatchSnapshot();
        wrapper
            .find('.one-search-box-icon')
            .at(0)
            .simulate('click');
        expect(onClearClick).toHaveBeenCalled();
        expect(wrapper.find('input').prop('value')).toEqual('');
    });

});

describe('overlay support', () => {
    test('should support overlay event', () => {
        const handleMenuClick = jest.fn();
        const options = [
            {label: 'Apple', value: 'Apple'},
            {label: 'Banana', value: 'Banana'},
            {label: 'Orange', value: 'Orange'}
        ];
        const ref = React.createRef();
        const wrapper = mount(<SearchBox
            handleMenuClick={handleMenuClick}
            options={options}
            ref={ref}
        />);
        expect(wrapper.find('MenuItem').length).toBe(0);
        wrapper.find('input').simulate('focus');
        expect(wrapper.find('MenuItem').length).toBe(3 * 2);
        // expect(wrapper).toMatchSnapshot();
        wrapper.find('MenuItem').at(1).simulate('click');
        expect(handleMenuClick).toHaveBeenCalled();
    });

    test('should support custom overlay', () => {
        const ref = React.createRef();
        const wrapper = mount(<SearchBox ref={ref}
            overlay={(<div className="test" style={{padding: '20px'}}>自由下拉面板内容</div>)}
        />);
        wrapper.find('input').simulate('focus');
        // expect(wrapper).toMatchSnapshot();
        const dropdownWrapper = mount(
            wrapper
                .find('Trigger')
                .instance()
                .getComponent(),
        );
        expect(dropdownWrapper.find('.test').length).toBe(1);
    });

});

// 这里有定时器递归调用，所以未使用公共的
describe('focus and blur', () => {
    beforeAll(() => {
        jest.useFakeTimers();
    });

    let container;
    beforeEach(() => {
        container = document.createElement('div');
        document.body.appendChild(container);
    });

    afterAll(() => {
        jest.useRealTimers();
    });

    afterEach(() => {
        document.body.removeChild(container);
    });

    test('blur() and onBlur', () => {
        const handleBlur = jest.fn();
        const ref = React.createRef();
        // eslint-disable-next-line max-len
        const wrapper = mount(<SearchBox ref={ref} onBlur={handleBlur} />, {attachTo: container});
        ref.current.focus();
        jest.runOnlyPendingTimers();
        ref.current.blur();
        jest.runOnlyPendingTimers();
        expect(handleBlur).toHaveBeenCalled();
    });

    test('focus() and onFocus', () => {
        const handleFocus = jest.fn();
        const ref = React.createRef();
        // eslint-disable-next-line max-len
        const wrapper = mount(<SearchBox onFocus={handleFocus} ref={ref} />, {attachTo: container});
        ref.current.focus();
        jest.runOnlyPendingTimers();
        expect(handleFocus).toHaveBeenCalled();
    });
});