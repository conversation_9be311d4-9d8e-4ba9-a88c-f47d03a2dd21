module.exports = [
    'src/components/alert/index.tsx',
    'src/components/alert/page.tsx',
    'src/components/alert/alert.tsx',
    'src/components/anchor/anchor.tsx',
    'src/components/anchor/index.tsx',
    'src/components/anchor/link.tsx',
    'src/components/backTop/index.tsx',
    'src/components/backTop/backTop.tsx',
    'src/components/badge/badge.tsx',
    'src/components/badge/index.tsx',
    'src/components/breadcrumb/breadcrumb.tsx',
    'src/components/breadcrumb/breadcrumbItem.tsx',
    'src/components/breadcrumb/index.tsx',
    'src/components/button/index.tsx',
    'src/components/carousel/index.tsx',
    'src/components/cascader/index.tsx',
    'src/components/cascader/cascader.tsx',
    'src/components/cascaderPane/cascaderPane.tsx',
    'src/components/cascaderPane/index.tsx',
    'src/components/cascaderPane/input.tsx',
    'src/components/checkbox/index.tsx',
    'src/components/checkbox/button.tsx',
    'src/components/checkbox/checkbox.tsx',
    'src/components/checkbox/group.tsx',
    'src/components/collapse/index.tsx',
    'src/components/collapse/collapse.tsx',
    'src/components/collapse/panel.tsx',
    'src/components/datePicker/index.tsx',
    'src/components/datePicker/datePicker.tsx',
    'src/components/datePicker/datePickerContainer.tsx',
    'src/components/datePicker/monthPicker.tsx',
    'src/components/datePicker/monthPickerContainer.tsx',
    'src/components/datePicker/rangePicker.tsx',
    'src/components/datePicker/rangePickerContainer.tsx',
    'src/components/dialog/index.tsx',
    'src/components/dialog/dialog.tsx',
    'src/components/dialog/confirm.tsx',
    'src/components/dialog/actionButton.tsx',
    'src/components/drawer/index.tsx',
    'src/components/drawer/drawer.tsx',
    'src/components/dropdown/index.tsx',
    'src/components/dropdown/dropdown.tsx',
    'src/components/dropdown/dropdownButton.tsx',
    'src/components/dropdown/dropdownButtonContainer.tsx',
    'src/components/embedded/index.tsx',
    'src/components/embedded/embedded.tsx',
    'src/components/embedded/horizon.tsx',
    'src/components/form/index.tsx',
    'src/components/form/formItem.tsx',
    'src/components/form/formField.tsx',
    'src/components/form/context.ts',
    'src/components/grid/col.tsx',
    'src/components/grid/row.tsx',
    'src/components/iconSvg/index.tsx',
    'src/components/input/index.tsx',
    'src/components/input/input.tsx',
    'src/components/link/index.tsx',
    'src/components/link/textLink.tsx',
    'src/components/loading/index.tsx',
    'src/components/loading/loading.tsx',
    'src/components/menu/index.tsx',
    'src/components/menu/menu.tsx',
    'src/components/menu/menuItem.tsx',
    'src/components/menu/subMenu.tsx',
    'src/components/nav/index.tsx',
    'src/components/numberInput/index.tsx',
    'src/components/numberInput/numberInput.tsx',
    'src/components/overlay/index.tsx',
    'src/components/overlay/content.tsx',
    'src/components/overlay/innerOverlay.tsx',
    'src/components/pagination/index.tsx',
    'src/components/pagination/pagination.tsx',
    'src/components/popover/index.tsx',
    'src/components/popover/popover.tsx',
    'src/components/progress/index.tsx',
    'src/components/progress/progress.tsx',
    'src/components/providerConfig/index.tsx',
    'src/components/radio/index.tsx',
    'src/components/radio/button.tsx',
    'src/components/radio/group.tsx',
    'src/components/radio/radio.tsx',
    'src/components/searchBox/index.tsx',
    'src/components/searchBox/search.tsx',
    'src/components/select/index.tsx',
    'src/components/select/optGroup.tsx',
    'src/components/select/option.tsx',
    'src/components/select/searchText.tsx',
    'src/components/select/select.tsx',
    'src/components/slider/index.tsx',
    'src/components/slider/slider.tsx',
    'src/components/steps/index.tsx',
    'src/components/steps/steps.tsx',
    'src/components/steps/step.tsx',
    'src/components/switch/index.tsx',
    'src/components/switch/switch.tsx',
    'src/components/tabs/index.tsx',
    'src/components/table/index.tsx',
    'src/components/table/table.tsx',
    'src/components/tabs/tabPane.tsx',
    'src/components/tabs/tabPaneContainer.tsx',
    'src/components/tabs/tabs.tsx',
    'src/components/tabs/tabsContainer.tsx',
    'src/components/tag/index.tsx',
    'src/components/tag/editableGroup.tsx',
    'src/components/tag/group.tsx',
    'src/components/tag/tag.tsx',
    'src/components/textArea/index.tsx',
    'src/components/textArea/textArea.tsx',
    'src/components/timePicker/index.tsx',
    'src/components/timePicker/timePicker.tsx',
    'src/components/toast/index.tsx',
    'src/components/tooltip/index.tsx',
    'src/components/tooltip/tooltip.tsx',
    'src/components/transfer/index.tsx',
    'src/components/transfer/transfer.tsx',
    'src/components/tree/index.tsx',
    'src/components/tree/tree.tsx',
    'src/components/uploader/index.tsx',
    'src/components/uploader/uploader.tsx'
];
