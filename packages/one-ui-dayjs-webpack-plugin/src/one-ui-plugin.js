/**
 * @file 语言配置
 * <AUTHOR>
 * @date 2020-11-26
 */

const localeMap = {
    'en_GB': 'en-gb',
    'en_US': 'en',
    'zh_CN': 'zh-cn',
    'zh_TW': 'zh-tw'
};

const parseLocale = locale => {
    const mapLocale = localeMap[locale];
    return mapLocale || locale.split('_')[0];
};

module.exports = function (option, dayjsClass, dayjsFactory) {
    let oldLocale = dayjsClass.prototype.locale;
    dayjsClass.prototype.locale = function (arg) {
        if (typeof arg === 'string') {
            arg = parseLocale(arg);
        }
        return oldLocale.call(this, arg);
    };
};