/**
 * @file 替换moment处理
 * <AUTHOR>
 * @date 2020-11-26
 */

const fs = require('fs');
const path = require('path');

const presets = {
    plugins: [
        'isSameOrBefore',
        'isSameOrAfter',
        'advancedFormat',
        'customParseFormat',
        'weekday',
        'weekYear',
        'weekOfYear',
        'isMoment',
        'localeData',
        'localizedFormat'
    ],
    replaceMoment: true
};

const makeEntry = (entry, initEntry) => {
    if (typeof entry === 'object' && !Array.isArray(entry)) {
        Object.keys(entry).forEach(e => {
            entry[e] = makeEntry(entry[e], initEntry);
        });
        return entry;
    }
    if (typeof entry === 'string') {
        return [initEntry, entry];
    }
    if (Array.isArray(entry)) {
        return [initEntry].concat(entry);
    }
    if (typeof entry === 'function') {
        return async () => {
            const originalEntry = await entry();
            return makeEntry(originalEntry, initEntry);
        };
    }
};

class WebpackDayjsPlugin {
    constructor(options = {}) {
        const {plugins, replaceMoment} = options;
        this.plugins = presets.plugins;
        this.replaceMoment = presets.replaceMoment;
        if (plugins) {
            this.plugins = plugins;
        };
        if (replaceMoment !== undefined) {
            this.replaceMoment = replaceMoment;
        };
    }

    apply(compiler) {
        if (this.plugins) {
            const initFilePath = path.resolve(__dirname, 'init-dayjs.js');
            let initContent = 'var dayjs = require( \'dayjs\');';
            this.plugins.forEach(plugin => {
                initContent += `var ${plugin} = require( 'dayjs/plugin/${plugin}');`;
            });
            this.plugins.forEach(plugin => {
                initContent += `dayjs.extend(${plugin});`;
            });
            // add special plugin
            initContent += 'var oneuiPlugin = require( \'./one-ui-plugin\');dayjs.extend(oneuiPlugin);';
            fs.writeFileSync(initFilePath, initContent);
            const {entry} = compiler.options;
            const initEntry = require.resolve(initFilePath);
            compiler.options.entry = makeEntry(entry, initEntry);
        }
        // set dayjs alias
        if (this.replaceMoment) {
            const {alias} = compiler.options.resolve;
            if (alias) {
                alias.moment = 'dayjs';
            }
            else {
                compiler.options.resolve.alias = {
                    moment: 'dayjs'
                };
            }
        }
    }
}

module.exports = WebpackDayjsPlugin;