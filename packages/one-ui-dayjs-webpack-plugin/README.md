# one-ui-dayjs-webpack-plugin

## 使用方式
1. npm install --save dayjs （安装dayjs）
2. npm install --save-dev  one-ui-dayjs-webpack-plugin
3. 更新业务配置
```javascript
// webpack-config.js
const OneUiDayjsWebpackPlugin = require('one-ui-dayjs-webpack-plugin');

module.exports = {
  // ...
  plugins: [
    new OneUiDayjsWebpackPlugin()
  ]
};

// index.js 
// 如果要使用非"英语"语言，请在项目文件里引入对应的语言包
import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')
```