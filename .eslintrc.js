/**
 * @file eslint配置文件
 * <AUTHOR>
 * @date 2020-04-22
 */
module.exports = {
    'root': true,
    'env': {
        'browser': true,
        'es6': true,
        'node': true,
        'jest': true
    },
    'extends': [
        '@ecomfe/eslint-config',
        '@ecomfe/eslint-config/react'
    ],
    'parserOptions': {
        'parser': 'babel-eslint',
        'ecmaVersion': 6,
        'sourceType': 'module'
    },
    'rules': {
        'linebreak-style': 0,
        'no-console': ['error', {'allow': ['error']}],
        'comma-dangle': [2, 'never'],
        'react/require-default-props': 0,
        'react/no-array-index-key': 0,
        'react/prefer-stateless-function': 0,
        'react/jsx-uses-react': 2
    },
    'plugins': [
        'react',
        '@typescript-eslint/eslint-plugin'
    ]
};
