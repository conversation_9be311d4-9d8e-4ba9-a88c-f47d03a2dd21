Global:
  version: 2.0

Default:
  profile: [buildProduction]

Publish:
  profile: [publish]

Profiles:

  - profile:
    name: buildProduction
    mode: AGENT
    environment:
      image: DECK_STD_CENTOS7
      tools:
        - nodejs: 20.0.0
    build:
      command: chmod +x custom_script/build.sh && mkdir output && sh custom_script/build.sh
    artifacts:
      release: true

  - profile:
    name: publish
    mode: AGENT
    environment:
      image: DECK_STD_CENTOS7
      tools:
        - nodejs: 20.0.0
    build:
      command: chmod +x publish.sh && sh publish.sh
    artifacts:
      release: true