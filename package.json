{"name": "@baidu/one-ui", "version": "4.0.0-alpha-94", "description": "使用 react 实现 Light design 设计", "private": true, "sideEffects": ["*.less", "*.css"], "files": ["lib", "es", "src"], "scripts": {"install": "lerna bootstrap", "lint-staged": "lint-staged", "test": "lerna run test", "build": "lerna run build", "link": "lerna add @baidu/one-ui --scope=@baidu/one-ui-doc"}, "lint-staged": {"packages/**/src/**/*.{js,jsx}": ["eslint"]}, "repository": {"type": "git", "url": "ssh://<EMAIL>:8235/baidu/fc-fe/one-ui"}, "keywords": ["light-desing", "react"], "author": {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "license": "ISC", "devDependencies": {"@babel/cli": "^7.4.4", "@babel/core": "^7.4.4", "@babel/preset-env": "^7.4.4", "@babel/preset-react": "^7.0.0", "@ecomfe/eslint-config": "^5.0.2", "@ecomfe/stylelint-config": "^1.0.0", "@typescript-eslint/eslint-plugin": "^4.33.0", "babel-eslint": "^10.1.0", "eslint": "^7.9.0", "eslint-plugin-babel": "^5.3.1", "eslint-plugin-react": "^7.20.6", "eslint-plugin-react-hooks": "^4.1.2", "husky": "^4.3.0", "jest": "^26.4.2", "jest-css-modules-transform": "^4.0.1", "lerna": "^3.22.1", "lint-staged": "^10.3.0", "rimraf": "^3.0.2", "stylelint": "^13.7.1", "typescript": "^4.4.4"}}